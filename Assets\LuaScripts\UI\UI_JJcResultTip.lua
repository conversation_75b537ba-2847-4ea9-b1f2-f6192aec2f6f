local UI_JJcResultTip = Class(BaseView)

function UI_JJcResultTip:OnInit()
	
end

function UI_JJcResultTip:OnCreate(data,resulet)
	self.rank_id = data and data.rank_id or 4
	self.rank_star = data and data.rank_star or 1
	self.result = resulet or 1
	local resultStr
	if self.result == 1 then
		resultStr = LangMgr:GetLang(70000527)
		--SetUIImage(self.ui.m_imgBg,"Sprite/ui_slg_jingjisai/jingji_jiesuan_bg2.png",false)
		
		SetActive(self.ui.m_goFail,false)
		SetActive(self.ui.m_goSuccess,true)
	else
		resultStr = LangMgr:GetLang(70000528)
		--SetUIImage(self.ui.m_imgBg,"Sprite/ui_slg_jingjisai/jingji_jiesuan_bg.png",false)
		SetActive(self.ui.m_goSuccess,false)
		SetActive(self.ui.m_goFail,true)
	end
	SetActive(self.m_goFailBg,self.result ~= 1)
	SetActive(self.m_goSuccessBg,self.result == 1)
	local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_rank,self.rank_id)
	self.ui.m_txtResult1.text = resultStr
	--self.ui.m_txtTitle.text = LangMgr:GetLang(config.langid)
	self.ui.m_txtTitle.text = LangMgr:GetLang(70000694)  --"晋级成功"
	
	self.ui.m_txtResult2.text = resultStr
	--self.ui.m_txtTitle2.text = LangMgr:GetLang(config.langid)
	self.ui.m_txtTitle2.text = LangMgr:GetLang(70000695)  --"晋级失败"
	
	JJcManager:UpdateJJcItem(self.ui.m_goJJcRankItem,self.rank_id,self.rank_star)


	if config.spine and self.result == 1 then
		SetActive(self.ui.m_goJJcRankItem,false)
		local imgRank = GetChild(self.ui.m_goJJcRankItem,"imgRank",UEUI.Image)
		SetActive(imgRank,false)
		SetActive(self.ui.m_goSpine,true)
		local spine = GetChild(self.ui.m_goSpine, "spine", CS.Spine.Unity.SkeletonGraphic)
		local function jjcResult()
			SetActive(self.ui.m_goJJcRankItem,true)				
			--SetActive(self.ui.m_goSpine,false)
			--SetActive(self.ui.m_goJJcRankItem,true)
			--SetSpineAnim(spine,"idle",-1)
			local rankConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_rank,self.rank_id)
			local result = self.result
			local star
			if rankConfig.star <= 5 then
				local type = "type"..rankConfig.star
				local starPath
				if result == 1 then
					starPath = "star"..self.rank_star
				elseif result == 2 then
					starPath = "star"..self.rank_star
				end
				star = GetChild(self.ui.m_goJJcRankItem,"starList/"..type.."/"..starPath.."/light_star")

			else
				star = GetChild(self.ui.m_goJJcRankItem,"starList/type1/star1/light_star")
			end
			if result == 1 and rankConfig.star <= 5 then
				SetActive(star,false)
			elseif result == 2 then

			end
			--TimeMgr:CreateTimer(self, function()
			--if not self.ui then
			--return
			--end
			local aniName
			if result == 1 then
				aniName = "jjcStar"
				SetActive(star,true)
			elseif result == 2 then
				aniName = "jjcStar_1"
			end
			local rankConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_rank,self.rank_id)
			if rankConfig.star > 5 then
				aniName = "jjcStar_2"
			end
			local anim = GetComponent(star, UE.Animation)
			if anim then
				if result == 2 and rankConfig.star <= 5 then
					anim:Stop()
					SetActive(star,true)
				end
				anim:Play(aniName)
			end
			--end, 1, 0.1)
		end
	
		
		local mat = "Spine/arena_badge_"..config.spine.."/duanwei_"..config.spine.."_Material.mat"
		local asset = "Spine/arena_badge_"..config.spine.."/duanwei_"..config.spine.."_SkeletonData.asset"
		--spine:Initialize(true)
		--SetSpineAnim(spine,"open",1)
		--SetSpineCallBack(spine,SPINE_CALLBACK_TYPE.Complete,jjcResult)
		
		local function funcW(mat)
			spine.material = mat
		end
		local function funcW2(asset)
			spine.skeletonDataAsset = asset
			spine:Initialize(true)
			SetSpineAnim(spine,"open",1)
			SetSpineCallBack(spine,SPINE_CALLBACK_TYPE.Complete,jjcResult)
		end
		ResMgr:LoadAssetAsync(mat, AssetDefine.LoadType.Instant,funcW)
		ResMgr:LoadAssetAsync(asset, AssetDefine.LoadType.Instant,funcW2)
	end
end

function UI_JJcResultTip:OnRefresh(param)
    
end

function UI_JJcResultTip:onDestroy()
	--BattleSceneManager:CloseScene()
end

function UI_JJcResultTip:onUIEventClick(go,param)
    local name = go.name

end

return UI_JJcResultTip