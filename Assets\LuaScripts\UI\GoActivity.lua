local GoActivity = {}
local M = GoActivity

local prePath = "Assets/ResPackage/Prefab/UI/GoActivity.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
	item.loadCallBack = loadCallBack
 
	
	local function callBack(obj)
		local newGo = CreateGameObjectWithParent(obj,parent)
		item.go = newGo
		item.trans = newGo.transform
		item:Init()
		if item.loadCallBack then
			item.loadCallBack(item)
		end
	end
	ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)
end

function M:Init()
    self.icon = GetChild(self.go,"bg/empty/icon",UEUI.Image)
    self.count = GetChild(self.go,"bg/Image/m_txtActivity",UEUI.Text)
    self.countGo = GetChild(self.go,"bg/Image")
    self.countActive = true
    AddUIComponentEventCallback(GetChild(self.go,"bg",UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
end

function M:SetItem(id)
    self.id = id
    local data = NetGiftBox:GetGiftById(id)
    self.time = data.time
    data = TriggerGiftConfig:GetDataByID(id)
    self.openViewType = data.open_view_type
    if data.main_icon then
        SetUIImage(self.icon,data.main_icon,false)
    end
	if data.show_priority then
		self.show_priority = data.show_priority
	end
    if self.time == nil then
        self.countActive = false
        SetActive(self.countGo,false)
    else
        self:ChangeItem()
    end
end

function M:ChangeItem()
    if not self.countActive then return end
    local time = self.time- TimeMgr:GetServerTime()
    if time > 0 then
        self.count.text = TimeMgr:CheckHMS(time)
    else
       self.countActive = false
        NetGiftBox:CloseGiftBox(self.id)
        UI_UPDATE(UIDefine.UI_LimitBox,2,self.id)
        self:Close()
    end
end



function M:ClickItem(arg1)
    if self.openViewType == 1 then
        UI_SHOW(UIDefine.UI_LimitList,tonumber(self.id),false)
    elseif self.openViewType == 2 then
        UI_SHOW(UIDefine.UI_BuyKeys,tonumber(self.id))
    elseif self.openViewType == 3 then
        UI_SHOW(UIDefine.UI_BuyKeysNew,tonumber(self.id))
	elseif self.openViewType == 4 then
		UI_SHOW(UIDefine.UI_ResourceGift,tonumber(self.id))
	elseif self.openViewType == 5 then
		UI_SHOW(UIDefine.UI_TwoForOneGift,tonumber(self.id))
	elseif self.openViewType == 6 then
		UI_SHOW(UIDefine.UI_BindGift,tonumber(self.id))
    end
end

function M:Close()
    UEGO.Destroy(self.go)
    self.go = nil
end

--设置item的缩放scale
function M:SetScale(scale)
    self.go.transform.localScale = Vector3.one*scale
end

--设置item的高
function M:GetHeight()
    local item = self.go:GetComponent(TP(UE.RectTransform))
    return item.rect.height
end

return M