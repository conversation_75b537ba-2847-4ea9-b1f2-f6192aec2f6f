local UI_ItemBox = Class(BaseView)
local magicGirl = require("UI.MagicGirl")
local rectTrans = nil
local stateTrans = nil

function UI_ItemBox:OnInit()
    self.m_ListItemShow = {}
    self.m_ListLength = 0
    self.m_ObjItem = nil
    self.m_price = 0
    self.m_moneyType = 0
    self.m_dropObj = nil
    self.m_isCutDown = false
    self.m_useType = 0
    self.m_itemId = 0
    self.m_globSecond = 0
end

function UI_ItemBox:OnCreate(itemObj, dropObj,onlyShow)
	if onlyShow then
		self.m_ObjItem= {}
		self.m_ObjItem.m_Config = ItemConfig:GetDataByID(itemObj.id)
		local idUse = itemObj.id_use
		local dropConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.drop_box, idUse)
		self.m_type = itemObj.m_type
		self.m_ListItemShow = string.split(dropConfig.drop_normal,"|")
		self.m_ListLength = #self.m_ListItemShow
		self.onlyShow = true
	else
		self.m_ObjItem = itemObj
		self.delta = 0
		self.m_type = itemObj.m_Type

		if dropObj then
			self.m_ListItemShow = dropObj:GetBoxUIList()
			self.m_ListLength = #self.m_ListItemShow
			self.m_dropObj = dropObj
		end	
		
		if self.m_ObjItem.m_Id_user3 and self.m_ObjItem.m_Id_user3 == 1 then
			SetActive(self.line,false)
		end
		self.m_itemId =  v2n(self.m_ObjItem.m_Config["id"])
	end
   
	self.content = GetChild(self.ui.m_TableViewH,"Viewport/Content")
	self.content2 = GetChild(self.ui.m_TableViewH2,"Viewport/Content")
	self.content3 = GetChild(self.ui.m_TableViewH3,"Viewport/Content")
	self.tbobj1 = GetChild(self.uiGameObject,"bg/TbObj")
	self.tbobj2 = GetChild(self.uiGameObject,"bg/TbObj2")
	self.tbobj3 = GetChild(self.uiGameObject,"bg/TbObj3")
	self.line = GetChild(self.uiGameObject,"bg/m_goBg1/line")
	self.new_listItemShow = {}
	if self.m_type == ItemUseType.PayBox then
		for k, v in pairs(self.m_ListItemShow) do
			local t = {}
			t.id = v
			t.count = 1
			local is_add = true
			if next(self.new_listItemShow) ~= nil then
				for k1, v1 in pairs(self.new_listItemShow) do
					if v1.id == v then
						is_add = false
						v1.count = v1.count + 1
					end
				end
			end
			if is_add then
				table.insert(self.new_listItemShow,t)
			end
		end
		table.sort(self.new_listItemShow, function(a, b)
				return v2n(a.id) < v2n(b.id)
			end)
		
		SetActive(self.ui.m_goBg1,false)
		SetActive(self.ui.m_goBg2,true)

		SetActive(self.tbobj1,false)
		SetActive(self.tbobj2,true)
		
		SetActive(self.ui.m_goBg3,false)
		SetActive(self.tbobj3,false)
	elseif self.m_type == ItemUseType.GameCurrencyBox then
		SetActive(self.ui.m_goBg1,false)
		SetActive(self.ui.m_goBg2,false)
		SetActive(self.tbobj1,false)
		SetActive(self.tbobj2,false)
		
		SetActive(self.ui.m_goBg3,true)
		SetActive(self.tbobj3,true)
	else
		
		SetActive(self.ui.m_goBg1,true)
		SetActive(self.ui.m_goBg2,false)

		SetActive(self.tbobj1,true)
		SetActive(self.tbobj2,false)
		
		SetActive(self.ui.m_goBg3,false)
		SetActive(self.tbobj3,false)
	end
	
	
    rectTrans = self.uiGameObject:GetComponent(typeof(UE.RectTransform))
    stateTrans = rectTrans:Find("bg"):GetComponent(typeof(UE.RectTransform))
    --stateTrans:DOAnchorPos(Vector2.New(0, 15), 0.5):SetEase(Ease.OutBack)
    self.m_globSecond =  GlobalConfig:GetNumber(1419,30)
    self:InitUI()
	if self.onlyShow then
		SetActive(self.ui.m_btnOpen,false)
	end
end

function UI_ItemBox:InitUI()
    SetActive(self.ui.m_btnOpen,true)
    SetActive(self.ui.m_btnCountDown,false)
    SetActive(self.ui.m_btnPay,false)
    SetActive(self.ui.m_imgTimeBG,false)
    --icon
    local item = GET_UI(self.uiGameObject, "itemimg", TP(UEUI.Image))
	if not self.onlyShow then
    	SetUIImage(item, self.m_ObjItem.m_Config["icon_b"], false)
	end

    local itemBg = GET_UI(self.uiGameObject, "Image", TP(UEUI.Image))
	local effect = GET_UI(self.uiGameObject, "effect", "RectTransform")
    EffectConfig:CreateEffect(17, 0, 0, 0, effect.transform, function( data, tGo, go )
		self:SortOrderAllCom(true)
	end)

    self.ui.m_txtStatusTips.text = LangMgr:GetLang(82)

   self:PayBoxLogic()
   self:CountDownBoxLogic()
    --scroll view
	
	
	local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_itemBox")
	ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant,function(cell)
        if self.m_type == ItemUseType.PayBox then
            for i, v in ipairs(self.new_listItemShow) do
                local go = UEGO.Instantiate(cell)
                if cell then
                    go.name = i
                    AddChild(self.content2,go)

                    self:LoadCellData(i, go, self.new_listItemShow[i])
                end
            end
        elseif self.m_type == ItemUseType.GameCurrencyBox then
            for i, v in ipairs(self.m_ListItemShow) do
                local go = UEGO.Instantiate(cell)
                if cell then
                    go.name = i
                    AddChild(self.content3,go)

                    self:LoadCellData(i, go, self.m_ListItemShow[i])
                end
            end
        else
            for i, v in ipairs(self.m_ListItemShow) do
                local go = UEGO.Instantiate(cell)
                if cell then
                    go.name = i
                    AddChild(self.content,go)

                    self:LoadCellData(i, go, self.m_ListItemShow[i])
                end
            end
        end
    end)
	
	
	
 
    self:ChangeGirlShow()
    self:SetIsUpdateTick(true)

    EventMgr:Add(EventID.PAY_SUCCESS,self.OnPaySuccess,self)
	EventMgr:Add(EventID.PAY_FAILED,self.OnPayFailed,self)
end

function UI_ItemBox:LoadCellData(idx, obj, item)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,item.id or item)
    if config then
        local itemImg = GET_UI(obj, "Image", TP(UEUI.Image))
        local btnTips = GET_UI(obj, "btnTips", TP(UEUI.Button))
		local count = GET_UI(obj, "count", TP(UEUI.Text))
		local obj_count = GET_UI(obj, "count", "RectTransform")
		SetActive(obj_count,false)
		if nil ~= item.count then
			SetActive(obj_count,true)
			count.text = item.count
		elseif self.m_ObjItem.m_Id_user3 then
			local min ,max = self.m_dropObj:GetBoxUIListCount()
			if  min or max then
				SetActive(obj_count,true)
				count.text = min .. "~" .. max 
			end
		end
        SetUIImage(itemImg, config["icon_b"], false)
        RemoveUIComponentEventCallback(btnTips,UEUI.Button)
        AddUIComponentEventCallback(btnTips,UEUI.Button,function (go,param)
            self:onUIEventClick(go,param)
        end)
        self.isInitButton = true
        SetControlExpand(btnTips,config["id"],0)
    end
end

function UI_ItemBox:TickUI(deltaTime)
	if self.onlyShow then
		return
	end
    self.delta = self.delta + deltaTime
    if self.delta > 0.5 then
        self.delta = 0
        self:ChangeGirlShow()
    end

    if self.m_isCutDown == false then
        return
    end
    self:CountDownBoxTick()
end

function UI_ItemBox:CloseMagic()
    if self.magicGirl then
        self.magicGirl:Close()
        self.magicGirl = nil
    end
end

function UI_ItemBox:ChangeGirlShow()
    if NetUpdatePlayerData.playerInfo.curMap ~= MAP_ID_MAIN then return end
    local isOpen,active = LimitActivityController:GetActiveIsOpen(ActivityTotal.Normal,ActivitySubtype.OpenBox)
    if isOpen and self.magicGirl == nil and active:GetScore(self.m_ObjItem.m_Config.id) then
        self.magicGirl = magicGirl:Create(self.ui.m_goGirl)
        self.magicGirl:SetPos(self.m_ObjItem.m_Config.id)
    elseif not isOpen and self.magicGirl then
        self:CloseMagic()
    end
end

function UI_ItemBox:OnRefresh(param)

end

function UI_ItemBox:onDestroy()
    EventMgr:Remove(EventID.PAY_SUCCESS,self.OnPaySuccess, self)
	EventMgr:Remove(EventID.PAY_FAILED,self.OnPayFailed,self)
    self:CloseMagic()
    self.m_ListItemShow = nil
    self.m_ListLength = 0
    self.m_ObjItem = nil
    self.m_price = 0
    self.m_moneyType = 0
    self.m_dropObj = nil
    self.m_isCutDown = nil
    self.m_useType = nil
    self.m_itemId = nil
    self.m_globSecond = nil
	self.onlyShow = nil
end

function UI_ItemBox:closeAnima()
    self.endPos = Vector2.New(0, -250)
    stateTrans:DOAnchorPos(Vector2(0, -rectTrans.rect.height), 0.1):SetEase(Ease.InBack):OnComplete(function()
        self:Close()
    end)
end

function UI_ItemBox:onUIEventClick(go, param)
    local name = go.name
    if name == "btnClose" then
        --self:closeAnima()
        self:Close()
    elseif name == "m_btnOpen" then
        self:Close(false, function()
            self:OpenFreeBox()
		 	if self.m_useType ~= ItemUseType.PayBox and self.m_useType ~= ItemUseType.GameCurrencyBox then
		 		self.m_ObjItem:OpenBox()
			else
		 		Log.Error("UI_ItemBox  self.m_useType : " , self.m_useType)
			end 
        end)
    elseif name == "m_btnPay" then
        self:PayBtnLogic()
    elseif name == "m_btnCountDown" then
        self:CountDownBtnLogic()
    elseif name == "btnTips" then
        local itemId = GetControlExpand(go,0)
        if itemId > 0  then
            UI_SHOW(UIDefine.UI_ItemTips,itemId,7097)
        end
    end
end


function UI_ItemBox:PayBoxLogic()
    --payBox
    if not self.m_dropObj then
        return
    end
    self.m_useType = v2n(self.m_ObjItem.m_Config["type_use"])
    if self.m_useType ~= ItemUseType.PayBox  and self.m_useType ~= ItemUseType.GameCurrencyBox then
        return
    end
    local priceRectTrans = GetComponent(self.ui.m_txtPrice.gameObject,UE.RectTransform)
    GameUtil.SetRTPosition(priceRectTrans,37,0)

    if self.m_useType  ==  ItemUseType.PayBox or self.m_useType == ItemUseType.GameCurrencyBox then
        SetActive(self.ui.m_btnOpen,false)
        SetActive(self.ui.m_btnPay,true)
    end

    if self.m_useType == ItemUseType.GameCurrencyBox then
        local countDownTime = self.m_ObjItem.m_Config["id_use2"]
        if not IsNilOrEmpty(countDownTime) then
            SetActive(self.ui.m_imgTimeBG,true)
            local diffTime = self.m_ObjItem:GetCutDownDiffTime()
            self.ui.m_txtCutTime.text = TimeMgr:CutDownTime(diffTime,3)
            self.m_isCutDown = true
        end

        local moneyStr = self.m_ObjItem.m_Config["id_waste"]
        if not IsNilOrEmpty(moneyStr) then
            local moneyData = SplitStringToNum(moneyStr,"|")
            self.m_moneyType = moneyData[1]
            self.m_price = moneyData[2]
            self.ui.m_txtPrice.text = v2s(self.m_price)

            local moneyIcon = ItemConfig:GetImg(self.m_moneyType)
            SetImageSprite(self.ui.m_imgDiamond,moneyIcon)
        end
        return
    end

    if self.m_useType == ItemUseType.PayBox then
        SetActive(self.ui.m_imgDiamond,false)
        local payId = self.m_ObjItem.m_Config["id_waste"]
        local payConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.payment,payId)
        self.m_moneyType = v2n(payId)
		local str = LangMgr:GetLang(payConfig["price_langid"])
        self.m_price =  v2n(payConfig["price"]) --v2n(payConfig["price"])
        self.ui.m_txtPrice.text = str --v2s(self.m_price) --string.format(LangMgr:GetLang(7080),
		self.payPriceTxt = str
        GameUtil.SetRTPosition(priceRectTrans,0,0)

        PurchaseManager:CreateScoreTag(payId,self.ui.m_btnPay.transform)
    end
end

function UI_ItemBox:CountDownBoxLogic( isFree )
    self.m_useType = v2n(self.m_ObjItem.m_Config["type_use"])
    if self.m_useType ~= ItemUseType.CutDownBox  then
        return
    end
    if not self.m_ObjItem then
        return
    end
    SetActive(self.ui.m_txtStatusTips,false)
    SetActive(self.ui.m_imgCountDown,false)
    SetActive(self.ui.m_btnOpen,false)
    SetActive(self.ui.m_btnCountDown,false)
    SetActive(self.ui.m_btnPay,false)
    SetActive(self.ui.m_imgTimeBG,false)
    local priceRectTrans = GetComponent(self.ui.m_txtPrice.gameObject,UE.RectTransform)
    GameUtil.SetRTPosition(priceRectTrans,37,0)

    local status = self.m_ObjItem:GetCountDownBoxStatus()
    local diffTime = self.m_ObjItem:GetCutDownDiffTime()
   local countDownTime = v2n(self.m_ObjItem.m_Config["id_use2"])
    if status < CD_BOX_STATUS.ACTIVE  then
        local hour = math.floor( countDownTime / 3600)
        local tips = string.format(LangMgr:GetLang(7081),hour)
        self.ui.m_txtStatusTips.text = tips
        SetActive(self.ui.m_txtStatusTips,true)
        SetActive(self.ui.m_btnCountDown,true)
        return
    end

    if status == CD_BOX_STATUS.FINISH then
        SetActive(self.ui.m_btnOpen,true)
        self.ui.m_txtStatusTips.text = LangMgr:GetLang(82)
        SetActive(self.ui.m_txtStatusTips,true)
    elseif status == CD_BOX_STATUS.ACTIVE then
        self.m_isCutDown = true
        self.ui.m_txtCountDown.text = TimeMgr:ConverSecondToString(diffTime)
        SetActive(self.ui.m_imgCountDown,true)
        if diffTime <= self.m_globSecond then
            isFree = true
        end
        if isFree then
            SetActive(self.ui.m_btnPay,false)
            SetActive(self.ui.m_btnOpen, true)
            return
        end
        SetActive(self.ui.m_btnPay,true)
        SetActive(self.ui.m_btnOpen, false)
        local isGold,money = GetSpeedUpDiamond(diffTime)
        self.m_moneyType = isGold and ItemID.COIN or ItemID.DIAMOND
        self.m_price = money

        local moneyIcon = ItemConfig:GetImg(self.m_moneyType)
        SetImageSprite(self.ui.m_imgDiamond,moneyIcon)
		--local payId = self.m_ObjItem.m_Config["id_waste"]
		--local payConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.payment,payId)
		--local str = LangMgr:GetLang(payConfig["price_langid"])
        self.ui.m_txtPrice.text =v2s(self.m_price)
    end
end

function UI_ItemBox:PayBtnLogic()
    if self.m_price <= 0 or self.m_moneyType <= 0  then
        return
    end
    if self.m_useType == ItemUseType.GameCurrencyBox or self.m_useType == ItemUseType.CutDownBox then
        local isCoin = false
        if self.m_moneyType == ItemID.COIN then
            isCoin = true
        end
        local sendStr = string.format(LangMgr:GetLang(7082),self.m_price)
        --UI_SHOW(UIDefine.UI_Tips,sendStr,function()
            if NetUpdatePlayerData:ConsumeResource(isCoin,self.m_price,25,self.m_itemId,1,nil,"UI_ItemBox") then
                self:OnBuySuccess()
            end
        --end)
        return
    end

    if self.m_useType == ItemUseType.PayBox then
		self.m_ObjItem:SetPaying(true)
        PaymentConfig:ShowPay(self.m_moneyType)
    end
end

function UI_ItemBox:CountDownBtnLogic()
    if MapController:IsQueueFull() then
        UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(7083))
        return
    end
    local cutDownValue = self.m_ObjItem.m_Config["id_use2"]
    if IsNilOrEmpty(cutDownValue) then
        Log.Error("Error excel item box: id_use2")
        return
    end
    self.m_ObjItem:SetOpenCutDownTime(v2n(cutDownValue))
    self.m_ObjItem:SetCountDownBoxStatus(CD_BOX_STATUS.ACTIVE)
    MapController:AddBoxQueue(1)
    self:CountDownBoxLogic()
end

function UI_ItemBox:OnBuySuccess()
    local function payCall()
        if self.m_useType == ItemUseType.CutDownBox then
            local status = self.m_ObjItem:GetCountDownBoxStatus()
            if status == CD_BOX_STATUS.ACTIVE then
                self.m_ObjItem:SetCountDownBoxStatus(CD_BOX_STATUS.FINISH)
                MapController:AddBoxQueue(-1)
            end
        end
        self.m_ObjItem:OpenBox()
    end
    self:Close(false, payCall)
end

function UI_ItemBox:OnPaySuccess()
	self:Close(false)
end

function UI_ItemBox:CountDownBoxTick()
    if not self.m_ObjItem then
       return
    end
    local diffTime = self.m_ObjItem:GetCutDownDiffTime()
    if diffTime <= 0  then
        self.m_isCutDown = false
        if self.m_useType == ItemUseType.GameCurrencyBox then
            UIMgr:Close(UIDefine.UI_ItemBox)
        elseif self.m_useType == ItemUseType.CutDownBox then
            self:CountDownBoxLogic()
        end
        return
    end
    if self.m_useType == ItemUseType.GameCurrencyBox then
        local diffTime = self.m_ObjItem:GetCutDownDiffTime()
        self.ui.m_txtCutTime.text = TimeMgr:CutDownTime(diffTime,3)
    elseif self.m_useType == ItemUseType.CutDownBox then
        self.ui.m_txtCountDown.text = TimeMgr:ConverSecondToString(diffTime)
        if diffTime == self.m_globSecond then
            self:CountDownBoxLogic(true)
        elseif diffTime > self.m_globSecond then
            local _,money = GetSpeedUpDiamond(diffTime)
            self.m_price = money
            self.ui.m_txtPrice.text = v2s(self.m_price)
        end
    end
   
end

function UI_ItemBox:OpenFreeBox()
    if self.m_useType == ItemUseType.CutDownBox then       
        local status = self.m_ObjItem:GetCountDownBoxStatus()
        if status == CD_BOX_STATUS.ACTIVE then
            local diffTime = self.m_ObjItem:GetCutDownDiffTime()
            if diffTime <= self.m_globSecond then
                MapController:AddBoxQueue(-1)
            end
        end
    end
end

function UI_ItemBox:OnPayFailed(id)
	local config = PaymentConfig:GetDataByID(self.payId)
	if config and config.type == 5 and self.m_ObjItem and self.m_ObjItem.m_Config then
		if self.m_ObjItem.m_Config.id_waste and v2n(self.m_ObjItem.m_Config.id_waste) == id then
			self.m_ObjItem:SetPaying(false)
		end
	end
end

return UI_ItemBox