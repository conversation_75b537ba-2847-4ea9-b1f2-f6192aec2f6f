local SkiingController = {}
local prePath = "Assets/ResPackage/Prefab/UI/SkiingController.prefab"
local pre
local commonHead 

local SkiingMovementHandler = require "UI.SkiingMovementHandler"
local RoadMaxNode = { [1] = 35; [2] = 27;[3] = 35;[4] = 27;}

--新滑雪活动 滑雪场景
function SkiingController:Create(parent)
    local item = {}
    setmetatable(item,{__index = SkiingController})
    
    local logic = function()
        local newGo = CreateGameObjectWithParent(pre,parent)
        item.go = newGo
        item.trans = newGo.transform

        if not commonHead then
            LoadCommonHeadAsync(function(headObj)
                commonHead = headObj
                item:CreateAfter()
                UI_UPDATE(UIDefine.UI_NewSkiingMatch,"RefreshLayer")
            end)
        else
            item:CreateAfter()
            UI_UPDATE(UIDefine.UI_NewSkiingMatch,"RefreshLayer")
        end
    end
    
    if pre == nil then
        ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,function(obj)
            pre = obj
            logic()
        end)
    else
        logic()
    end
    return item
end

function SkiingController:CreateAfter()
	self.gameObject = self.go
	self.controller = SkiingMovementHandler.new()
	self.controller:Init(self.go,self.go.transform)

	self.roadRewardItemList = {}
	self.needCheckRoadReward = false
	self.paused = false

	self.SkierObj = GetChild(self.gameObject,"Player/m_goPlayerObj")
	self.SkierRect = GetComponent(self.SkierObj,UE.RectTransform)
	self.carObj = GetChild(self.SkierObj, "Car")--本玩家雪车obj
	self.TopRaycast = GetChild(self.gameObject,"TopRaycast",CS.TopTrigger)
	if self.TopRaycast and self.TopRaycast.InitData then
		self.TopRaycast:InitData(self.SkierObj)
	end
	-----------------雪车数据显示-----------------
	self.myHeadObj = GetChild(self.SkierObj, "Head")--本玩家头像obj
	self.myHeadIcon = GetChild(self.SkierObj, "Head/icon", UEUI.Image)  -- 玩家头像
	self.myRank = GetChild(self.SkierObj, "Head/rank", UEUI.Text)--本玩家排名
	self.myDistance = GetChild(self.SkierObj, "Head/distance", UEUI.Text)--本玩家路程

	-----------------特效表现-----------------
	self.flySnowEffect = GetChild(self.SkierObj, "Car/flySnow")--飞雪特效
	self.speedLine = GetChild(self.gameObject, "SpeedLine")--速度线
	self.happyFace = GetChild(self.SkierObj, "HappyFace")--开心表情
	self.sadFace = GetChild(self.SkierObj, "SadFace")--伤心表情
	self.strikeEffect = GetChild(self.SkierObj, "Car/strikeEffect")--暴击氮气特效
	self.collectEffect = GetChild(self.SkierObj, "Car/collectEffect")--撞击道具特效

	self.myPlayerTrans = self.SkierObj.transform
	self.m_goOtherPlayerObj = GetChild(self.gameObject, "Player/m_goOtherPlayerObj")
    
    CreatCommonHeadView(commonHead,GetChild(self.SkierObj, "Head/headNode",UE.Transform),0.5)
    CreatCommonHeadView(commonHead,GetChild(self.m_goOtherPlayerObj, "Head/headNode",UE.Transform),0.5)
    -----------------游标-----------------
	self.cursorRoot = GetChild(self.gameObject, "Cursor")
	self.cursorRect = self.cursorRoot.transform
	self.m_goLeftCursor = GetChild(self.gameObject, "Cursor/m_goLeftCursor")
	self.m_goRightCursor = GetChild(self.gameObject, "Cursor/m_goRightCursor")

	self.leftCursorOtherTrans = GetChild(self.gameObject, "Cursor/m_goLeftCursorNode/Other",UE.Transform)
	self.rightCursorOtherTrans = GetChild(self.gameObject, "Cursor/m_goRightCursorNode/Other",UE.Transform)

	self.m_goLeftCursorNode = GetChild(self.gameObject, "Cursor/m_goLeftCursorNode")
	self.m_goRightCursorNode = GetChild(self.gameObject, "Cursor/m_goRightCursorNode")

	--暴击道具
	self.baoJiProp = GetChild(self.gameObject, "BaoJiProp")
	SetActive(self.baoJiProp, false)
	--道路积分币
	self.roadCoinObj = GetChild(self.gameObject, "RoadCoin")
	SetActive(self.roadCoinObj, false)
	--滑雪掉落道具
	self.skiingProp = GetChild(self.gameObject, "SkiingProp")
	SetActive(self.skiingProp, false)

	self.baoJiProp = GetChild(self.gameObject, "BaoJiProp")
	--用于表现动画的角色
	self.animObj = GetChild(self.gameObject,"Player/OtherPlayerAnim")

	SetActive(self.SkierObj, false)
	SetActive(self.m_goLeftCursor, false)
	SetActive(self.m_goRightCursor, false)
	SetActive(self.animObj, false)
	SetActive(self.m_goOtherPlayerObj, false)
	self.lastSpeed = 10;

	self.cursorCount = 0

    CreatCommonHeadView(commonHead,GetChild(self.m_goLeftCursor, "headNode",UE.Transform),0.5)
    CreatCommonHeadView(commonHead,GetChild(self.m_goRightCursor, "headNode",UE.Transform),0.5)
    
	self:OnInit()
end
--雪道节点
local RoadType = {
    Bg = "Bg";
    Middle = "Middle";
    Road = "Road";
    Front = "Front";
}

function SkiingController:OnInit()
    self.canSkiing = false
    self:IsStopMove(true)
    --展示表情
    self.emojiTime = 0
    self.showEmoji = false
    self.isHappy = false
    
    self:InitDropList()
    self.controller:InitNodeList()
    
    self:InitRoadNodeDic()
    self:InitTop3Cursor()
    self:InitScenePosition()
    self:InitOtherPlayerPosition()

    --刚进入场景判断前方四个节点处是否有奖励
    local length = NetNewSkiingData:GetRoadLengthSum()
    local nodeName = NetNewSkiingData:GetStopNode()
    if length > 0 and nodeName ~= "" then
        self:CheckFrontExistCoin(length,nodeName)
    else
        self:CheckFrontExistCoin(0,"1_3")
    end
    self:CheckOtherCar(length,nodeName)
    self:UpdateMyHeadIcon()
end

local LimitHeight = {
    [1] = 1.6;
    [2] = 1.4;
    [3] = 0.4;
}
function SkiingController:TickUI(deltaTime)
	if self.paused then
		return
	end

	if self.controller then
		self.controller:Update(deltaTime)

        if not self.controller.isStop then
            self:FollowPlayerPos()
        end
	end
    
	if self.needCheckRoadReward then
		self.needCheckRoadReward = false
		self:TryRemoveRoadReward()
	end
end

--排行榜数据更新
function SkiingController:UpdateRankData()
    self:InitTop3Cursor()--更新游标
    --self:InitOtherPlayerPosition()--更新场景中雪车
end

-------------------------------------业务逻辑-------------------------------------
--开始滑雪
function SkiingController:StartSkiing(count)
    self:IsStopMove(false)
    self:SpeedUpPerformance(count)
    self.canSkiing = true
end

--设置玩家排名文本
function SkiingController:SetPlayerRank(rank)
    local canShow = rank and rank ~= 0
    local right = GetChild(self.myHeadObj,"Right")
    SetActive(right,canShow)

    if self.myRank then
        self.myRank.text = canShow and rank or ""
    end
end

--展示玩家滑完后里程数动态变化文本
function SkiingController:ShowDistanceAddAnim(pastSumExp,addLength)
	self.myDistance.text = Mathf.Floor(NetNewSkiingData:GetRoadLengthSum()).."m"
end

function SkiingController:UpdateDistance(exp)
	self.myDistance.text = Mathf.Floor(exp) .."m"
end

--- 刷新玩家头像
function SkiingController:UpdateMyHeadIcon()
    --local playerHead = NetUpdatePlayerData:GetPlayerInfo().head
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerHead)
    --if headConfig then
    --    SetUIImage(self.myHeadIcon, headConfig["icon"], false)
    --end
    
    local obj = GetChild(self.myHeadObj,"headNode/CustomHead")
    SetMyHeadAndBorderByGo(obj)
    obj.transform.localPosition = Vector3.zero
end

--动画停止时相关特效或数值重置
function SkiingController:StopAnimReset()
    self:IsStopMove(true)
    self.canSkiing = false
    SetActive(self.flySnowEffect,false)
    SetActive(self.strikeEffect,false)
	SetActive(self.collectEffect,false)
    SetActive(self.speedLine,false)
    self:ShowCameraCloser(false)
    self:SaveScene()  
  
end

function SkiingController:GetNodeInfo(nodeName)
    local str = Split1(nodeName,"_")
    return v2n(str[1]),v2n(str[2])
end

-------------------------------------动画特效表现-------------------------------------
--加速表现
function SkiingController:SpeedUpPerformance(carCount)
    --倍数为5、10、20倍时，雪车有激起飞雪效果，速度会根据倍数加快
    --倍数为50、100倍时，雪车有激起飞雪效果、速度会根据倍数加快、表现虚化镜头、拉近相机
    local performance = self:GetPerformanceConfig(carCount)
    self:SetSkiingSpeed(performance.speed)
    self.lastSpeed = performance.speed
    if performance.snowFly then
        self:ShowSnowFly()
    end

    if performance.cameraBlur then
        self:ShowCameraBlur()
    end

    if performance.cameraCloser then
        self:ShowCameraCloser(true)
    end
end

--获取倍数速度效果配置
---@param count number 倍数
function SkiingController:GetPerformanceConfig(count)
    local configId
    local result = {
        speed = 14;--速度
        snowFly = false;--飞雪效果
        cameraBlur = false;--镜头虚化
        cameraCloser = false;--镜头拉近
    }
    local baseSpeed = 10;
    if count == 1 then
        configId = 10
        result.speed = baseSpeed;
        result.snowFly = false;--飞雪效果
        result.cameraBlur = false;--镜头虚化
        result.cameraCloser = false;--镜头拉近
    elseif  count == 2 then
        configId = 11
        result.speed = baseSpeed+1;
        result.snowFly = false;--飞雪效果
        result.cameraBlur = false;--镜头虚化
        result.cameraCloser = false;--镜头拉近
    elseif  count == 3 then
        configId = 12
        result.speed = baseSpeed+2;
        result.snowFly = true;--飞雪效果
        result.cameraBlur = false;--镜头虚化
        result.cameraCloser = false;--镜头拉近
    elseif  count == 5 then
        configId = 13
        result.speed = baseSpeed+3;
        result.snowFly = true;--飞雪效果
        result.cameraBlur = false;--镜头虚化
        result.cameraCloser = false;--镜头拉近
    elseif  count == 10 then
        configId = 14
        result.speed = baseSpeed+4;
        result.snowFly = true;--飞雪效果
        result.cameraBlur = true;--镜头虚化
        result.cameraCloser = false;--镜头拉近
    end

    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.skating_setting_new, configId)
    if config then
        result.speed = v2n(config.value)
    end
    return result
end

--是否停止雪车移动
function SkiingController:IsStopMove(isStop)
    self.controller.isStop = isStop
end

--设置滑雪速度
function SkiingController:SetSkiingSpeed(speed)
    self.controller.managerSpeed = speed
end

--展示飞雪效果
function SkiingController:ShowSnowFly()
    SetActive(self.flySnowEffect,true)
end

--展示镜头虚化效果
function SkiingController:ShowCameraBlur()
    SetActive(self.speedLine,true)
end

--展示镜头拉近效果
function SkiingController:ShowCameraCloser(isCloser)
    local scale = isCloser and 1.5 or 1
    local list = {"Bg","Middle","Road","Front","Player"}
    for _,v in ipairs(list) do
        local trans = GetChild(self.gameObject,v,UE.Transform)
        trans:DOScale(Vector3.New(scale,scale,1),0.5)
    end
end

--展示暴击氮气效果
---@param count number 第几次暴击
function SkiingController:ShowStrikeEffect()
    self:SetSkiingSpeed(self.strikeSpeed)
	
	SetActive(self.collectEffect,true)
	AddDOTweenNumberDelay(0,1,1,0,function (value)end,function ()
		SetActive(self.collectEffect,false)
		
	end)
	
	--self.gameObject.transform:DOPunchScale(Vector3.New(0.4, 0.4, 0), 0.5, 1, 1)
	self.paused = true
	AddDOTweenNumberDelay(0,1,0.8,0,function (value)end,function ()
		self.paused = false
		SetActive(self.strikeEffect,true)
	end)
	
end

--收集路程积分比飞入动画
function SkiingController:CollectCoinFly(shopTrans,id,count)
    local posScreen = UIMgr:GetObjectScreenPos(shopTrans)
    local startPos = UIMgr:GetObjectScreenPos(self.myPlayerTrans)
    MapController:FlyUIAnim(startPos.x,startPos.y, id, count,posScreen.x, posScreen.y)
    NetNewSkiingData:AddRoadCoin(count)
end

--设置暴击滑雪速度
function SkiingController:SetStrikeSpeed(count)
    self.strikeSpeed = self:GetStrikeSpeed(count)
end

--获取暴击速度
---@param count number 第几次暴击
function SkiingController:GetStrikeSpeed(count)
    local configId = count == 1 and 18 or 19
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.skating_setting_new, configId)
    local multiRatio = 1
    if config then
        multiRatio = v2n(config.value)
    end
    
    local result = multiRatio*self.lastSpeed
    result = (result >= 36) and 36 or result 
    return result
end
-------------------------------------玩家游标-------------------------------------
--初始化前五玩家的游标信息和位置
--（未获取排行榜数据时，不显示其他玩家的游标和雪车）
function SkiingController:InitTop3Cursor()
    local top5List = NetNewSkiingData:GetTop5Player()
    if not top5List then
        return
    end
    local isEmpty = next(top5List) == nil
    self.cursorRoot:SetActive(not isEmpty)
    SetActive(self.myRank,not isEmpty)
    if isEmpty then
        return 
    end
    --清理上一次的数据
    self:DestroyAllChild(self.leftCursorOtherTrans)
    self:DestroyAllChild(self.rightCursorOtherTrans)
    local topList = {}
    for i = 1,3 do
        topList[i] = top5List[i]
    end
    local myIndex = NetNewSkiingData:GetMyRank()  --本玩家是否前五
    self:SetPlayerRank(myIndex)
    local rightList = {}
    local leftList = {}
    --[[界面中只显示前三名玩家的游标 不显示本玩家游标
      1)玩家排名 > 3  前三名游标都显示在右边
      2)玩家排名 = 3  其他2名玩家显示在右边
      3)3<玩家排名<1  （例如第2名），第3名玩家显示左边；第1名玩家显示右边
      4)玩家排名=1   其他2名玩家显示左边
    ]]
    local topValue = 3
    if myIndex > 0 and myIndex <= topValue then
        if myIndex == topValue then
            for i = 1,topValue do
                table.insert((i == topValue) and leftList or rightList,topList[i])
            end
        elseif myIndex == 1 then
            for i = 1,topValue do
                table.insert((i == 1) and rightList or leftList,topList[i])
            end
        else
            for i = 1,topValue do
                table.insert((i > myIndex) and leftList or rightList,topList[i])
            end
        end
    else
        for i = 1,3 do
            table.insert(rightList,topList[i])
        end
    end
    
    local leftObj = self.m_goLeftCursor
    local rightObj = self.m_goRightCursor
    SetActive(leftObj,false)
    SetActive(rightObj,false)
    local leftCount = 0;
    for _,v in ipairs(leftList)do
        local isMyPlayer = v.rank == myIndex
        if not isMyPlayer then
            leftCount = leftCount + 1
            self:CreateCursorObj(leftObj, self.leftCursorOtherTrans,v)
        end
    end
    local rightCount = 0;
    for _,v in ipairs(rightList)do
        local isMyPlayer = v.rank == myIndex
        if not isMyPlayer then
            rightCount = rightCount + 1
            self:CreateCursorObj(rightObj, self.rightCursorOtherTrans,v)
        end
    end
    self.cursorCount = math.max(leftCount,rightCount)
    self:FollowPlayerPos()
end

--创建玩家游标实例
function SkiingController:CreateCursorObj(obj,parent,info)
    local item = UEGO.Instantiate(obj, parent)
    item.transform.localPosition = Vector3.New(0,0,0)
    self:InitCursorInfo(item,info)
    SetActive(item,true)
    return item
end

--显示玩家游标信息
function SkiingController:InitCursorInfo(obj,info)
    --local icon = GetChild(obj, "Icon", UEUI.Image)
    local length = GetChild(obj, "length", UEUI.Text)
    local rank = GetChild(obj, "rank", UEUI.Text)
    local name = GetChild(obj, "name", UEUI.Text)
    --local headData = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set,v2n(info.icon))
    --if headData and obj then
    --    SetImageSync(icon,headData.icon,false)
    --end

    local customHeadObj = GetChild(obj,"headNode/CustomHead")
    SetHeadAndBorderByGo(customHeadObj,info.icon,info.border)
    customHeadObj.transform.localPosition = Vector3.zero
    
    length.text = info.point.."m"
    local rankFlag = {"st","nd","rd"}
    local rankStr = rankFlag[info.rank] or "th"
    rank.text = info.rank..rankStr
    name.text = info.name
end

--显示玩家头像信息
function SkiingController:InitHeadInfo(headObj,info)
    --local icon = GetChild(headObj, "Head/icon", UEUI.Image)--头像
    local rank = GetChild(headObj, "Head/rank", UEUI.Text)--排名
    local distance = GetChild(headObj, "Head/distance", UEUI.Text)--路程

    --local headData = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set,info.head)
    --if headData then
    --    SetUIImage(icon,headData.icon,false)
    --end
    
    local customHeadObj = GetChild(headObj,"Head/headNode/CustomHead")
    SetHeadAndBorderByGo(customHeadObj,info.icon,info.border)
    customHeadObj.transform.localPosition = Vector3.zero
    
    rank.text = info.rank or ""
    distance.text = (info.point or "").."m"
end

--初始化本玩家位置
function SkiingController:InitScenePosition()
    for k,v in pairs(RoadType) do
        self:InitSceneStatus(v)
    end
    self.myDistance.text = Mathf.Floor(NetNewSkiingData:GetRoadLengthSum()).."m"
    self.controller:CheckRectOrder()

    local posData = NetNewSkiingData:GetStopPos()
    if posData.height == nil then
        local start = self.RoadNodeDic[1]["1_3"].position
        self.SkierObj.transform.position = Vector3.New(start.x,start.y+0.1,start.z)
        self.carObj.transform.localRotation = Quaternion.Euler(0, 0, 0)
    else
        local y = posData.height or -64
        local x = self.SkierRect.anchoredPosition.x
        self.SkierRect.anchoredPosition = Vector2.New(x,y)
    end
    SetActive(self.SkierObj, true)
end

-------------------------------------场景信息存储与初始化-------------------------------------

function SkiingController:GetSceneStatus(nodeName)
    local mainRoad = GetChild(self.gameObject, nodeName.."/MainRoad")
    local transition = GetChild(self.gameObject, nodeName.."/Transition")
    local road1 = GetChild(mainRoad, "road1")
    local road2 = GetChild(transition, "road2")
    local road3 = GetChild(mainRoad, "road3")
    local road4 = GetChild(transition, "road4")

    --道路节点和过渡节点的显隐
    return {
        mainRoadPositionX = math.modf(GetComponent(mainRoad,UE.RectTransform).anchoredPosition.x);
        transitionPositionX = math.modf(GetComponent(transition,UE.RectTransform).anchoredPosition.x);
        road1Active = road1.activeInHierarchy;
        road2Active = road2.activeInHierarchy;
        road3Active = road3.activeInHierarchy;
        road4Active = road4.activeInHierarchy;
    }
end

function SkiingController:InitSceneStatus(nodeName)
    --初始化背景地图位置
    local roadRect = GetChild(self.gameObject, nodeName.."/MainRoad",UE.RectTransform)
    local roadTransitionRect = GetChild(self.gameObject, nodeName.."/Transition",UE.RectTransform)

    local road1 = GetChild(self.gameObject, nodeName.."/MainRoad/road1",UE.Transform)
    local road2 = GetChild(self.gameObject, nodeName.."/Transition/road2",UE.Transform)
    local road3 = GetChild(self.gameObject, nodeName.."/MainRoad/road3",UE.Transform)
    local road4 = GetChild(self.gameObject, nodeName.."/Transition/road4",UE.Transform)

    local info = NetNewSkiingData:GetSceneInfo()[nodeName]
    if not info then
        return
    end
    road1.gameObject:SetActive(info.road1Active)
    road2.gameObject:SetActive(info.road2Active)
    road3.gameObject:SetActive(info.road3Active)
    road4.gameObject:SetActive(info.road4Active)

    roadRect.anchoredPosition = Vector2.New(info.mainRoadPositionX,0)
    roadTransitionRect.anchoredPosition = Vector2.New(info.transitionPositionX,0)

	if road1.gameObject.activeSelf == road3.gameObject.activeSelf then
		SetActive(road1,true)
		SetActive(road3,false)
		local thinkTable = {
			["fix16_type"] = 1,
			["fix16_nodename"] = nodeName
		}
		SdkHelper:ThinkingTrackEvent("FIX_16_Skiing", thinkTable)
	end

	if road2.gameObject.activeSelf == road4.gameObject.activeSelf then
		SetActive(road2,true)
		SetActive(road4,false)
		local thinkTable = {
			["fix16_type"] = 2,
			["fix16_nodename"] = nodeName
		}
		SdkHelper:ThinkingTrackEvent("FIX_16_Skiing", thinkTable)
	end
end

--存储场景信息
function SkiingController:SaveScene()
    local info = {
        [RoadType.Bg] = self:GetSceneStatus(RoadType.Bg);
        [RoadType.Middle] = self:GetSceneStatus(RoadType.Middle);
        [RoadType.Road] = self:GetSceneStatus(RoadType.Road);
        [RoadType.Front] = self:GetSceneStatus(RoadType.Front);
    }
    NetNewSkiingData:SetSceneInfo(info)
    self:SaveCarStopData()
end

--存储本玩家雪车位置数据
function SkiingController:SaveCarStopData()
    NetNewSkiingData:SaveStopPos({
        height = math.modf(self.SkierRect.anchoredPosition.y);
        angleZ = math.modf(self.carObj.transform.eulerAngles.z);
    })
end
-------------------------------------道路节点-------------------------------------
--缓存滑道上的路节点
function SkiingController:InitRoadNodeDic()
    self.RoadNodeDic = {}
    local roadSequence = {"MainRoad/road1","Transition/road2","MainRoad/road3","Transition/road4"}
    for k,v in ipairs(roadSequence)do
        local road = GetChild(self.gameObject, "Road/"..v.."/RoadNode",UE.Transform)
        self.RoadNodeDic[k] = {}
        for i = 0,road.childCount-1 do
            local child = road:GetChild(i)
            --key = 节点名称  value = 节点Transform
            self.RoadNodeDic[k][child.name] = child
        end
    end
end

function SkiingController:InitDropList()
    self.DropConfig = {}
    local config = ConfigMgr:GetData(ConfigDefine.ID.skating_track_new)
    if config then
        for _,v in ipairs(config)do
            self.DropConfig[v.mileage] = v
        end
        self.DropConfigMax = config[#config].mileage --最大配置掉落公里数
    end
end

--碰到一个路桩，检测前方4个节点是否有金币奖励
function SkiingController:CheckFrontExistCoin(length,nodeName)
    local unitLength = NetNewSkiingData:GetUnitLength();
    local str = Split1(nodeName,"_")
    local key = v2n(str[1])
    local value = v2n(str[2])
    local max = RoadMaxNode[key]
    local result = {}
    for i = 1,4 do
        local temp = value+i
        local roadIndex = 1
        local nodeIndex = 1
        if value+i > max then
            roadIndex = (key == 4) and 1 or key+1
            nodeIndex = temp - max
        else
            roadIndex = key
            nodeIndex = temp
        end
        table.insert(result,{roadIndex = roadIndex;nodeIndex = nodeIndex;length = length+i*unitLength})
    end
    
    for _,v in ipairs(result) do
        local config = self.DropConfig[v.length]
        if config then
            self:CreateRoadReward(v.roadIndex,v.nodeIndex,config.reward)
        else
            --处理超过配表后，循环奖励的情况
            local len = v.length
            if len > self.DropConfigMax then
                len = math.modf(len%self.DropConfigMax)
                config = self.DropConfig[len]
                if config then
                    self:CreateRoadReward(v.roadIndex,v.nodeIndex,config.reward)
                end
            end
        end
    end
end

--获取当前节点的前方指定距离处的路桩数据
--如 "2_3" ,前方第2个节点是 "2_5"，返回2和5
function SkiingController:GetFrontNodeName(nodeName,length)
    local unitLength = NetNewSkiingData:GetUnitLength();
    local str = Split1(nodeName,"_")
    local key = v2n(str[1])
    local value = v2n(str[2])
    local unit = math.modf(length/unitLength)
    local max = RoadMaxNode[key]
    local temp = value+unit
    local roadIndex = 1
    local nodeIndex = 1
    if value+unit > max then
        roadIndex = (key == 4) and 1 or key+1
        nodeIndex = temp - max
    else
        roadIndex = key
        nodeIndex = temp
    end
    return roadIndex,nodeIndex
end

--在路节点上生成掉落奖励
function SkiingController:CreateRoadReward(roadIndex,nodeIndex,reward)
    local key = roadIndex.."_"..nodeIndex
    local nodeTrans = self.RoadNodeDic[roadIndex][key]
    if(nodeTrans == nil)then
        return;
    end
    
    if nodeTrans.childCount > 0 then
        return false
    end
    local rewardData = Split1(reward,"|")
    local id = v2n(rewardData[1])
    local count = v2n(rewardData[2])
    local typeItem = (id == 73009) and self.roadCoinObj or self.skiingProp
    
    local item = UEGO.Instantiate(typeItem, nodeTrans)
    item.transform.localPosition = Vector3.New(0,0,0)
    SetActive(item, true)
    
    local pos = GetChild(item,"Pos")
    local posY =  (id == 73009) and 0 or 90
    pos.transform.localPosition = Vector3.New(0,posY,0)
    
    SetUIImage(GetChild(item,"Pos/node/Icon",UEUI.Image),ItemConfig:GetIcon(id),false)
    local txt = GetChild(item,"Pos/node/Text",UEUI.Text)
    txt.text = "x"..count
    item.name = reward
	
	if not self.roadRewardItemList[key] then
		self.roadRewardItemList[key] = item
	end

	self.needCheckRoadReward = true

    return true
end

function SkiingController:TryRemoveRoadReward()
	--取到玩家的位置
	if self.carObj then
		for key, value in pairs(self.roadRewardItemList) do
			local posCar = self.carObj.transform.position
			local posItem = value.transform.position
			if posItem.x+1.5 < posCar.x then
				UI_UPDATE(UIDefine.UI_NewSkiingMatch,"CollectRoadCoin",value.name,key)
			end
		end
	end
end

--生成暴击道具
function SkiingController:CreateStrikeProp(nodeName,disCreate)
    local roadIndex,nodeIndex = SkiingController:GetFrontNodeName(nodeName,disCreate)
    local key = roadIndex.."_"..nodeIndex
    local parent = self.RoadNodeDic[roadIndex][key]
    local item = UEGO.Instantiate(self.baoJiProp, parent)
    item.name = "BaoJiProp"
    item.transform.localPosition = Vector3.New(-50,0,0)
    SetActive(item, true)
end


--删除指定路节点上的孩子
function SkiingController:DestroyChild(nodeName,childName)
    local str = Split1(nodeName,"_")
    local key = v2n(str[1])
    if self.RoadNodeDic[key] and self.RoadNodeDic[key][nodeName] then
        local nodeTrans = self.RoadNodeDic[key][nodeName]
        local target = childName and nodeTrans:Find(childName) or nodeTrans:GetChild(0)
        if target then
            UEGO.Destroy(target.gameObject)
        end
    end

	if self.roadRewardItemList[nodeName] then
		self.roadRewardItemList[nodeName] = nil
	end

end

function SkiingController:IsStrikeNode(nodeName)
	local roadIndex,nodeIndex = self:GetNodeInfo(nodeName)
	local nodeTrans = self.RoadNodeDic[roadIndex][nodeName]
	local baoji = GetChild(nodeTrans,"BaoJiProp")
	if baoji then
		return true
	end
	return false
end

--销毁指定节点下所有子物体
function SkiingController:DestroyAllChild(nodeTrans)
    local childCount = nodeTrans.childCount
    if childCount > 0 then
        for i = 0,childCount-1 do
            local child = nodeTrans:GetChild(i)
            if child then
                UEGO.Destroy(child.gameObject)
            end
        end
    end
end
-------------------------------------玩家雪车相关-------------------------------------
--初始化其他玩家位置
function SkiingController:InitOtherPlayerPosition()
    self:InitOtherCarList()
    local length = NetNewSkiingData:GetRoadLengthSum()
    if length == 0 then
        return
    end
    local nodeName = NetNewSkiingData:GetStopNode()
    if nodeName == "" then
        return 
    end
    local roadIndex,nodeIndex = self:GetNodeInfo(nodeName)
    local key = roadIndex.."_"..nodeIndex
    self:CheckOtherCar(length,key)
    SetActive(self.m_goOtherPlayerObj, false)
end

--判断玩家表情
function SkiingController:CheckEmoji()
    --展示表情倒计时
    if self.showEmoji then
        local obj = self.isHappy and self.happyFace or self.sadFace
        if self.emojiTime > 0 then
            self.emojiTime = self.emojiTime - 1
            SetActive(obj,true)
        else
            self.showEmoji = false
            SetActive(obj,false)
        end
    end
end

--是否显示笑脸
function SkiingController:CheckHappy()
    --展示表情倒计时
    if self.showEmoji then
        if self.emojiTime > 0 then
            self.emojiTime = self.emojiTime - 1
            SetActive(self.happyFace,true)
        else
            self.showEmoji = false
            SetActive(self.happyFace,false)
        end
    end
end

--超过其他玩家逻辑
--msg:奖励对象的名称(玩家的里程数)  msg1:父节点（路节点）的名称
function SkiingController:RunPastOtherPlayer(msg,msg1)
    SetActive(self.happyFace,true)
    self.showEmoji = true
    self.emojiTime = 2;
    self.isHappy = true
    --NewSkiingManager:UpdateRankData(5)
end

--初始化对象池用于显示玩家雪车
function SkiingController:InitOtherCarList()
    self.otherCarList = {}
    for i = 1,5 do
        local obj = UEGO.Instantiate(self.m_goOtherPlayerObj)
        SetActive(obj, false)
        table.insert(self.otherCarList,{active = false;obj = obj;point = 0;stay = false})
    end
    SetActive(self.m_goOtherPlayerObj, false)
end

--判断控制其他玩家雪车的显示与消失
function SkiingController:CheckOtherCar(length,nodeName)
    local top5List = NetNewSkiingData:GetTop5Player()
    if not top5List then
        return
    end
    local isEmpty = next(top5List) == nil
    if isEmpty then
        return
    end
    local roadIndex,nodeIndex = self:GetNodeInfo(nodeName)
    local min = (length - 50 < 0) and 0 or length - 50
    local max = length + 50
    
    --重置保留状态
    for _,v in ipairs(self.otherCarList) do
        v.stay = false
    end
    
    local preList = {}
    local myRank = NetNewSkiingData:GetMyRank()
    for _, v in ipairs(top5List) do
        if myRank ~= v.rank then
            local value = v2n(v.point)
            if value >= min and value <= max then
                local isExist = false
                for _,n in ipairs(self.otherCarList)do
                    if n.point == value then
                        n.stay = true
                        n.active = true
                        SetActive(v.obj,true)
                        isExist = true
                    end
                end
                if not isExist then
                    table.insert(preList,v)
                end
            end
        end
    end
    
    for _,v in ipairs(self.otherCarList)do
        if not v.stay then
            SetActive(v.obj,false)
            v.active = false
        end
    end

    for _,v in ipairs(preList) do
        local value = v2n(v.point)
        local Car = self:GetUseCar()
        local item = Car.obj
        local x,y = self:GetPosCarNode(length,roadIndex,nodeIndex,v.point)
        local trans = item.transform
        local nodeParent = self.RoadNodeDic[x][x.."_"..y]
        trans:SetParent(nodeParent)
        trans.localPosition = Vector3.New(0,0,0)
        trans.localScale = Vector3.New(1,1,1)
        trans:Find("Car").localRotation = Quaternion.Euler(0,0,nodeParent.eulerAngles.z);
        item.name = "OtherPlayer|"..value
        self:InitHeadInfo(item, v)
        SetActive(item, true)
        Car.active = true
        Car.point = value
    end
end

--从对象池中取出可用的其他玩家雪车实例
function SkiingController:GetUseCar()
    for _,v in ipairs(self.otherCarList) do
        if not v.active then
            v.active = true
            return v
        end
    end
end

--获取当前玩家50米范围内(左右5个节点内的玩家位置)
function SkiingController:GetPosCarNode(myLength,myRoadIndex,myNodeIndex,targetLength)
    if myLength == targetLength then
        return myRoadIndex,myNodeIndex
    end
    local max = RoadMaxNode[myRoadIndex]
    local unitLength = NetNewSkiingData:GetUnitLength();
    local needValue = targetLength - myLength
    local count = math.modf(math.abs(needValue)/unitLength)
    local roadIndex = 1
    local nodeIndex = 1
    
    if needValue > 0 then
        --在本玩家右边500m内
        local temp = myNodeIndex+count
        if temp > max then
            roadIndex = (myRoadIndex == 4) and 1 or myRoadIndex+1
            nodeIndex = temp - max
        else
            roadIndex = myRoadIndex
            nodeIndex = temp
        end
    elseif needValue < 0 then
        --在本玩家左边500m内
        local temp = myNodeIndex-count
        if temp < 0 then
            local tempIndex = myRoadIndex-1
            if tempIndex < 1 then
                local isOver4 = tempIndex == 0
                if isOver4 then
                    tempIndex = isOver4 and 4 or tempIndex
                    local tempMax = RoadMaxNode[tempIndex]
                    roadIndex = tempIndex
                    nodeIndex = tempMax - math.abs(temp)
                else
                    roadIndex = tempIndex
                    nodeIndex = temp
                end
            else
                local tempMax = RoadMaxNode[tempIndex]
                roadIndex = tempIndex
                nodeIndex = tempMax - math.abs(temp)
            end
        else
            roadIndex = myRoadIndex
            nodeIndex = temp
        end
    end
    return roadIndex,nodeIndex
end

--展示玩家排名下滑动画表现
--其他玩家雪车滑过，本玩家露出伤心表情
function SkiingController:ShowDeclineAnim(callback)
    --对手擦肩而过
    SetActive(self.animObj,true)
    local animTrans = self.animObj.transform
    local originalPos = animTrans.position
    local snowCar = animTrans:Find("Car");
    local _slopeMask = 262144
    animTrans:DOLocalMoveX(1312, 2):OnUpdate(function()
        local pos = animTrans.position;
        local hit = UE.Physics2D.Raycast(Vector2.New(pos.x,pos.y),Vector2.New(0,-1), 50,_slopeMask);
        --坡度适配
        local slopeAngle = Vector2.Angle(hit.normal, animTrans.right) - 90;
        snowCar.rotation = Quaternion.Lerp(snowCar.rotation, Quaternion.Euler(0, 0, slopeAngle) ,Time.deltaTime*8);
        --高度适配
        local targetPos = Vector3.New(pos.x,hit.point.y+0.5,pos.z);
        animTrans.position = Vector3.Lerp(pos,targetPos,1);
    end):OnComplete(function()
        self.animObj.transform.position = originalPos
        SetActive(self.animObj,false)
        if callback then
            callback()
        end
    end);
end

--展示伤心表情
function SkiingController:ShowSad()
    SetActive(self.sadFace,true)
    self.showEmoji = true
    self.emojiTime = 2;
    self.isHappy = false
end

function SkiingController:CheckAllRoadClose(msg)
    if msg == "1_30" or msg == "3_30" or msg == "2_24"or msg == "4_24" then
        self.controller:CheckAllRoadClose()
    end
end

--其他玩家游标跟随本玩家雪车高度
function SkiingController:FollowPlayerPos()
    local pos = self.cursorRect.position
    local targetY = self.myPlayerTrans.position.y+1
    local height = LimitHeight[self.cursorCount] or 1
    targetY = (targetY > height) and height or targetY
    self.cursorRect.position = UE.Vector3.Lerp(pos,Vector3.New(pos.x,targetY,pos.z),0.1);
end

function SkiingController:SetTopRaycastRunning(running)
	if self.TopRaycast then
		if self.TopRaycast.running == true or self.TopRaycast.running == false then
			self.TopRaycast.running = running
		end
	end
end

return SkiingController