local UI_MarketBox = Class(BaseView)
local rectTrans = nil
local stateTrans = nil

function UI_MarketBox:OnInit()
    self.config = nil 
end

function UI_MarketBox:OnCreate(config, marketConfig, itemObj)
    -- 参数类型
    local paramType = type(config)
    if paramType == "table" then
        self.config = config
    elseif paramType == "number" then
        self.config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, config)
    end
    self.newData = {}
    -- 地图上的宝箱
    self.m_ObjItem = itemObj
    if self.m_ObjItem then
        self.m_useType = v2n(self.m_ObjItem.m_Config["type_use"])
    end
    self.m_globSecond = GlobalConfig:GetNumber(1419, 30)

    -- 地图上的宝箱有打开按钮
    SetActive(self.ui.m_btnOpen, itemObj ~= nil)

    if marketConfig and type(marketConfig) == "table" then
		local price = string.split(marketConfig.buy_type, "|")
		if price[1] == 3 then
			NetNotification:NotifyNormal(NotifyDefine.OpenTips,5)
		end
	end

    self:DealData()
    self:InItUI()
end

function UI_MarketBox:DealData()
	
	self.commonLang =
	{
		[1] = LangMgr:GetLang(59),
		[2] = LangMgr:GetLang(58),
		[3] = LangMgr:GetLang(60),
		[4] = LangMgr:GetLang(61),
		[6] = LangMgr:GetLang(94),
        [7] = LangMgr:GetLang(7098),
	}
	
	self.newData = nil
	self.newData = {}
    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, self.config.id)
    local idUse = itemConfig.id_use
    local dropConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.drop_box, idUse)
    if not dropConfig then
        Log.Error("drop_box 表没有配置", "itemID = ", self.config.id, "id_use = ", idUse)
        return self.newData
    end
    local BuildData = dropConfig.drop_mission
    local heroData = dropConfig.drop_hero
    local prodData = dropConfig.drop_seedling
    local materialData = dropConfig.drop_product
    local ActiveData = dropConfig.drop_normal
    local dropCastleData = dropConfig.drop_castle

	--if NetUpdatePlayerData.playerInfo.curMap ~= MAP_ID_MAIN then
		if ActiveData ~= nil and ActiveData ~= "" then
			local item_info, isSplit = self:get_NetItem(E_DataType.ActiveData , ActiveData)
			if item_info then
                if isSplit then
                    for _, value in ipairs(item_info.itemList) do
                        local obj = {}
                        obj.tag = item_info.tag
                        obj.name = value.title
                        obj.itemList = {}
                        table.insert(obj.itemList, {
                            itemId = value.itemId,
                            ratio = value.ratio
                        })
                        table.insert(self.newData , obj)
                    end
                else
                    table.insert(self.newData , item_info)
                end
			end
		end
	--else
		if BuildData ~= nil and BuildData ~= "" then
			local item_info = self:get_BuildItems(E_DataType.BuildData , BuildData)
			if item_info then
				table.insert(self.newData , item_info)
			end
		end

		if heroData ~= nil and heroData ~= "" then
			local item_info = self:get_NetItem(E_DataType.heroData , heroData)
			if item_info then
				table.insert(self.newData , item_info)
			end
		end

		if prodData ~= nil and prodData ~= "" then
			local item_info = self:get_NetItem(E_DataType.prodData , prodData)
			if item_info then
				table.insert(self.newData , item_info)
			end
		end

		if materialData ~= nil and materialData ~= "" then
			local item_info = self:get_NetItem(E_DataType.materialData , materialData)
			if item_info then
				table.insert(self.newData , item_info)
			end
		end

        if dropCastleData ~= nil and dropCastleData ~= "" then
            local item_info = self:get_NetItem(E_DataType.BuildData , dropCastleData)
            if item_info then
                table.insert(self.newData , item_info)
            end
        end
	--end
    
    return self.newData
end

function UI_MarketBox:get_BuildItems(tag , staticData)
	
	local stDataList = string.split(staticData, ";")
	--stDataList[1] -- 10-15
	--stDataList[2] -- 25001|25002|25003
	--stDataList[3] -- 1|1|1
	local build_items = string.split(stDataList[3] , "|")
	local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, tonumber(stDataList[2]))
	local config_items = string.split(itemConfig["id_waste"] , "|")
	local have_items = ""
	for i = 1, #build_items do
		if i == #build_items then
			have_items = have_items .. config_items[i]
		else
			have_items = have_items .. config_items[i] .. "|"
		end
	end
	local static_str = stDataList[1] .. ";" .. have_items .. ";" .. stDataList[3]
	return self:get_NetItem(tag , static_str)
end

--获得一个表结构
function UI_MarketBox:get_NetItem(tag , staticData)
	local stDataList = string.split(staticData, ";")
	--stDataList[1] -- 10-15
	--stDataList[2] -- 25001|25002|25003
	--stDataList[3] -- 1|1|1

    local listItems = {}
    local itemNums = {}
    local itemNumMax = 0
    local isSplit = false

    if #stDataList == 1 then
        local tempListItems = string.split(stDataList[1], '|')
        itemNumMax = #tempListItems
        for _, value in ipairs(tempListItems) do
            if not itemNums[value] then
                table.insert(listItems, value)
                itemNums[value] = 1
            else
                itemNums[value] = itemNums[value] + 1
            end
        end
        isSplit = true
    elseif #stDataList == 3 then
        --先获取元素列表
        listItems = string.split(stDataList[2], '|')
    end

	--英雄列表是特例
	local wasteItems = self:GetHeroItemList() or {}
	local wasteLen = #wasteItems
    local listHotCastle = GlobalConfig:GetHotCastleItemList(1)
	local itemList = {}
	for i, v in ipairs(listItems) do
		local arrStrH = string.split(v, 'h')
		if #arrStrH == 2 then
			local lv = tonumber(arrStrH[2])
			if wasteLen >= lv then
				table.insert(itemList, wasteItems[lv])
			end
		else
            arrStrH = string.split(v, 'n')
            if #arrStrH == 2 then
                local lv = tonumber(arrStrH[2])
                table.insert(itemList, listHotCastle[lv])
            else
                if self:GetItemState(tonumber(v)) > 0 then
                    table.insert(itemList, v)
                end
            end
        end
	end
	--itemList就是最后需要显示处理的列表
	local itemCount = #itemList

    local percentParam
    local total_ratio = 0
    local length = #listItems
    if #stDataList == 1 then

    elseif #stDataList == 3 then
        --先获取元素列表
        percentParam = string.split(stDataList[3], "|")
        for i = 1, itemCount do
            if i <= length then
                total_ratio = total_ratio + percentParam[i]
            end
        end
        if total_ratio <= 0 then
            return nil
        end
    end

	--计算比值
	local re_item_list = {}
	for i = 1, itemCount do
		if i <= length then
			local itemId = itemList[i]
            local item_info = {}
            if percentParam then
                local item_ratio = percentParam[i] / total_ratio
                item_info.ratio = item_ratio
            else
                item_info.ratio = 1
            end

			item_info.itemId = itemId
            if isSplit then
                item_info.title = self.commonLang[tag] .. LangMgr:GetLangFormat(7052, tostring(itemNums[tostring(itemId)])) .. LangMgr:GetLang(201)
            end
			table.insert( re_item_list ,item_info)
		end
	end
	if #re_item_list <= 0 then
		return nil
	end
	--文字
    local title

    if #stDataList == 1 then
        title = itemNumMax
    elseif #stDataList == 3 then
        --先获取元素列表
        title = stDataList[1]
    end

    for _, value in pairs(itemList) do
        local itemID = tonumber(value)
        -- 排除种子苗
        if itemID ~= 31011 then
            local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
            if config then
                local backID = config["back_id"]  -- 物品地图归属
                if backID == MAP_ID_ZOO then
                    tag = MAP_ID_ZOO
                end
            end
        end
    end

	local itemName = self.commonLang[tag] .. LangMgr:GetLangFormat(7052, tostring(title)) .. LangMgr:GetLang(201)
	--构造
	local return_tab = {}
	return_tab.tag = tag
	return_tab.name = itemName
	return_tab.itemList = re_item_list
	return return_tab, isSplit
end


function UI_MarketBox:GetItemState(itemID)
	local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
	if not config then
		Log.Error("###### excel nil ", itemID)
		return 0
	end

	local chainId = config["chain_series"]
	if not chainId then
		return 2
	end

	local mapId = MapController.m_MapId

    -- 非主营地 1 和 2 的其他地图，都按照主营地 1 的内容显示
    if mapId ~= MAP_ID_MAIN and mapId ~= MAP_ID_SECOND then
        mapId = MAP_ID_MAIN
    end

    local backID = config["back_id"]  -- 物品地图归属
    if backID and mapId ~= backID then
        mapId = backID
    end

	local itemExState = NetMapNoteData:GetNoteCount(mapId, NetMapNoteData.ID.item_extinction, itemID)
	if itemExState > 0 then
		return -1
	end

	local chainCloseState = NetMapNoteData:GetNoteCount(mapId, NetMapNoteData.ID.chain_extinction, chainId)
	if chainCloseState > 0 then
		return -1
	end

	local chainOpenState = NetMapNoteData:GetNoteCount(mapId, NetMapNoteData.ID.chain_opened, chainId)
	if chainOpenState > 0 then
		return 1
		--local itemType = config["type_use"]
		--local idUse = config["id_use"]
		--if itemType == ItemUseType.Material and idUse then
			--local pNeedCount = NetMapNoteData:GetNoteCount(mapId, NetMapNoteData.ID.item_has, idUse)
			--if pNeedCount <= 0 then
				--return 0
			--end
		--end
	else
		return 0
	end
	return 1
end

function UI_MarketBox:InItUI()
    local BoxImage = GET_UI(self.uiGameObject, "Image", TP(UEUI.Image))
    SetUIImage(BoxImage, self.config.icon_b, false)

    local txtDesc = GET_UI(self.uiGameObject, "m_txtTitle", TP(UEUI.Text))
    txtDesc.text = LangMgr:GetLang(self.config.id_lang)

 
    self:LoadListData()
end

function UI_MarketBox:LoadListData()
    local dataCount = self.newData and GetTableLength(self.newData) or 0
    local listTrans = self.ui.m_goContent.transform
    for i = 1, dataCount do
        local cellItemObj
        if listTrans.transform.childCount <= i then
            cellItemObj = UEGO.Instantiate(self.ui.m_goMarketBoxCellNormal.transform,listTrans)
            SetActive(cellItemObj,true)
        else
            cellItemObj = listTrans.transform:GetChild(i - 1)
        end
        if cellItemObj then
            self:LoadCellData(i,cellItemObj)
        end
    end
    local rect = GetComponent(self.ui.m_goContent,UE.RectTransform)
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(rect)
end

function UI_MarketBox:LoadCellData(idx, obj)
    if not obj then
        return
    end
	local data = self.newData[idx]

	local titleTxt = GET_UI(obj, "m_txtTip", TP(UEUI.Text))
	titleTxt.text = data.name

    local parent = GetChild(obj, "itemObj", UE.RectTransform)
    local itemNode = GetChild(obj, "item")
    local childCount = parent.transform.childCount - 1
    -- 先全部隐藏
    for i = 1, childCount, 1 do
        local item = parent.transform:GetChild(i)
        SetActive(item, false)
    end

	local dataLength = #data.itemList

    for i = 1, dataLength, 1 do
        local item
        -- 有可用的 item 直接获取
        if i <= childCount then
            item = parent.transform:GetChild(i)
        -- item 不够用，创建新的
        else
            item = CreateGameObjectWithParent(itemNode, parent)
        end
        local txtNum = GetChild(item, "m_txtNum", UEUI.Text)
        local icon = GetChild(item, "itemImg", UEUI.Image)

        local itemId = data.itemList[i].itemId
        local ratio = data.itemList[i].ratio

        local itemCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemId)
        SetUIImage(icon, itemCfg.icon_b, false)

        if ratio <= 0 then
            txtNum.text = "0%"
        else
            txtNum.text = string.format("%.1f", ratio * 100) .. "%"
        end

        SetActive(item, true)
    end

    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(parent)
    --local height = parent.rect.height
    --SetUISize(obj, 0, height + 165)

    -- for i = 1, childCount do
    --     local items = GET_UI(obj, "item"..i, "RectTransform")
    --     local TxtNum = GET_UI(items, "m_txtNum", TP(UEUI.Text))
    --     local itemImg = GET_UI(items, "itemImg", TP(UEUI.Image))
    --     if i <= dataLength then
    --         local itemId = data.itemList[i].itemId
    --         local ratio = data.itemList[i].ratio
    --         local itemCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemId)
    --         SetUIImage(itemImg, itemCfg.icon_b, false)
    --         if ratio <= 0 then
    --             TxtNum.text = "0%"
    --         else
    --             TxtNum.text = string.format("%.1f", ratio * 100) .. "%"
    --         end
    --         SetActive(items, true)
    --     else
    --         SetActive(items, false)
    --     end
    -- end

	--local bg = GET_UI(self.uiGameObject, "bg", TP(UEUI.Image))
    --RemoveUIComponentEventCallback(obj, UEUI.Button)
    --AddUIComponentEventCallback(obj, UEUI.Button, function(arg1,arg2)
	--		if arg1.name == "m_BtnTip" then
	--			if self.tipGo == nil or not self.tipGo.gameObject.activeSelf then
	--				self:LoadTips(obj)
	--			else
	--				SetActive(self.tipGo , false)
	--			end
	--		end
    --end) 
end
   
function UI_MarketBox:LoadTips(obj)
	local bg = GET_UI(self.uiGameObject, "bg", TP(UEUI.Image))
	local mbtnTip = GET_UI(obj, "m_BtnTip1", TP(UEUI.Button))

	if self.tipGo == nil then
		self.tipGo = FineFromParent(bg, "MarketBoxTip")
		if nil == self.tipGo  then
			local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "MarketBoxTip")
			local cellObj = ResMgr:LoadAssetSync(assetPath, AssetDefine.LoadType.Instant)
			self.tipGo = UEGO.Instantiate(cellObj)
			self.tipGo.transform:SetParent(bg.transform)
			self.tipGo.name = "MarketBoxTip"
		end

		local scrollrect = GetChild(self.uiGameObject,"bg/TbObj/m_TableViewV",UEUI.ScrollRect)
		scrollrect.onValueChanged:RemoveAllListeners()
		scrollrect.onValueChanged:AddListener(function (value)
			if self.tipGo.gameObject.activeSelf then
				SetActive(self.tipGo , false)
			end
		end)
	end

	local newpos = bg.transform:InverseTransformPoint(mbtnTip.transform.position)
	self.tipGo.transform.localPosition = newpos
	self.tipGo.transform.localScale = Vector3.New(1,1,1)

	local tip = GET_UI(self.tipGo, "tips", TP(UEUI.Image))
	local txtTips = GET_UI(self.tipGo, "m_txtTips", TP(UEUI.Text))
	txtTips.text = LangMgr:GetLang(95)

	SetActive(self.tipGo.transform, true)
end

function UI_MarketBox:GetHeroItemList()
	local heroId = MapController:GetHeroIdThatWillUnlock()
	if heroId == 1001 then
		return nil
	end
	local wasteItems = {}
	if heroId then
		local heroConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, heroId)
		if heroConfig then
			wasteItems = string.split(heroConfig["id_waste"], '|')
		end
	else
		local str = GlobalConfig:GetString(1013)
		if str then
			wasteItems = string.split(str, '|')
		end
	end
	return wasteItems, heroId
end

function UI_MarketBox:OnRefresh(param)
    
end

function UI_MarketBox:onDestroy()
    self.config = nil
    self.newData = nil
	self.tipGo = nil
end


function UI_MarketBox:onUIEventClick(go,param)
    local name = go.name
    if name == "btn_close" then
        self:Close()
    -- 打开宝箱
    elseif name == "m_btnOpen" then
        self:Close(false, function()
            self:OpenFreeBox()
            if self.m_useType ~= ItemUseType.PayBox and self.m_useType ~= ItemUseType.GameCurrencyBox then
                self.m_ObjItem:OpenBox()
            else
                Log.Error("UI_ItemBox  self.m_useType : " , self.m_useType)
            end
        end)
    end
end

function UI_MarketBox:OpenFreeBox()
    if self.m_useType == ItemUseType.CutDownBox then
        local status = self.m_ObjItem:GetCountDownBoxStatus()
        if status == CD_BOX_STATUS.ACTIVE then
            local diffTime = self.m_ObjItem:GetCutDownDiffTime()
            if diffTime <= self.m_globSecond then
                MapController:AddBoxQueue(-1)
            end
        end
    end
end

return UI_MarketBox