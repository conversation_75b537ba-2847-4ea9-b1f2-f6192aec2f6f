local GoLimit14 = {}
local M = GoLimit14

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit14.prefab"
local pre


local strBgPach = {
	"Sprite/ui_mainface/iconbox2.png",
	"Sprite/ui_mainface/iconbox4.png"
}

local srtTimerKey = "GoLimit14_TimerKey"

local timer_useBuff

local timer_minusBuff
local i_minusDeltaTime = 180 --衰减时间间隔

local i_LastLv = -1

function M:Create(parent,loadCallBack)
	local item = {}
	setmetatable(item,{__index = M})
	item.loadCallBack = loadCallBack
 
	local function callBack(obj)
		local newGo = CreateGameObjectWithParent(obj,parent)
		item.go = newGo
		item.trans = newGo.transform
		item:Init()
		if item.loadCallBack then
			item.loadCallBack(item)
		end
	end
	ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

	--return item
end

function M:Init()
	self.bg = GetChild(self.go,"bg",UEUI.Image)
	self.icon = GetChild(self.go,"bg/icon",UEUI.Image)
	self.icon2 = GetChild(self.go,"bg/icon2",UEUI.Image)
	--self.red =  GetChild(self.go,"bg/goPoint")
	--self.rank = GetChild(self.go,"bg/rank",UEUI.Text)
	self.scheduleText = GetChild(self.go,"bg/num",UEUI.Text)
	self.scheduleImg = GetChild(self.go,"bg/Image/progress",UEUI.Image)
	self.goCountDown = GetChild(self.go,"bg/CountDown")
	self.count = GetChild(self.go,"bg/CountDown/countTxt",UEUI.Text)
	--self.showRed = false
	AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
			self:ClickItem(arg1,arg2)
		end)

	timer_useBuff = nil
	timer_minusBuff = nil

	self:OnCheckBuffMinus()
end

function M:SetItem(param)
	self.id = param.id
	self.totalType = param.totalType
	self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
	local active = self.activeInfo.form.activeMess
	self.info = self.activeInfo.info

	NetEnergyBuff:SetActiveId(self.id)

	--SetImageSprite(self.icon,self.activeInfo.form.activeMess.icon,false)
	self:SetIcon()

	self:ChangeItem()
	self:ChangeValue()

	self:OnCheckBuffTime()
end

function M:ChangeItem()
	local time = self.activeInfo:GetRemainingTime()
	if time <= 0 then

	end
	--self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetRemainingTime())
end

function M:ChangeValue()
	local actInfo = self.activeInfo.info


	local AllCount = ConfigMgr:GetConfigCount(ConfigDefine.ID.energy_effect)
	--local i_CurLv = 7
	local i_CurLv = NetEnergyBuff:GetLv()

	if i_LastLv >= 0 then
		self:OnCheckBuffLvUp(i_LastLv, i_CurLv)
	end
	i_LastLv = i_CurLv


	local lv = i_CurLv + 1
	if AllCount == i_CurLv then
		lv = i_CurLv
	end

	local data = ConfigMgr:GetDataByID(ConfigDefine.ID.energy_effect, lv)
	if data then
		self.scheduleText.text = string.format("%d/%d", NetEnergyBuff:GetSchedule(), data.energy)
		self.scheduleImg.fillAmount = NetEnergyBuff:GetSchedule() / data.energy

		if AllCount == i_CurLv then
			self.scheduleImg.fillAmount = 1
			self.scheduleText.text = LangMgr:GetLang(8215)
		else

		end

		self:OnCheckBuffTime()
	end


end

function M:SetIcon(isInBuffTime)

	local AllCount = ConfigMgr:GetConfigCount(ConfigDefine.ID.energy_effect)

	--local lv = 7
	local lv = NetEnergyBuff:GetLv()
	if not isInBuffTime and lv < AllCount then
		lv = lv + 1
	end


	local data = ConfigMgr:GetDataByID(ConfigDefine.ID.energy_effect, lv)

	if data then
		SetImageSprite(self.icon, data.icon, false)
	end

	if isInBuffTime or (lv == AllCount) then
		SetImageSprite(self.bg, strBgPach[2], false)
		SetUIImageGray(self.icon, false)
		SetUIImageGray(self.bg, false)
	else
		SetImageSprite(self.bg, strBgPach[1], false)
		SetUIImageGray(self.icon, true)
		SetUIImageGray(self.bg, true)
	end

	if isInBuffTime then
		SetActive(self.goCountDown, true)
	else
		SetActive(self.goCountDown, false)
	end


end

function M:OnCheckBuffLvUp(oldLv, curLv)
	if oldLv < curLv then
		--UI_SHOW(UIDefine.UI_EnergyBuff, {self.activeInfo, true})
		NetPushViewData:PushView(PushDefine.EnergyBuff, {self.activeInfo.info.activeId, true})
	end
end

function M:OnCheckBuffTime()
	local curTime = TimeZoneMgr:GetServerStampWithServerZone()
	local startTime = NetEnergyBuff:GetBuffStartTime()
	local endTime = NetEnergyBuff:GetBuffEndTime()

	if curTime >= startTime and curTime <= endTime then
		local deltaTime = endTime - curTime
		self.count.text = TimeMgr:CheckHMSNotEmpty(deltaTime)

		TimeMgr:DestroyTimer(srtTimerKey, timer_useBuff)
		timer_useBuff = TimeMgr:CreateTimer(srtTimerKey,
			function()
				if deltaTime > 0 then
					deltaTime = deltaTime - 1
					self.count.text = TimeMgr:CheckHMSNotEmpty(deltaTime)
				end

				if deltaTime == 0 then
					-- 祝福状态关闭  bb_buff
					local thinkTable = {["bb_buff"] = 0}
					SdkHelper:ThinkingTrackEvent(ThinkingKey.brianblessing, thinkTable)
					
					self:SetIcon()
				end

			end,
			1, deltaTime)

		self:SetIcon(true)
	end

end

function M:OnCheckBuffMinus()
	self:BuffMinus()

	TimeMgr:DestroyTimer(srtTimerKey , timer_minusBuff)
	timer_minusBuff = TimeMgr:CreateTimer(srtTimerKey,
		function()
			--Log.Info("*****  OnCheckBuffMinus()")
			self:BuffMinus()
		end,
		10, -1)
end

function M:BuffMinus()
	local value = NetEnergyBuff:GetSchedule()
	local start = NetEnergyBuff:GetMinusStartTime()

	if value > 0 and start > 0 then
		local curTime = TimeZoneMgr:GetServerStampWithServerZone()

		if (curTime - start) > i_minusDeltaTime then
			local iMinus = math.floor((curTime - start) / i_minusDeltaTime)

			if iMinus < 1 then
				iMinus = 1
			end
			if iMinus > value then
				iMinus = value
			end

			NetEnergyBuff:OnEnergyChange(iMinus)
			NetEnergyBuff:SetMinusStartTime(curTime)
		end

		EventMgr:Dispatch(EventID.ENERGY_BUFF_MINUS)
	end

end


function M:End()

end

function M:ClickItem(arg1)
	UI_SHOW(UIDefine.UI_EnergyBuff, {self.activeInfo.info.activeId, false})
end



function M:Close()
	TimeMgr:DestroyTimer(srtTimerKey, timer_useBuff)
	TimeMgr:DestroyTimer(srtTimerKey, timer_minusBuff)

	UEGO.Destroy(self.go)

end

return M