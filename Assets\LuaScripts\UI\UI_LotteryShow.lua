--抽卡动画展示界面
local UI_LotteryShow = Class(BaseView)
local DOTween = CS.DG.Tweening.DOTween
local ResBar = require("UI.ResBar")

function UI_LotteryShow:OnInit()
    --是否正在播放动画
    self.isPlaying = false
    --次数类型  
    self.countType = LotteryManager.CountType.One
    --十抽卡牌列表
    self.tenCardList = {}
    --十抽目标格子列表
    self.tenGridList = {}
    --Dotween 动画序列管理器
    self.sequenceManager = {}
    --未确认查看的数量(所有卡牌查看完才会显示返回按钮)
    self.unconfirmedCount = 0
	--抽卡历史记录
	self.lotteryHistory = {}
    ----------初始化界面相关逻辑----------
    SetActive(self.ui.m_goSingle,false)
    SetActive(self.ui.m_goTen,false)
    SetActive(self.ui.m_goTenCard,false)

    self:UpdateRes()
end

function UI_LotteryShow:OnCreate(countType,lotteryType,initData)
    self.countType = countType or LotteryManager.CountType.One
    self.lotteryType = lotteryType or LotteryManager.LotteryType.Hero
	SetUIImage(self.ui.m_imgCostIcon,LotteryManager:GetCostIcon(self.lotteryType),false)
	--抽奖类型
    if self.countType == LotteryManager.CountType.One then
        AudioMgr:Play(125)
    elseif  self.countType == LotteryManager.CountType.Ten then
        AudioMgr:Play(123)
    end


    SetActive(self.ui.m_imgBg1,self.lotteryType == LotteryManager.LotteryType.Hero)
    SetActive(self.ui.m_imgBg2,self.lotteryType == LotteryManager.LotteryType.Equip)
    
    ----------初始化业务逻辑----------
    --self.diamondRes = ResBar:Create(self.ui.m_goDiamond)
    --self.diamondRes:SetItem(2)
    --
    --self.coinRes = ResBar:Create(self.ui.m_goCoin)
    --self.coinRes:SetItem(3)
    
    SetActive(self.ui.m_goSingle,self.countType == LotteryManager.CountType.One)
    SetActive(self.ui.m_goTen,self.countType == LotteryManager.CountType.Ten)
    SetActive(self.ui.m_btnBuyAnother,false)
    SetActive(self.ui.m_btnQuickShow,false)
    
    if self.countType == LotteryManager.CountType.One then
        self.singleCardTrans = GetComponent(self.ui.m_goSingleCard,UE.Transform)
        self.singleCardRect = GetComponent(self.ui.m_goSingleCard,UE.RectTransform)
        self:InitCardView(self.ui.m_goSingleCard)
        self.singleCardRect.anchoredPosition = Vector2.New(0,-1000)
        self.singleCardTrans.localScale = Vector3.one*1.2
        
        self:ShowSingleCardInfo(initData)
        self:ShowSingleEnterAnim()
        self.unconfirmedCount = 1
    elseif self.countType == LotteryManager.CountType.Ten then
        self:InitCardView(self.ui.m_goTenCard)
        self.ui.m_goTenCard.transform.localScale = Vector3.one*1.2
        self:CollectInitPosData()
        SetActive(self.ui.m_goInitNode,true)
        
        self:ShowTenCardInfo(initData)
        self:ShowTenEnterAnim()
        self.unconfirmedCount = 10
    end
    self:BindLotteryBtnLogic(self.ui.m_btnBuyAnother.gameObject,self.countType)

    SetActive(self.ui.m_btnReturn,self.unconfirmedCount<=0)

	self:AddHistory(initData)
    EventMgr:Add(EventID.BAG_CHANGE, self.BagChange, self)
end

function UI_LotteryShow:OnRefresh(param,msg)
    if param == 1 then
        --成功抽取单张卡片
        self:OpenSingleCard()
    elseif param == 2 then
        --再次抽取单张卡片
        self:SingleCardOneMoreTime(msg)
    elseif param == 10 then
        --成功抽取10张卡片
        self:TenCardOneMoreTime()
    elseif param == 11 then
        --再次成功抽取10张卡片
        self:ShowTenCardInfo(msg)
        self:TenCardOneMoreTime()
    end

	self:AddHistory(msg)
end

function UI_LotteryShow:onDestroy()
    for _,v in ipairs(self.tenCardList) do
        if v then
            UEGO.Destroy(v)
        end
    end
    self.unconfirmedCount = 0
    self.tenCardList = nil
    self.tenGridList = nil
    self:ClearSequence()
    --self.diamondRes:Destroy()
    --self.coinRes:Destroy()

	for key, value in pairs(self.lotteryHistory) do
		EventMgr:Dispatch(EventID.TRIGGER_GUIDE, 24, key)
		EventMgr:Dispatch(EventID.EVENT_GUIDE, 24, key)
	end
	self.lotteryHistory = nil
    EventMgr:Remove(EventID.BAG_CHANGE, self.BagChange, self)
end

function UI_LotteryShow:onUIEventClick(go,param)
    if self.isPlaying then
        return
    end
    
    local name = go.name
    if name == "m_btnReturn" then
        self:Close()
    elseif name == "m_btnQuickShow" then
        self.isPlaying = true
        local sequence = self:GetSequence("2")
        SetActive(self.ui.m_btnQuickShow,false)
        for _,v in ipairs(self.tenCardList) do
            if not self:IsOpen(v) then
                sequence:AppendInterval(0.07)
                sequence:AppendCallback(function()
                    self:PlaySingleCardAnim(v)
                end)
            end
        end
        sequence:AppendInterval(0.7)
        sequence:AppendCallback(function()
            SetActive(self.ui.m_btnBuyAnother,true)
            self.isPlaying = false
        end)
        AudioMgr:Play(124)
    elseif name == "checkSingleCard" then
        if not self:CheckCanClick(self.ui.m_goSingleCard) then
            return
        end
        UI_UPDATE(UIDefine.UI_LotteryShow,1)
        AudioMgr:Play(124)
    elseif name == "m_btnBuyAnother" then
        if self.isPlaying then
            return
        end
		LotteryManager:OnReqDraw(self.lotteryType,self.countType,function()
            AudioMgr:Play(124)
        end,true) 
        --SetActive(self.ui.m_btnReturn,false)
    end
end

--********************************业务逻辑********************************
--------------------------公共逻辑--------------------------
--判断卡牌是否可点击
function UI_LotteryShow:CheckCanClick(obj)
    --动画播放中不能点击
    if self.isPlaying then
        return false
    end
    
    --已经翻开的卡牌不能点击
    if self:IsOpen(obj) then
        return false
    end
    
    return true
end

--该卡牌是否翻开查看
function UI_LotteryShow:IsOpen(obj)
    local front = GetChild(obj,"card/front")
    return front.activeInHierarchy
end

--获取Sequence序列管理
function UI_LotteryShow:GetSequence(key)
    if self.sequenceManager[key] then
        self:KillSequence(self.sequenceManager[key])
    end
    self.sequenceManager[key] = DOTween.Sequence()
    return self.sequenceManager[key]
end

--清理Sequence序列资源
function UI_LotteryShow:ClearSequence()
    for _,v in pairs(self.sequenceManager) do
        self:KillSequence(v)
    end
end

--初始化卡牌样式
function UI_LotteryShow:InitCardView(obj)
    local front = GetChild(obj,"card/front")
    local back = GetChild(obj,"card/back")
    local node = GetChild(obj,"card/node")
    
    SetActive(node,false)
    SetActive(back,true)
    SetActive(front,false)
end

--播放单抽动画
function UI_LotteryShow:PlaySingleCardAnim(obj,time,callback)
    local trans = GetChild(obj,"card",UE.Transform)
    local front = GetChild(trans,"front")
    local back = GetChild(trans,"back")
    local node = GetChild(obj,"card/node")
    local effect = GetChild(obj,"card/node/effect")
    local flag = GetChild(obj,"card/node/hasEffect")

    SetActive(node,false)
    trans:DOLocalRotate(Vector3.New(0, 180, 0), time or 0.8):SetEase(Ease.InExpo):OnUpdate(function()
        local value = trans.eulerAngles.y
        local frontActive = front.activeInHierarchy
        local backActive = back.activeInHierarchy
        local nodeActive = node.activeInHierarchy
        if value <= 90 then
            if frontActive then
                SetActive(front,false)
            end

            if not backActive then
                SetActive(back,true)
            end

            if nodeActive then
                SetActive(node,false)
            end
        else
            if not frontActive then
                SetActive(front,true)

                --翻转后播放高品质特效
                if flag then
                    SetActive(effect,true)
                end
            end
            
            if backActive then
                SetActive(back,false)
            end

            if not nodeActive then
                SetActive(node,true)
            end
        end
    end):OnComplete(function()
        if callback then
            callback()
        end
        local count = self.unconfirmedCount - 1
        self.unconfirmedCount = (count <=0) and 0 or count
        SetActive(self.ui.m_btnReturn,self.unconfirmedCount<=0)
        if self.unconfirmedCount <=0 and self.countType == LotteryManager.CountType.Ten and self.ui.m_btnQuickShow.gameObject.activeInHierarchy then
            SetActive(self.ui.m_btnQuickShow,false)
            --SetActive(self.ui.m_btnBuyAnother,true)
        end
    end)
end

--暂停&销毁Dotween序列
function UI_LotteryShow:KillSequence(sequence)
    if sequence then
        sequence:Pause()
        sequence:Kill()
        sequence = nil
    end
end

--绑定招募按钮逻辑（招募1次&招募10次）
function UI_LotteryShow:BindLotteryBtnLogic(obj,type)
    local btn = GetComponent(obj,UEUI.Button)
    local btnSprite = GetComponent(obj,UEUI.Image)
    local desc = GetChild(obj,"desc",UEUI.Text)
    local count = GetChild(obj,"count",UEUI.Text)
    local icon = GetChild(obj,"icon",UEUI.Image)
    
    desc.text = LangMgr:GetLangFormat(type == LotteryManager.CountType.One and 70000027 or 70000028,type)
    count.text = type == LotteryManager.CountType.One and "1" or "10"
end

--展示单卡信息
function UI_LotteryShow:ShowCardInfo(obj,msg)
    local icon = GetChild(obj,"card/node/icon",UEUI.Image)
    local name = GetChild(obj,"card/node/name",UEUI.Text)
    local back = GetChild(obj,"card/back",UEUI.Image)
    local count = GetChild(obj,"card/node/count",UEUI.Text)
    local cardFront = GetChild(obj,"card/front",UEUI.Image)
    local cardBorder = GetChild(obj,"card/node/border",UEUI.Image)
    local effect = GetChild(obj,"card/node/effect")
    local flag = GetChild(obj,"card/node/flag") or GetChild(obj,"card/node/hasEffect")
    SetActive(effect,false)

    if self.lotteryType == LotteryManager.LotteryType.Hero then
        SetUIImage(back,"Sprite/ui_slg_chouka/chouka_kabei.png",false)
    elseif self.lotteryType == LotteryManager.LotteryType.Equip then
        SetUIImage(back,"Sprite/ui_slg_chouka/chouka_kabei1.png",false)
    end
    
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,msg.code)
    if config then
        SetUIImage(icon, config.icon_b, false)
        name.text = LangMgr:GetLang(config.id_lang)
        SetUIImage(cardFront,"Sprite/ui_slg_jueseyangcheng/yangcheng_ka"..config.slg_quality..".png",false)
        SetUIImage(cardBorder,"Sprite/ui_slg_jueseyangcheng/yangcheng_ka"..config.slg_quality.."_1.png",false)
        if config.slg_quality == 3 then
            self:CreateEffect(effect.transform,3)
            flag.name = "hasEffect"
        elseif config.slg_quality == 4 then
            self:CreateEffect(effect.transform,4)
            flag.name = "hasEffect"
        else
            flag.name = "flag"
        end
    end
    count.text = "x"..msg.amount
end

--创建特效预制体
function UI_LotteryShow:CreateEffect(trans,type)
    if trans.childCount > 0 then
        self:DestroyAllChild(trans)
    end
    local path = "battle_kapai_huang1"
    if type == 3 then
        path = "battle_kapai_zise2"
    elseif type == 4 then
        path = "battle_kapai_huang1"
    end
    
    local path = "Assets/ResPackage/Effect/BattlePrefab/"..path..".prefab"
    

    ResMgr:LoadAssetAsync(path,AssetDefine.LoadType.Instant,function(asset)
        UEGO.Instantiate(asset,trans)
        self:SortOrderAllCom(true)
    end)
end

--删除所有子节点
function UI_LotteryShow:DestroyAllChild(trans)
    local childCount = trans.childCount
    if childCount <= 0 then
        return
    end

    for i = childCount-1,0,-1 do
        local child = trans:GetChild(i)
        if child then
            UEGO.Destroy(child.gameObject)
        end
    end
end
--------------------------单抽逻辑--------------------------

--展示单抽入场动画
--第一次进入时，播放一次进场动画，然后播放旋转动画
--再次招募时，直接播放旋转动画，隐藏招募按钮，动画播完后再显示
function UI_LotteryShow:ShowSingleEnterAnim()
    self.isPlaying = true
    self.singleCardTrans:DOAnchorPos(Vector2.New(0,0),0.3):OnComplete(function()
        self.singleCardTrans:DOScale(Vector3.one, 0.2)
        self.isPlaying = false
    end)
end

--再次单抽卡牌
function UI_LotteryShow:SingleCardOneMoreTime(msg)
    self.isPlaying = true
    local sequence = self:GetSequence("3")
    sequence:AppendCallback(function()
        self:InitCardView(self.ui.m_goSingleCard)
        local cardNode = GetChild(self.singleCardTrans,"card",UE.Transform)
        cardNode.rotation = Quaternion.Euler(0,0,0)
        SetActive(self.ui.m_btnBuyAnother,false)
        self:ShowSingleCardInfo(msg)
    end)
    sequence:AppendInterval(0.5)
    sequence:AppendCallback(function()
        self:OpenSingleCard()
    end)
end

--翻开单张卡片动画
function UI_LotteryShow:OpenSingleCard()
    self.isPlaying = true
    self:PlaySingleCardAnim(self.ui.m_goSingleCard,0.8,function()
        SetActive(self.ui.m_btnBuyAnother,true)
        self.isPlaying = false
    end)
end

--展示单卡信息
function UI_LotteryShow:ShowSingleCardInfo(msg)
    self:ShowCardInfo(self.ui.m_goSingleCard,msg[1])
end

--------------------------十抽逻辑--------------------------
--展示十抽入场动画
--第一次进入时，播放一次进场动画，然后播放逐个旋转动画
--再次招募时，直接播放旋转动画（所有卡牌同时旋转），隐藏招募按钮，动画播完后再显示
function UI_LotteryShow:ShowTenEnterAnim()
    self.isPlaying = true
    local sequence = self:GetSequence("1")
    sequence:AppendInterval(0.3)
    sequence:AppendCallback(function()
        self:_ShowTenAnim()
    end)
    sequence:AppendInterval(1)
    sequence:AppendCallback(function()
        SetActive(self.ui.m_btnQuickShow,true)
        self.isPlaying = false
    end)
end

function UI_LotteryShow:_ShowTenAnim()
    -- 洗牌算法
    local list = {1, 2, 3, 4, 5, 6, 7, 8, 9,10}
    local count = #list
    while count > 0 do
        local index = Random(1,count)
        list[count], list[index] = list[index], list[count]
        count = count - 1
    end

    local sequence = self:GetSequence("5")
    for _,v in ipairs(list) do
        sequence:AppendInterval(0.06)
        sequence:AppendCallback(function()
            local pos = self.tenGridList[v].transform.localPosition
            local trans = self.tenCardList[v].transform
            trans:DOLocalMove(Vector3.New(pos.x,pos.y,trans.z), 0.2):SetEase(Ease.OutCubic):OnComplete(function()
                trans:DOLocalRotate(Vector3.zero, 0.2)
                trans:DOScale(Vector3.one, 0.2)
            end);
        end)
    end
end

--再次十抽卡牌
function UI_LotteryShow:TenCardOneMoreTime()
    self.isPlaying = true
    local sequence = self:GetSequence("4")
    sequence:AppendCallback(function()
        SetActive(self.ui.m_btnBuyAnother,false)
    end)
    sequence:AppendCallback(function()
        for _,v in ipairs(self.tenCardList) do
            self:InitCardView(v)
            local cardNode = GetChild(v,"card",UE.Transform)
            cardNode.rotation = Quaternion.Euler(0,0,0)
        end
    end)
    sequence:AppendInterval(0.5)
    sequence:AppendCallback(function()
        self:OpenAllTenCard()
    end)
    sequence:AppendInterval(0.8)
    sequence:AppendCallback(function()
        SetActive(self.ui.m_btnBuyAnother, true)
        self.isPlaying = false
    end)
end

--收集十抽卡牌初始位置数据
function UI_LotteryShow:CollectInitPosData()
    --目标格子数据收集
    local targetGird = GetChild(self.ui.m_goTen,"grid")
    local gridTrans = targetGird.transform
    local childCount = gridTrans.childCount
    
    for i = 0,childCount-1 do
        local child = gridTrans:GetChild(i)
        table.insert(self.tenGridList,child)
    end
    
    --卡牌组初始位置展示
    local dataGrid = GetChild(self.ui.m_goTen,"dataGrid")
    local trans = dataGrid.transform
    childCount = trans.childCount
    
    local dataList = {}
    for i = 0,childCount-1 do
        local child = trans:GetChild(i)
        local rect = GetComponent(child,UE.RectTransform)
        table.insert(dataList,{
            x = rect.anchoredPosition.x;
            y = rect.anchoredPosition.y;
            angle = child.eulerAngles.z;
        })
    end
    
    local initTrans = self.ui.m_goInitNode.transform
    for _,v in ipairs(dataList) do
        local obj = UEGO.Instantiate(self.ui.m_goTenCard,initTrans)
        local rect = GetComponent(obj,UE.RectTransform)
        local btn = GetChild(obj,"checkTenCard",UEUI.Button)
        rect.anchoredPosition = Vector2.New(v.x,v.y)
        obj.transform.rotation = Quaternion.Euler(0,0,v.angle)
        obj.transform.localScale = Vector3.one*1.2
        
        RemoveUIComponentEventCallback(btn,UEUI.Button)
        AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
            if not self:CheckCanClick(obj) then
                return
            end
            self.isPlaying = true
            self:PlaySingleCardAnim(obj,0.8,function()
                self.isPlaying = false
            end)
            AudioMgr:Play(124)
        end)
        
        SetActive(obj,true)
        table.insert(self.tenCardList,obj)
    end
end

--一起同时开卡
function UI_LotteryShow:OpenAllTenCard()
    for _,v in ipairs(self.tenCardList) do
        self:PlaySingleCardAnim(v)
    end
end

--展示十张卡信息
function UI_LotteryShow:ShowTenCardInfo(msg)
    for k,v in ipairs(self.tenCardList) do
        if v and msg[k] then
            self:ShowCardInfo(v,msg[k])
        end
    end
end

function UI_LotteryShow:AddHistory(data)
	if self.lotteryHistory then
		for index, value in ipairs(data) do
			if not self.lotteryHistory[value.code] then
				self.lotteryHistory[value.code] = value.amount
			else
				self.lotteryHistory[value.code] = self.lotteryHistory[value.code] + value.amount
			end

		end
	end
end

--更新资源栏
function UI_LotteryShow:UpdateRes()
    local BagModule = BagManager:GetBagDataById(ItemID.HeroTicket)
    if BagModule then
        self.ui.m_txtHero.text = BagModule.num
    else
        self.ui.m_txtHero.text = 0
    end
    BagModule = nil
    BagModule = BagManager:GetBagDataById(ItemID.EquipmentTicket)
    if BagModule then
        self.ui.m_txtEquip.text = BagModule.num
    else
        self.ui.m_txtEquip.text = 0
    end
    self.ui.m_txtDiamond.text = NetUpdatePlayerData:GetResourceNumByID(ItemID.DIAMOND)
end

function UI_LotteryShow:BagChange(data)
    self:UpdateRes()
end

return UI_LotteryShow