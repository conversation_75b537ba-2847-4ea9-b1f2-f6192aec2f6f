﻿using System;
using AssetBundles;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.U2D;

public class SpriteAtlasMgr : MonoBehaviour
{
    private static SpriteAtlasMgr mInstance = null;
    public static SpriteAtlasMgr Instance
    {
        get
        {
            if (mInstance == null)
            {
                mInstance = GameObject.FindObjectOfType(typeof(SpriteAtlasMgr)) as SpriteAtlasMgr;
                if (mInstance == null)
                {
                    GameObject go = new GameObject(typeof(SpriteAtlasMgr).Name);
                    mInstance = go.AddComponent<SpriteAtlasMgr>();
                    GameObject parent = GameObject.Find("Managers");
                    if (parent == null)
                    {
                        parent = new GameObject("Managers");
                        GameObject.DontDestroyOnLoad(parent);
                    }
                    if (parent != null)
                    {
                        go.transform.parent = parent.transform;
                    }
                }
            }
            return mInstance;
        }
    }

    private Dictionary<string, SpriteAtlas> m_DictAtlas = new Dictionary<string, SpriteAtlas>();

    public void Init()
    {
        LogMan.Info("****** SpriteAtlasMgr");
    }

    private void OnEnable()
    {       
        SpriteAtlasManager.atlasRequested += RequestAtlas;      
    }

    private void OnDisable()
    {       
        SpriteAtlasManager.atlasRequested -= RequestAtlas;      
    }

    public bool HasLoaded(string tag)
    {
        if (m_DictAtlas.ContainsKey(tag))
        {
            return true;
        }
        return false;
    }
    
    /// <summary>
    /// 当SpriteAB 加载的时候同步加载 AtlasAB
    /// </summary>
    /// <param name="spriteABName"></param>
    /// <param name="callback"></param>
    /// <returns></returns>
    public string RequestAtlasBySpriteABName(string spriteABName, System.Action<SpriteAtlas> callback)
    {
        var spritePath = spriteABName;//类似:sprite/new_hero_1_role
        string pathHead = (AssetBundleConfig.SpritePath + "/").ToLower();
        if (spritePath.StartsWith(pathHead, StringComparison.OrdinalIgnoreCase))
        {
            string tag = spritePath[pathHead.Length..];
            RequestAtlas(tag, callback, 1);
            return (AssetBundleConfig.AtlasPath + "/" + tag).ToLower();
        }
        return string.Empty;
    }
    
    void RequestAtlas(string tag, System.Action<SpriteAtlas> callback)
    {
        RequestAtlas(tag, callback, AssetBundleLoaderWithPriority.DEFAULT_PRIORITY);
    }
    void RequestAtlas(string tag, System.Action<SpriteAtlas> callback,int priority = AssetBundleLoaderWithPriority.DEFAULT_PRIORITY)
    {
        string path = AssetBundleConfig.AtlasPath + "/" + tag;
        
        bool isTiny = tag.Contains("ui_tg_");
    
        if (isTiny)
        {
            int iStart = tag.LastIndexOf("tg");
            string strSub = tag.Substring(iStart + 3);

            int iEnd = strSub.IndexOf("_");
            string tgName = strSub.Substring(0, iEnd);

            path = $"TinyGame/{tgName}/Atlas/{tag}";
        }

        if (m_DictAtlas.ContainsKey(tag))
        {
       //     LogMan.Info("****** RequestAtlas In Cache : " + path);
            if (null != callback)
                callback(m_DictAtlas[tag]);
            return;
        }
        LogMan.Info("****** RequestAtlas : " + path);

        System.Action<UnityEngine.U2D.SpriteAtlas> call = (atlas) => 
            {
                if (atlas && !m_DictAtlas.ContainsKey(tag))        
                    m_DictAtlas.Add(tag, atlas);
                
                if (null != callback)
                    callback(atlas);
            };
        AssetManager.Instance.LoadSpriteAtlasAsync(path + ".spriteatlas", call, priority);
    }

    public Sprite GetSprite(string spritePath,int priority = AssetBundleLoaderWithPriority.DEFAULT_PRIORITY)
    {
        Sprite sp = null;
        string pathHead = AssetBundleConfig.SpritePath + "/";

        //bool isTiny = spritePath.Contains("TinyGame/MakeupSalon");

        //if (isTiny)
        //{
        //    pathHead = "TinyGame/MakeupSalon/Sprite" + "/";
        //}

        string pathTail = spritePath.Substring(spritePath.IndexOf(pathHead) + pathHead.Length);
        string[] pathSp = pathTail.Split('/');
        if(pathSp.Length == 2)
        {
            System.Action<SpriteAtlas> call = (spa) => 
            {
                if(spa != null)
                {
                    sp = spa.GetSprite(pathSp[1].Substring(0, pathSp[1].LastIndexOf('.')));
                }
            };
            RequestAtlas(pathSp[0], call,priority);
        }
        return sp;
    }
}
