local GoLimit21 = {}
local M = GoLimit21
local timerId = "GoLimit21"

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit21.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.count = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
	self.message = GetChild(self.go,"doLimit/bg/txtBg/m_txtMessage",UEUI.Text)
    self.red = GetChild(self.go,"doLimit/bg/goPoint")
    self.newReward = GetChild(self.go,"doLimit/m_goNewReward",UE.CanvasGroup)
    self.newRewardText = GetChild(self.newReward,"m_txtNewReward",UEUI.Text)
    self.slider = GetChild(self.go,"doLimit/bg/Limit/Slider",UEUI.Slider)
    self.textProgress = GetChild(self.go,"doLimit/bg/Limit/text_progress",UEUI.Text)
    self.txtLevelValue = GetChild(self.go,"doLimit/bg/Limit/img/m_txtLevelValue",UEUI.Text)
    self.newRewardText.text = LangMgr:GetLang(8125)
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
   
    SetActive(self.red,MonthlySeasonManager:HasCanReward())
    EventMgr:Add(EventID.MONTHLYSEASON_CLOCKZERO,self.OnClockZero,self)
    EventMgr:Add(EventID.MONTHLYSEASON_LEVELUP,self.OnLevelUp,self)
    EventMgr:Add(EventID.MONTHLYSEASON_SHOWMESSAGE,self.OnShowMessage,self)
	EventMgr:Add(EventID.NEW_ACTIVITY,self.NewActivity,self)
end
function M:OnClockZero()
    self:SetRedShow(MonthlySeasonManager:HasCanReward())
end
function M:OnLevelUp()
    self:ShowLevelUpUI()
    self:SetRedShow(true)
end
function M:OnShowMessage(isShow)
    if isShow then
        self:ShowNewTaskMessage()
    else
        self:ClearMessage()
    end
end
function M:SetItem(param)
    self.id = param.id
    self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
    local active = self.activeInfo.form.activeMess
    self.totalType = param.totalType
    self.condition = param.condition
    SetImageSprite(self.icon,active.icon,false)

    local time = self.activeInfo:GetRemainingTime()
    if NetMonthlySeasonData.data.hasNewMessage then
        if time >= OneDaySeconds then
            NetMonthlySeasonData.data.hasNewMessage = false
        end
    end
    if not MonthlySeasonManager.hasFirstHasNewMessage and NetMonthlySeasonData.data.hasNewMessage then
        self:ShowEndMessage()
    elseif NetMonthlySeasonData.data.hasNewTask then
        self:ShowNewTaskMessage()
    end
    self:updateProgressInfo()
end

function M:NewActivity(totalType)
	if totalType == ActivityTotal.MonthlySeason then
		self:updateProgressInfo()
	end
end

function M:updateProgressInfo()
    SetActive(self.red,MonthlySeasonManager:HasCanReward())
    local energyConfig = NetMonthlySeasonData:GetNowEnergyInfo()
    self.txtLevelValue.text = energyConfig.curLevel
    local cur = energyConfig.curEnergy
    local max = energyConfig.nextEnergy
    if energyConfig.curLevel >= energyConfig.maxLevel then
        cur = energyConfig.nextEnergy
    end
    self.textProgress.text = cur.."/"..max
    self.slider.value = cur/energyConfig.nextEnergy
end
function M:ChangState(id)

end
function M:ChangeValue()
    self:updateProgressInfo()
end
---来自timer 的调用
function M:ChangeItem()
    local time = self.activeInfo:GetRemainingTime()
    self.count.text = TimeMgr:CheckHMSNotEmpty(time)
    if not MonthlySeasonManager.hasFirstHasNewMessage and not NetMonthlySeasonData.data.hasNewMessage then
        if time < OneDaySeconds then
            self:ShowEndMessage()
            NetMonthlySeasonData.data.hasNewMessage = true
        end
    end
end
function M:ShowLevelUpUI()
    self.newReward:DOKill()
    self.newReward:DOFade(1,0.3):OnComplete(function ()
        self.newReward:DOFade(0,0.5):SetDelay(6)
    end)
end
function M:ShowEndMessage()
    SetActive(self.message.transform.parent,true)
    self.message.text = LangMgr:GetLang(7074)
end

function M:ShowNewTaskMessage()
    SetActive(self.message.transform.parent,true)
    self.message.text = LangMgr:GetLang(26)
end
function M:ClearMessage()
    SetActive(self.message.transform.parent,false)
end
function M:SetRedShow(isShow)
    SetActive(self.red,isShow)
end
function M:ClickItem(arg1)
	UI_SHOW(UIDefine.UI_MonthlySeason)
end

function M:Close()
    EventMgr:Remove(EventID.MONTHLYSEASON_CLOCKZERO,self.OnClockZero)
    EventMgr:Remove(EventID.MONTHLYSEASON_LEVELUP,self.OnLevelUp)
    EventMgr:Remove(EventID.MONTHLYSEASON_SHOWMESSAGE,self.OnShowMessage)
	EventMgr:Remove(EventID.NEW_ACTIVITY,self.NewActivity)
    TimeMgr:DeleteTimer(timerId)
    UEGO.Destroy(self.go)
end

return M