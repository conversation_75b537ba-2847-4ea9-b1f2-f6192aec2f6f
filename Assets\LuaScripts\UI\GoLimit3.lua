local GoLimit3 = {}
local M = GoLimit3

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit3.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"bg/icon",UEUI.Image)
    GetChild(self.go,"bg/countDown",UEUI.Text).text = LangMgr:GetLang(7073)
    self.count = GetChild(self.go,"bg/CountDown/countTxt",UEUI.Text)
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
end

function M:SetItem(param)
    self.id = param.id
    self.totalType = param.totalType
    self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
    self:ChangeItem()

    local foreshow_icon = self.activeInfo.form.activeMess.foreshow_icon
    if foreshow_icon then
        SetUIImage(self.icon,foreshow_icon,false)
    end
end

function M:ChangeItem()
    self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetStartRemainingTime())
end

function M:ChangeValue()
    
end

function M:ClickItem(arg1)
    UI_SHOW(UIDefine.UI_UpcomingEvents,self.id)
end

function M:Close()
    UEGO.Destroy(self.go)
end

return M