local GoLimit27 = {}
local M = GoLimit27
local prePath = "Assets/ResPackage/Prefab/UI/GoLimit30.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.time = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
    self.red = GetChild(self.go,"doLimit/bg/goPoint")
    self.text_progress = GetChild(self.go,"doLimit/bg/Limit/text_progress",UEUI.Text)
	self.img = GetChild(self.go,"doLimit/bg/Limit/img",UEUI.Image)
	--self.showDiceTips = GetChild(self.go,"doLimit/bg/AddDiceTips/view",UE.RectTransform)
    --self.diceTipsPosY = self.showDiceTips.localPosition.y
	
	self.itemPop = GetChild(self.go,"doLimit/bg/itemPop")
	self.popIcon = GetChild(self.go,"doLimit/bg/itemPop/popIcon",UEUI.Image)
	self.popNum = GetChild(self.go,"doLimit/bg/itemPop/popNum",UEUI.Text)

	SetActive(self.itemPop,false)
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
		--Log.Error("ShowCatView")
		--UI_SHOW(UIDefine.UI_ChanceCatItem)
		if self.activeInfo and self.activeInfo:IsActivityEnd() then
		 	NetKeepCatData:CheckEndPush(self.id)
			return
		end
		local useEnergy = NetKeepCatData:GetAllCatCostEnergy()
		local needPointAndGetRollNum = KeepCatManager:GetSettingConfigById(1).value:split("|")
		if useEnergy >= v2n(needPointAndGetRollNum[1]) then
			UI_SHOW(UIDefine.UI_KeepCatView)
		else
			UI_SHOW(UIDefine.UI_KeepCatRescue)
		end
        self:RefreshUI()
    end)
    self.slider = GetChild(self.go,"doLimit/bg/Limit/Slider",UEUI.Slider)
end
--init
function M:SetItem(param)
    self.id = param.id
    local activeInfo = LimitActivityController:GetActiveMessage(self.id)
	self.activeInfo = activeInfo
    self.active = activeInfo.form.activeMess
	self.totalType = param.totalType
    SetImageSprite(self.icon,self.active.icon,false)
	if self.active.pass_icon2 then
		SetImageSprite(self.img,self.active.pass_icon2,false)
	end

    local needPointAndGetRollNum = KeepCatManager:GetSettingConfigById(1).value:split("|")
    self.needPoint = tonumber(needPointAndGetRollNum[1])
    --self.acquireRollNum = tonumber(needPointAndGetRollNum[2])

    local present = NetKeepCatData:GetCostEnergy() / self.needPoint
    self.slider.value = present
    self.text_progress.text = math.floor(present * self.needPoint) .. "/" .. self.needPoint

	self:SetRedShow()
end

function M:RefreshUI()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end
    self.hasLastSequence = false
    self:SetRedShow()
    local needPointAndGetRollNum = KeepCatManager:GetSettingConfigById(1).value:split("|")
    self.needPoint = tonumber(needPointAndGetRollNum[1])
    local present = NetKeepCatData:GetCostEnergy() / self.needPoint
    self.slider.value = present
    self.text_progress.text = math.floor(present * self.needPoint) .. "/" .. self.needPoint

    if Tween.IsTweening(self.img.transform) then
        self.img.transform:DOKill()
        self.img.transform.localScale = Vector3.one
    end
end

function M:SetProgress()
    local upgrageNum = KeepCatManager.upgrageNum

	local needPointAndGetRollNum = KeepCatManager:GetSettingConfigById(1).value:split("|")
	self.needPoint = tonumber(needPointAndGetRollNum[1])
	--self.acquireRollNum = tonumber(needPointAndGetRollNum[2])

	local present = NetKeepCatData:GetCostEnergy() / self.needPoint
    local tweenSpeed = 2
    if not upgrageNum then
        upgrageNum = 0
    end

    if self.sequence then
        self.sequence:Kill()
    end
    self.sequence = Tween.Sequence()

    self.sequence:SetAutoKill(true)
    if not self.hasLastSequence then
        self.sequence:PrependInterval(0.7)
    end
    self.hasLastSequence = true
    self.img.transform:DOScale(Vector3.one * 1.2,0.2):SetDelay(0.7)
    if upgrageNum > 0 then
        for i = 1, upgrageNum, 1 do
            local speed
            if i == 1 then
                speed = (1 - self.slider.value) * tweenSpeed
            else
                speed = tweenSpeed
            end
            self.sequence:Append(self.slider:DOValue(1,speed))
            self.sequence:AppendCallback(function ()
                self:SetRedShow()
                self.slider.value = 0
                KeepCatManager.upgrageNum = KeepCatManager.upgrageNum - 1
            end):OnUpdate(function ()
                local value = self.slider.value * self.needPoint
                self.text_progress.text = Mathf.Floor(value + 0.5) .. "/" .. self.needPoint
            end)
        end
    end
    self.sequence:Append(self.slider:DOValue(present,tweenSpeed * present):OnUpdate(function ()
        local value = self.slider.value * self.needPoint
        self.text_progress.text = Mathf.Floor(value + 0.5) .. "/" .. self.needPoint
    end))
    self.sequence:AppendCallback(function ()
        self.img.transform:DOScale(Vector3.one,0.2)
        self.hasLastSequence = false
    end)
end

function M:ChangState(id)
    self:SetRedShow()
end

function M:ChangeValue()
    self:SetProgress()
	self:SetRedShow()
	self:RefreshGoLimitPop()
end

function M:RefreshGoLimitPop()
	local addNum = NetKeepCatData.data.addNum
	local hasTips = NetKeepCatData.data.hasTips
	if nil == self.popIng then
		self.popIng = false
	end
	if self.popIng then
		self.popNum.text = "x"..addNum
		return
	end
	
	if hasTips and not self.popIng then
		self.popIng = true
		local itemId = KeepCatManager:GetSettingConfigById(10).value or "198424"
		SetUIImage(self.popIcon,ItemConfig:GetIcon(v2n(itemId)),false)
		self.popNum.text = "x"..addNum	
		self.timer = TimeMgr:CreateTimer("GoLimit30",function ()
				SetActive(self.itemPop,false)
				self.popIng = false
				NetKeepCatData.data.addNum = 0
				NetKeepCatData.data.hasTips = false
			end,3.2,1)
		
		DOScale(self.itemPop.transform,0.2,0,function ()
				SetActive(self.itemPop,true)
				DOScale(self.itemPop.transform,1.0,0.5,function()					
				end)
			end)	
	end
end
---tick
function M:ChangeItem()
	if not self.activeInfo then
		return
	end
	local time = self.activeInfo:GetRemainingTime()
	if time > 0 then
		self.time.text = TimeMgr:CheckHMSNotEmpty(time)
	else
		self.time.text = LangMgr:GetLang(7077)
	end
end
function M:SetRedShow()
	local red = NetKeepCatData:IsShowCatRed()
    SetActive(self.red,red)
	--if NetKeepCatData.data. then
		
	--end
end


function M:Close()
	if self.timer then
		TimeMgr:DestroyTimer("GoLimit30",self.timer)		
	end
	NetKeepCatData.data.addNum = 0
	NetKeepCatData.data.hasTips = false
    UEGO.Destroy(self.go)
end

return M