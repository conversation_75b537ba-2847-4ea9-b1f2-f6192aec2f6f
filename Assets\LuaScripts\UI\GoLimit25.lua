local GoLimit25 = {}
local M = GoLimit25
local prePath = "Assets/ResPackage/Prefab/UI/GoLimit25.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.time = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
    self.red = GetChild(self.go,"doLimit/bg/redPoint")
    self.text_progress = GetChild(self.go,"doLimit/bg/Limit/text_progress",UEUI.Text)
	self.img = GetChild(self.go,"doLimit/bg/Limit/img",UEUI.Image)
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        UI_SHOW(UIDefine.UI_ContinuousRecharge)
    end)
    self.slider = GetChild(self.go,"doLimit/bg/Limit/progressBg",UEUI.Slider)
	self.message = GetChild(self.go,"doLimit/m_goNewReward")
	self.message:GetComponentInChildren(typeof(UEUI.Text)).text = LangMgr:GetLang(7035)
end
--init
function M:SetItem(param)
    self.id = param.id
    local activeInfo = LimitActivityController:GetActiveMessage(self.id)
	self.activeInfo = activeInfo
    self.active = activeInfo.form.activeMess
	self.totalType = param.totalType
    ContinuousRechargeManager:InitActive(activeInfo.info.activeId)
    SetImageSprite(self.icon,self.active.icon,false)
	if NetContinuousRechargeData.data.allLuckyDraws > 0 then
		self:SetRedShow(true)
	end
end

function M:ChangState(id)

end
function M:ChangeValue()
end
---tick
function M:ChangeItem()
	if not self.activeInfo then
		return
	end
	local time = self.activeInfo:GetRemainingTime()
	if time > 0 then
		self.time.text = TimeMgr:CheckHMSNotEmpty(time)
	else
		self.time.text = LangMgr:GetLang(7077)
	end
end
function M:SetRedShow(isShow)
	self.red:SetActive(isShow)
end
function M:ShowMessageUI(isShow)
	return self.message:GetComponent(typeof(UE.CanvasGroup)):DOFade(isShow and 1 or 0,isShow and 1 or 0.5)
	-- self.message:SetActive(isShow)
end
function M:Close()
    UEGO.Destroy(self.go)
end

return M