local UI_SevenDayView = Class(BaseView)
local ItemBase = require("UI.Common.BaseSlideItem")
local GiftItem = Class(ItemBase)
local RankBox = require("UI.RankBox")
local ItemShowNums = 5


function UI_SevenDayView:OnInit()
    
	EventMgr:Add(EventID.GLOBAL_TOUCH_BEGIN, self.OnGlobalTouchBegin, self)
end

function UI_SevenDayView:OnGlobalTouchBegin(vtArray,touchCount,isForce3D)
	if self.rankBox and self.rankBox:IsInRankBoxRect(vtArray[0]) then

	else
		self:RemoveRankBox()
	end
end

function UI_SevenDayView:OnCreate(param)
	self.clickType = self:AutoSelectTab()
	self.tablist = {}
	self.stepRewardList = {}
		
	self.slider = require("UI.Common.SlideRect").new()
	self.slider:Init(self.uiGameObject.transform:Find("bg/giftviewPort"):GetComponent(typeof(UEUI.ScrollRect)),2)
	self.giftItemList = {}
	for i = 1, ItemShowNums do
		self.giftItemList[i] = GiftItem.new()
		self.giftItemList[i]:Init(UEGO.Instantiate(self.ui.m_goSevenCell.transform),self)
	end
	self.slider:SetItems(self.giftItemList,-15,Vector2.New(0,0))
	
	--预览图
	--local skinCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.skin_collection,SevenDayManager:Get7SettingValueById(1))
	--if skinCfg and skinCfg.skin_icon then
		--SetImageSprite(self.ui.m_imgRewardPreview,skinCfg.skin_icon,false)
	--end

	self:SetIsUpdateTick(true)	
	self:InitTab()
	self:InitStepReward()

	self:OnClickTabEnd(self.clickType)
	
	self:TickUI(1)
end

function UI_SevenDayView:AutoSelectTab()
	local day = NetSevenDayData:Get7CurDayIndex()

	for i = 1, day do
		if SevenDayManager:HasFinishTaskNotReward(i) then
			return i
		end
	end

	--已经没有可以领奖的了
	if day < 7 then
		--7天以内
		return day
	else
		--超过7天 先选择 有没有  没完成任务的
		for i = 1, 7 do
			if not SevenDayManager:IsAllTaskFinishedByDay(i) then
				return i
			end
		end

		return 7
	end
end

function UI_SevenDayView:InitTab()
	SetActive(self.ui.m_goTog,false)
	local btnObj = self.ui.m_goTog
	local parent = self.ui.m_goParentTog.transform

	for i = 1, 7 do
		local obj = UEGO.Instantiate(btnObj,parent)
		obj.name = "m_goTog" .. i
		obj.transform.localScale = Vector3.New(1, 1, 1)
		SetActive(obj,true)
		local dt = {}
		dt.day = i
		dt.index = i
		dt.light_img = GetChild(obj, "light_btn",UE.RectTransform)
		dt.light_txt = GetChild(obj, "layoutA/light_txt",UEUI.Text)
		dt.text = GetChild(obj, "layoutB/Text",UEUI.Text)
		dt.red_img = GetChild(obj, "red_img",UE.RectTransform)
		
		dt.complete = GetChild(obj, "complete",UE.RectTransform)
		dt.layoutA = GetChild(obj, "layoutA",UE.RectTransform)
		dt.layoutB = GetChild(obj, "layoutB",UE.RectTransform)
		dt.lockA = GetChild(obj, "layoutA/lock",UE.RectTransform)
		dt.lockB = GetChild(obj, "layoutB/lock",UE.RectTransform)

		dt["obj"] = obj
		table.insert(self.tablist,dt)
		RemoveUIComponentEventCallback(obj,UEUI.Button)
		AddUIComponentEventCallback(obj,UEUI.Button,function (go,param)
			-- local day = NetSevenDayData:Get7CurDayIndex()
			-- if i <= day then
				self:OnClickTabEnd(i)
			-- else
			-- 	UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(2202058,i))
			-- end
		end)
		
		
		dt.text.text = LangMgr:GetLangFormat(2202012, i)
		dt.light_txt.text = LangMgr:GetLangFormat(2202012, i)
	end
end

function UI_SevenDayView:InitStepReward()
	local rewardCell = self.ui.m_goScoreItem
	local parent = rewardCell.transform.parent
	local rect = GetComponent(parent.gameObject,UE.RectTransform)
	local totalWidth = rect.sizeDelta.x

	local config = ConfigMgr:GetData(ConfigDefine.ID.rookietarget_reward)

	for i, v in ipairs(config) do
		
		local tempGo = UEGO.Instantiate(rewardCell)
		AddChild(parent,tempGo,false)

		local icon = GetChild(tempGo,"boxIcon",UEUI.Image)
		local imgFinish = GetChild(tempGo,"imgFinish",UEUI.Image)
		local txtCnt = GetChild(tempGo,"score",UEUI.Text)
		local anchor = GetChild(tempGo,"ancher")
		local ani = GetChild(tempGo,"boxIcon",TweenAnim)
		local red = GetChild(tempGo,"red_img")
		local complete = GetChild(tempGo,"complete")

		SetImageSprite(icon,v.icon,false)
		txtCnt.text = v.rank
		
		self.stepRewardList[i] = {
			id = v.id,
			finish = imgFinish,
			score = v.rank,
			ani = ani,
			red = red,
			complete = complete
		}

		local rt = GetComponent(tempGo,UE.RectTransform)
		rt:SetLocalPositionXY((i/#config)*totalWidth,0)

		local btnStep = GetComponent(tempGo,UEUI.Button)
		btnStep.onClick:RemoveAllListeners()
		local function onStepClick()
			Log.Info("click"..i)
			if SevenDayManager:CanStepReward(i) then
				SevenDayManager:SetStepRewared(i)
				--可以领奖 发放奖励
				local reward = SevenDayManager:GetStepRewardById(i)
				local param = {}
				param.type = 13
				param.rewards = reward
				UI_SHOW(UIDefine.UI_Recharge, param, function ()
					NetGlobalData:GetRewardToMap(reward,NetSevenDayData.REASON)
				end, false)
				self:ResetStepReward()
			else
				self:CreateRankBox(anchor,SevenDayManager:GetStepRewardById(i))
			end
		end
		btnStep.onClick:AddListener(onStepClick)

	end

	SetActive(rewardCell,false)
	
end

function UI_SevenDayView:ResetStepReward()
	local score = NetSevenDayData:GetTaskScore()

	self.ui.m_txtScore.text = score

	local finishCnt = 0
	local pct = 0

	for i, v in ipairs(self.stepRewardList) do
		if score >= v.score then
			finishCnt = finishCnt + 1
		else
			if i > 1 then
				local upStep = self.stepRewardList[i-1]
				local temp = (v.score-upStep.score) > 0 and (v.score-upStep.score) or 0.001
				pct = (score - upStep.score)/temp
			else
				local temp = (v.score) > 0 and (v.score) or 0.001
				pct = score/temp
			end
			break
		end
	end

	local progress = finishCnt * (1/#self.stepRewardList) + (1/#self.stepRewardList) * pct
	if progress>1 then
		progress = 1
	end

	self.ui.m_slider3.value = progress

	for i, v in ipairs(self.stepRewardList) do
		local state = NetSevenDayData:GetTaskScoreStateByIndex(i)

		if state == ITEM_STATE.REWARED then
			--已经领奖
			SetActive(v.finish,true)
			SetActive(v.red,false)
			-- v.ani:DOPause()
		else
			if score >= v.score then
				
				--可以领奖
				SetActive(v.finish,false)
				SetActive(v.red,true)
				-- v.ani:DORestart()
			else
				--不可以领奖
				-- v.ani:DOPause()
				SetActive(v.finish,false)
				SetActive(v.red,false)
			end
		end
	end
end

function UI_SevenDayView:SetItemListDatas()

	local datas = SevenDayManager:GetSevenTaskByDay(self.clickType)
	if not datas then
		return
	end
	for k, v in pairs(datas) do
		local state = NetSevenDayData:Get7TaskData(v.id).state
		v.state = state or 1
	end
	table.sort(datas, function(a, b)
		if a.state == b.state then
			return a.sort < b.sort
		else
			return a.state < b.state
		end
	end)
	self.slider:SetData(datas)
end

function UI_SevenDayView:OnClickTabEnd(type)
	self.clickType = type
	for k, v in pairs(self.tablist) do
		v.light_img.gameObject:SetActive(self.clickType == v.index)
		v.light_txt.gameObject:SetActive(self.clickType == v.index)
		v.text.gameObject:SetActive(self.clickType ~= v.index)
		SetActive(v.layoutA,self.clickType == v.index)
		SetActive(v.layoutB,self.clickType ~= v.index)

		local diffDay = NetSevenDayData:Get7CurDayIndex()
		if v2n(diffDay) < v2n(v.day) then
			v.lockA.gameObject:SetActive(true)
			v.lockB.gameObject:SetActive(true)
			v.light_txt.text = ""
			v.text.text = ""
		else
			v.lockA.gameObject:SetActive(false)
			v.lockB.gameObject:SetActive(false)
			v.text.text = LangMgr:GetLangFormat(2202012, k)
			v.light_txt.text = LangMgr:GetLangFormat(2202012, k)
			SetActive(v.red_img,SevenDayManager:HasFinishTaskNotReward(v.day))
		end
		SetActive(v.complete,SevenDayManager:IsAllTaskFinishedByDay(v.day))
		
	end
	-- Log.Error(self.clickType)
	--滑动任务
	self:SetItemListDatas()
	--礼包刷新
	self:RefreshBuyGift()
	--刷新阶段奖励
	self:ResetStepReward()
end

function UI_SevenDayView:RefreshBuyGift()
	local id = self.clickType
	local config = ConfigMgr:GetDataByID(ConfigDefine.ID.rookietarget_gift,id)
	local reward = config.reward
	local isCurrentDay = id == NetSevenDayData:Get7CurDayIndex()
	local isLock = id > NetSevenDayData:Get7CurDayIndex()

	local hasBuy = NetSevenDayData:IsGiftPay(id)
	if hasBuy then
		--已经购买
		SetActive(self.ui.m_goComplete,true)
		SetActive(self.ui.m_goNotComplete,false)
		
	else
		--还没有购买
		SetActive(self.ui.m_goComplete,false)
		SetActive(self.ui.m_goNotComplete,true)
		

		local pay_config1 = ConfigMgr:GetDataByID(ConfigDefine.ID.payment, config.pay_id1)
		local pay_config2 = ConfigMgr:GetDataByID(ConfigDefine.ID.payment, config.pay_id2)

		if isCurrentDay then
			--打折
			SetActive(self.ui.m_goGiftPrice,true)
			SetActive(self.ui.m_goGiftTimer,true)
			
			self.ui.m_txtGetGift2.text = LangMgr:GetLang(pay_config1.price_langid)
			self.ui.m_txtGetGift.text = LangMgr:GetLang(pay_config2.price_langid)
		else
			--不打折
			SetActive(self.ui.m_goGiftPrice,false)
			SetActive(self.ui.m_goGiftTimer,false)
			self.ui.m_txtGetGift.text = LangMgr:GetLang(pay_config1.price_langid)
		end
		SetUIImageGray(self.ui.m_btnGetGift,isLock)
		
		PurchaseManager:CreateScoreTag(config.pay_id1,self.ui.m_btnGetGift.transform)
		
		local outline = GetComponent(self.ui.m_txtGetGift,UEUI.Outline)
		local shadow = GetComponent(self.ui.m_txtGetGift,CS.Coffee.UIEffects.UIShadow)
		if isLock then
			local color = Color.HexToRGB("5d5d5d")
			if outline then outline.effectColor = color end
			if shadow then shadow.effectColor = color end
		else
			local color = Color.HexToRGB("B75200")
			if outline then outline.effectColor = color end
			if shadow then shadow.effectColor = color end
		end
		
	end

	local itemParentRect = GetChild(self.ui.m_goGiftRewardList,"Viewport/Content",UE.RectTransform)
	local itemList = SplitString(reward,";")

	local count = itemParentRect.childCount
	for i = 1, count do
		local child = itemParentRect:GetChild(i - 1)

		if i <= #itemList then
			local icon = GetChild(child,"icon",UEUI.Image)
			local cnt = GetChild(child,"num",UEUI.Text)

			local itemData = SplitStringToNum(itemList[i],"|")
			if #itemData >=2 then
				SetImageSprite(icon,ItemConfig:GetIcon(itemData[1]),false)
				cnt.text = "x"..itemData[2]

				SetActive(child,true)

				RemoveUIComponentEventCallback(child,UEUI.Button)
				AddUIComponentEventCallback(child,UEUI.Button,function (go,param)
					UI_SHOW(UIDefine.UI_ItemTips,itemData[1])
				end)
			else
				SetActive(child,false)
			end
		else
			SetActive(child,false)
		end
	end
end

function UI_SevenDayView:OnRefresh(type,param)
	if type == 1 then--付费购买 回调
		local id = param
		if self.clickType ~= id then
			self:OnClickTabEnd(id)
		end

		local config = ConfigMgr:GetDataByID(ConfigDefine.ID.rookietarget_gift,id)
		if config then
			local param = {}
			param.type = 13
			param.rewards = config.reward
			UI_SHOW(UIDefine.UI_Recharge, param, nil, false)
		end

		self:RefreshBuyGift()
	elseif type == 2 then
		local id = param or self.clickType
		self:OnClickTabEnd(id)
	elseif type == 3 then
		--获得积分 显示动画
		local number = param or 1
		--飞积分
		local flyIcon = "Sprite/ui_mainface/qirimubiao_jifen.png"
		local Pos = Vector3.zero--UIMgr:GetObjectScreenPos(self.rewardA_icon.transform)
		local Pos2 =UIMgr:GetObjectScreenPos(self.ui.m_goFlyScoreAnchor.transform)
		local lock = false
		MapController:FlyUIAnimByImg(Pos.x,Pos.y,flyIcon, 20,Pos2.x, Pos2.y,nil,nil,nil,nil,FlyResourceType.FlyNone,function ()
			if not lock then
				UI_UPDATE(UIDefine.UI_SevenDayView,2)
			end
			lock = true
		end)
	end
end

function UI_SevenDayView:TickUI(delta)

	if not self.lastTickTime then
		self.lastTickTime = 1
	end
	self.lastTickTime = self.lastTickTime + delta
	if self.lastTickTime < 1 then
		return
	end
	self.lastTickTime = 0

	local remainTime = SevenDayManager:GetRemainTime()
	if remainTime > 0 then
		self.ui.m_txtTime.text = TimeMgr:CutBuyWorkTime(remainTime)
	else
		self.ui.m_txtTime.text = ""
	end

	--更新礼包倒计时
	local id = self.clickType
	local hasBuy = NetSevenDayData:IsGiftPay(id)
	if not hasBuy and NetSevenDayData:Get7CurDayIndex() == id then
		local nowTime = TimeMgr:GetServerTimestamp()
		local zeroTime = TimeZoneMgr:GetServerClockStampByNDay(1,nowTime)
		self.ui.m_txtGiftTimer.text = TimeMgr:ConverSecondToString(zeroTime - nowTime)
	end

end

-- function UI_SevenDayView:SendReward(reward)
-- 	local param = {}
-- 	param.type = 13
-- 	param.rewards = reward
-- 	UI_SHOW(UIDefine.UI_Recharge, param, function ()
-- 		NetGlobalData:GetRewardToMap(reward)
-- 	end, false)
-- end

function UI_SevenDayView:onUIEventClick(go,param)
	self:RemoveRankBox()
	local name = go.name
	if name == "btn_close" then
		self:Close()
	elseif name == "m_btnGetGift" then
		local id = self.clickType
		if id > NetSevenDayData:Get7CurDayIndex() then
			return
		end

		local hasBuy = NetSevenDayData:IsGiftPay(id)
		if not hasBuy then
			local isCurrentDay = id == NetSevenDayData:Get7CurDayIndex()
			local config = ConfigMgr:GetDataByID(ConfigDefine.ID.rookietarget_gift,id)

			if isCurrentDay then
				PaymentConfig:ShowPay(config.pay_id2)--当天买打折
			else
				PaymentConfig:ShowPay(config.pay_id1)--原价购买
			end
		end
	elseif name == "m_btnRewardPreview" then
		local rewardItem = SevenDayManager:Get7SettingValueById(1)
		rewardItem = v2n(rewardItem)
		local curMapID = NetUpdatePlayerData.playerInfo.curMap
		local skinInfo = SkinCollectConfig:GetDataByRoleId(rewardItem)
		local mapItem = MapController:GetItemById(v2n(skinInfo.belong_obj))						
		if not mapItem then
			--if mapItem then
				--MapController:ChangeMap(MAP_ID_MAIN)
				UI_SHOW(UIDefine.UI_GoToMainMap,rewardItem,tonumber(skinInfo.map))
			--end
		else
			if mapItem then
				UI_SHOW(UIDefine.UI_MapDressPreview, mapItem[1],rewardItem)
			end
		end
	end
end

function UI_SevenDayView:onDestroy()
	self.slider = nil
	EventMgr:Remove(EventID.GLOBAL_TOUCH_BEGIN, self.OnGlobalTouchBegin, self)
end

function UI_SevenDayView:CreateRankBox(obj,rewards)
	if self.rankBox == nil then
		self.rankBox = RankBox.new(self.uiGameObject)
		self.rankBox:InitUI(2,function (itemId)
			if itemId then
				UI_SHOW(UIDefine.UI_ItemTips,itemId)
				self:RemoveRankBox()
			end
		end,function()
			self.rankBox:UpdateItem(obj,rewards)
		end)
	end
end

function UI_SevenDayView:RemoveRankBox()
	if self.rankBox then
		self.rankBox:Destory()
		self.rankBox = nil
	end
end

--region GiftItem
function GiftItem:OnInit(transform,basePanel)
	self.basePanel = basePanel
	self.transform = transform
	ResMgr:LoadAssetAsync("Sprite/ui_task/taskbox1.png",AssetDefine.LoadType.Sprite,function(img)
			self.stateNormalBg = img
	end)

	self.taskIcon = transform:Find("obj/taskBg/imgTarget"):GetComponent(typeof(UEUI.Image))
	--任务进度
	self.complete = transform:Find("obj/complete")
	--self.go_obj = transform:Find("obj")
	----self.m_imgFinish = transform:Find("obj/m_imgFinish")
	----任务标题
	self.taskTitle = transform:Find("obj/txtTargetContent"):GetComponent(typeof(UEUI.Text))
	----奖励文字
	self.txtReward = transform:Find("obj/txtReward"):GetComponent(typeof(UEUI.Text))
	----奖励Icon
	--self.imgRewardIcon = transform:Find("obj/txtReward/imgIcon"):GetComponent(typeof(UEUI.Image))
	----奖励数量文字
	--self.txtRewardNum = transform:Find("obj/txtReward/imgIcon/txtNum"):GetComponent(typeof(UEUI.Text))
	----第二个奖励
	--self.go_twoRewardIcon = transform:Find("obj/txtReward/imgIcon_2")
	--self.twoRewardIcon = transform:Find("obj/txtReward/imgIcon_2"):GetComponent(typeof(UEUI.Image))
	--self.go_twoRewardNum = transform:Find("obj/txtReward/imgIcon_2/txtNum")
	--self.twoRewardNum = transform:Find("obj/txtReward/imgIcon_2/txtNum"):GetComponent(typeof(UEUI.Text))
	self.taskProgress = transform:Find("obj/numPercent"):GetComponent(typeof(UEUI.Text))
	----按钮背景图
	--self.btnImg = transform:Find("obj/btnGet"):GetComponent(typeof(UEUI.Image))
	----按钮
	self.go_btn = transform:Find("obj/btnGet")
	self.find_btn = transform:Find("obj/btnFind")
	self.btnFind = transform:Find("obj/btnFind"):GetComponent(typeof(UEUI.Button))
	self.btnGetReward = transform:Find("obj/btnGet"):GetComponent(typeof(UEUI.Button))
	----按钮的文字
	self.btnContent = transform:Find("obj/btnGet/txtBtnContent"):GetComponent(typeof(UEUI.Text))
	self.btnContentFind = transform:Find("obj/btnFind/txtBtnContent"):GetComponent(typeof(UEUI.Text))
	----设置奖励静态文字
	self.txtReward.text = LangMgr:GetLang(2202060)
	--self.btnContent.text = LangMgr:GetLang(17)
	--self.maskGraphics = transform:GetComponentsInChildren(typeof(UEUI.MaskableGraphic),true)
	--self.grayed = false
	self.rewardA_icon = GetChild(transform,"obj/imgIcon",UEUI.Image)
	self.rewardA_num = GetChild(transform,"obj/imgIcon/txtNum",UEUI.Text)
	self.rewardB_icon = GetChild(transform,"obj/imgIcon_2",UEUI.Image)
	self.rewardB_num = GetChild(transform,"obj/imgIcon_2/txtNum",UEUI.Text)
	self.imgLock = GetChild(transform,"obj/imgLock",UEUI.Image)
end

function GiftItem:UpdateData(data,index)
	if nil == data then return end
	self.taskTitle.text =LangMgr:GetLang(data.lang_id)-- "("..data.id..")"
	local netTaskData = NetSevenDayData:Get7TaskData(data.id)
	local state = netTaskData.state
	local curProgress = math.floor(netTaskData.progress)
	
	-- local taskConfig = NetSevenDayData:Get7TaskData(data.id)
	local maxProgress = data.condition

	if curProgress > maxProgress then
		curProgress = maxProgress
	end
	
	if data.icon then
		SetUIImage(self.taskIcon,data.icon,false)
	end
	
	self.btnContent.text = LangMgr:GetLang(17)
	self.btnContentFind.text = LangMgr:GetLang(9212)

	SetActive(self.complete,state == DAY_TASK_STATE.FINISHED_REWARDED)

	if self.basePanel.clickType > NetSevenDayData:Get7CurDayIndex() then
		SetActive(self.imgLock,true)
		SetActive(self.go_btn,false)
		SetActive(self.find_btn,false)

		if state == DAY_TASK_STATE.FINISHED_REWARDED then
			self.taskProgress.text = maxProgress.."/"..maxProgress
		else
			if state == DAY_TASK_STATE.FINISHED_NOT_REWARD then
				self.taskProgress.text = GetStrRichColor(maxProgress.."/"..maxProgress,"00FF00")
			else
				self.taskProgress.text = GetStrRichColor(curProgress,"FF0000").."/"..maxProgress
			end
		end
	else
		SetActive(self.imgLock,false)
		if state == DAY_TASK_STATE.FINISHED_REWARDED then
			SetActive(self.go_btn,false)--已经领取完奖励
			SetActive(self.find_btn,false)

			self.taskProgress.text = maxProgress.."/"..maxProgress
		else
			if state == DAY_TASK_STATE.FINISHED_NOT_REWARD then
				SetActive(self.go_btn,true)--任务完成但是没领取奖励
				SetActive(self.find_btn,false)
				self.taskProgress.text = GetStrRichColor(maxProgress.."/"..maxProgress,"00FF00")
			else
				SetActive(self.go_btn,false)--没有完成是否显示查找按钮
				SetActive(self.find_btn,(data.look_for or 0) > 0)
				self.taskProgress.text = GetStrRichColor(curProgress,"FF0000").."/"..maxProgress
			end
		end
	end
	
	if data.reward then
		local arr = string.split(data.reward, ';')
		if #arr >= 1 then
			SetActive(self.rewardA_icon,true)
			SetActive(self.rewardA_num,true)
			local rewards =  SplitStringToNum(arr[1],"|")
			SetImageSprite(self.rewardA_icon,ItemConfig:GetIcon(rewards[1]),false)
			self.rewardA_num.text = rewards[2]
		else
			SetActive(self.rewardA_icon,false)
			SetActive(self.rewardA_num,false)
		end

		if #arr >= 2 then
			SetActive(self.rewardB_icon,true)
			SetActive(self.rewardB_num,true)
			local rewards =  SplitStringToNum(arr[2],"|")
			SetImageSprite(self.rewardB_icon,ItemConfig:GetIcon(rewards[1]),false)
			self.rewardB_num.text = rewards[2]
		else
			SetActive(self.rewardB_icon,false)
			SetActive(self.rewardB_num,false)
		end

	end

	RemoveUIComponentEventCallback(self.btnFind)
	AddUIComponentEventCallback(self.btnFind, UEUI.Button, function(arg1,arg2)
		if self.basePanel.clickType > NetSevenDayData:Get7CurDayIndex() then
			return
		end
		local config = ConfigMgr:GetDataByID(ConfigDefine.ID.rookietarget_task,data.id)
		local look_for = config.look_for
		local look_for_value = config.look_for_value
		if look_for then
			FindController.LookFindItem(look_for,look_for_value)
			UI_CLOSE(UIDefine.UI_SevenDayView)
		end
	end)

	RemoveUIComponentEventCallback(self.btnGetReward)
	AddUIComponentEventCallback(self.btnGetReward, UEUI.Button, function(arg1,arg2)
		if self.basePanel.clickType > NetSevenDayData:Get7CurDayIndex() then
			return
		end
			if state == DAY_TASK_STATE.FINISHED_NOT_REWARD then
				--完成但是没有奖励
				local res = NetSevenDayData:Click7FinishTaskById(data.id)
			end
	end)
end

function GiftItem:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec
end

function GiftItem:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition
end

--endregion

return UI_SevenDayView