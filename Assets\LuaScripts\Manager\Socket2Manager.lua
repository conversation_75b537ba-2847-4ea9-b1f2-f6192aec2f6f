local Socket2Manager = Class()
local DEF_HEART_TICK_COUNT = 3
local MAX_SOCKET_RECONNECT_TIME = 5
local MAX_SOCKET_RECONNECT_COUNT = 6 --长连接重连次数限s制
local SOCKET_TIMER = "SOCKET_TIMER"

local SocketEventEnum = {
    NetworkEvent_None = 0,

    NetworkEvent_StartConnect = 1, --发起连接
    NetworkEvent_Disconnect = 2, --断开连接
    NetworkEvent_SendMessage = 3, --发送消息

    NetworkEvent_ConnectOK = 4, --发起连接成功
    NetworkEvent_ConnectFail = 5, --发起连接失败
    NetworkEvent_ConnectionLost = 6, --连接断开
    NetworkEvent_ConnectionError = 7, --连接发生错误
    NetworkEvent_ReceivedMessage = 8, --收到一条消息
    NetworkEvent_SocketClosed = 9, --socket已经关闭
}

function Socket2Manager:ctor()
    self.m_gameConnID = 0
    self.m_socketTimer = 0
    self.m_isSocketLogin = false
    self.m_isReconnecting = false
    self.m_isKicked = false
    self.m_TickHeartBeat = 0
    self.m_socketReConnectCount = 0
	self.m_isKickedSave = false

	self.m_hasProtoLoaded = false
	self.m_cid = 0
	self.m_sendedData = {}
end

function Socket2Manager:IsOnLine()
    return (self.m_isSocketLogin and not self.m_isKicked)
end

function Socket2Manager:IsKickedSave()
	return self.m_isKickedSave
end

function Socket2Manager:SetKickedSave(value)
	 self.m_isKickedSave = value
end

function Socket2Manager:Tick(deltaTime)
    if not self.m_IsReady then
        return
    end
    if self.m_isSocketLogin then
        self.m_TickHeartBeat = self.m_TickHeartBeat + deltaTime
        if self.m_TickHeartBeat >= DEF_HEART_TICK_COUNT then
            self:C2S_HeartBeat()
            self.m_TickHeartBeat = 0
        end
    else
        self.m_TickHeartBeat = self.m_TickHeartBeat + deltaTime
        if self.m_TickHeartBeat >= DEF_HEART_TICK_COUNT then
            Log.Info("****** wait reconnect", self.m_socketReConnectCount)
            self.m_TickHeartBeat = 0
        end
    end
end

function Socket2Manager:C2S_Login()
	SdkHelper:ThinkingTrackEvent(ThinkingKey.login_slg_start,nil)
	local params = {}
    params.token = HttpClient.STR_TOKEN
    params.id = StorageMgr:GetPlayerDataId() --NetUpdatePlayerData:GetPlayerInfo().id
	Log.Info("params.token:"..params.token)
	Log.Info("params.id:"..params.id)
	local account = require("Proto.Handler.NetRequest.Account")
	account:OnReqLogin(params.id,params.token,function (isOk,data)
		if isOk then
    	    --Log.Info("OnReqLogin:".. table.dump(data))
            ServerPushManager:SetSyncFlag(data.sync_flag)
            HeroManager:SetHeroTotalFight(data.power)
		    self.m_isSocketLogin = true
		end
	end)
end

function Socket2Manager:C2S_RoleEnter()
    local role = require("Proto.Handler.NetRequest.Role")
    role:OnReqEnter()
	LotteryManager:OnReqDrawOpen(1)
	LotteryManager:OnReqDrawOpen(2)
    TowerManager:RequestTowerInfo()
	BagManager:OnReqQuickBuyItemData()
	BagManager:RoleGiftInfoRequest()
    JJcManager:OnReqArenaLoad()
    TradeWagonsManager:RequestAllInfo()
    TradeWagonsManager:RequestTrainLoadData()
    WorldBossManager:OnCheckBossInfo()
	TopFightManager:OnReqTopfightLoad()
	TopFightManager:OnRoleEnterReqest()
    PurchaseManager:Initialize()
end

function Socket2Manager:C2S_HeartBeat ()
	self:Send(ProtocolTab.MessageID.Heart)
end

function Socket2Manager:C2S_ACK (msgId, sig)
    -- local params = {}
    -- params.id = msgId
    -- params.sig = sig
    -- local json = Json.encode(params)
    -- self:SendData(self.m_gameConnID, 100, json)
end

function Socket2Manager:IsConnect(connID)
    return NetEventMgr.IsConnected(connID)
end

function Socket2Manager:Disconnect(connID)
    Log.Info("------ push close: " .. connID)
    NetEventMgr.Disconnect(connID)
end

function Socket2Manager:GetConnectDetail(connID)
    local data = self.m_connectDict[connID]
    if data == nil then
        Log.Error("###### GetConnectDetail:", connID)
        return "", 0
    end
    return data[2], data[3]
end

function Socket2Manager:Send(protocolId, msg, callback, caller, callBackData)
	local protoPath = ProtocManager:GetProtoRequestById(protocolId)
	
	local bytes = nil
    
	if msg and next(msg) ~= nil then
		bytes = assert(ProtocManager:Encode(protoPath, msg))
	end
    if protocolId ~= ProtocolTab.MessageID.Heart then
        UIMgr:ShowWaiting(true)
    end
	if callback then
		self:SendSync(protocolId,bytes,function (response)
            UIMgr:ShowWaiting(false)
			if response.ok == false then
				if caller == nil then
                    callback(false, response.code, callBackData)
                else
                    callback(caller, false, response.code, callBackData)
                end
				--TODO 错误信息 调整
				if not self:IsIgnoreError(protocolId) then
					local errorInfo = string.format("(%s) 协议返回失败: %s  错误码：%s", response.protocol ,v2s(response.code),response.devmsg)
					Log.Error(errorInfo)
					UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetErrLang(response.code))
				end
					
				if response.code == ServerDefine.marge_topia_protocol_ResultCode.ErrorNeedLogin then
					self:C2S_Login()	
				end
				return
			end
			local protoName = ProtocManager:GetProtoResponseById(response.protocol)
			if protoName == nil or string.len(protoName) == 0 then
				if caller == nil then
                    callback(false, response.code, callBackData)
                else
                    callback(caller, false, response.code, callBackData)
                end
				return
			end
			
			local data = assert(ProtocManager:Decode(protoName, response.data))
			if caller == nil then
				callback(true, data, callBackData)
			else
				callback(caller, true, data, callBackData)
			end
		end)
	else
		self:SendSync(protocolId,bytes,function (response)
            UIMgr:ShowWaiting(false)
			if response.ok == false then
				if not self:IsIgnoreError(protocolId) then
					local errorInfo = string.format("(%s) 协议返回失败: %s  错误码：%s", response.protocol ,v2s(response.code),response.devmsg)
					Log.Error(errorInfo)
					UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetErrLang(response.code))
				end
				if response.code == ServerDefine.marge_topia_protocol_ResultCode.ErrorNeedLogin then
					self:C2S_Login()
				end
				return
			end
		end)
	end
	
end

--是否忽略错误
function Socket2Manager:IsIgnoreError(protocolId)
	if protocolId == ProtocolTab.MessageID.AuditLogDiamond then
		return true
	end
	return false
end

function Socket2Manager:SendSync(msg_id,param,callBack)
	local cid = self:GetNextCID()

	local sendData = {}
	sendData.cid = cid
	sendData.callBack = callBack
	self.m_sendedData[cid] = sendData

	local protoData = {}
	protoData.cid = cid			-- 通信ID
	protoData.protocol = msg_id	-- 通信协议
	protoData.param = param		-- 参数

	local binarydata = assert(ProtocManager:Encode("marge.topia.gate.Request", protoData))
	NetEventMgr.SendMessage(self.m_gameConnID, 0, binarydata)
end



function Socket2Manager:OnNetworkEvent(eventType, curConnID, protoID, msg)
    if eventType == SocketEventEnum.NetworkEvent_ReceivedMessage then
        self:HandleMessage(curConnID, protoID, msg)
    else
        Log.Info("****** push state:", eventType)
        if eventType == SocketEventEnum.NetworkEvent_ConnectOK then
            self:OnConnectSuccess(curConnID)

        elseif eventType == SocketEventEnum.NetworkEvent_ConnectFail then
            self:OnConnectFail(curConnID)

        elseif eventType == SocketEventEnum.NetworkEvent_ConnectionError then
            self:OnConnectFail(curConnID)

        elseif eventType == SocketEventEnum.NetworkEvent_ConnectionLost then
            self:OnConnectLost(curConnID)

        elseif eventType == SocketEventEnum.NetworkEvent_SocketClosed then
            self:OnConnectClosed(curConnID)

        end
    end
end

function Socket2Manager:OnConnectSuccess()
    self:C2S_Login()
    self.m_isReconnecting = false
end

function Socket2Manager:OnConnectFail(connID)
    self.m_isSocketLogin = false
    self.m_isReconnecting = false
    self:ResetSocket()
    self:TriggerReconnectTimer()

    Log.Warning("###### push fail")
end

function Socket2Manager:OnConnectLost(connID)
    self.m_isSocketLogin = false
    self.m_isReconnecting = false
    self:ResetSocket()

    Log.Warning("###### push lost")

    if self.m_isKicked then
        Log.Info("###### push kick down")
    else
        self:TriggerReconnectTimer()
    end
end

function Socket2Manager:OnConnectClosed (connID)
    self.m_isSocketLogin = false
end

function Socket2Manager:TriggerReconnectTimer()
    local function onReConnect()
       self:ReConnectSocket()
    end
    if not self.reconnectTime then
        self.reconnectTime = 1
    else
        if self.reconnectTime < MAX_SOCKET_RECONNECT_TIME then
            self.reconnectTime = self.reconnectTime + 1
        end
    end
    TimeMgr:CreateTimerSingleOne(self, onReConnect, self.reconnectTime, 1)
end

function Socket2Manager:ReConnectSocket()
    Log.Info("****** retry push")
    self.m_socketReConnectCount = self.m_socketReConnectCount + 1
    if self.m_socketReConnectCount >= MAX_SOCKET_RECONNECT_COUNT then
        self:ReStartGame()
        return
    end
    self.m_isReconnecting = true
    self:ResetSocket()
    self:ConnectSocket(1)
end

function Socket2Manager:ConnectSocket(state)
    if not Game.IsUseTCPNet then
        return
    end

    local function _func(eventType, curConnID, protoID, msg)
        self:OnNetworkEvent(eventType, curConnID, protoID, msg)
        if state == 0 then
            self.m_IsReady = true
        end
    end
    local ip, port = HttpClient:GetSocket2Inf()
	if not ip then
            Log.Error("未找到配置SLG, ip")
        return
    end    --无配置下正常进入
	if not self.m_hasProtoLoaded then
		self.m_hasProtoLoaded = true
		ProtocManager:LoadPB(function ()
			self.m_gameConnID = NetEventMgr.StartConnect2(ip, port, _func)
		end)
	else
		self.m_gameConnID = NetEventMgr.StartConnect2(ip, port, _func)
	end
    
end

function Socket2Manager:DisconnectGame()
    if self.m_gameConnID > 0 then
        self:Disconnect(self.m_gameConnID)
        self.m_gameConnID = 0
    end
end

function Socket2Manager:ResetSocket(reTryTimes)
    self.m_isSocketLogin = false
    self.m_isKicked = false
    self:DisconnectGame()
    if reTryTimes then
        self.m_socketReConnectCount = reTryTimes
        if reTryTimes == 0 then
            self.m_IsReady = false
        end
    end
end

-- 处理服务器推送消息
function Socket2Manager:HandlePushMessage(response)
	if response.ok == false then
		--TODO 错误信息 调整
		local errorInfo = string.format("(%s) 推送消息返回失败: %s  错误码：%s", response.protocol ,v2s(response.code),response.devmsg)
		Log.Error(errorInfo)
		UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetErrLang(response.code))
		return
	end

	local protoName = ProtocManager:GetProtoResponseById(response.protocol)
	if protoName == nil or string.len(protoName) == 0 then
       if response.code ~= 0 then
			local errorInfo = string.format("(%s) 推送消息返回失败: %s  错误码：%s", response.protocol ,v2s(response.code),response.devmsg)
			Log.Error(errorInfo)
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetErrLang(response.code))
      end
		return
	end

	local data = assert(ProtocManager:Decode(protoName, response.data))

	NetMessageEvent:Broadcast(response.protocol,data)

end

function Socket2Manager:HandleMessage (curConnID, protoID, strMsg)
	--Log.Info("[S2M]:", protoID)

	-- data.cid
	-- data.protocol
	-- data.ok
	-- data.code
	-- data.data
	-- data.devmsg

	local response = assert(ProtocManager:Decode("marge.topia.gate.CallBack", strMsg))

	if response then
		if self:IsPushMsg(response) then
			-- 推送消息
			self:HandlePushMessage(response)
		else
			-- 有回调
			local sendData = self.m_sendedData[response.cid]
			if sendData and sendData.callBack then
				sendData.callBack(response)
                self.m_sendedData[response.cid] = nil
			else
				Log.Error("[S2M] no callback found error", protoID)
			end
		end
	else
		Log.Error("[S2M] proto decode error ", protoID)
	end
end

-- 判断是或是 推送
---@param response any
---@return boolean
function Socket2Manager:IsPushMsg(response)
	if response.cid == 0 and response.protocol >= 100000 then
		return true
	end
	return false
end

function Socket2Manager:ClearUp()
    self.m_gameConnID = nil
    self.m_socketTimer = nil
    self.m_isSocketLogin = nil
    self.m_isKicked = nil
    self.m_TickHeartBeat = nil
    self.m_socketReConnectCount = 0
end

function Socket2Manager:ReLoginNeeded()
    Log.Info("****** push relogin")
    HttpClient:ShowUIReConnect()
end

function Socket2Manager:ReStartGame()
	local function onRetry()
		SceneMgr:SwitchScene(UISceneDefine.LoginScene)
	end
	UI_SHOW(UIDefine.UI_TipsTop, 7062, onRetry, nil, 2)
	Socket2Mgr:ResetSocket(0)
	SdkHelper:ThinkingTrackEvent(ThinkingKey.login_slg_restart,nil)
end

function Socket2Manager:GetNextCID()
	if not self.m_cid then
		self.m_cid = 0
	end
	self.m_cid = self.m_cid + 1
	return self.m_cid
end

return Socket2Manager