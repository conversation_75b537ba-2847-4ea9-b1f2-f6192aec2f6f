
local UI_JJcShopHelp = Class(BaseHelpView)

function UI_JJcShopHelp:OnInit()

end

function UI_JJcShopHelp:OnCreate(callback)
	local config = ConfigMgr:GetDataByKey(ConfigDefine.ID.activity_help, "prefab", UIDefine.UI_JJcShopHelp, true)
	if not config then
		Log.Error("获取帮助界面配置表错误 error ")
		return
	end

	-- 先隐藏所有页签
	local childCount = self.ui.m_scrollview.content.transform.childCount
	for i = 1, childCount, 1 do
		local page = GetChild(self.ui.m_scrollview.content, "Page" .. i)
		SetActive(page, false)
	end

	-- 设置页签数量
	self:OnCreate_Base(nil, nil, #config)
	self.callback = callback

	for _, value in pairs(config) do
		local page = GetChild(self.ui.m_scrollview.content, "Page" .. value.page)
		SetActive(page, true)

		local nodeList = {}
		nodeList.item1 = GetChild(page, "Content/item1")
		nodeList.arrowUp = GetChild(page, "Content/arrowUp")
		nodeList.item2 = GetChild(page, "Content/item2")
		nodeList.arrowDown = GetChild(page, "Content/arrowDown")
		nodeList.item3 = GetChild(page, "Content/item3")

		-- 先全部隐藏
		for _, node in pairs(nodeList) do
			SetActive(node, false)
		end

		-- 图标列表
		local iconList = string.split(value.how_icon, "|")
		for index, icon in ipairs(iconList) do
			local path = string.format("Content/item%d/img%d", index, index)
			local img = GetChild(page, path, UEUI.Image)
			SetUIImage(img, icon, true)
		end

		-- 根据配置的图标数量，显示对应的节点
		local length = #iconList
		if length == 1 then
			SetActive(nodeList.item1, true)
		elseif length == 2 then
			SetActive(nodeList.item1, true)
			SetActive(nodeList.arrowUp, true)
			SetActive(nodeList.item2, true)
		elseif length == 3 then
			SetActive(nodeList.item1, true)
			SetActive(nodeList.arrowUp, true)
			SetActive(nodeList.item2, true)
			SetActive(nodeList.arrowDown, true)
			SetActive(nodeList.item3, true)
		end

		-- 标题
		local title = GetChild(self.ui.m_transDesc, "txt" .. value.page, UEUI.Text)
		title.text = LangMgr:GetLang(value.describe)
		-- 角色
		local roleconfig = RoleSpineConfig:GetDataByID(value.spine)
		if roleconfig and roleconfig.img_spine then			
			ResMgr:LoadAssetAsync(roleconfig.img_spine, AssetDefine.LoadType.Instant,function (skeletonData)
					self.ui.m_spui.skeletonDataAsset = skeletonData
					self.ui.m_spui:Initialize(true)
			end)					
		end
	end
end

function UI_JJcShopHelp:OnRefresh(param)

end

function UI_JJcShopHelp:onDestroy()
	if self.callback then
		self.callback()
		self.callback = nil
	end
end

function UI_JJcShopHelp:onUIEventClick(go,param)
	local name = go.name

end

function UI_JJcShopHelp:OnMoveStart_Virtual(pageIndex)
	-- 页码边界检查
	if pageIndex > self.ui.m_transDesc.childCount then
		return
	end
	-- 隐藏已显示的标题
	local child
	for i = 0, self.ui.m_transDesc.childCount - 1, 1 do
		child = self.ui.m_transDesc:GetChild(i)
		if child.gameObject.activeSelf then
			child.gameObject:SetActive(false)
			break
		end
	end
	-- 显示页码对应的标题
	self.ui.m_transDesc:GetChild(pageIndex - 1).gameObject:SetActive(true)
end

return UI_JJcShopHelp