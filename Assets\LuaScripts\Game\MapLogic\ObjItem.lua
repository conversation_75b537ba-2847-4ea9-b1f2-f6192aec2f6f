---



---
local ObjNode = require "Game.MapLogic.ObjNode"
---@class ObjItem :ObjNode
local ObjItem = Class(ObjNode)
local M = ObjItem

local triggerGiftBubble = require "Game.MapLogic.DoubleOreBubble"
local asset_name_1 = "Assets/ResPackage/Sprite/tile/map_dibanguang4.png"
local asset_name_2 = "Sprite/tile/tile_back0.png"
local asset_name_3 = "Sprite/tile/map_dibanguang.png"
local asset_name_4 = "Sprite/tile/tile_back1.png"

local cutTressPath = "Assets/ResPackage/Prefab/Map/CutTress.prefab"
local StackNumFlag = require "Game.MapLogic.StackNumFlag"
 
function M:ctor()
    self.m_OrderLayerId = SortingLayerInGame.WORLD_ITEM
    self.m_OrderIdx = 0

    self.m_Config = nil
    --self.m_EditorInf = nil

    --from config to memory
    self.m_Arr4x4 = nil
    self.m_Len4x4 = 0
    self.m_IsHasBottomTile = 0
    self.m_IsCanDelete = false
    self.m_IsCanMove = false
    self.m_IsCanCombined = false

    --from many ways to memory
    self.m_InactiveState = 0
    self.m_LockState = 0
    self.m_IceState = 0
    self.m_DropExtinction = nil
    self.m_PicState = 0
    self.m_DropExtra = nil

    --object base
    self.m_Id = 0
    self.m_Type = 0
    self.m_IsSpine = false
    self.m_IsCantPut = nil

    self.m_SpItem = nil
    self.m_TransSpritePa = nil
    self.m_TransSpinePa = nil
    self.m_SpineGo = nil
    self.m_TransWidgetBottom = nil
    self.m_TransWidgetTop = nil

    --proper from editor or config and it will save
    --...

    --memory options
    self.m_IsActive = false
    self.m_ItemShakeTarget = nil
    self.m_IsShake = false
    self.m_IsLightUp = false
    self.m_IsLightUpBack = false
    self.m_IsMerging = false
    self.m_IsGray = false
    self.m_IsTouchEnable = nil
    self.m_IsHasGrayTile = false

    self.m_LightUpBackRGBA = 0

    self.m_NewFromWhere = 0
    self.m_NewFromParam = nil

    self.m_IsDuringBirth = 0
	self.m_ArrowClick = false
    self.cur_tile_state = 0
    self.m_IsMagnetLightUp = false

    self.multipleNumFlag = nil
    
    self.isMultipleBuffUp = false
    self.curIceNum = 0
    self.curSelectIceUnlock = false
end

function M:InitObj(gridX, gridY, config, edInf, inf, parentTrans, isEnergyBuffUp,fromWhereId)
    self:setGrid(gridX, gridY, true)
    self.m_Config = config
    --self.m_EditorInf = edInf
	self.m_isEnergyBuffUp = isEnergyBuffUp

    self:loadProper(config, edInf, inf)

    if parentTrans then
        self:setParent(parentTrans)
        self:onInit(inf)
        self:initNode(2, "Assets/ResPackage/Prefab/Map/Item.prefab")
    end

    self:AddFlagIcon()

    self:AddEnvent()
    if self:IsCanUse() and self.m_NewFromWhere ~= ItemFromWhere.MapInit then
        ServerPushManager:ChangeMapItem(self.m_Id,self.m_Type,true)
    end
    --trace
    if self:IsCanUse() then
        if inf then
			if self.m_Type == ItemUseType.ObjItemStackTree and self.stackData~=nil then
				for index, value in ipairs(self.stackData) do
					MapController:TraceItem(3,value,1,fromWhereId)
				end
			end
			MapController:TraceItem(3,config.id,1,fromWhereId)
        else
			local fId = fromWhereId
			if fromWhereId == ItemFromWhere.DropBox and self.m_NewFromParam and self.m_NewFromParam[5] then
				fId = self.m_NewFromParam[5]
			end
            MapController:TraceItem(1,config.id,1,fId)
        end
    end
end

function M:AddEnvent()
    EventMgr:Add(EventID.REFRESH_MAP_TOYITEM, self.SetToyTargetIcon, self)
end

function M:loadProper(config, edInf, inf)
    self.m_Id = config["id"]
    self.m_Type = config["type_use"]
    if not string.empty(config["img_spine"]) then
        self.m_IsSpine = true
    end
    self.m_Arr4x4, self.m_Len4x4 = Load4x4Inf(config["box4x4"])
    self:ReadCollider(config["collider"])

    if not string.empty(config["drop_offset"]) then
        local off = ItemConfig:GetOffsetDrop(self.m_Id)
        self:setNodeOffset(off.x, off.y)
    end
	
 	self:setNodeRota(config["drop_rota"])
	
    local strArrInf = self.m_Config["arrow_inf"]
    if not string.empty(strArrInf) then
        local arrInf = string.split(strArrInf, '|')
        if #arrInf < 2 then
            return
        end
        local newTb = {}
        table.insert(newTb, tonumber(arrInf[1]))
        table.insert(newTb, tonumber(arrInf[2]))
        table.insert(newTb, tonumber(arrInf[3]))
		if nil ~= arrInf[4] then
			table.insert(newTb, tonumber(arrInf[4]))
		end
        self.m_ArrowInf = newTb
    end

    self.m_IsHasBottomTile = GetNumberValue(config["is_has_bottom"], 0)
    self.m_IsCanDelete = GetBoolValue(config["is_can_delete"])
    self.m_IsCanMove = GetBoolValue(config["is_can_move"])
    self.m_IsCanCombined = GetBoolValue(config["is_can_combine"])
    self.m_InactiveState = 0
    self.m_PicState = 0

    self:loadEditorProper(edInf)
    self.isIceItem = self.m_IceNum ~= 0

    if inf then
        self.m_InactiveState = inf["state0"] or 0
        self.m_LockState = inf["state1"]
        self.m_IceState = inf["state2"] or 0
		--临时修复线上地图开云了但是没有开地(修复付费岛付费后，地块解锁，但物品未解锁)
		if (self.m_LockState == 2 or self.m_LockState == 3) and  (MapController.m_MapId == 7 or MapController.m_MapId == 1) then
			local group = MapController:GetGroupByGrid(self.m_GridX, self.m_GridY)
			if group == nil then
				local groupId = MapController.m_MapGroupGrid[tostring(GetGridID(self.m_GridX, self.m_GridY))]
				if groupId ~= nil then
					Log.Error("修复地图物件灰色：mapId:".. MapController.m_MapId .." groupId:"..groupId.." 位置:" .. self.m_GridX.."_"..self.m_GridY.. " " ..table.dump(self) .. "  self.m_LockState:" .. self.m_LockState)
					self.m_LockState = 0
				end
			end
		end

		self.m_ArrowClick = inf["arrowClick"] or false
        local strLock0 = inf["lock0"]
        if strLock0 then
            local tbLocks = {}
            local arrLocks = string.split(strLock0, '|')
            for i, v in ipairs(arrLocks) do
                tbLocks[v] = true
            end
            if table.count(tbLocks) > 0 then
                self.m_LockArr = tbLocks
            end
        end
        local jDrop = inf["drop"]
        if jDrop then
            self:onExtinction(jDrop)
        else
            local jDropEx = inf["drop_ex"]
            if jDropEx then
                self:onExtraDrop(nil, jDropEx)
            end
        end

        self.oldCombineNum = inf["oldCombineNum"]
        self.curIceNum = inf["curIceNum"] or 0
        self.curSelectIceUnlock = inf["curSelectIceUnlock"] or false
        self:SetMultipleCanNotCombineNum()
    end
	
end

function M:CreateDrop(itemId, jDrop, isExtinctionDrop, onDoneDrop)
    if itemId == nil and jDrop == nil then
        return
    end
    if isExtinctionDrop then
        onDoneDrop = function(drop)
            drop:DestroyUI()
            MapController:DeleteItem(self, true)
        end
    end

    local x, y = nil
    if self:IsCastle() then
        x = self.m_Vt3Pos.x + 1.2
        y = self.m_Vt3Pos.y + 1.5
    end

    local drop = DropController.new()
    drop:SetOffsetByItem(nil, self.m_Config)
    drop:Init(false, onDoneDrop)
    drop:SetGrid(self.m_GridX, self.m_GridY, x, y)

    if jDrop then
        if not drop:Load(jDrop) then
            return nil
        end
    else
        local useSelf = 0
        if isExtinctionDrop then
            useSelf = 1
        end
        if type(itemId) == "table" then
            drop:SetDropId(itemId, useSelf, DropSetting.ItemList)
        else
            drop:SetDropId(itemId, useSelf, DropSetting.ItemOnly)
        end
    end

    return drop
end

function M:SaveObj(tb)
    tb["item"] = self.m_Id
    tb["item_inf"] = self:SaveInf()
end

function M:SaveInf()
    local saveInf = {}
    saveInf["state0"] = self.m_InactiveState
    saveInf["state1"] = self.m_LockState
    saveInf["state2"] = self.m_IceState
	if self.m_ArrowInf then
		saveInf["arrowClick"] = self.m_ArrowClick
	else
		saveInf["arrowClick"] = nil
	end
    if self.m_LockArr then
        local str = nil
        for k, v in pairs(self.m_LockArr) do
            if v then
                if str == nil then
                    str = k
                else
                    str = str .. "|" .. k
                end
            end
        end
        saveInf["lock0"] = str
    end

    if self.m_DropExtinction then
        saveInf["drop"] = self.m_DropExtinction:Save()

    elseif self.m_DropExtra then
        saveInf["drop_ex"] = self.m_DropExtra:Save()
    end

    if self.oldCombineNum then
        saveInf["oldCombineNum"] = self.oldCombineNum
    end
    if self.curIceNum then
        saveInf["curIceNum"] = self.curIceNum
    end    
    if self.curSelectIceUnlock then
        saveInf["curSelectIceUnlock"] = self.curSelectIceUnlock
    end
    
    self:onSaveInf(saveInf)

    return saveInf
end

--restore some information and clear
function M:onClear()
    if self.m_DropExtra then
        self.m_DropExtra:DestroyUI()
        self.m_DropExtra = nil
    end

    self.m_IsDuringBirth = 0
    self:setVisible(true)
    --self:setEffGray(false, true)
    self:onDestroy()

    self.m_TransWidgetTop:DestroyChildren()
    self.m_TransWidgetBottom:DestroyChildren()
    self:setEffBottomLightUp(false, true)

    if self.m_IsSpine then
        if self.m_SpineGo then
            UEGO.Destroy(self.m_SpineGo)
        end
    else
        if self.m_IsAnimScaleYoyo then
            DOKill(self.m_SpItem.transform)
            self.m_SpItem.transform:SetLocalScale(1.0, 1.0, 1.0)
            self.m_IsAnimScaleYoyo = false
        end
        self.m_SpItem.sprite = nil
    end

    if self.toyTween then
        DOKill(self.m_SpItem.transform)
        self.toyTween = nil
    end
    self:RemoveFlagIcon()

    if self.iceSliderGo then
        UEGO.Destroy(self.iceSliderGo)
        self.iceSliderGo = nil
        self.iceSlider = nil
        self.iceProgressTxt = nil
    end
    
    --trace
    if self:IsCanUse() and not SceneMgr.isSwitchScene then
        if self.m_beforeWastedId then
            MapController:TraceItem(2,self.m_beforeWastedId,1)
            self.m_beforeWastedId = nil
        else
            MapController:TraceItem(2,self.m_Config.id,1)
        end
        
    end
    if self.multipleNumFlag then
        self.multipleNumFlag:Destroy()
        self.multipleNumFlag = nil
    end
    self.oldCombineNum = nil
    if not SceneMgr.isSwitchScene then
        ServerPushManager:ChangeMapItem(self.m_Id,self.m_Type,false)
    end
end

function M:onNodeLoaded(isNew, nodeGo, nodeTrans)
    self:updateGridName()

    self.m_TransWidgetBottom = SearchChild(nodeTrans, "widget_bottom")
    self.m_SpTileGray = SearchChild(nodeTrans, "spGrayTile", SpriteRenderer)
    self.m_SpTileBottom = SearchChild(nodeTrans, "spTile", SpriteRenderer)
    self.m_TransSpinePa = SearchChild(nodeTrans, "spineItemPa")
    self.m_TransSpritePa = SearchChild(nodeTrans, "spItemPa")
    self.m_SpItem = SearchChild(nodeTrans, "spItemPa/spItem", SpriteRenderer)
    self.m_iceSpItemBottom = SearchChild(nodeTrans, "widget_bottom/iceSpItemBottom", SpriteRenderer)
    self.m_TransWidgetTop = SearchChild(nodeTrans, "widget_top")
    self.m_iceSpItemTop = SearchChild(nodeTrans, "widget_top/iceSpItemTop", SpriteRenderer)
    -- self:setTestInfo()

    if not isNew then
        --restore some information and clear
        self:setEffGray(false, true)
    end

    self:loadImgBottomTile()
    self:loadImgByState(function()
        self:onLoadFinished()
    end)
end

function M:loadImgByState(onFinish)
    local function onLoaded(prefabOrSp)
        if prefabOrSp then
            if self.m_IsSpine then
                local go, trans = CreateGOAndTrans(prefabOrSp)
                trans:SetParent(self.m_TransSpinePa)
				if self.m_Type == ItemUseType.HeroFly or self.m_Type == ItemUseType.NpcOrder  then
					trans:SetLocalPosition(0,0, 0)
				else
					trans:SetLocalPosition(0, -2, 0) -- todo 为啥-2？
				end
                trans:SetLocalScale(1.0, 1.0, 1.0)
                self.m_SpineGo = go
            else
                SetSprite3D(self.m_SpItem, prefabOrSp)
            end
        else
            if self.m_IsSpine then
                Log.Error("###### item spine load null : ", self.m_Id, self.m_Config["img_spine"])
            elseif self.m_PicState == 0 then
                Log.Error("###### item sprite load null : ", self.m_Id, self.m_Config["img"])
            elseif self.m_PicState == 1 then
                Log.Error("###### item sprite load null : ", self.m_Id, self.m_Config["withered_img"])
            end
        end
        if onFinish then
            onFinish()
        end
    end
    if self.m_IsSpine then
        ResMgr:LoadAssetAsync(self.m_Config["img_spine"], AssetDefine.LoadType.Instant, onLoaded)
    else
        if self.m_PicState == 0 then
            -- 双倍矿物品id
            local item_id = GlobalConfig:GetNumber(1507)
            local asset_path = self.m_Config["img"]
            if self.m_Config["id"] == item_id then
                local doubleOrePicConfig = ConfigMgr:GetData(ConfigDefine.ID.double_mine_pic)
                local listHotCastleItem = GlobalConfig:GetHotCastleItem()
                for k, v in pairs(doubleOrePicConfig) do
                    if v.item == v2n(listHotCastleItem) then
                        asset_path = v.picture
                    end
                end
            end
            if self.m_NewFromWhere == ItemFromWhere.DropBox then
                -- 是否开启多倍采矿
                local isOpenMultiple = self.m_NewFromParam[8]
                -- 多倍采矿升级前id
                local mulitpleOldItemId = self.m_NewFromParam[9]
                if isOpenMultiple and mulitpleOldItemId then
                    local oldData = ItemConfig:GetDataByID(mulitpleOldItemId)
                    asset_path = oldData["img"]
                end
            end
            ResMgr:LoadAssetWithCache(asset_path, AssetDefine.LoadType.Sprite, onLoaded)

        elseif self.m_PicState == 1 then
            ResMgr:LoadAssetWithCache(self.m_Config["withered_img"], AssetDefine.LoadType.Sprite, onLoaded)
        end
    end
end

function M:loadImgBottomTile()
    --some tree is has bottom but not high light
    if self.m_IsHasBottomTile > 0 then
        local function loadBack(sp)
            if sp then
                SetSprite3D(self.m_SpTileBottom, sp)
                if self.m_IsHasBottomTile > 1 then
                    SetActive(self.m_SpTileBottom, true)
                end
            else
                Log.Error("###### item loadBack null ", self.m_IsHasBottomTile)
            end
        end
        local name = string.format("Assets/ResPackage/Sprite/tile/tile_back%d.png", self.m_IsHasBottomTile)
        ResMgr:LoadAssetWithCache(name, AssetDefine.LoadType.Sprite, loadBack)
    end
end

function M:updateOffsetAndScale()
    local trans = nil
    if self.m_IsSpine then
        trans = self.m_TransSpinePa
    else
        trans = self.m_TransSpritePa
    end
    if not IsNil(trans) and self.m_Config then
        local vtOff = GetVector3ByStr(self.m_Config["offset"])
        if vtOff then
            trans:SetLocalPosition(vtOff.x, vtOff.y, vtOff.z)
        end

        local vtScale = GetVector3ByStr(self.m_Config["scale"])
        if vtScale then
            trans:SetLocalScale(vtScale.x, vtScale.y, vtScale.z)
        end
    end
end

function M:setImgVisible(isV)
    if self.m_IsSpine then
        SetActive(self.m_TransSpinePa, isV)
    else
        SetActive(self.m_TransSpritePa, isV)
    end
end

-- 做多倍采集升级动画
function M:DoMultipleUpAnimation(mulitpleOldItemId)
    if mulitpleOldItemId == nil then
        self.isMultipleBuffUp = false
        return
    end
    local oldData = ItemConfig:GetDataByID(mulitpleOldItemId)
    local asset_path = oldData["img"]
    local function onLoaded(prefabOrSp)
        if prefabOrSp then
            if self.m_IsSpine then
                local go, trans = CreateGOAndTrans(prefabOrSp)
                trans:SetParent(self.m_TransSpinePa)
                if self.m_Type == ItemUseType.HeroFly or self.m_Type == ItemUseType.NpcOrder  then
                    trans:SetLocalPosition(0,0, 0)
                else
                    trans:SetLocalPosition(0, -2, 0) -- todo 为啥-2？
                end
                trans:SetLocalScale(1.0, 1.0, 1.0)
                self.m_SpineGo = go
            else
                SetSprite3D(self.m_SpItem, prefabOrSp)
            end
        else
            if self.m_IsSpine then
                Log.Error("###### item spine load null : ", self.m_Id, self.m_Config["img_spine"])
            elseif self.m_PicState == 0 then
                Log.Error("###### item sprite load null : ", self.m_Id, self.m_Config["img"])
            elseif self.m_PicState == 1 then
                Log.Error("###### item sprite load null : ", self.m_Id, self.m_Config["withered_img"])
            end
        end
    end
    if self.m_IsSpine then
        ResMgr:LoadAssetAsync(oldData["img_spine"], AssetDefine.LoadType.Instant, onLoaded)
    else
        if self.m_PicState == 0 then
            ResMgr:LoadAssetWithCache(asset_path, AssetDefine.LoadType.Sprite, onLoaded)
        elseif self.m_PicState == 1 then
            ResMgr:LoadAssetWithCache(oldData["withered_img"], AssetDefine.LoadType.Sprite, onLoaded)
        end
    end
    
    if mulitpleOldItemId ~= self.m_Id then
        --local function CreateGo(asset)
        --    local newGo, newTrans = CreateGOAndTrans(asset)
        --    local vt = self.m_Vt3Pos + Vector3.New(0.5, 0.5, 0)
        --    SetUIPos(newGo,  vt.x, vt.y)
        --    UEGO.Destroy(newGo, 2)
        --end
        local curSelectMultiple = NetMulitpleDropData:GetDataByKey("curSelectMultiple")
        local effId = 171
        local chuiZiEffId = 172
        if curSelectMultiple == MultipleNum.THREE then
            effId = 171
            chuiZiEffId = 172
        elseif curSelectMultiple == MultipleNum.NINE then
            effId = 170
            chuiZiEffId = 173
        elseif curSelectMultiple == MultipleNum.TWENTY_SEVEN then
            effId = 169
            chuiZiEffId = 174
        end
        local function delayCreateWorkerEff()
            EffectConfig:CreateEffect(chuiZiEffId, 0, 0, 0, self.m_TransWidgetBottom,
                    function(data, tGo, go)
                        --ResMgr:LoadAssetWithCache(cutTressPath, AssetDefine.LoadType.Instant, CreateGo)
                        local function delay()
                            if oldData then
                                Log.Info("***** 做多倍采集升级动画   oldID = " .. mulitpleOldItemId .. "   newID = " .. self.m_Id)
                                local targetLevel = self.m_Config["level"]
                                local item_level = oldData.level
                                local level_diff = targetLevel - item_level
                                local idCombined = ItemConfig:GetItemIDCombined(mulitpleOldItemId)
                                if idCombined == self.m_Id then
                                    level_diff = 1
                                end
                                self.isMultipleBuffUp = false
                                self:DoMultipleUpAnimation(self.m_Id)
                                local effStr = LangMgr:GetLang(9008) .. "+" .. level_diff
                                local x, y, z = GetPosByGrid(self.m_GridX, self.m_GridY)
                                MapController:PlayEffectText(effStr, x, y, z, GlobalConfig.TIME_52COMBINE_DUR, GlobalConfig.TIME_52COMBINE_SHOW)
                                EffectConfig:CreateEffect(effId, 0, 0, 0, self.m_TransWidgetBottom)
                            else
                                Log.Error("***** 做多倍采集升级动画  Error oldID=" .. mulitpleOldItemId .. "   newID = " .. self.m_Id)
                            end
                        end
                        TimeMgr:CreateTimer(self, delay, 1, 1)
                    end)
        end
        TimeMgr:CreateTimer(self, delayCreateWorkerEff, 0.5, 1)
    else
        self.isMultipleBuffUp = false
        self:MergeBuildingChain()
    end
end

-- 多倍采集出不能合成的物体时显示数量
function M:SetMultipleCanNotCombineNum()
    local pos = self.m_Vt2Pos + Vector2.New(0.0, 0.5)
    if self.oldCombineNum and nil == self.multipleNumFlag then
        self.multipleNumFlag = StackNumFlag:Create(pos)
    end
    if self.multipleNumFlag then
        local num = self.oldCombineNum
        -- 设置重叠数量
        self.multipleNumFlag:SetNumber(num)
        if num > 0 then
            self.multipleNumFlag:Show(pos.x,pos.y)
        else
            self.multipleNumFlag:Hide()
        end
    end
end

function M:onLoadFinished()
    --prevent spine clicked when cloud transparent
    if self.m_IsSpine then
        SetActive(self.m_TransSpritePa, false)
        SetActive(self.m_TransSpinePa, true)
    else
        SetActive(self.m_TransSpritePa, true)
        SetActive(self.m_TransSpinePa, false)
    end

    self:updateOffsetAndScale()

    --fly position
    if self.m_NewFromWhere == ItemFromWhere.DropBox then
        local x = self.m_NewFromParam[1]
        local y = self.m_NewFromParam[2]
        local isEff = self.m_NewFromParam[3]
        local isArr = self.m_NewFromParam[4]
        local whereSub = self.m_NewFromParam[5]
        -- 是否开启多倍采矿
        local isOpenMultiple = self.m_NewFromParam[8]
        -- 多倍采矿升级前id
        local mulitpleOldItemId = self.m_NewFromParam[9]
        -- 采集出的物品是不可合成的 需要合并起来只生成一个物体只记数
        self.oldCombineNum = self.m_NewFromParam[10]

        local fun = function()
            if isEff then
                MapController:PlayEffectAtGrid(self.m_GridX, self.m_GridY, 7)
                MapController:PlayEffectAtGrid(self.m_GridX, self.m_GridY, 23)
				
				
				if self.m_isEnergyBuffUp then
					
					local function CreateGo(asset)
						local newGo, newTrans = CreateGOAndTrans(asset)
						local vt = self.m_Vt3Pos + Vector3.New(0.5, 0.5, 0)
						SetUIPos(newGo,  vt.x, vt.y)
						UEGO.Destroy(newGo, 2)
						
						local function delay()

							
							local oldData = ItemConfig:GetDataByID(self.m_Id)
							local newId = self.m_Id + 1
							local newData = ItemConfig:GetDataByID(newId)
							if newData.type_name == 2 and newData.subclass == oldData.subclass then
								Log.Info("***** EnergyBuffUp   oldID=" .. self.m_Id .. "   newID=" .. newId)
								MapController:DeleteAndNew(self, self.m_GridX, self.m_GridY, newId)
							else
								Log.Error("***** EnergyBuffUp  Error  oldID=" .. self.m_Id .. "   newID=" .. newId)
							end
							
						end
						
						TimeMgr:CreateTimer(self, delay, 2, 1)
						
						----第一次获得体力祝福时，移动到此物体处
						--if NetEnergyBuff:GetIsAleadyFirstUp() == false then
							--NetEnergyBuff:SetIsAleadyFirstUp(true)
							--MapController:MoveCameraToGrid(self.m_GridX, self.m_GridY)
						--end
						

					end
					
					local function delayCreateWorkerEff()
						EffectConfig:CreateEffect(133, 0, 0, 0, self.m_TransWidgetBottom,
							function( data, tGo, go )
								ResMgr:LoadAssetWithCache(cutTressPath, AssetDefine.LoadType.Instant, CreateGo)
							end)
					end
					
					TimeMgr:CreateTimer(self, delayCreateWorkerEff, 0.3, 1)
					
					
				end
                -- 开启多倍采矿
                if isOpenMultiple then
                    self.isMultipleBuffUp = true
                    if self.oldCombineNum then
                        self:SetMultipleCanNotCombineNum()
                    end
                    self:DoMultipleUpAnimation(mulitpleOldItemId)
                end
                
            end
            if isArr then
                --MapController:MarkArrowOnGround(self.m_StrGrid, self:getPositionVt3WithOffset())
            end
        end
        self:setEffJumpFrom(x, y, fun)
        if isEff then
           	EffectConfig:CreateEffect(6, 0, 0, 0, self.m_TransWidgetBottom)
        end
		
        if whereSub then
            if whereSub == ItemFromWhere.Aircraft then
                if self.m_Id == ItemID.MAGIC_HEART_0 then
                    if NetMapNoteData:GetNoteCount(MapController.m_MapId, NetMapNoteData.ID.SingleFlag, 1) == 0 then
                        MapController:MoveCameraToGrid(self.m_GridX, self.m_GridY)
                        NetMapNoteData:AddNoteCount(MapController.m_MapId, NetMapNoteData.ID.SingleFlag, 1, 1)
                    end
                end
            end
        end

    elseif self.m_NewFromWhere == ItemFromWhere.Balloon then
		self:setImgVisible(false)
		local function funEnd()
			self:setImgVisible(true)
		end
        local function delayBalloon()
            self:setEffJumpFrom(self.m_NewFromParam[1], self.m_NewFromParam[2],funEnd)
        end
        self:CreateScheduleFunND(delayBalloon, 0.3, 1)


    elseif self.m_NewFromWhere == ItemFromWhere.HeroOrder then
        if self.m_NewFromParam[3] then
            self:setEffJumpFrom(self.m_NewFromParam[1], self.m_NewFromParam[2])
        else
            local x, y, z = GetPosByGrid(self.m_NewFromParam[1], self.m_NewFromParam[2])
            self:setEffJumpFrom(x, y)
        end
		EffectConfig:CreateEffect(6, 0, 0, 0, self.m_TransWidgetBottom)

    elseif self.m_NewFromWhere == ItemFromWhere.Somewhere then
        local pTb = self.m_NewFromParam
        if pTb["type"] == 0 then
            local function onReached()
                if MapController:GetGroupByGrid(self.m_GridX, self.m_GridY) then
                    self:setEffSpriteColor(1, 1, 1, 0.29)
                end
            end
            self:setEffJumpFrom(pTb.x, pTb.y, onReached)

        end
    else
        self:updatePosition()
    end

    if self.m_LockState == 2 then
        self:setOrderLayer(nil, 0)
    else
        self:setOrderLayer()
    end
    self:onLoaded()
    self:CreateSpriteCollider()
    self:setTouchEnable(self.m_LockState == 0)
    if self.m_InactiveState == 1 then
        if self.isIceItem and self.m_IceState == 0 then
            self.m_IceState = 1
        end
    end
    self:SetIceInfo()
    self:UpdateGrayState()

    if self.m_NewFromWhere ~= ItemFromWhere.MapInit then
        self.m_IsDuringBirth = 1
        self:CreateScheduleFunND(self.onBirthEnd, 0.5, 1, self, self.m_NewFromWhere)
    end

    if self:IsCanUse() then
        self:onActive(self.m_NewFromWhere)
    end

    if self.m_NewFromWhere == ItemFromWhere.Combine then
        --from combined scale out
        if self.m_NewFromParam[6] ~= nil then
            local x,y,z=  GetPosByGrid(self.m_NewFromParam[6][1],self.m_NewFromParam[6][2])
            self:setEffJumpFrom(x, y)
            EffectConfig:CreateEffect(6, 0, 0, 0, self.m_TransWidgetBottom)
        else
            if self.m_NewFromParam[2] == 1 then
                NodeMgr:AddActionByTrans(self.m_NewFromParam[3], self.m_NodeTrans, 0, 0, 0, 1,1, 1)
            end	
        end
        self:MergeBuildingChain()
    elseif self.m_NewFromWhere == ItemFromWhere.FireFly then
        GameUtil.DOScale(self.m_NodeTrans, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.5, 1):SetEase(Ease.OutBack):SetDelay(0.6)

    elseif self.m_NewFromWhere == ItemFromWhere.Build then
        EffectConfig:CreateEffect(1, 0, 0, 0, self.m_TransWidgetBottom)
        self:MergeBuildingChain()
    end

    if self.m_NewFromWhere == ItemFromWhere.CloudBig
            or self.m_NewFromWhere == ItemFromWhere.CloudMagic
            or self.m_NewFromWhere == ItemFromWhere.CloudPayment
            or self.m_NewFromWhere == ItemFromWhere.TreeLast
            or self.m_NewFromWhere == ItemFromWhere.Build
            or self.m_NewFromWhere == ItemFromWhere.HeroWaste
            or self.m_NewFromWhere == ItemFromWhere.Producer 
			or self.m_NewFromWhere == ItemFromWhere.Guide then
        --from unlock scale out {3, 5}
        --from unlock scale out and gray and opacity 0.5 {4}

        local delay = 0.0
        if self.m_NewFromParam and self.m_NewFromParam["delay"] then
            delay = self.m_NewFromParam["delay"]
        end
		
		if self.m_NewFromWhere == ItemFromWhere.CloudMagic then 
			if MapController.m_MapId ~= MAP_ID_ZOO then 
				DoAction(4, 0.8, 0.4, 1, 1006, 1, nil, self.m_NodeTrans,{ 0.0, 0.0, 0.0, 1.0, 1.0, 1.0 })
			end
		elseif self.m_NewFromWhere == ItemFromWhere.CloudBig and MapController.m_MapId == MAP_ID_MAIN then
            DoAction(4, 0.6, 0.4+1, 1, 1006, 1, nil, self.m_NodeTrans,{ 0.0, 0.0, 0.0, 1.0, 1.0, 1.0 })
        else
			DoAction(4, 0.8, 0.4, 1, 1006, 1, nil, self.m_NodeTrans,{ 0.0, 0.0, 0.0, 1.0, 1.0, 1.0 })
		end	
		
    end

    self.m_NewFromWhere = 0
end

--- 合成建筑链
function M:MergeBuildingChain()
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, self.m_Id)
    if not config then return end
    local param = {}
    param.bestlord_score_type = config.bestlord_score_type
    param.level = config.level
    local gridX = MapController.m_GridXPut or self.m_GridX
    local gridY = MapController.m_GridYPut or self.m_GridY
    -- 设置飞积分的起点
    AthleticTalentManager:SetGridXY(gridX, gridY)
    AthleticTalentManager:SetFlyParamByGrid(1, gridX, gridY)
    AthleticTalentManager:OnTaskComplete(DAY_TEASK_TYPE.MERGE_BUILDING_CHAIN, 1, param)
end

function M:onBirthEnd(fromWhere)
    if self.m_IsDuringBirth == 1 then
        self.m_IsDuringBirth = 0
    end
end

function M:onActive(fromWhere)
    self.m_IsActive = true
    
    self:ResetFlagIcon()
    self:CreateScheduleFunND(self.MarkArrow, 0.5, 1, self, fromWhere)

    if GetNumberValue(self.m_Config["big_small"], 0) == 1 then
        if not self.m_IsSpine then
            self.m_SpItem.transform:SetLocalScale(1.0, 1.0, 1.0)
            DOScaleLoop(self.m_SpItem.transform, Vector3.New(1.2, 1.2, 1.2), 0.7, LoopType.Yoyo, Ease.InOutSine)
            self.m_IsAnimScaleYoyo = true
        end
    end

	if self.m_Type == ItemUseType.OrderGift or self.m_Type == ItemUseType.Material or self.m_Type == ItemUseType.SmallStorageBoxMaterial then
		GameUtil.SetSpriteRendererSMaterial(self.m_SpItem, "MySprite/sprite_highlight_loop")
	end
    if self.m_Type == ItemUseType.ToyOre and not IntelligentWorkerManager:IsRunning() then
        MapController:FindItem(self, 1)
    end
    if self.m_Type == ItemUseType.BuildingUp or self.m_Type == ItemUseType.AutoBuild then
		if self.m_SpineGo ~= nil then
			self:setEffSpineGray(true)
		end
		self:setEffSpriteColor(1, 1, 1, AlphaType.TreeUnBuild, true)
		self:setEffSpriteGrayHalf(true)
    else
        self:setEffSpriteColor(1, 1, 1, 1.0, true)
    end
    if self.m_Type == ItemUseType.Toy then
        self:SetToyTargetIcon()
    end    
    if self.m_Type == ItemUseType.ObjItemHalloweenItem then
        --
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,self.m_Id)
        if nil == config.id_combined then
            self:SetCompleteIcon(true)
        else
            self:SetCompleteIcon(false)
        end
    end

    self:setEffBottomColor()
    --if self.m_Type == ItemUseType.Hero then
    --    EventMgr:Dispatch(EventID.MAP_HERO_NEW, MapController.m_MapId, self.m_Id)
    --end
    if fromWhere ~= ItemFromWhere.MapInit and not self.m_IsSetOnGrid then
        MapController:SetItemByGridUnsafe(self, self.m_GridX, self.m_GridY)
        EventMgr:Dispatch(EventID.MAP_ITEM_NEW, MapController.m_MapId, self.m_Id, false, self)
        self.m_IsSetOnGrid = true
    end
    --EventMgr:Dispatch(EventID.COLLECTION_ITEM_UNLOCK, self.m_Id)
    self:setEffLockBee(nil)

    if self.m_DropExtinction then
        self.m_DropExtinction:ShowUI()
    else
        local isExt = false
        local count = NetMapNoteData:GetNoteCount(MapController.m_MapId, NetMapNoteData.ID.item_extinction, self.m_Id)
        if count <= 0 then
            isExt = MapController:IsExtinction(self.m_Id)
        else
            isExt = true
        end

        if isExt then
            self:onExtinction()
            return

        elseif self.m_DropExtra then
            self.m_DropExtra:ShowUI()
        end

        --某地表元素 由于等级 等原因, 导致被淘汰掉, 此时只显示淘汰后 掉落如:金币 的状态, 就不再让子类启动相应的方法了
        --如遇到其他问题, 可以解开此项, 子类使用 IsExtinction() 做判断 是否已经淘汰
        self:onEnable(fromWhere)

        if fromWhere ~= ItemFromWhere.MapInit then
            --获得物品就加积分
            LimitActivityController:CreateLimitScoreRun(self.m_Config.id, self.m_GridX, self.m_GridY)

            if self.m_Type ~= ItemUseType.Activity then
                local isCanAutoOut = true
                if self.m_NewFromWhere == ItemFromWhere.Aircraft then
                    isCanAutoOut = false
                elseif self.m_NewFromWhere == ItemFromWhere.Combine and self.m_NewFromParam[1] == 0 then
                    isCanAutoOut = false
                --elseif LimitActivityController:GetIsMaxLimit() then -20220119 beiquxiaole
                --    isCanAutoOut = false
                end
                if isCanAutoOut then
                    local res = MapController:DoGiftByCombine(self)
                    if res == 2 or res == 3 then
                        local onGift = nil
                        if res == 3 then
                            onGift = function(tDrop)
                                if tDrop then
                                    tDrop:ShowUI()
                                end
                            end
                        elseif res == 2 then
                            onGift = function(tDrop)
                                if tDrop then
                                    local isDone = tDrop:onClick()
                                    if not isDone then
                                        tDrop:ShowUI()
                                    end
                                end
                            end
                        end
                        self:CreateScheduleFunND(onGift, 1.4, 1, self.m_DropExtra)
                        self:CreateScheduleFunND(self.setEffPoke, 1.2, 1, self)

                        self:DeleteScheduleFunND(self.onBirthEnd)
                        self:CreateScheduleFunND(self.onBirthEnd, 1.4, 1, self, self.m_NewFromWhere)
                    end
                end
                if self.m_NewFromWhere == ItemFromWhere.TriggerGiftAddReward then
                   self:CheckPlayTriggerGiftAni() 
                end
            end
        end
    end

    --if fromWhere ~= ItemFromWhere.mapInit then
    self:LoadEffects()
    --end
end

function M:LoadEffects(callBack)
    local strEff = self.m_Config["effect_g"]
    if strEff then
        local strArr = string.split(strEff, ';')
        for k, v in pairs(strArr) do
            local arrInf = string.split(v, '|')
            local type = tonumber(arrInf[1])
            local id = tonumber(arrInf[2])
            local function onSet(data, tGo, go)
                local scaleStr = self.m_Config["effect_e"]
                if not string.empty(scaleStr) then
                    local vtScale = GetVector3ByStr(scaleStr)
                    if vtScale then
                        go.transform:SetLocalScale(vtScale.x, vtScale.y, vtScale.z)
                    end
                end
				if(callBack) then 
					callBack(tGo,go)
				end
            end

            if type == 0 then
                EffectConfig:CreateEffect(id, 0, 0, 0, self.m_TransWidgetBottom, onSet)
            elseif type == 1 then
                EffectConfig:CreateEffect(id, 0, 0, 0, self.m_TransWidgetTop, onSet)
            end
        end
    end
end

function M:setEffJumpFrom(x, y, fun, duration)
    if not duration then
        local dis = Vector2.Distance(Vector2.New(x, y), self.m_Vt3Pos)
        duration = math.max(dis / (15), 0.4)
    end
    self:setTransPos(x, y, self.m_Vt3Pos.z)
    DOLocalJump(self.m_NodeTrans, self.m_Vt3Pos, y + 2, 1, duration, function()
        self.m_IsEffJumping = false
	 	DoAction(4, 0.4, 0, 1, 1009, 1, nil, self.m_NodeTrans,{ 0.7, 0.7, 0.7, 1.0, 1.0, 1.0 })
        if fun then
            fun()
        end
    end)
    self.m_IsEffJumping = true
end

function M:isEqualsGrid(gridX, gridY)
    if self.m_GridX == gridX and self.m_GridY == gridY then
        return true
    end
    return false
end

function M:setOrderLayer(layerID, order, isSaveDefault)
    if not layerID then
        layerID = self.m_OrderLayerId
    end
    if not order then
        order = self.m_OrderIdx
    end
    if isSaveDefault then
        self.m_OrderLayerId = layerID
        self.m_OrderIdx = order
    end

    if self.m_IsSpine then
        SetRendererOrder(GetComponent(self.m_SpineGo, UE.Renderer), layerID, order)
		
		if layerID == SortingLayerInGame.WORLD_PICK then
			SetRendererOrder(self.m_SpTileBottom, layerID, -1)
		else
        	SetRendererOrder(self.m_SpTileBottom, SortingLayerInGame.WORLD_LINE, -1)
		end
    else
        SetRendererOrder(self.m_SpItem, layerID, order)
		if layerID == SortingLayerInGame.WORLD_PICK then
			SetRendererOrder(self.m_SpTileBottom, layerID, -1)
		else
			SetRendererOrder(self.m_SpTileBottom, SortingLayerInGame.WORLD_LINE, -1)
		end
    end
end

function M:CreateSpriteCollider()
    local trans = nil
    if self.m_IsSpine then
        trans = self.m_TransSpinePa
    else
        trans = self.m_SpItem.transform
    end
    self:CreateCollider(trans, self:Is4x4())
end

function M:setTouchEnable(isEnable)
    if self.m_IsTouchEnable ~= isEnable then
        local trans = nil
        if self.m_IsSpine then
            trans = self.m_TransSpinePa
        else
            trans = self.m_SpItem.transform
        end
        self:SetColliderEnable(trans, isEnable)
        self.m_IsTouchEnable = isEnable
    end
end

function M:IsActive()
    return self.m_IsActive
end

function M:IsCanUse()
    if self.m_InactiveState ~= 0 then
        return false
    end
    if self.m_LockState ~= 0 then
        return false
    end
    --it minds the blood label show up
    --if self.m_IsDuringBirth then
    --    return false
    --end
    return true
end

function M:IsInactiveWall()
    return self.m_Config["inactive_wall"] ~= nil or self.m_IceNum ~= 0
end

function M:IsInactiveWallCheck()
    return self:IsInactiveWall()
end

function M:IsCanCombine()
    if not self.m_IsCanCombined then
        return false
    end
	if self.m_isEnergyBuffUp then
		return false
	end
	if self.isMultipleBuffUp then
		return false
	end
	if MapController:IsBePolluteByGrid(self.m_GridX,self.m_GridY,self) then
		return false
	end
    return self:IsCanUse() and not self:IsBusy(0) and not self:IsExtinction() and not self:IsLock0()
end

function M:IsCanMove()
    if not self.m_IsCanMove then
        return false
    end
	if self.m_isEnergyBuffUp then
		return false
	end
    if self.isMultipleBuffUp then
        return false
    end
    if self.m_IceState == 1 and self.isIceItem and self.curIceNum and self.curIceNum < self.m_IceNum then
        return false
    end
	if MapController:IsBePolluteByGrid(self.m_GridX,self.m_GridY,self) then
		return false
	end
    return self:IsCanUse() and not self:IsBusy(1) and not self:IsExtinction() and not self.m_IsEffJumping and not self.m_IsForbidden and not self:IsLock0()
end

function M:IsHasGas()
	if not self.m_IsCanMove then
		return false
	end
	return true
end

function M:IsBusy(limitFrom)
    return false
end

function M:IsCanDelete()
    return self.m_IsCanDelete and not self:IsExtinction() and not self:IsLock0() and self:IsCanUse()
end

function M:IsExtinction()
    if self.m_DropExtinction then
        return true
    end
    return false
end

function M:IsLock0()
    if self.m_LockArr and table.count(self.m_LockArr) > 0 then
        return true
    end
    return false
end

function M:Is4x4()
    if self.m_Len4x4 == 0 then
        return false
    end
    return true
end

function M:IsRedWorker()
    if self.m_Type == ItemUseType.RedWorker then
        return true
    end
    return false
end

function M:IsBlueWorker()
    if self.m_Type == ItemUseType.BlueWorker then
        return true
    end
    return false
end

function M:IsNPCUB()
	if self.m_Type == ItemUseType.NPCUnBuild then
		return true
	end
	return false
end

function M:IsTorch()
    if self.m_Type == ItemUseType.Torch then
        return true
    end
    return false
end

function M:IsBoom()
    if self.m_Type == ItemUseType.Boom then
        return true
    end
    return false
end

function M:IsCastleUB()
    if self.m_Type == ItemUseType.CastleUnBuild then
        return true
    end
    return false
end

function M:IsBigTreeUB()
    if self.m_Type == ItemUseType.BigTreeBuild then
        return true
    end
    return false
end

function M:IsCastle()
    if self.m_Type == ItemUseType.Castle then
        return true
    end
    return false
end

function M:IsAnimal()
    return false
end

function M:IsAnimalFood()
    return false
end

function M:IsCanUseForRedWorker()
    return false
end

function M:IsCanUseForBlueWorker()
    return false
end

function M:GetGrid()
    return self.m_GridX, self.m_GridY
end

function M:Get4x4Grid()
    return self.m_Arr4x4, self.m_Len4x4
end

function M:setFromWhere(type, param)
    self.m_NewFromWhere = type
    self.m_NewFromParam = param
end

function M:setGrayTile(isHas)
    if isHas == self.m_IsHasGrayTile then
        return
    end
    self.m_IsHasGrayTile = isHas
    --if isHas then
    --    LoadSprite3D(self.m_SpTileGray, "Assets/ResPackage/Sprite/tile/tile_back_g.png")
    --end
    --SetActive(self.m_SpTileGray, isHas)

    --    local gird = self.m_GridX + self.m_GridY
    --    if gird % 2 == 0 then
    --        self.m_SpTileGray.color = Color.New(0.9, 0.9, 0.9, 1.0)
    --    else
    --        self.m_SpTileGray.color = Color.New(1.0, 1.0, 1.0, 1.0)
    --    end
end

function M:setEffDeleteLightUp(isLightUp, isHasBottom)
		if self.m_IsSpine then
			SetSpineLight(self.m_SpineGo, 1)
		else
			GameUtil.SetSpriteRendererSMaterial(self.m_SpItem, "MySprite/DelectHighlight_Anim")
		end
		if isHasBottom then
			GameUtil.SetSpriteRendererSMaterial(self.m_SpTileBottom, "MySprite/DelectHighlight_Anim")
		end
end
function M:setEffDeleteGray()
	if self.m_IsGray or self.m_IsGryHalf then 
		return 
	end
	
	if self.m_IsSpine then
		self:setEffSpineGray(true)
	else
		GameUtil.SetSpriteRendererSMaterial(self.m_SpItem, "MySprite/DelectGray")
	end
		GameUtil.SetSpriteRendererSMaterial(self.m_SpTileBottom, "MySprite/DelectGray")
end

function M:setEffNorm()
	--Warning this is all gray
	if self.m_IsSpine then
		if self.m_SpineGo ~= nil then
			SetSpineLight(self.m_SpineGo, 0)
		end
		self:setEffSpineGray(self.m_IsGray)
		self:setEffBottomGray(self.m_IsGray)
	else
		if self.m_IsGryHalf then
			self:setEffSpriteGrayHalf(true)
		else
			self:setEffSpriteGray(self.m_IsGray)
		end
		self:setEffBottomGray(self.m_IsGray)
	end
end

function M:setEffGray(isGray, isForce)
    --Warning this is all gray
    if self.m_IsGray == isGray and not isForce then
        return
    end
    self.m_IsGray = isGray

    if self.m_IsSpine then
        self:setEffSpineGray(isGray)
        self:setEffBottomGray(isGray)
    else
        if self.m_IsGryHalf then
            self:setEffSpriteGrayHalf(true)
        else
            self:setEffSpriteGray(isGray)
        end
        self:setEffBottomGray(isGray)
    end
	self:OnSetEffGray()
end

function M:setEffSpineGray(isGray)
    if isGray then
        SetSpineGray(self.m_SpineGo, 1, 1)
    else
        SetSpineGray(self.m_SpineGo, 0, 1)
    end
end

function M:setEffSpriteGray(isGray)
    if isGray then
        GameUtil.SetSpriteRendererSMaterial(self.m_SpItem, "MySprite/Gray")
        GameUtil.SetSpriteRendererSMaterial(self.m_iceSpItemTop, "MySprite/Gray")
    else
        GameUtil.SetSpriteRendererSMaterial(self.m_SpItem, "MySprite/Default")
        GameUtil.SetSpriteRendererSMaterial(self.m_iceSpItemTop, "MySprite/Default")
    end
end

function M:setEffSpriteGrayHalf(isHalf)
    if isHalf then
        GameUtil.SetSpriteRendererSMaterial(self.m_SpItem, "MySprite/Gray_half")
        self.m_IsGryHalf = true
    else
        GameUtil.SetSpriteRendererSMaterial(self.m_SpItem, "MySprite/Default")
    end
end

function M:setEffBottomGray(isGray)
    if self.m_IsHasBottomTile > 1 then
        if isGray then
            GameUtil.SetSpriteRendererSMaterial(self.m_SpTileBottom, "MySprite/Gray")
        else
            GameUtil.SetSpriteRendererSMaterial(self.m_SpTileBottom, "MySprite/Default")
        end
    end
end

function M:setEffSpriteColor(r, g, b, a, isSave)
    if r == nil then
        if self.m_IsSpine then
			self.m_SpriteColorSave.a = 1
            SetSpineColor(self.m_SpineGo, self.m_SpriteColorSave, 1)
        else
            self.m_SpItem.color = self.m_SpriteColorSave
        end
        return
    end
    a = a or 1
    local color = Color.New(r, g, b, a)
    if self.m_IsSpine then
		color.a = 1
        SetSpineColor(self.m_SpineGo, color, 1)
    else
        self.m_SpItem.color = color
    end
    if isSave then
        self.m_SpriteColorSave = color
    end
end

function M:setEffBottomColor(r, g, b, a)
    --this is use for a light tile
    a = a or 1
    if self.m_IsLightUpBack then
        a = 0.1
    end
    if r == nil then
        self.m_SpTileBottom.color = Color.New(1.0, 1.0, 1.0, a)
        self.m_LightUpBackRGBA = 0
        return
    end

    local rgb = (r + g + b + a)
    if self.m_LightUpBackRGBA ~= rgb then
        self.m_SpTileBottom.color = Color.New(r, g, b, a)
        self.m_LightUpBackRGBA = rgb
    end
end

function M:setEffBottomLightUp(isLightUp, isAlways)

    --like a dead tree
    if self.m_IsHasBottomTile ~= 1 then
        return
    end

    if self.m_IsAlwaysLightUp and not isAlways then
        return
    end
    if isAlways then
        self.m_IsAlwaysLightUp = isLightUp
    end

    if self.m_IsLightUpBack ~= isLightUp then
        if isLightUp then
            SetActive(self.m_SpTileBottom, true)
            GameUtil.DOFadeSpriteRendererTrans(self.m_SpTileBottom.transform, 0.1, 1.0, 0.5, 1):SetLoops(-1, LoopType.Yoyo)
        else
			if not self.isTouching then
            	SetActive(self.m_SpTileBottom, false)
            	GameUtil.DOKillSpriteRenderer(self.m_SpTileBottom)
			end
        end
        self.m_IsLightUpBack = isLightUp
    end
end

function M:setSameEffBottomLightUp(isLightUp)
	self.isTouching = isLightUp
	if isLightUp then
		SetActive(self.m_SpTileBottom, true)
		GameUtil.DOFadeSpriteRendererTrans(self.m_SpTileBottom.transform, 0.1, 1.0, 0.5, 1):SetLoops(-1, LoopType.Yoyo)
	else
		SetActive(self.m_SpTileBottom, false)
		GameUtil.DOKillSpriteRenderer(self.m_SpTileBottom)
	end
end

function M:setEffCantPut(isCant)
    if self.m_IsCantPut ~= isCant then
        if isCant then
            self:setEffSpriteColor(1.0, 0.0, 0.0, 0.8)
            if self.m_IsHasBottomTile ~= 0 and self.m_IsHasBottomTile ~= 2 then
                self:setEffBottomColor(1.0, 0.0, 0.0, 0.8)
            end
        else
            self:setEffSpriteColor()
            if self.m_IsHasBottomTile ~= 0 and self.m_IsHasBottomTile ~= 2 then
                self:setEffBottomColor()
            end
        end
        self.m_IsCantPut = isCant
    end
end

function M:setEffLightUp(isLightUp, isHasBottom)
    if self.m_IsLightUp == isLightUp then
        return
    end
    self.m_IsLightUp = isLightUp

    if isLightUp then
        if self.m_IsSpine then
			SetSpineLight(self.m_SpineGo, 1)
        else
            GameUtil.SetSpriteRendererSMaterial(self.m_SpItem, "MySprite/Highlight_Anim")
        end
        if isHasBottom then
            GameUtil.SetSpriteRendererSMaterial(self.m_SpTileBottom, "MySprite/Highlight_Anim")
        end
    else
        if self.m_IsSpine then
			SetSpineLight(self.m_SpineGo, 0)
        end
        self:setEffGray(self.m_IsGray, true)
    end
end

function M:setEffShakeBatch(isShake, itemTarget)

    local isChangeValue = 0

    if itemTarget ~= self.m_ItemShakeTarget then
        self.m_ItemShakeTarget = itemTarget
        isChangeValue = isChangeValue + 1
    end

    if self.m_IsShake ~= isShake then
        self.m_IsShake = isShake
        isChangeValue = isChangeValue + 1
    end

    if isChangeValue > 0 then
        if isShake then
            local trans = nil
            if self.m_IsSpine then
                trans = self.m_TransSpinePa
            else
                trans = self.m_TransSpritePa
            end
            if trans then
                local posFrom = self:getPositionVt3()
                local dir = Vector3.Normalize(posFrom - itemTarget:getPositionVt3())
                local des = -dir * 0.01 * 30

                local x, y, z = trans:GetLocalPosition()
                NodeMgr:AddActionByTrans(90000, trans, x, y, 0, x + des.x, y + des.y, 0)
            else
                Log.Warning("###### why this nil ?", self.m_Id, self.m_StrGrid)
            end

            self:setEffLightUp(true)
            self:setEffBottomLightUp(true)
        else
            self:setEffLightUp(false)
            self:setEffBottomLightUp(false)
        end

    end
end

function M:setEffMagnet(x, y, z,times)
	--TODO lerp性能高
    if self.m_IsMagnetGrid then
        DOKill(self.m_NodeTrans)
    end
    if x == nil then
        return
    end
    local function onDone()
        self.m_IsMagnetGrid = false
    end
	local time = times or 0.2
    DOLocalMove(self.m_NodeTrans, Vector3.New(x, y, z), time, onDone, Ease.OutSine)--:SetDelay(0.5)
    self.m_IsMagnetGrid = true
end

function M:setEffCombinedBatch()
    if self.m_IsMerging then
        return
    end
    self.m_IsMerging = true

    NodeMgr:AddActionByNode(90002, self)
end

function M:setLockState(state)
    if self.m_LockState == state then
        return
    end
    self.m_LockState = state
    if state == 0 then
        ServerPushManager:ChangeMapItem(self.m_Id,self.m_Type,true)
    end
end

function M:refreshLockState(fromUnlock)
    self:setTouchEnable(self.m_LockState == 0)
    self:UpdateGrayState()
    if self:IsCanUse() then
        local fromWhere = ItemFromWhere.UnlockByGridBlack
        if fromUnlock == 0 then
            fromWhere = ItemFromWhere.UnlockByCloudMagic
        end
        self:onActive(fromWhere)
    end
    self:setOrderLayer()--back to nomal
	self:OnRefreshState()
end

function M:setInactive(state)
    local isNew = false
    if state == 0 and self.m_InactiveState ~= state then
        EffectConfig:CreateEffect(61, 0, 0, 0, self.m_TransWidgetTop)
        isNew = true
    end
    self.m_InactiveState = state
    if isNew then
        ServerPushManager:ChangeMapItem(self.m_Id,self.m_Type,true)
    end
    return isNew
end

function M:GetLockState()
    return self.m_LockState, self.m_InactiveState
end

function M:UpdateGrayState()
    if self.m_LockState > 0 then
        if self.m_LockState == 2 then
            self:setEffSpriteColor(1, 1, 1, AlphaType.ObjLuck2)
            if self.m_IsHasBottomTile > 1 then
                self:setEffBottomColor(1, 1, 1, 0.0)
            end
        elseif self.m_LockState == 3 then
            self:setEffSpriteColor(1, 1, 1, AlphaType.ObjLuck3)
            if self.m_IsHasBottomTile > 1 then
                self:setEffBottomColor(1, 1, 1, 0.0)
            end
            self:setGrayTile(true)
        end

    else
        self:setEffSpriteColor(1, 1, 1, AlphaType.ObjLuck0Gray)
        if self.m_IsHasBottomTile == 1 then
            self:setGrayTile(self.m_InactiveState > 0)
        else
            self:setEffBottomColor(1, 1, 1, AlphaType.ObjLuck0Gray)
            self:setGrayTile(false)
        end
    end
    self:setEffGray(not self:IsCanUse())
end

function M:setPicState(state)
    if self.m_PicState ~= state then
        self.m_PicState = state
        self:loadImgByState()
    end
end

function M:setEffChangeToWasteId()
    local newId = self.m_Config["id_waste"]
    self.m_beforeWastedId = self.m_Id
	--Log.Info("变化前ID ===>" .. self.m_Id .. " ，变化后ID => " .. tostring(newId))
	local onHide = nil
	if newId then
		local newConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, tonumber(newId))
		
		if newConfig then
			--Log.Info("开始变化")
			if self.m_Type == ItemUseType.ObjItemHalloweenItem or self.m_Type == ItemUseType.ObjItemEnd2Less then
				--南瓜物品类型的点击不一样
				local subOnClicked = function()

				end
				self.onClicked = subOnClicked
			end
			self:InitObj(nil, nil, newConfig, nil, nil, nil)
			onHide = function()
				self:setFromWhere(ItemFromWhere.HeroWaste)
				self:onClear()
				self:loadImgBottomTile()
				self:loadImgByState(function()
						self:onLoadFinished()
					end)
			end
		end
	else
		onHide = function()
			self:destroyNode()
			MapController:SetItemByGridUnsafe(nil,self.m_GridX,self.m_GridY)
		end
	end    
	if onHide then
		DOScale(self.m_NodeTrans, Vector3.New(0.0, 0.0, 0.0), 0.4, onHide, Ease.OutBack)
	end
end

function M:setEffPoke()
    local trans = nil
    if self.m_IsSpine then
        trans = self.m_SpineGo.transform
    else
        trans = self.m_SpItem.transform
    end
    if not trans then
        return
    end
    if self.m_Type ~= ItemUseType.Toy or (self.m_Type == ItemUseType.Toy and self.toyTween == nil) then
        DOKill(trans)
        trans:SetLocalScale(1, 1, 1)
        trans:DOPunchScale(Vector3.New(0.1, -0.1, 0), 0.4, 8)
    end

    local transBottom = self.m_SpTileBottom.transform
    DOKill(transBottom)
    transBottom:SetLocalScale(1, 1, 1)
    transBottom:DOPunchScale(Vector3.New(0.05, -0.05, 0), 0.4, 8) -- DOPunchScale(Vector3.New(0.05, -0.1, 0), 0.4, 8)
end

function M:setEffDelete(isForce)
    if not self:IsCanDelete() and not isForce then
        return false
    end
    local function onHide()
        self:destroyNode()
    end
    if not IsNil(self.m_NodeTrans) then
        DOScale(self.m_NodeTrans, Vector3.New(0.0, 0.0, 0.0), 0.4, onHide, Ease.InBack)
    end
    self:onDelete()
    return true
end

function M:setEffWillDelete(isWillDelete)
    if isWillDelete then
        if self:IsCanDelete() then
            self:setEffLightUp(true)
        else
            self:setEffSpriteColor(1, 1, 1, 0.5)
        end
    else
        self:setEffLightUp(false)
        self:setEffSpriteColor()
    end
end

--lockType:
--nil = initByArr
--num = add type
function M:setEffLockBee(lockType, isLock)
    if lockType == nil then
        if self.m_LockArr then
            for k, v in pairs(self.m_LockArr) do
                self:CreateLockEff(k)
            end
        end
        return
    end
    local strLockType = tostring(lockType)
    if isLock then
        if not self.m_LockArr then
            self.m_LockArr = {}
        end
        if self.m_LockArr[strLockType] == nil then
            self.m_LockArr[strLockType] = true
            self:CreateLockEff(strLockType)
        end
    else
        if not self.m_LockArr then
            return
        end
        local inf = self.m_LockArr[strLockType]
        UEGO.Destroy(inf.go)
        inf = nil
        self.m_LockArr[strLockType] = nil

    end
end

function M:CreateLockEff(strLockType)
    if type(self.m_LockArr[strLockType]) ~= "boolean" then
        return
    end

    local function onGetEff(dataEff, goBig, goEff)
        local lang = 0
        if strLockType == "1" then
            lang = 211142
        end
        self.m_LockArr[strLockType] = { ["data"] = dataEff, ["go"] = goBig, ["langOrder"] = tonumber(strLockType), ["lang"] = lang}
    end
    if strLockType == "1" then
        EffectConfig:CreateEffect(130, 0, 0, 0, self.m_TransWidgetTop, onGetEff)
    end
end

function M:setEffForbidden(isForbidden, isColor)
    if isForbidden then
        if isColor then
            self:setEffSpriteColor(1, 1, 1, 0.8)
        end
    else
        if isColor then
            self:setEffSpriteColor()
        end
    end
    self.m_IsForbidden = isForbidden
end

--0 from cloud
--1 from black block
function M:setEffBeUseful(unlockFrom)
    local function onFinished1()
        self:refreshLockState(unlockFrom)
        self.m_IsDuringBirth = 0
    end
    local function onFinished0()
        self:UpdateGrayState()
        self:setEffImgScale(Vector3.New(1.0, 1.0, 1.0), 0.5, onFinished1, Ease.OutBack)
    end
    self:setEffImgScale(Vector3.New(0.0, 0.0, 0.0), 0.4, onFinished0, Ease.OutSine)
    self.m_IsDuringBirth = 1
end

function M:setEffImgScale(vtDes, duration, fun, ease)
    local trans = nil
    if self.m_IsSpine and not IsNil(self.m_SpineGo) then
        trans = self.m_SpineGo.transform
    elseif not IsNil(self.m_SpItem) then
        trans = self.m_SpItem.transform
    end
    if not trans then
        return
    end
    if duration == 0 then
        DOKill(trans)
        DOKill(self.m_SpTileBottom.transform)
        DOKill(self.m_iceSpItemTop.transform)
        trans:SetLocalScale(vtDes.x, vtDes.y, vtDes.z)
        self.m_SpTileBottom.transform:SetLocalScale(vtDes.x, vtDes.y, vtDes.z)
        self.m_iceSpItemTop.transform:SetLocalScale(vtDes.x, vtDes.y, vtDes.z)
        return
    end
    DOScale(trans, vtDes, duration, fun, ease)
    DOScale(self.m_SpTileBottom.transform, vtDes, duration, nil, ease)
    DOScale(self.m_iceSpItemTop.transform, vtDes, duration, nil, ease)
end

function M:changeGrid(gridX, gridY, animType, funAnimDone, actionFrom)
    local function onReached()
        if funAnimDone then
            funAnimDone()
        end
        if actionFrom > 0 then
            self:onGridChange(gridX, gridY, 1, actionFrom)
        end
    end

    self:setGrid(gridX, gridY, true)
    self:updateGridName()
    self:updatePosition(animType, nil, onReached)
    if actionFrom > 0 then
        self:onGridChange(gridX, gridY, 0, actionFrom)
    end

    if self.m_DropExtra then
        self.m_DropExtra:SetGrid(self.m_GridX, self.m_GridY)
        self.m_DropExtra:onEffBurstOut()
    end
    if self:IsAnimal() then
        self:SetHungryTipsGrid()
    end
    if self.multipleNumFlag then
        self:SetMultipleCanNotCombineNum()
    end
end

function M:moveToGrid(gridX, gridY, animType, funAnimDone)
    local vt3Pos = GetPosVt3ByGrid(gridX, gridY)
    self:moveToPos(vt3Pos, animType, funAnimDone)
end

function M:moveToPos(vt3Pos, animType, funAnimDone,isEffect)
    self:setPositionVt3(vt3Pos)
	if isEffect then
		EffectConfig:CreateEffect(6, 0, 0, 0, self.m_TransWidgetBottom)
	end
    self:updatePosition(animType, nil, funAnimDone , isEffect)
end

function M:onPick()
    if self.m_Id == 134 then
        --万能魔方
        AudioMgr:PlayBySort(65)
    else
        AudioMgr:PlayBySort(18)
    end
	
    self:setOrderLayer(SortingLayerInGame.WORLD_PICK)
    self:setEffBottomLightUp(true)
    self:setEffCantPut(false)
    self:ShowArrow(false)

    if self.m_Type == ItemUseType.Material or self.m_Type == ItemUseType.OrderGift or self.m_Type == ItemUseType.SmallStorageBoxMaterial then
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(5002))
    end
    if self.m_DropExtra then
        self.m_DropExtra:onEffDisappear()
    end
    if self.multipleNumFlag then
        self.multipleNumFlag:Hide()
    end
    self:ChangeImgBottomTileByState(2)
    self:onDrag(0)
end

function M:onPut(isGoBack)
    AudioMgr:PlayBySort(19)
    self:setOrderLayer()
    self:setEffBottomLightUp(false)
    self:setEffCantPut(false)

    if self.m_ArrowInf then		
        if self.m_ArrowInf[1] == 0 then
            self:ShowArrow(true)
        else
            self:DeleteScheduleFunND(self.ShowArrow)
            self:CreateScheduleFunND(self.ShowArrow, 2, 1, self, true)
        end
		
    end

    if isGoBack then
        if self.m_DropExtra then
            self.m_DropExtra:onEffBurstOut()
        end
    end
    if self.multipleNumFlag then
        local pos = self.m_Vt2Pos + Vector2.New(0.0, 0.5)
        self.multipleNumFlag:Show(pos.x,pos.y)
    end

    self:onDrag(1)
end

function M:onCombineEnd()
	self:destroyNode()
	--self:destroyNode(true, true)
end

function M:onClickPos(touchPos)
	
end

function M:openIceItemHelpTips()
    UI_SHOW(UIDefine.UI_IceUnLockHelp)
end

function M:onClickedItem()
	if tgSwitchIDBoxMgr and tgSwitchIDBoxMgr:GetShowItemID() then
		UIMgr:Show(UIDefine.UI_WidgetTip, "id = " .. self.m_Id)
	end
	
    Log.Info(self.m_Id)
    if self.m_IsDuringBirth > 0 then
        return 6
    end

    --临时修复
	if MapController.m_MapId == 120 and self.m_Id == 1098 then
		MapController:SafeDeleteItem(self,true)

		local thinkTable = {
			["fix_pos"] = self.m_GridX.."_"..self.m_GridY
		}
		SdkHelper:ThinkingTrackEvent("FIX_120_1098", thinkTable)
		return
	end

    local itemType = self.m_Type
    if itemType ~= -1 then
        if self.m_IsAnimScaleYoyo or itemType == ItemUseType.CastleUnBuild or itemType == ItemUseType.Castle then
            self:setEffPokeNode()
        else
			if itemType ~= ItemUseType.Dress and itemType ~= ItemUseType.SeasonIsland then
            	self:setEffPoke()
			end
        end
    end
	if itemType == ItemUseType.Normal then
		local curMapID = NetUpdatePlayerData.playerInfo.curMap
		if not curMapID ~= 7 and curMapID ~= MAP_ID_MAIN and curMapID ~= MAP_ID_SECOND then
			local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,self.m_Id)
			if nil == config.id_combined then
				UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(8114))
			end
		end
		
	end
    if self.m_ArrowInf then
        if self.m_ArrowInf[1] == 0 then
			self.m_ArrowClick = true
            self:RemoveArrow()
        end
    end

    if self:IsLock0() then
        local inf = nil
        local langOrder = 0
        for k, v in pairs(self.m_LockArr) do
            if type(v) == "table" then
                if v.langOrder > langOrder then
                    inf = v
                end
            end
        end

        if inf then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(inf.lang))
        end
        return 8
    end

    if self.m_InactiveState ~= 0 then
        MapController:ClickedInActiveGrid(self)
        self:onClickedInActive()
		self:FixGas()
        return 1
    end

    if self.m_DropExtinction then
        self.m_DropExtinction:onClick()
        return 4

    elseif self.m_DropExtra then
        self.m_DropExtra:onClick()
        return 5
    end

    local arrUbId = ItemConfig.m_MapTopItemInCastleUB[tostring(self.m_Id)]
    if arrUbId then
        for k, v in pairs(arrUbId) do
            local castleUBId = v
            local castleType = 1
            local obj2in1 = MapController:GetCastleUB(castleUBId)
            if not obj2in1 then
                local newCastleId = ItemConfig:GetItemIDCombined(castleUBId)
                obj2in1 = MapController:GetCastle(newCastleId)
                castleType = 0
            end
            if obj2in1 then
                if obj2in1:IsCanUse() and obj2in1:IsCanEat(self) then
                    self:RemoveArrow()
                    MapController:MoveCameraToItem(obj2in1)
                    MapController:JumpItemToCastle(self, obj2in1, castleType)
                    return 7
				else
					MapController:MoveCameraToItem(obj2in1)
					UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(7061))
                end
            else
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(7061))
            end
        end
    end

    AudioMgr:PlayBySort(self.m_Config["audio"])

    --如果物体有冰块状态要消冰块的值才能解锁物体
    local subRes
    if self.m_IceState == 1 and self.isIceItem then
        if self.curIceNum and self.curIceNum >= self.m_IceNum then
            subRes = self:onClicked()
        else
            subRes = self:openIceItemHelpTips()
        end
    else
        subRes = self:onClicked()
    end
    

    if itemType == ItemUseType.Resource then
        --add resource
		local function  onFun()
	        local id = self.m_Config["id_use"]
	        local num = self.m_Config["id_use2"]
	        if id == ItemID.COIN then
	            MapController:PlayEffectAtGrid(self.m_GridX, self.m_GridY, 23)
	            NetNotification:NotifyCoin(2, num)
	        end
	
	        local specialUIFlyPos = nil
	        if id == ItemID.LimitEnergy then
	            specialUIFlyPos = ItemID.ENERGY
	        end
			if id == ItemID.MAGIC then
				EventMgr:Dispatch(EventID.DAILY_TASK, DAY_TEASK_TYPE.COLLECT_MAGIC_TASK, num)
			end
            -- 设置飞积分的起点
            AthleticTalentManager:SetGridXY(self.m_GridX, self.m_GridY)
            AthleticTalentManager:SetFlyParamByGrid(1, self.m_GridX, self.m_GridY)
	        MapController:AddResourceAnim(self.m_GridX, self.m_GridY, id, num, nil, specialUIFlyPos,"UseTypeResource")
	        MapController:SetItemByGridUnsafe(nil, self.m_GridX, self.m_GridY)
	        self:setEffDelete(true)
		end
		local auto_c_level = ConfigMgr:GetDataByID(ConfigDefine.ID.global_setting,1500).value
		local player_level = NetUpdatePlayerData:GetLevel()
		if self:IsCanCombine() and tonumber(player_level) > tonumber(auto_c_level) then 
			ClearController:Show(ClearType.Collect, self, onFun)
		else
			onFun()
		end
        return 2

    elseif self.m_Type == ItemUseType.Material then
        --add RECIPE item
		--6500 升级船务会去掉
        if self.m_Id == 65000 and not NetInfoData:GetDataMess(NetInfoData.Define.MaterialInit) then
			if self.m_Config["id_use2"] then
				EventMgr:Dispatch(EventID.ADD_RECIPE_ITEM, self.m_Id, 1)
			end
            NetInfoData:SetDataMess(NetInfoData.Define.MaterialInit, true)

        ResMgr:LoadAssetAsync(
                "Prefab/Map/FlyItem1.prefab",
                AssetDefine.LoadType.Instant,
                function(pre)

                local pos = RecipesController:GetPort().m_NodeTrans.position
                local go, trans = CreateGOAndTrans(pre)
                trans.position = self.m_NodeTrans.position
                trans:DOMove(pos, 0.8):SetEase(Ease.InBack):OnComplete(
                        function()
                            UEGO.Destroy(go)
                        end
                )
                MapController:SetItemByGridUnsafe(nil, self.m_GridX, self.m_GridY)
                self:setVisible(false)
                self:CreateScheduleFunND(self.destroyNode, 0.4, 1, self)
            end)     
			return 
        end
		
		
		--不知道为啥， 新手引导两个精灵要这样处理？
		if self.m_Id == 30050 or self.m_Id == 30051 then
			local posScreen = MapController:GetUIPosByGrid(self.m_GridX, self.m_GridY)
			MapController:AddResourceBoomAnim(posScreen.x, posScreen.y, self.m_Id, 1)
			MapController:SetItemByGridUnsafe(nil, self.m_GridX, self.m_GridY)
			self:setVisible(false)
			self:CreateScheduleFunND(self.destroyNode, 0.4, 1, self)
			return
		end
		
		local list,itemlist = MapController:GetItemByType(self.m_Type,nil,nil,true)

		for k, v in pairs(itemlist) do
			if v.item.m_Config["id_use2"] then
				EventMgr:Dispatch(EventID.ADD_RECIPE_ITEM, v.item.m_Id, v.count)
			end
		end
		
		local centerPos = MapController:GetUIPosByGrid(self.m_GridX, self.m_GridY) + Vector3.New(0.1,0.1,0)
		
		if #list >= 2 then 
			table.sort(list,function (data1,data2)
					local posScreen = MapController:GetUIPosByGrid(data1.m_GridX, data1.m_GridY)
					local posScreen2 = MapController:GetUIPosByGrid(data2.m_GridX, data2.m_GridY)
					local dis1 = Vector3.Distance(centerPos,posScreen)
					local dis2 = Vector3.Distance(centerPos,posScreen2)
					 
					 if dis1 < dis2 then 
					 	return true
					 end
					 	return false
					 
			end)
		end
		
		local count = #list  -- 0.12  0.8
		local space = 0.12
		if count > 8 then 
			space = 1.0/count
		end
		
		local open_flower, active_flower = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.FlowerCompetition)
		local iActivityId_flower = active_flower.info.activeId		
        
        local open_luck, active_luck = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.LuckOre)
		local iActivityId_luck = active_luck.info.activeId
		
		if (self.m_Id == ItemID.Flower_Competition and (open_flower == false or NetFlowerCompetition:IsRoundEnd()))
		then
			UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(177))
		end
        if self.m_Id == ItemID.LuckOreItem and (open_luck == false) then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(7077))
            TimeMgr:CreateTimer(self,
                    function()
                        for i, item in pairs(list) do
                            if item.m_Id == ItemID.LuckOreItem then
                                item:setEffChangeToWasteId()
                            end
                        end
                    end, 1, 1)
            return
        end

        local isOpenDayilyTarget = DailyTargetManager:IsCanShowDailyTarget()
        local is_dailyTargetObj = DailyTargetManager:IsDailyTargetObjItem(self.m_Id)
        local todayObjItemId = DailyTargetManager:GetTodayObjItemId()
        if is_dailyTargetObj then
            if not isOpenDayilyTarget or todayObjItemId ~= self.m_Id then
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(7077))
                TimeMgr:CreateTimer(self,
                        function()
                            for i, item in pairs(list) do
                                local is_otherDailyTarget = DailyTargetManager:IsDailyTargetObjItem(item.m_Id)
                                if is_otherDailyTarget and todayObjItemId ~= item.m_Id then
                                    item:setEffChangeToWasteId()
                                end
                            end
                        end, 1, 1)
                return
            end
        end
        
		local flowerNum = 0
		local LuckNum = 0
        local addDailyTargetNum = 0
        local slgDungeonTickNum = 0
		for i, v in ipairs(list) do
			local posScreen = MapController:GetUIPosByGrid(v.m_GridX, v.m_GridY)
            local isDailyTargetObj = DailyTargetManager:IsDailyTargetObjItem(v.m_Id)
            if not isDailyTargetObj or todayObjItemId == v.m_Id then 
                MapController:SetItemByGridUnsafe(nil, v.m_GridX, v.m_GridY)
            end
            local flyAnimCount = 1
            local addValue = 1
            if v.oldCombineNum then
                addValue =  v.oldCombineNum
                flyAnimCount = v.oldCombineNum
            end
			if v.m_Id == ItemID.Flower_Competition then
				flowerNum = flowerNum + addValue
			end		
            if v.m_Id == ItemID.LuckOreItem then
                LuckNum = LuckNum + addValue
			end
            if isDailyTargetObj and todayObjItemId == v.m_Id then
                addDailyTargetNum = addDailyTargetNum + addValue
            end
            if v.m_Id == ItemID.SlgDungeonTick then
                slgDungeonTickNum = slgDungeonTickNum + addValue
            end
            v:CreateScheduleFunND(function ()
                if isDailyTargetObj then
                    if todayObjItemId ~= v.m_Id then
                        v:setEffChangeToWasteId()
                    else
                        v:setVisible(false)
                        v:destroyNode()
                    end
                else
                    v:setVisible(false)
                    v:destroyNode()
                end

                if v.m_Id == ItemID.Flower_Competition then
                    local faceView = UIMgr:GetUIItem(UIDefine.UI_MainFace)
                    --faceView:RollLimitIconFirst(iActivityId_flower)

                    if open_flower == false or NetFlowerCompetition:IsRoundEnd() then
                        return
                    end

                    if UIMgr:ViewIsShow(UIDefine.UI_MainFace) then
                        --local goLimit17 = faceView:GetLimits(iActivityId_flower)
                        --local goLimit17 = faceView:GetLimits(iActivityId_flower)
                        local goLimit17Pos = LimitActivityController:GetActivePos(iActivityId_flower)
                        local pos = UIMgr:GetUIPosByWorld(goLimit17Pos)
                        MapController:SetUIResourcePos(tonumber(iActivityId_flower), pos.x, pos.y)
                        MapController:AddResourceBoomAnim(posScreen.x, posScreen.y, v.m_Id, flyAnimCount, nil, nil, tonumber(iActivityId_flower), 8)
                    end
                elseif v.m_Id == ItemID.LuckOreItem then
                    local pos = LimitActivityController:GetActivePos(iActivityId_luck)
                    local endPosScreen = UIMgr:GetUIPosByWorld(pos)
                    MapController:SetUIResourcePos(tonumber(iActivityId_luck), endPosScreen.x, endPosScreen.y)
                    MapController:AddResourceBoomAnim(posScreen.x, posScreen.y, v.m_Id, flyAnimCount, nil, nil, tonumber(iActivityId_luck), 8)
                elseif isDailyTargetObj then
                    if not isOpenDayilyTarget or todayObjItemId ~= v.m_Id then
                        return
                    end
                    EventMgr:Dispatch(EventID.PLAY_DAILYTARGET_SCORE_TWEEN)
                    MapController:AddResourceBoomAnim(posScreen.x, posScreen.y, v.m_Id, flyAnimCount, nil, nil, v.m_Id, 8)
                elseif v.m_Id == ItemID.SlgDungeonTick then
                    local endPos = EquipmentManager:GetFlyEndPos(ItemID.SlgDungeonTick)
                    MapController:SetUIResourcePos(tonumber(ItemID.SlgDungeonTick), endPos.x, endPos.y)
                    MapController:AddResourceBoomAnim(posScreen.x, posScreen.y, v.m_Id, flyAnimCount, nil, nil, tonumber(ItemID.SlgDungeonTick), 8)
                else
                    MapController:AddResourceBoomAnim(posScreen.x, posScreen.y, v.m_Id, 1, nil, nil, nil, 8)
                end
			end
			,space * i, 1, v)
		end
		
		--鲜花比拼
		if flowerNum > 0 then
			--local open, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.FlowerCompetition)
			if open_flower and NetFlowerCompetition:IsRoundEnd() == false then
				NetFlowerCompetition:SetInAddFlowerAnim(true)
				--NetFlowerCompetition:AddFlowerCount(1, count)
				TimeMgr:CreateTimer(self,
					function()
						NetFlowerCompetition:AddFlowerCount(1, flowerNum)
						NetFlowerCompetition:SetInAddFlowerAnim(false)
					end,
					1, 1)
			end
		end
        --幸运神矿
        if LuckNum > 0 then
            if open_luck then
                TimeMgr:CreateTimer(self, function()
                            NetLuckOreData:AddLuckScore(LuckNum)
                        end, 1, 1)
            end
        end
        -- 每日目标
        local canAdd = DailyTargetManager:CheckIsCanAddScore()
        TimeMgr:CreateTimer(self, function()
            if addDailyTargetNum > 0 and canAdd then
                NetDailyTargetData:AddTargetScore(addDailyTargetNum)
            end
            if addDailyTargetNum > 0 then
                NetDailyTargetData:AddTargetRankScore(addDailyTargetNum)
            end
        end, 1, 1)
        
        -- slg关卡挑战券
        if slgDungeonTickNum > 0 then
            local dataList = {}
            for i = 1, slgDungeonTickNum do
                table.insert(dataList,ItemID.SlgDungeonTickServer)
            end
            ServerPushManager:AddClientItem(dataList)
        end
        
        return 3
	elseif self.m_Type == ItemUseType.NPCFood then
		self:CreateScheduleFunND(function()
				local id_combine = self.m_Config["id_combine"]
				if not id_combine then
					local isSuc = MapController:JumpIntoNPC(self)
				end
		end, 0.4, 1, self)
	elseif self.m_Type == ItemUseType.MixDiamond then
		ClearController:Show(ClearType.Cube, self, nil)
	elseif self.m_Type == ItemUseType.Toy then
		--dd
		local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,self.m_Id)
		local open, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Toy)
		
		if open then
			--if nil ~= config.id_combined then
			--	
			--else		
			--	--local curNum = NetActToyData.data.curNum
            --    -- 是否已满
            --    --local isOver = NetActToyData:CheckIsOver()
            --    --if isOver then
            --    --    UI_SHOW(UIDefine.UI_ActToy,NetActToyData.data.activityId)
            --    --else
            --    --    local curNum = NetActToyData:GetDataByKey("curScore")
            --    --    NetActToyData:SetDataByKey("curScore",curNum+1)
            --    --    NetActToyData:CheckLevelReward()
            --    --    MapController:DeleteItem(self, true)
            --    --
            --    --    local starPos = MapController:GetUIPosByGrid(self.m_GridX, self.m_GridY)
            --    --    --local faceView = UIMgr:GetUIItem(UIDefine.UI_MainFace)
            --    --    local pos = LimitActivityController:GetActivePos(NetActToyData.data.activityId)--faceView:GetLimits(NetActToyData.data.activityId)
            --    --    local posScreen = UIMgr:GetUIPosByWorld(pos)
            --    --    MapController:SetUIResourcePos(tonumber(NetActToyData.data.activityId), posScreen.x, posScreen.y)
            --    --    MapController:AddResourceBoomAnim(starPos.x, starPos.y,self.m_Id,1,nil,nil,tonumber(NetActToyData.data.activityId),8)
            --    --end
			--end
            local targetID = NetActToyData:GetCurToyTargetItemID()
            if targetID ~= 0 and targetID == self.m_Id then
                UI_SHOW(UIDefine.UI_ActToy,NetActToyData.data.activityId)
            end
		else
			self:setEffChangeToWasteId()
		end
    elseif self.m_Type == ItemUseType.SmallStorageBoxMaterial then
        local list,itemlist = MapController:GetItemByType(self.m_Type)

        for k, v in pairs(itemlist) do
            EventMgr:Dispatch(EventID.ADD_SMALL_STORAGE_BOX_ITEM, v.item.m_Id, v.count)
		end

        local centerPos = MapController:GetUIPosByGrid(self.m_GridX, self.m_GridY) + Vector3.New(0.1,0.1,0)

        if #list >= 2 then 
			table.sort(list,function (data1,data2)
					local posScreen = MapController:GetUIPosByGrid(data1.m_GridX, data1.m_GridY)
					local posScreen2 = MapController:GetUIPosByGrid(data2.m_GridX, data2.m_GridY)
					local dis1 = Vector3.Distance(centerPos,posScreen)
					local dis2 = Vector3.Distance(centerPos,posScreen2)
					 
					 if dis1 < dis2 then 
					 	return true
					 end
					 	return false
					 
			end)
		end

        local count = #list  -- 0.12  0.8
		local space = 0.12
		if count > 8 then 
			space = 1.0/count
		end

        for i, v in ipairs(list) do
			local posScreen = MapController:GetUIPosByGrid(v.m_GridX, v.m_GridY)
			MapController:SetItemByGridUnsafe(nil, v.m_GridX, v.m_GridY)
			v:CreateScheduleFunND(function ()
					v:setVisible(false)
					v:destroyNode()
					
                    if UIMgr:ViewIsShow(UIDefine.UI_CopyMainFace2) then
                        local faceView = UIMgr:GetUIItem(UIDefine.UI_CopyMainFace2)

                        local goIcon = faceView.smallStorageBox
                        local pos = UIMgr:GetUIPosByWorld(goIcon.go.transform.position)
                        
                        MapController:FlyUIAnim(posScreen.x, posScreen.y, v.m_Id, 1, pos.x, pos.y, nil,nil ,8)
                    end
			end
			,space * i, 1, v)
		end

    elseif self.m_Type == ItemUseType.ObjItemConditionItem then
        local conditionBoxHelper = MapController:GetComponent(MapComponentId.ConditionBoxHelperComponent)
        if conditionBoxHelper and conditionBoxHelper:IsWorking() then

        else
            local lookFor = self.m_Config["id_use4"]
            if lookFor then
                FindController.LookFindItem(22,lookFor)
            end
        end
	elseif self.m_Type == ItemUseType.ObjitemLianPipe then
		UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(53241069))
    elseif self.m_Type == ItemUseType.ObjItemSkinDecorate then
        local skinDecorateId = self.m_Config["id_use"]
        local skinDecorateCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.skin_decorate, skinDecorateId)
        if not skinDecorateCfg or not skinDecorateCfg.belong_skin then
            return
        end
        local skinCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.skin_collection,skinDecorateCfg.belong_skin)
        if not skinCfg then
            return
        end

        if not NetHandbook:IsHaveSkin(skinCfg.skin_type,skinCfg.id) then
            --未解锁皮肤
            UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(8601,LangMgr:GetLang(skinCfg.name)))
            return
        end

        local list = NetHandbook:GetSkinDecorate(skinCfg.id)
        local inList = false
        for index, value in ipairs(list) do
            if value == skinDecorateId then
                inList = true
                break
            end
        end
        
        --判断是否已经有了这个皮肤挂件
        if inList then
            --如果有了就删除
            UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(8603))
            MapController:SetItemByGridUnsafe(nil, self.m_GridX, self.m_GridY)
	        self:setEffDelete(true)
            return
        end

        --如果没有弹出confirm 界面
        UI_SHOW(UIDefine.UI_Reconfirm,LangMgr:GetLang(8121), LangMgr:GetLangFormat(8602,LangMgr:GetLang(skinCfg.name)),
			function ()
                UI_CLOSE(UIDefine.UI_Reconfirm)
                --领取皮肤挂件 
                NetHandbook:AddSkinDecorate(skinCfg.id,skinDecorateId)
                --切换成当前皮肤
                local mapItems,cnt = MapController:GetItemById(v2n(skinCfg.belong_obj))
                if cnt < 1 or not mapItems or not mapItems[1] then return end
                local mapItem = mapItems[1]
                if mapItem.m_Type ~= ItemUseType.Dress then return end

                

                if skinDecorateCfg.map_focus then
                    local cameraPos = string.split(skinDecorateCfg.map_focus,"|")
                    if cameraPos and cameraPos[1] and cameraPos[2] then
                        local gridX = v2n(cameraPos[1])
                        local gridY = v2n(cameraPos[2])
                        MapController:MoveCameraToGrid(gridX,gridY,function ()
                            
                        end,true)
                        MapController:JumpItemToPos(self,GetPosVt3ByGrid(gridX,gridY),function ()
                            mapItem:ChangeSkin(skinCfg.id)
                            MapController:PlayEffectAtGrid(gridX, gridY, 163)
                        end)
                    end
                end
                
			end,
			nil)     
    end
    return subRes
 end

function M:GetIsMagnetLightUp()
    return self.m_IsMagnetLightUp
end

function M:SetIsMagnetLightUp(is_light)
    if self.m_IsLightUpBack then
        self.m_IsMagnetLightUp = false
        return
    end
    self.m_IsMagnetLightUp = is_light
end

function M:GetEnergyOutPut()
	local id = self.m_Config["id_use"]
	local num = self.m_Config["id_use2"]
	return num ,id
end

function M:onExtinction(json)
    if json then
        self.m_DropExtinction = self:CreateDrop(nil, json, true)
        return
    end

    if self.m_DropExtinction then
        return
    end
    local newItem = self.m_Config["id_waste"]
    if newItem then
        local objDrop = self:CreateDrop(newItem, nil, true)
        self.m_DropExtinction = objDrop
        if self:IsCanUse() then
            objDrop:ShowUI()
        end
    else
        Log.Error("###### ObjItem:onExtinction -> id_waste nil")
    end

    self:onExtinct()
end

function M:onExtraDrop(list, json)
    local function onDropDone(drop)
        drop:DestroyUI()
        self.m_DropExtra = nil
    end
    local objDrop = self:CreateDrop(list, json, false, onDropDone)
    if objDrop and json and self:IsCanUse() then
        objDrop:ShowUI()
    end
    objDrop:SetFromWhere(ItemFromWhere.Gift2)
    objDrop:SetEffect(true)
    self.m_DropExtra = objDrop
end

function M:MarkArrow(fromWhere)
    --test   self.m_ArrowInf = {[1] = 1, [2] = 0}
    if not self.m_ArrowInf then
        return
    end
	local type = self.m_ArrowInf[4]
	if nil ~= type then
		local level = NetUpdatePlayerData:GetLevel()
		if level > type then
			return
		end
	end

    local type = self.m_ArrowInf[1]
    if type < 0 or type > 2 then
        return
    end

    local state = self.m_ArrowInf[2]
    if fromWhere == ItemFromWhere.MapInit and state == 1 then
        return
    end
	if state == 2 then
		if self.m_ArrowClick then
			return
		end
	end

    local times = self.m_ArrowInf[3]
    local count = NetMapNoteData:GetNoteCount(MapController.m_MapId, NetMapNoteData.ID.item_has, self.m_Id)
    if count > times and times ~= 0 then
        return
    end

    if type == 2 then
        MapController:MarkArrowOnGround(self.m_StrGrid, self:getPositionVt3WithOffset(), self:getNodeRota())
    else
		if self.m_RenderArrow then 
			return
		end
		--有引导箭头不加arrow_inf箭头
		local guide_arrow = MapController.m_MapMarkArrow
		if nil ~= guide_arrow[self.m_StrGrid] then
			return 
		end
        local function onLoaded(prefab)
            if prefab then
                local newGo, newTrans = CreateGOAndTrans(prefab)
                newTrans:SetParent(self.m_TransWidgetTop)
                newTrans:SetLocalScale(1, 1, 1)
                if self.m_Offset then
                    newTrans:SetLocalPosition(self.m_Offset.x, self.m_Offset.y, 0)
                else
                    newTrans:SetLocalPosition(0, 0, 0)
                end
				
				if self:getNodeRota() then
					newTrans.localEulerAngles = Vector3.New(0, 0, self:getNodeRota())
				end
				
                newTrans.name = "mark_top_item"
                local renderer = SearchChild(newTrans, "offset/m_sp_arrow", SpriteRenderer)
                DOLocalMoveYLoop(renderer.transform, 0.3, 0.5, LoopType.Yoyo, Ease.InOutSine)
                self.m_RenderArrow = renderer
            end
        end
		if not MapControllerVisit:IsVisit() then
			ResMgr:LoadAssetWithCache("Assets/ResPackage/Prefab/Map/guide_find_arrow_1.prefab", AssetDefine.LoadType.Instant, onLoaded)
		end
    end
end

function M:RemoveArrow()
    if self.m_RenderArrow then
        UEGO.Destroy(self.m_RenderArrow.gameObject)
        self.m_RenderArrow = nil
    end
end

function M:ShowArrow(isShow)
	if self.m_ArrowInf then
		if self.m_ArrowInf[4] then
			local level = NetUpdatePlayerData:GetLevel()
			if level > v2n(self.m_ArrowInf[4]) then
				if self.m_RenderArrow then
					self.m_RenderArrow:DOFade(0, 0.1)
					return
				end
			end
		end
	end
	
    if self.m_RenderArrow then
        self.m_RenderArrow:DOKill()
        if isShow then
            self.m_RenderArrow:DOFade(1, 0.1)
        else
            self.m_RenderArrow:DOFade(0, 0.1)
        end
    end
end

function M:GetPosJumpIn()
    return self.m_Vt3Pos
end

function M:setDropHide(isHide)
    local dropObj = self.m_DropExtinction or self.m_DropExtra
    if dropObj then
        if isHide then
            dropObj:onEffDisappear()
        else
            dropObj:onEffBurstOut()
        end
    end
end

function M:AddSign()
	--test   self.m_ArrowInf = {[1] = 1, [2] = 0}
	--if not self.m_ArrowInf then
		--return
	--end

	--local count = NetMapNoteData:GetNoteCount(nil, NetMapNoteData.ID.item_has, self.m_Id)
	--if count > times and times ~= 0 then
		--return
	--end

	if nil ~= self.m_Sign then
		return 
	end
	if self.has_sign then
		return 
	end
	self.has_sign = true
	local function onLoaded(prefab)
		if prefab then
			local newGo, newTrans = CreateGOAndTrans(prefab)
			newTrans:SetParent(self.m_TransWidgetTop)
			newTrans:SetLocalScale(0.5, 0.5, 1)
			if self.m_Offset then
				newTrans:SetLocalPosition(self.m_Offset.x, self.m_Offset.y, 0)
			else
				newTrans:SetLocalPosition(0, 0, 0)
			end

			--if self:getNodeRota() then
				--newTrans.localEulerAngles = Vector3.New(0, 0, self:getNodeRota())
			--end

			--newTrans.name = "mark_top_item"
			local renderer = SearchChild(newTrans, "sprite", SpriteRenderer)
			self.m_Sign = renderer
		end
	end
	ResMgr:LoadAssetWithCache("Assets/ResPackage/Prefab/Map/Sign.prefab", AssetDefine.LoadType.Instant, onLoaded)
end
-- 1.非拖曳物件的亮片 2.拖曳中物件的亮片 3.待合成物件的亮片 4.拖曳物件与待合成物件重叠位置的亮片
function M:ChangeImgBottomTileByState(state)
    local asset_name
    if self.m_IsHasBottomTile >= 2 then
        return
    end
    if self.cur_tile_state ~= state then
        self.cur_tile_state = state
        if state == 0 then
            asset_name = string.format("Assets/ResPackage/Sprite/tile/tile_back%d.png", self.m_IsHasBottomTile)
        elseif state == 1 then
            asset_name = asset_name_1
        elseif state == 2 then
            asset_name = asset_name_2
        elseif state == 3 then
            asset_name = asset_name_3
        elseif state == 4 then
            asset_name = asset_name_4
        end
    end

    if asset_name then
        local function loadBack(sp)
            if sp then
                SetSprite3D(self.m_SpTileBottom, sp)
            else
                Log.Error("###### item loadBack null ", asset_name)
            end
        end
        local name = asset_name
        ResMgr:LoadAssetWithCache(name, AssetDefine.LoadType.Sprite, loadBack)
    end
end

function M:RemoveSign()
	if self.m_Sign and self.has_sign then
		UEGO.Destroy(self.m_Sign.gameObject)
		self.m_Sign = nil
		self.has_sign = false
	end
end


function M:FixGas()
    if self.m_InactiveState ~= 1 then
        return 
    end
	if MapController.m_MapId == 1 then
		local group = MapController:GetGroupByGrid(self.m_GridX, self.m_GridY)
		if group == nil then
			local groupId = MapController.m_MapGroupGrid[tostring(GetGridID(self.m_GridX, self.m_GridY))]
			if groupId ~= nil then
				local tile = MapController:GetTileByGridUnsafe(self.m_GridX, self.m_GridY)
				if not tile then
					return
				end
				if tile:GetInactiveState() == 0 and MapController:IsInactiveBorderGas(self.m_GridX, self.m_GridY) then
					Log.Error("修复地图物件开云未触发地是否有气：mapId:".. MapController.m_MapId .." groupId:"..groupId.." 位置:" .. self.m_GridX.."_"..self.m_GridY.. " " ..table.dump(self) .. "  self.m_InactiveState:" .. self.m_InactiveState)
					self:setInactive(0)
					self:setEffBeUseful()
				end
			end
		end
	elseif self.m_Type == ItemUseType.ObjItemConditionBox then
        --特殊关卡 点修  临时 找到问题就删除
		local group = MapController:GetGroupByGrid(self.m_GridX, self.m_GridY)
		if group ~= nil then
            return
        end
        local groupId = MapController.m_MapGroupGrid[tostring(GetGridID(self.m_GridX, self.m_GridY))]
        if groupId ~= nil then
            return
        end

        --没有云区的物品 如果不是障碍物
        local tile = MapController:GetTileByGridUnsafe(self.m_GridX, self.m_GridY)
        if not tile then
            return
        end
        local lock,inactive = tile:GetLockState()
        if inactive > 0 and lock == 0 and MapController:IsInactiveBorderGas(self.m_GridX, self.m_GridY) then
            if self:setInactive(0) then
                self:setEffBeUseful(1)
            end
            if self:Is4x4() then
                local gridXOrg, gridYOrg = self:GetGrid()
                local tbGrid, count = self:Get4x4Grid()
                for i = 1, count do
                    local gridOffset = tbGrid[i]
                    local goX = gridOffset.x + gridXOrg
                    local goY = gridOffset.y + gridYOrg
                    local tile = MapController:GetTileByGridUnsafe(goX, goY)
                    if tile then
                        if tile:setInactive(0) then
                            tile:setEffBeUseful(1)
                        end
                    end
                end
            else
                if tile:setInactive(0) then
                    tile:setEffBeUseful(1)
                end
            end
        end
    end
		
end

--#region 关于flagIcon
function M:AddFlagIcon()

    if not self.m_Config then return end
    if not self.m_Config.flag_icon then return end

    local url = self.m_Config.flag_icon

    if self.m_flagIcon ~= nil then
        return
    end

    local function onLoaded(prefab)
        if prefab then
            local newGo, newTrans = CreateGOAndTrans(prefab)
            newTrans:SetParent(self.m_TransWidgetTop)
			newTrans:SetLocalScale(0.5, 0.5, 1)
            if self:Is4x4() then
				newTrans:SetLocalPosition(2, 0, 0)
			else
				newTrans:SetLocalPosition(0.55, 0, 0)
			end

            local renderer = SearchChild(newTrans, "sprite", SpriteRenderer)
			self.m_flagIcon = renderer
            
            ResMgr:LoadAssetWithCache(url, AssetDefine.LoadType.Sprite, function(img)
                SetSprite3D(self.m_flagIcon, img)
                self:ResetFlagIcon()
            end)
        end
    end
    ResMgr:LoadAssetWithCache("Assets/ResPackage/Prefab/Map/item_flag_icon.prefab", AssetDefine.LoadType.Instant, onLoaded)
end

function M:ResetFlagIcon()
    if not self.m_flagIcon then return end

    if self:IsCanUse() then
        GameUtil.SetSpriteRendererSMaterial(self.m_flagIcon, "MySprite/Default")
    else
        GameUtil.SetSpriteRendererSMaterial(self.m_flagIcon, "MySprite/Gray")
    end
end

function M:RemoveFlagIcon()
    if self.m_flagIcon then
        UEGO.Destroy(self.m_flagIcon.gameObject)
		self.m_flagIcon = nil
    end
end

function M:SetToyTargetIcon()
    if self.m_Type ~= ItemUseType.Toy then
        return
    end

    local isOpen , active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Toy) and IsHomeMap(NetUpdatePlayerData.playerInfo.curMap)
    if not isOpen then
        return
    end
    local targetID = NetActToyData:GetCurToyTargetItemID()
    if targetID ~= 0 and targetID == self.m_Id then
        local spr = "Sprite/ui_public/StatusIcon-complete2.png"
        if self.toyIconGo == nil then
            local function onLoaded(prefab)
                if prefab then
                    local newGo, newTrans = CreateGOAndTrans(prefab)
                    newTrans:SetParent(self.m_TransWidgetTop)
                    newTrans:SetLocalScale(1, 1, 1)
                    self.toyIconGo = newGo
                    SetLocalPositionTrans(newTrans,0,-0.1)
                    if spr then
                        local sprite = GetChild(self.toyIconGo,"sprite",SpriteRenderer)
                        LoadSprite3D(sprite,spr)
                    end
                end
            end
            ResMgr:LoadAssetWithCache("Assets/ResPackage/Prefab/Map/BoxToyItem.prefab", AssetDefine.LoadType.Instant, onLoaded)
        else
            SetActive(self.toyIconGo,true)
        end
        if not IsNil(self.m_SpItem) then
            self.m_SpItem.transform:SetLocalScale(1.0, 1.0, 1.0)
            if self.toyTween == nil then
                self.toyTween = DOScaleLoop(self.m_SpItem.transform, Vector3.New(1.2, 1.2, 1.2), 0.7, LoopType.Yoyo, Ease.InOutSine)
            end
        end
    elseif self.toyIconGo ~= nil then
        SetActive(self.toyIconGo,false)
        if self.toyTween and not IsNil(self.m_SpItem) then
            DOKill(self.m_SpItem.transform)
            self.toyTween = nil
            self.m_SpItem.transform:SetLocalScale(1.0, 1.0, 1.0)
        end
    end
    EventMgr:Dispatch(EventID.REFRESH_TOY_ROUND)
end

function M:SetCompleteIcon(isComplete)
    if isComplete then
        local spr = "Sprite/ui_public/StatusIcon-complete2.png"
        if self.CompleteIconGo == nil then
            local function onLoaded(prefab)
                if prefab then
                    local newGo, newTrans = CreateGOAndTrans(prefab)
                    newTrans:SetParent(self.m_TransWidgetTop)
                    newTrans:SetLocalScale(1, 1, 1)
                    self.CompleteIconGo = newGo
                    SetLocalPositionTrans(newTrans,0,-0.1,0)
                    if spr then
                        local sprite = GetChild(self.CompleteIconGo,"sprite",SpriteRenderer)
                        LoadSprite3D(sprite,spr)
                    end
                end
            end
            ResMgr:LoadAssetWithCache("Assets/ResPackage/Prefab/Map/BoxToyItem.prefab", AssetDefine.LoadType.Instant, onLoaded)
        else
            SetActive(self.CompleteIconGo,true)
        end
        --if not IsNil(self.m_SpItem) then
        --    self.m_SpItem.transform:SetLocalScale(1.0, 1.0, 1.0)
        --    if self.toyTween == nil then
        --        self.toyTween = DOScaleLoop(self.m_SpItem.transform, Vector3.New(1.2, 1.2, 1.2), 0.7, LoopType.Yoyo, Ease.InOutSine)
        --    end
        --end
    else
        if self.CompleteIconGo ~= nil then
            SetActive(self.CompleteIconGo,false)
            --if self.CompleteIconGo and not IsNil(self.m_SpItem) then
            --    DOKill(self.m_SpItem.transform)
            --    self.CompleteIconGo = nil
            --    self.m_SpItem.transform:SetLocalScale(1.0, 1.0, 1.0)
            --end
        end
    end
end

function M:CheckPlayTriggerGiftAni()
    self:setVisible(false)
    local function onStart()
        self:setVisible(true)
        local function movingAction()
            self.m_NodeTrans:DOPunchScale(Vector3.New(0.1, 0.5, 1), 0.4, 2,0.1)
        end
        self.m_NodeTrans:SetLocalScale(0, 0, 0)
        local tween = DOScale(self.m_NodeTrans, Vector3.New(1, 1, 1), 0.3, movingAction)
        tween:SetDelay(0.1)
    end

    TimeMgr:CreateTimer(self.m_NodeID, onStart, 1.2, 1)
    if nil == self.triggerGiftBubble then
        local pos = self.m_Vt2Pos 
        self.triggerGiftBubble = triggerGiftBubble:Create(pos,self.m_Config["img"])
    end
    if self.triggerGiftBubble then
        self.m_playTriggerGiftAni = false
        self.triggerGiftBubble:PlayBubbleAnim()
    end
end

function M:AddIceValue(value)
    if self.isIceItem then
        if not self.curSelectIceUnlock then
            self.curSelectIceUnlock = true
        end
        self.curIceNum = self.curIceNum + value
        if self.curIceNum >= self.m_IceNum then
            self.curSelectIceUnlock = false
            -- 满级后重置当前选中解锁冰块物体
            MapController:ResetIceItem()
        end
    end
end

function M:PlayAddIceTween(oldValue)
    if nil == oldValue then
        return
    end
    if self.isIceItem then
        local startValue = (self.m_IceNum - oldValue) / self.m_IceNum
        local endValue = (self.m_IceNum - self.curIceNum) / self.m_IceNum

        local oneStage = self.m_IceNum / 3
        local oldStage = math.floor(oldValue/oneStage)
        local newStage = math.floor(self.curIceNum/oneStage)
        
        local function onFinishCallBack()
            if newStage > oldStage then
                EffectConfig:CreateEffect(178, 0, 0, 0, self.m_TransWidgetBottom, function(data, tGo, go)
                    self:SetIceInfo()
                end)
            else
                self:SetIceInfo()
            end
            if self.curIceNum >= self.m_IceNum then
                local function onFinished1()
                    EffectConfig:CreateEffect(179, 0, 0, 0, self.m_TransWidgetBottom)
                end
                local function onFinished0()
                    self:setEffImgScale(Vector3.New(1.0, 1.0, 1.0), 0.5, onFinished1, Ease.OutBack)
                end
                self:setEffImgScale(Vector3.New(0.0, 0.0, 0.0), 0.4, onFinished0, Ease.OutSine)
            end
        end
        self.iceSlider.value = startValue
        self.iceProgressTxt.text = math.floor((self.m_IceNum - oldValue)) .. "/" .. self.m_IceNum
        SetActive(self.iceSliderGo,self.curSelectIceUnlock)
        AddDOTweenNumberDelay(startValue, endValue, 0.3, 0.1, function(value)
            self.iceSlider.value = value
        end, onFinishCallBack):SetEase(Ease.Linear):SetId("iceSliderTween")        
        AddDOTweenNumberDelay(oldValue, self.curIceNum, 0.3, 0.1, function(value)
            self.iceProgressTxt.text = math.floor((self.m_IceNum - value)) .. "/" .. self.m_IceNum
        end):SetEase(Ease.Linear):SetId("iceSliderTween")
    end
end

function M:SetIceInfo()
    if not self.isIceItem then
        return
    end
    if self.m_IceState == 0 then
        SetActive(self.m_iceSpItemTop,false)
        SetActive(self.m_iceSpItemBottom,false)
        return
    end
    if self.iceSliderGo == nil then
        self.iceSliderGo = AddIceSlider()
    end

    self.iceSlider = GetChild(self.iceSliderGo,"Background/IceSlider",UEUI.Slider)
    self.iceProgressTxt = GetChild(self.iceSliderGo,"Background/IceSlider/txt_IceProgress",UEUI.Text)
    
    if self.curIceNum and self.curIceNum < self.m_IceNum then
        local value = self.m_IceNum - self.curIceNum
        self.iceSlider.value = value / self.m_IceNum
        self.iceProgressTxt.text = value .. "/" .. self.m_IceNum
        SetActive(self.iceSliderGo,self.curSelectIceUnlock)
    else
        self.m_IceState = 0
        SetActive(self.iceSliderGo,false)
    end
    self:SetIceSliderPos()
    self:updatePreIceImg()
    self:updateBottomIceImg()
end

-- 前冰块
function M:updatePreIceImg()
    local function onLoaded(prefabOrSp)
        if prefabOrSp then
            SetSprite3D(self.m_iceSpItemTop, prefabOrSp)
        end
    end
    SetActive(self.m_iceSpItemTop,true)
    local asset_path = "Assets/ResPackage/Sprite/ui_object/ice_1.png"
    if self.curIceNum then
        local oneStage = self.m_IceNum / 3
        local curStage = math.floor(self.curIceNum/oneStage)
        if curStage == 1 then
            asset_path = "Assets/ResPackage/Sprite/ui_object/ice_3.png"
        elseif curStage == 2 then
            asset_path = "Assets/ResPackage/Sprite/ui_object/ice_6.png"
        elseif self.curIceNum >= self.m_IceNum then
            SetActive(self.m_iceSpItemTop,false)
            return
        end
    end
    ResMgr:LoadAssetWithCache(asset_path, AssetDefine.LoadType.Sprite, onLoaded)
end

--后冰块
function M:updateBottomIceImg()
    local function onLoaded(prefabOrSp)
        if prefabOrSp then
            SetSprite3D(self.m_iceSpItemBottom, prefabOrSp)
        end
    end
    local asset_path = "Assets/ResPackage/Sprite/ui_object/ice_1.png"
    if self.curIceNum then
        local oneStage = self.m_IceNum / 3
        local curStage = math.floor(self.curIceNum/oneStage)
        if curStage == 0 then
            SetActive(self.m_iceSpItemBottom,false)
            return
        end
        SetActive(self.m_iceSpItemBottom,true)
        if curStage == 1 then
            asset_path = "Assets/ResPackage/Sprite/ui_object/ice_4.png"
        elseif curStage == 2 then
            asset_path = "Assets/ResPackage/Sprite/ui_object/ice_7.png"
        elseif self.curIceNum >= self.m_IceNum then
            SetActive(self.m_iceSpItemBottom,false)
            return
        end
        ResMgr:LoadAssetWithCache(asset_path, AssetDefine.LoadType.Sprite, onLoaded)
    end
end

function M:SetIceSliderPos()
    if self.iceSliderGo == nil then
        return
    end
    self.iceSliderPos = Vector2.New(self.m_Vt2Pos.x,self.m_Vt2Pos.y + 2)
    SetUIPos(self.iceSliderGo,self.iceSliderPos.x,self.iceSliderPos.y)
    SetUIZPos(self.iceSliderGo, self.m_GridX, self.m_GridY)
end
--#endregion

------------------------------------------------------------------------------------------------------------------------------

function M:onSaveInf(inf)
    --override in child
end
function M:onInit(inf)
    --override in child
end
function M:onLoaded()
    --override in child
end
function M:onEnable()
    --override in child
end
function M:onDestroy()
    --override in child
end
function M:onDrag(state)
    --override in child
end
function M:onDelete()
    --override in child
end
function M:onGridChange(gridX, gridY, state, from)
    --override in child
end
function M:onClicked()
    --override in child
end
function M:onClickedInActive()
    --override in child
end
function M:onExtinct()
    --override in child
end
function M:onCommand(cmd)
    --override in child
end
function M:OnRefreshState()
	--override in child
end
function M:OnSetEffGray()
	
end

function M:onVisitClickedItem()
	Log.Info("点击了拜访别人的物品")
end
return M