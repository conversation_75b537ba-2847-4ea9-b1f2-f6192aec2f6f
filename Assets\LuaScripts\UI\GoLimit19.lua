local GoLimit19 = {}
local M = GoLimit19

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit19.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.count = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
    self.red = GetChild(self.go,"doLimit/bg/Red")

    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UE<PERSON>.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
end


function M:SetItem(param)
    self.id = param.id
    self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
    local active = self.activeInfo.form.activeMess
    self.totalType = param.totalType
    self.condition = param.condition
    --SetImageSprite(self.icon,active.icon,false)
    self:RefreshIcon()
end

function M:ChangState(id)

end

---通过 Refresh的方法调用
function M:ChangeValue()

end

---来自timer 的调用
function M:ChangeItem()
    local time = self.activeInfo:GetRemainingTime()
    self.count.text = TimeMgr:CheckHMSNotEmpty(time)
end

function M:SetRedShow(IsShow)
    SetActive(self.red,IsShow)
end

function M:RefreshIcon()
    local doubleOrePicConfig = ConfigMgr:GetData(ConfigDefine.ID.double_mine_pic)
    local listHotCastleItem = GlobalConfig:GetHotCastleItem()
    for k, v in pairs(doubleOrePicConfig) do
        if v.item == v2n(listHotCastleItem) then
            SetImageSprite(self.icon,v.icon,false)
        end
    end
end

function M:ClickItem(arg1)
    UI_SHOW(UIDefine.UI_DoubleOreTips,self.id)
end

function M:Close()
    UEGO.Destroy(self.go)
end

return M