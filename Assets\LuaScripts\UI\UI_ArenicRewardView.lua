local UI_ArenicRewardView = Class(BaseView)

local SlideRect = require("UI.Common.SlideRect");
local BaseSlideItem = require("UI.Common.BaseSlideItem");
local RewardItem = Class(BaseSlideItem);

local CurSelectIndex = -1

local TagType = {
    SeasonStage = 2;--赛季段位
    DailyTask = 1;--每日任务
    FirstStage = 3;--初次段位
}

local RewardType = {
    Unfinish = 1;--未达成
    CanGet = 2;--奖励可领取
    Received = 3;--奖励已领取
}

--奖励item模板
local RewardPrefab

function UI_ArenicRewardView:OnInit()
    ----------变量声明---------
    RewardPrefab = self.ui.m_goRewardItem
    ----------初始化界面相关逻辑----------
    SetActive(self.ui.m_goPrefab,false)
    SetActive(self.ui.m_goListItem,false)
end

function UI_ArenicRewardView:OnCreate(index)
    self.tagList = {}
    self.curRankId = JJcManager:GetCurRankId()

    ----------初始化业务逻辑----------
    self:InitSlideRect()
    self:InitTagList()
    self:OnSelectBtn(index or 1);
    self:CheckAllTagRedDot()
end

function UI_ArenicRewardView:OnRefresh(param,data)
    if param == TagType.DailyTask then
        --领取每日任务奖励更新
        self:DisplayMsg(CurSelectIndex)
        self:CheckDailyTaskRedDot()
    elseif param == TagType.FirstStage then
        --领取首次段位奖励更新
        self:DisplayMsg(CurSelectIndex)
        self:CheckFirstStageRedDot()
    elseif param == 4 then
        --显示奖励弹窗
        if data and next(data) ~= nil then
            local list = {}
            for k,v in ipairs(data) do
                table.insert(list,{code = v.code;amount = v.amount})
            end
            UI_SHOW(UIDefine.UI_EquipmentRecharge,list)
        end
    end
end

function UI_ArenicRewardView:onDestroy()
    CurSelectIndex = -1
end

function UI_ArenicRewardView:onUIEventClick(go,param)
    local name = go.name
    if name == "btnClose" then
        self:Close()
    elseif name == "m_btnReceiveAll" then
        local list = {}
        if CurSelectIndex == TagType.DailyTask then
            local config = ConfigMgr:GetData(ConfigDefine.ID.slg_rank_task)
            if config then
                for _,v in ipairs(config) do
                    if v.reward then
                        local status = JJcManager:CheckTaskRewardStatus(v.id)
                        if status == 2 then
                            table.insert(list,v.id)
                        end
                    end
                end
            end
        elseif  CurSelectIndex == TagType.FirstStage then
            local config = ConfigMgr:GetData(ConfigDefine.ID.slg_rank)
            if config then
                for _,v in ipairs(config) do
                    if v.reward_one then
                        local status = JJcManager:CheckFirstRewardStatus(v.id)
                        if status == 2 then
                            table.insert(list,v.id)
                        end
                    end
                end
            end
        end

        if next(list) == nil then
            --"当前没有可领取奖励"
            UI_SHOW(UIDefine.UI_WidgetTip,LangMgr:GetLang(70000566))
        else
            if CurSelectIndex == TagType.DailyTask then
                JJcManager:OnReqArenaTaskReward(list)
            elseif  CurSelectIndex == TagType.FirstStage then
                JJcManager:OnReqArenaRankFirstReward(list)
            end
        end
    end
end

--------------------------标签逻辑--------------------------
--展示页签列表
function UI_ArenicRewardView:InitTagList()
    local list = {
        [1] = {lanId = 70000556};--每日任务
        [2] = {lanId = 70000557};--赛季段位
        [3] = {lanId = 70000558};--初次段位
    }
    
    local parent = self.ui.m_goTagRoot.transform
    for k,v in ipairs(list) do
        local obj = UEGO.Instantiate(self.ui.m_goTag,parent)
        local default = GetChild(obj,"btn/default")
        local mask = GetChild(obj,"btn/mask")
        local txt1 = GetChild(obj,"btn/default/txt1",UEUI.Text)
        local txt2 = GetChild(obj,"btn/mask/txt2",UEUI.Text)
        local btn = GetChild(obj,"btn",UEUI.Button)
        
        txt1.text = LangMgr:GetLang(v.lanId)
        txt2.text = LangMgr:GetLang(v.lanId)
        
        RemoveUIComponentEventCallback(btn, UEUI.Button);
        AddUIComponentEventCallback(btn, UEUI.Button, function(arg1, arg2)
            self:OnSelectBtn(k);
        end)
        
        local dot = GetChild(obj,"btn/dot/BeatRedDot",UE.Transform)
        local redPoint = dot --self:CreateRetDot(dot)
        table.insert(self.tagList,{index = k;default = default;mask = mask;redPoint = redPoint})
    end
end

--选中按钮逻辑
function UI_ArenicRewardView:OnSelectBtn(index)
    if CurSelectIndex == index then
        return
    end
    CurSelectIndex = index
    self:DisplayMsg(index)
end

function UI_ArenicRewardView:DisplayMsg(index)
    for _,v in ipairs(self.tagList)do
        local isSelect = index == v.index
        SetActive(v.default,not isSelect)
        SetActive(v.mask,isSelect)
    end

    self:ShowItemList()
    self:UpdateTipStr()
    SetActive(self.ui.m_btnReceiveAll,CurSelectIndex ~= TagType.SeasonStage)
end

--更新底部提示文本
function UI_ArenicRewardView:UpdateTipStr()
    local tipStr = ""
    if CurSelectIndex == TagType.DailyTask then
        --"挑战次数"
        tipStr = LangMgr:GetLang(70000559).."<size=34> "..JJcManager:GetCurChallengeCount().."</size>"
    else
        local curRankId = JJcManager:GetCurRankId()
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_rank,curRankId)
        if config then
            --"当前段位"
            tipStr = LangMgr:GetLang(70000565).."<size=34> "..LangMgr:GetLang(config.langid).."</size>"
        end
    end
    self.ui.m_txtTip.text = tipStr
end
--------------------------列表展示逻辑--------------------------
--初始化循环列表组件
function UI_ArenicRewardView:InitSlideRect()
    self.rankItemList = {};
    local item;
    local itemTrans = self.ui.m_goListItem.transform;
    for i = 1,10 do
        item = RewardItem.new();
        item:Init(UEGO.Instantiate(itemTrans));
        table.insert(self.rankItemList, item);
    end

    self.slideRect = SlideRect.new();
    self.slideRect:Init(GetComponent(self.ui.m_goScroll, UEUI.ScrollRect), 2);
    self.slideRect:SetItems(self.rankItemList, 3, Vector2.New(10, 0));
end

--展示列表数据
function UI_ArenicRewardView:ShowItemList()
    self.slideRect:SetData({})
    local list = {}
    local index = -1
    if CurSelectIndex == TagType.DailyTask then
        local config = ConfigMgr:GetData(ConfigDefine.ID.slg_rank_task)
        if config then
            list = config
            local isRed,target = JJcManager:CheckDailyTaskRedDot()
            if isRed then
                index = target
            end
        end
    elseif  CurSelectIndex == TagType.FirstStage then
        local config = ConfigMgr:GetData(ConfigDefine.ID.slg_rank)
        if config then
            list = config
            local isRed,target = JJcManager:CheckFirstStageRedDot()
            if isRed then
                index = target
            end
        end
    elseif  CurSelectIndex == TagType.SeasonStage then
        local config = ConfigMgr:GetData(ConfigDefine.ID.slg_rank)
        if config then
            list = config
        end
    end
    self.slideRect:SetData(list)
    if index ~= -1 then
        self.slideRect:MoveToIndex(index)
    end
end

----------------------------------- RankItem -----------------------------------
function RewardItem:OnInit(transform)
    self.transform = transform
    
    self.btn = GetChild(transform, "btn", UEUI.Button)
    self.unfinish = GetChild(transform, "unfinish")
    self.received = GetChild(transform, "received")
    self.name = GetChild(transform, "name", UEUI.Text)
    self.icon = GetChild(transform, "icon", UEUI.Image)
    self.taskName = GetChild(transform, "taskName", UEUI.Text)
    self.tip = GetChild(transform, "tip", UEUI.Text)
    self.numIcon = GetChild(transform, "icon/numIcon", UEUI.Image)
    
    self.content = GetChild(transform,"scroll/Viewport/Content",UE.Transform)
    self.ScrollRect = GetChild(transform,"scroll",UEUI.ScrollRect)
    
    self.simple = GetChild(transform,"simple",UE.Transform)
    self.rewardPrefabList = {}
    self.simplePrefabList = {}
end

function RewardItem:UpdateData(data, index)
    SetActive(self.name,CurSelectIndex ~= TagType.DailyTask)
    SetActive(self.icon,CurSelectIndex ~= TagType.DailyTask)
    SetActive(self.taskName,CurSelectIndex == TagType.DailyTask)
	local rectTrans = GetComponent(self.ScrollRect,UE.RectTransform)
	local rectTransSimple = GetComponent(self.simple,UE.RectTransform)
	if CurSelectIndex == TagType.DailyTask then
		self.ScrollRect.transform.localPosition = Vector3(-412.9,-17, 0)
		self.simple.transform.localPosition = Vector3(-412.9,-17, 0)
		rectTrans.sizeDelta = Vector2.New(580, 118.5)
		rectTransSimple.sizeDelta = Vector2.New(580, 118.5)
	else
		self.ScrollRect.transform.localPosition = Vector3(-287.8,-17, 0)
		self.simple.transform.localPosition = Vector3(-287.8,-17, 0)
		rectTrans.sizeDelta = Vector2.New(470, 118.5)
		rectTransSimple.sizeDelta = Vector2.New(470, 118.5)
	end
    local rewardStr = ""
    --1:未达成  2:可领取 3：已领取
    local status = 1
    if CurSelectIndex == TagType.DailyTask then
        self.taskName.text = LangMgr:GetLangFormat(data.describe,data.num)
        rewardStr = data.reward
        status = JJcManager:CheckTaskRewardStatus(data.id)
    elseif  CurSelectIndex == TagType.FirstStage then
        self.name.text = LangMgr:GetLang(data.langid)
        rewardStr = data.reward_one
        JJcManager:SetRankIcon(data.id,self.icon,self.numIcon)
        status = JJcManager:CheckFirstRewardStatus(data.id)
    elseif  CurSelectIndex == TagType.SeasonStage then
        self.name.text = LangMgr:GetLang(data.langid)
        rewardStr = data.reward
        JJcManager:SetRankIcon(data.id,self.icon,self.numIcon)
        status = JJcManager:CheckSeasonRewardStatus(data.id)
    end
    
    self:SetStatusView(status)
    RemoveUIComponentEventCallback(self.btn,UEUI.Button)
    AddUIComponentEventCallback(self.btn,UEUI.Button,function()
        if CurSelectIndex == TagType.DailyTask then
            JJcManager:OnReqArenaTaskReward({data.id})
        elseif  CurSelectIndex == TagType.FirstStage then 
            JJcManager:OnReqArenaRankFirstReward({data.id})
        end
        
        RedPointMgr:Dirty(RedID.ArenaEntry)
    end)
    self:DisplayRewardList(rewardStr,status)
end

function RewardItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec;
end

function RewardItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition;
end

function RewardItem:onDestroy()
    UEGO.Destroy(self.transform.gameObject)
end

--控制领取状态显隐 1:可领取  2：未达成  3：已领取
function RewardItem:SetStatusView(status)
    SetActive(self.btn,status == RewardType.CanGet)
    SetActive(self.unfinish,status == RewardType.Unfinish)
    SetActive(self.received,status == RewardType.Received)
end

--展示奖励列表
function RewardItem:DisplayRewardList(rewardStr,status)
    local data = rewardStr and  Split1(rewardStr,";") or {}
    local count = table.count(data)
    local isOverflow =  count >= 4
    
    SetActive(self.ScrollRect,isOverflow)
    SetActive(self.simple,not isOverflow)
    
    if isOverflow then
        for _,v in ipairs(self.rewardPrefabList) do
            SetActive(v,false)
        end
        local  listCount = table.count(self.rewardPrefabList)
        
        if count <= listCount then
            for i = 1,count do
                local obj = self.rewardPrefabList[i]
                SetActive(obj,true)
                self:SetRewardInfo(obj,data[i],status)
            end
        else
            local offset = count - listCount
            self:AddPrefabCount(offset)
            listCount = table.count(self.rewardPrefabList)
            for i = 1,listCount do
                local obj = self.rewardPrefabList[i]
                SetActive(obj,true)
                self:SetRewardInfo(obj,data[i],status)
            end
        end
    else
        for _,v in ipairs(self.simplePrefabList) do
            SetActive(v,false)
        end
        local  listCount = table.count(self.simplePrefabList)
        
        if count <= listCount then
            for i = 1,count do
                local obj = self.simplePrefabList[i]
                SetActive(obj,true)
                self:SetRewardInfo(obj,data[i],status)
            end
        else
            local offset = count - listCount
            for i = 1, offset do
                local obj = UEGO.Instantiate(RewardPrefab,self.simple)
                table.insert(self.simplePrefabList,obj)
            end
            
            listCount = table.count(self.simplePrefabList)
            for i = 1,listCount do
                local obj = self.simplePrefabList[i]
                SetActive(obj,true)
                self:SetRewardInfo(obj,data[i],status)
            end
        end
    end
    
    self.ScrollRect.normalizedPosition = Vector2.New(0,1)

    --"暂无奖励"
    self.tip.text = LangMgr:GetLang(53241129)
    local isEmpty = count == 0
    SetActive(self.tip,isEmpty)
    if isEmpty then
        SetActive(self.btn,false)
        SetActive(self.unfinish,false)
        SetActive(self.received,false)
        
    end
end

function RewardItem:SetRewardInfo(obj,info,status)
    local temp = Split1(info,"|")
    
    local itemId = v2n(temp[1])
    local count = v2n(temp[2])
    local quality = GetChild(obj,"quality",UEUI.Image)
    local icon = GetChild(obj,"icon",UEUI.Image)
    local num = GetChild(obj,"num",UEUI.Text)
    local isGet = GetChild(obj,"isGet")
    local btn = GetComponent(obj,UEUI.Button)
    
    SetActive(isGet,CurSelectIndex ~= TagType.SeasonStage and status == RewardType.Received)
    
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,itemId)
    if config then
        SetUIImage(icon,ItemConfig:GetIcon(itemId),false)
        SetUIImage(quality,BAGITEM_QUALITY_PATH[config.slg_quality],false)
        num.text = NumToGameString(count)
    end
    RemoveUIComponentEventCallback(btn,UEUI.Button)
    AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
        UI_SHOW(UIDefine.UI_ItemTips,itemId)
    end)
end

--新增奖励item预制体
function RewardItem:AddPrefabCount(count)
    for i = 1, count do
        local obj = UEGO.Instantiate(RewardPrefab,self.content)
        table.insert(self.rewardPrefabList,obj)
    end
end

------------------------------红点逻辑判断--------------------------
function UI_ArenicRewardView:CheckAllTagRedDot()
    self:CheckDailyTaskRedDot()
    self:CheckFirstStageRedDot()
end

--判断每日任务红点逻辑
function UI_ArenicRewardView:CheckDailyTaskRedDot()
    local active = JJcManager:CheckDailyTaskRedDot()
    SetActive(self:GetRedDot(TagType.DailyTask),active)
end

--判断首次段位红点逻辑
function UI_ArenicRewardView:CheckFirstStageRedDot()
    local active = JJcManager:CheckFirstStageRedDot()
    SetActive(self:GetRedDot(TagType.FirstStage),active)
end

function UI_ArenicRewardView:GetRedDot(type)
    for _,v in ipairs(self.tagList) do
        if v.index == type then
            return v.redPoint
        end
    end
end

return UI_ArenicRewardView