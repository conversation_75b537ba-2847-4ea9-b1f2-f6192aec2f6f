local UI_DailyTask = Class(BaseView)
--里面的小容器的 承载类
local ItemBase = require("UI.Common.BaseSlideItem")
local dailyTaskItem = Class(ItemBase)
local mainTaskItem = Class(ItemBase);

local colorNormal = Color.New(1,1,1,1)
local colorFade = Color.New(1,1,1,0)
local grayMat = nil

local BubbleItem = require("UI.BubbleItem")
local Daily_MaxID = 100
---------------------------------------------主界面--START ---------------------------
function UI_DailyTask:OnInit()
    self.isCheckClose = false;
end

function UI_DailyTask:OnCreate(param)
    UI_UPDATE(UIDefine.UI_MainFace,12,{isShow = true,list={PlayerDefine.Coin,PlayerDefine.Diamond}})

    self:InitPanel()
end

function UI_DailyTask:InitPanel()
	self:FlushDailyTask()
	
    grayMat = UE.Material(UE.Shader.Find("UIShader/UIShader_Gray"))
    grayMat.color = Color.New(1,1,1,0.9)

    local transform = self.uiGameObject.transform
	--头部按钮的高亮部分
	self.btn_DailyObj = transform:Find("state/bg/Buttons/btn_daily").gameObject
	self.dailyLightObj = transform:Find("state/bg/Buttons/btn_daily/btn_daily_light").gameObject
	self.dailyRedObj = transform:Find("state/bg/Buttons/btn_daily/dailyRedObj").gameObject

	self.btn_WeekObj = transform:Find("state/bg/Buttons/btn_week").gameObject
	self.weekLightObj = transform:Find("state/bg/Buttons/btn_week/btn_week_light").gameObject
	self.weekRedObj = transform:Find("state/bg/Buttons/btn_week/weekRedObj").gameObject
	
	self.btn_RookieObj = transform:Find("state/bg/Buttons/btn_rookie").gameObject
	self.rookieLightObj = transform:Find("state/bg/Buttons/btn_rookie/btn_rookie_light").gameObject
	self.rookieRedObj = transform:Find("state/bg/Buttons/btn_rookie/rookieRedObj").gameObject

	self.btn_mainObj = transform:Find("state/bg/Buttons/btn_main").gameObject
	self.mainLightObj = transform:Find("state/bg/Buttons/btn_main/btn_main_light").gameObject
	self.mainRedObj = transform:Find("state/bg/Buttons/btn_main/mainRedObj").gameObject
	
	--Root
	self.dailyRoot = transform:Find("state/daily_panel").gameObject
	self.weekRoot = transform:Find("state/week_panel").gameObject
	self.rookieRoot = transform:Find("state/rookie_panel").gameObject
	self.mainRoot = transform:Find("state/main_panel").gameObject
	
	--week
	self.weekTimeText = GetChild(self.uiGameObject,"state/week_panel/dailyProcess/timeBg/m_txtWeekTime",UEUI.Text)
	self.weekTimeTip = transform:Find("state/week_panel/dailyProcess/timeBg/m_txtAuto8084").gameObject
	
    self.daily_timeRectTrans = transform:Find("state/daily_panel/dailyProcess/Image/time")
	
	self.week_timeRectTrans = transform:Find("state/week_panel/dailyProcess/timeBg/m_txtWeekTime")
	
    self.curTipRectTrans = self.ui.m_rtransCurrent
	
	--叹号
	self.curTipRootTrans = GetChild(self.uiGameObject,"state/daily_panel/dailyProcess/completeState/tipCurrent",UE.RectTransform)
	
	--slider
	self.m_RookieSlider = GetChild(self.uiGameObject,"state/rookie_panel/m_RookieSlider",UEUI.Slider)
	
    self.slider = require("UI.Common.SlideRect").new()
    self.slider:Init(transform:Find("state/viewPort"):GetComponent(typeof(UEUI.ScrollRect)),2)
	
	self.rookie_slider = require("UI.Common.SlideRect").new()
	self.rookie_slider:Init(transform:Find("state/rookie_panel/rookieviewPort"):GetComponent(typeof(UEUI.ScrollRect)),2)

   	--先特殊加载4个
	local itemTrans = transform:Find("state/item")
	local itemTrans2 = transform:Find("state/rookie_item")
   	local items = {}
	local rookie_items = {}
	local main_items = {};
    for i = 1, 15 do
        items[i] = dailyTaskItem.new()
        items[i]:Init(UEGO.Instantiate(itemTrans))
		items[i]:SetType(TASK_SPLIT.DAILY)
		
		rookie_items[i] = dailyTaskItem.new()
		rookie_items[i]:Init(UEGO.Instantiate(itemTrans2),true)
		rookie_items[i]:SetType(TASK_SPLIT.ROOKIE)

		main_items[i] = mainTaskItem.new();
		main_items[i]:Init(UEGO.Instantiate(self.ui.m_transMainItem), true)
    end

    main_items[5] = mainTaskItem.new();
    main_items[5]:Init(UEGO.Instantiate(self.ui.m_transMainItem), true)
	
    self.items = items
	self.rookie_items = rookie_items 
    self.slider:SetItems(items,5,Vector2.New(5,10))
	self.rookie_slider:SetItems(rookie_items,12,Vector2.New(5,10))

	self.main_slider = require("UI.Common.SlideRect").new();
	self.main_slider:Init(self.ui.m_scrollviewMain, 2);
	self.main_slider:SetItems(main_items, 10, Vector2.New(0, 10));
	
	--已经完成后的提示语句
    self.tipNewTaskObj = self.ui.m_txtNewTaskTip.gameObject
    self.ui.m_txtNewTaskTip.text = LangMgr:GetLang(1034)
	--循环列表
    self.viewPortObj = transform:Find("state/viewPort").gameObject

	self.rookieviewPortObj = transform:Find("state/rookie_panel/rookieviewPort").gameObject
    self.daily_updateTipObj = transform:Find("state/daily_panel/dailyProcess/m_goTopTime/m_txtAuto1041").gameObject
	
	local posImg = GetChild(self.uiGameObject,"state/week_panel/dailyProcess/completeState/tipCurrent/Image",UE.RectTransform)
	
	local pos = UIMgr:GetUIPosByWorld(posImg.transform.position)
	MapController:SetUIResourcePos(ItemID.WEEK_ACTIVE, pos.x, pos.y)
	
	self:SetIsUpdateTick(false)
	
	--气泡部分
	self.bubble = BubbleItem.new("UI_DailyTask")
	self.bubble:Init(self.uiGameObject,{x= -730,y=426},	function() self:Close() end)
	--self:FlyItem(50002 , 1 , Vector3.New(0 , 0 , 0))

	if DailyTaskController:IsCanShowDailyTask() then
		if NetDailyTaskData:GetRookieBoxRewardActive() then
			self.viewType = TASK_VIEW_TYPE.ONLY_DAILY
		else
			self.viewType = TASK_VIEW_TYPE.DAILY_AND_ROOKIE
			--新7日屏蔽旧新手
			if not NetDailyTaskData:IsShowRookieTask() then
				self.viewType = TASK_VIEW_TYPE.ONLY_DAILY
			end
		end
	else
		self.viewType = TASK_VIEW_TYPE.ONLY_ROOKIE
		--新7日屏蔽旧新手
		if not NetDailyTaskData:IsShowRookieTask() then
			self.viewType = TASK_VIEW_TYPE.ONLY_MAIN
		end
	end

	local daily_Count = NetDailyTaskData:get_CompleteAndNotRewardCount();
	local week_Count = NetDailyTaskData:get_WeekCompleteAndNotRewardCount();
	local rookie_Count = NetDailyTaskData:get_RookieCompletAndNotRewardCount();
	local showType = TASK_SPLIT.MAIN;
	if self.viewType == TASK_VIEW_TYPE.ONLY_DAILY then
		if daily_Count > 0 then
			showType = TASK_SPLIT.DAILY;
		elseif week_Count > 0 then
			showType = TASK_SPLIT.WEEK;
		end
	elseif self.viewType == TASK_VIEW_TYPE.ONLY_ROOKIE then
		if rookie_Count > 0 then
			showType = TASK_SPLIT.ROOKIE;
		end
	elseif self.viewType == TASK_VIEW_TYPE.DAILY_AND_ROOKIE then
		if daily_Count > 0 then
			showType = TASK_SPLIT.DAILY;
		elseif week_Count > 0 then
			showType = TASK_SPLIT.WEEK;
		elseif rookie_Count > 0 then
			showType = TASK_SPLIT.ROOKIE;
		end
	end
	DailyTaskController:SetUI(self, showType);

	if self.viewType == TASK_VIEW_TYPE.DAILY_AND_ROOKIE or self.viewType == TASK_VIEW_TYPE.ONLY_ROOKIE then
		local time = NetDailyTaskData:get_RookieZeroTime()
		if time < 0 then
			EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.ONLINE_DYA,1)
			NetDailyTaskData:set_RookieZeroTime()
		end
	end
	self:InitRookieRewardBox()
end

--强制打开时刷新一次
function UI_DailyTask:FlushDailyTask()
	local _,netRookie = NetDailyTaskData:get_RookieTaskInfos()
	local add
	for k, v in pairs(netRookie) do
		add = true
		for k2, v2 in pairs(v) do
			if v2.taskState == DAY_TASK_STATE.NOT_FINISH and v2.taskType ~= DAY_TEASK_TYPE.ROOKIE_LEVEL_TASK then
				add = false
			end
			
			if add and v2.taskType == DAY_TEASK_TYPE.ROOKIE_LEVEL_TASK then
				if v2.taskState == DAY_TASK_STATE.NOT_FINISH then
					EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.ROOKIE_LEVEL_TASK,1,v2.level_id)
				end
			end		
		end
	end
end

function UI_DailyTask:SetRookieBtn(allRookieConfig,select_index)
	local index = 1
	local count = 0
	local transform = self.uiGameObject.transform
	local btnObj = transform:Find("state/rookie_panel/rookie_btns/rookie_btn")
	local parent = transform:Find("state/rookie_panel/rookie_btns")
	
	if nil == self.rookieBtns then
		self.rookieBtns = {}		
		for k, v in pairs(allRookieConfig) do
			local dt = {
				level = v[1].level_id,
				index = index,
				}
			local obj = UEGO.Instantiate(btnObj,parent)
			obj.name = "rookie_btn" .. index
			obj.transform.localScale = Vector3.New(1, 1, 1)
			SetActive(obj,true)
			
			dt.light_img = GetChild(obj, "light_btn",UE.RectTransform)
			dt.text = GetChild(obj, "Text",UEUI.Text)
			dt.lockText = GetChild(obj, "lock/lockText",UEUI.Text)
			dt.red_img = GetChild(obj, "red_img",UE.RectTransform)
			dt.lock = GetChild(obj, "lock",UE.RectTransform)
			dt.complete = GetChild(obj, "complete",UE.RectTransform)
			dt["obj"] = obj
			
			RemoveUIComponentEventCallback(obj,UEUI.Button)
			AddUIComponentEventCallback(obj,UEUI.Button,function (go,param)
					self:onUIEventClick(go,param)
				end)
			
			table.insert(self.rookieBtns,dt)
			index = index + 1
		end
		self:UpdateRookieLevelButtonRed()
	end
	
	self.rookieBtn_index = select_index or 1
	self:FlushRookiePanel()
	
	--UIRefreshLayout(parent)
end

function UI_DailyTask:FlushRookiePanel()
	self:FlushRookieButtonState()
	self:FlushRookieDatas()
end

function UI_DailyTask:FlushRookieDatas()
	if self.rookieBtn_index == nil then self.rookieBtn_index = 1 end
	local rookieConfig = NetDailyTaskData:get_RookieTaskInfos(self.rookieBtn_index)
	self:SetRookieDatas(rookieConfig)
end

function UI_DailyTask:SetBubbleAni(bAni)
	if nil ~= self.bubble then
		self.bubble.isAni = bAni
	end	
end

function UI_DailyTask:FlyItem(item_id , num , position)
	self.bubble:FlyItem(item_id,num,position)
end

function UI_DailyTask:UpdateRookieProgress()
	local all_rookie_count,now_cow = NetDailyTaskData:get_RookieTaskCount()
	self.m_RookieSlider.value = now_cow/all_rookie_count
	self.ui.m_txtRookieCount.text =  now_cow.."/"..all_rookie_count
end

function UI_DailyTask:UpdateRookieLevelButtonRed()
	if self.rookieBtns then
		local is_show = false
		local is_complete = false
		for i = 1, #self.rookieBtns do			
			is_show = NetDailyTaskData:IsShowRookieLevelBtnRedByIndex(i)
			is_complete = NetDailyTaskData:IsCompleteRookidByIndex(i)
			SetActive(self.rookieBtns[i].red_img,is_show)
			SetActive(self.rookieBtns[i].complete,is_complete)
		end
	end
end

function UI_DailyTask:UpdateItem(data)
	if data.taskId < Daily_MaxID then --data.task_split == TASK_SPLIT.ROOKIE or
		for i=1,#self.items do
			local itm = self.items[i]
			if itm.data and itm.data.taskId == data.taskId then
				itm:UpdateItemShowState(data)				
				return
			end
		end
	else
		for i =1,#self.rookie_items do
			local itm = self.rookie_items[i]
			if itm.data ~= nil then
				if itm.data.taskId == data.taskId then
					itm:UpdateItemShowState(data)
					self:UpdateRookieProgress()
					return
				end
			end
		end
	end
	
end

--设置WEEK的进度条
function UI_DailyTask:SetWeekSliderValue(_cur , _max)
	
	local projres = _cur / _max
	if projres > 1 then
		projres = 1
	end
	local rect = GetComponent(self.viewPortObj,UE.RectTransform)
	rect.sizeDelta = Vector2.New(895, 1000)
	self.ui.m_sliderWeek.value = projres
	--文字
	self.ui.m_txtWeekAllNum.text = string.format("%d/%d" , _cur , _max)
end

function UI_DailyTask:SetWeekSliderValueByNew(projres)
	if projres > 1 then
		projres = 1
	end
	self.ui.m_sliderWeek.value = projres
end

--是否显示列表
function UI_DailyTask:SetComplete(isComplete)
    if isComplete then
        self.tipNewTaskObj:SetActive(true)
		SetActive(self.ui.m_goTopTime,false)
        self.viewPortObj:SetActive(false)
        self:SetTimeTipPos(Vector2.New(-88,-232.1))
    else
        self.tipNewTaskObj:SetActive(false)
		SetActive(self.ui.m_goTopTime,true) 
        self.viewPortObj:SetActive(true)
        self:SetTimeTipPos(Vector2.New(491.5,172))
    end
	local rect = GetComponent(self.viewPortObj,UE.RectTransform)
	rect.sizeDelta = Vector2.New(895, 680)
end

--周任务的部分
function UI_DailyTask:SetWeekComplete(isComplete)
	
	if isComplete then
		self.tipNewTaskObj:SetActive(true)
		self.viewPortObj:SetActive(false)
		self:SetWeekTimeTipPos(Vector2.New(846,-253))
		self.weekTimeTip:SetActive(false)
	else
		self.tipNewTaskObj:SetActive(false)
		self.viewPortObj:SetActive(true)
		self:SetWeekTimeTipPos(Vector2.New(1375,155))
		self.weekTimeTip:SetActive(true)
	end
end

function UI_DailyTask:SetDatas(datas)
    if not datas then
    	self.isFristIn = true;
        return
    end
	
    self.slider:SetData(datas)

    if self.isFristIn == nil then
    	self.isFristIn = true;
		self:SliderMoveReward();
	end
end

function UI_DailyTask:SetRookieDatas(datas)
	if not datas then
		self.isFristIn = true;
		return
	end
	table.sort(datas, function(a, b)
			if a.taskState ~= b.taskState then
				return a.taskState < b.taskState
			end
			if a.taskState == b.taskState then
				return a.sort < b.sort
			end
		end)
	self.rookie_slider:SetData(datas)

	if self.isFristIn == nil then
		self.isFristIn = true;
		self:MoveToNextReward();
	end
end

function UI_DailyTask:SliderMoveNone()
	local sliderData = self.slider:GetDatas()
	local dataLen = #sliderData
	--找到State==2的那个数据索引
	local index = -1
	local itemid = -1
	for key, value in pairs(sliderData) do
		if value.taskState == DAY_TASK_STATE.NOT_FINISH then
			index = key
			itemid = value.taskId
			break
		end
	end
	if index == -1 then
		return
	end
	--移动
	--Log.Info("找到未完成的索引：" .. tostring(index))
	local height = self.slider:GetContentHeight()
	--Log.Info("总高度：" .. height)
	
	local function FinishTween()
		Log.Info("动画完成！！")
		--播放闪烁的动画
		--开始 255，,255，,255，,255 FFFFFF 结束：255，,250，206，,255 FFFACE
		for i=1,#self.items do
			local itm = self.items[i]
			if itm.data.taskId == itemid then
				itm:BlingBg()
				return
			end
		end
	end	
	--起始的比例
	local curPos = self.slider:GetPos()
	--到达的比值
	local endPos = 1 - ((height / dataLen) * (index - 1) ) / height
	
	local moveTime = (endPos - curPos)/2
	
	AddDOTweenNumberComplete(curPos, endPos, moveTime, function(num)
			self.slider:SetPos(num)
		end, FinishTween):SetEase(Ease.InOutSine)
end

function UI_DailyTask:SliderMoveReward()
	local sliderData = self.slider:GetDatas();
	local dataLen = #sliderData;
	local index = -1;
	for k, v in pairs(sliderData) do
		if v.taskState == DAY_TASK_STATE.FINISHED_NOT_REWARD then
			index = k;
			break;
		end
	end
	if index == -1 then
		return
	end

	--移动
	local height = self.slider:GetContentHeight()
	--起始的比例
	local curPos = self.slider:GetPos()
	--到达的比值
	local endPos = 1 - ((height / dataLen) * (index - 1) ) / height
	
	local moveTime = (endPos - curPos)/2
	
	AddDOTweenNumberComplete(curPos, endPos, moveTime,
		function(num)
			self.slider:SetPos(num)
		end, nil
		):SetEase(Ease.InOutSine)
end

function UI_DailyTask:SetCurTipIndex(index)
    if not index or index >5 or index<1 then
        return
    end
    self.curTipRectTrans.anchoredPosition = self.ui[string.format("m_imgStep_%d",index)].rectTransform.anchoredPosition
	--self.curTipRootTrans.anchoredPosition = self.ui[string.format("m_imgStep_%d",index)].rectTransform.anchoredPosition
end

function UI_DailyTask:GetWeekRewardIndexTrans(index)
	if self.ui[string.format("m_goFlystar_%d",index)] then
		--Log.Error("xqq",index,self.ui[string.format("m_goFlystar_%d",index)])
		return self.ui[string.format("m_goFlystar_%d",index)].transform
	end
end

function UI_DailyTask:SetWeekRewardSprite(index,rewardSpr , curExp , Count , isReward,itemid)
	if not index or index<1 or index>5 then
		return 
	end
	local img = self.ui[string.format("m_imgweek_Step_%d",index)]
	local function funcW(spr)
		img.sprite = spr
	end
	ResMgr:LoadAssetAsync(rewardSpr,AssetDefine.LoadType.Sprite,funcW)
	
	local exp = self.ui[string.format("m_txtweek_Num_%d",index)]
	exp.text = tostring(curExp)
	
	local hit = self.ui[string.format("m_imgweek_Finish_%d",index)]
	hit.gameObject:SetActive(isReward)
	SetUIImageGray(img,isReward)
	local re_Num = self.ui[string.format("m_txtweek_Reward_%d",index)]
	re_Num.text = string.format("x%d" , Count)
	
	
	local data = ItemConfig:GetDataByID(v2n(itemid))
	local btn = self.ui[string.format("m_imgweek_Step_%d",index)].transform:GetComponent(typeof(UEUI.Button))
	RemoveUIComponentEventCallback(btn,UEUI.Button)
	if data.type_name == 5 then
		AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
				if ItemConfig:IsShowUIBoxProbShow(data.id) then
					UI_SHOW(UIDefine.UI_BoxProbShow, data)
				else
					UI_SHOW(UIDefine.UI_MarketBox, data)
				end
			end)
		end
end

--完成所有的今日奖励获得的奖励部分
function UI_DailyTask:SetRewardInfoByIndex(index,rewardSpr , Count,itemId)
    if not index or index<1 or index>5 then
        return
    end
    local img = self.ui[string.format("m_imgReward_%d",index)]
	local function funcW(spr)
		img.sprite = spr
	end
	ResMgr:LoadAssetAsync(rewardSpr,AssetDefine.LoadType.Sprite,funcW)
	
	local data = ItemConfig:GetDataByID(itemId)
	local btn = self.ui[string.format("m_imgReward_%d",index)].transform:GetComponent(typeof(UEUI.Button))
	RemoveUIComponentEventCallback(btn,UEUI.Button)
	if data.type_name == 5 then
		AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
				if ItemConfig:IsShowUIBoxProbShow(data.id) then
					UI_SHOW(UIDefine.UI_BoxProbShow, data)
				else
					UI_SHOW(UIDefine.UI_MarketBox, data)
				end
			end)
	end
	--local countNum = self.ui[string.format("m_imgNum_%d",index)]
	--countNum.text = string.format("x%d" , Count)
    -- img:SetNativeSize()
end
--设置每天的全部完成的奖励
function UI_DailyTask:SetStateByIndex(index,stateSpr,state)
    if not index or index<1 or index>5 then
        return
    end
	local imgGo = self.ui[string.format("m_imgGo_%d",index)]
	if state == 1 or state == 2 then 
		SetActive(imgGo,true)
	else
		SetActive(imgGo,false)
	end
	local img = self.ui[string.format("m_imgStep_%d",index)]
	if state == 2 or state == 3 then
		SetActive(img.transform:Find("m_txtAuto600" .. (index + 1)).gameObject,false)
	else
		SetActive(img.transform:Find("m_txtAuto600".. (index + 1)).gameObject,true)
	end
	local function funcW(spr)
		img.sprite = spr
	end
	ResMgr:LoadAssetAsync(stateSpr,AssetDefine.LoadType.Sprite,funcW)
end

function UI_DailyTask:SetTotalSurplusTime(time)
	self.ui.m_txtTime.text = TimeMgr:ConverSecondToString(time)
	self.ui.m_txtTime2.text = TimeMgr:ConverSecondToString(time)
end

function UI_DailyTask:SetTotalWeekSurplusTime(time)
	self.weekTimeText.text = TimeMgr:CutBuyWorkTime(time)
	self.ui.m_txtTime2.text = TimeMgr:CutBuyWorkTime(time)
end

function UI_DailyTask:TickUI(delta)
	
end

function UI_DailyTask:SetTimeTipPos(vec2)
   -- self.daily_timeRectTrans.anchoredPosition = vec2
end

function UI_DailyTask:SetWeekTimeTipPos(vec2)
	--self.week_timeRectTrans.anchoredPosition = vec2
end

function UI_DailyTask:SetCurTipVisable(visable)
    local obj = self.curTipRectTrans.gameObject
    if obj.activeSelf~=visable then
        obj:SetActive(true)
        self.daily_updateTipObj:SetActive(true)
    end
	local tipRoot = self.curTipRootTrans.gameObject
	if tipRoot.activeSelf~=visable then
		tipRoot:SetActive(true)
	end
end

function UI_DailyTask:OnRefresh(param) 
end

function UI_DailyTask:onDestroy()
    UI_UPDATE(UIDefine.UI_MainFace,12,{isShow = false,list={PlayerDefine.Coin,PlayerDefine.Diamond}})
    DailyTaskController:RemovePanel()
    for i=1,#self.items do
        self.items[i]:onDestroy()
    end
	for i=1,#self.rookie_items do
		self.rookie_items[i]:onDestroy()
	end

	if not self.isCheckClose and self.bubble and self.bubble:IsHaveItem() then
		self.isCheckClose = true;
		self.bubble:SetCloseCall(nil);
		self.bubble:CloseImmediately();
	end

	self.rookieBtns = nil
	self.rookieRewardComp = nil
	self.isFristIn = nil;
    UEGO.Destroy(self.uiGameObject)
end

function UI_DailyTask:onUIEventClick(go,param)
    local name = go.name
    if name=="m_btnClose" or name == "btn_mask" then
    	self:CloseCheck();          
	elseif name == "btnTip" then
		UI_SHOW(UIDefine.UI_ItemDetail, 1, nil)
	elseif name == "btn_daily" then	
		DailyTaskController:OnPanelType(TASK_SPLIT.DAILY)
	elseif name == "btn_week" then
		DailyTaskController:OnPanelType(TASK_SPLIT.WEEK)
	elseif name == "btn_rookie" then
		--self:SetComplete(false)	
		
		DailyTaskController:OnPanelType(TASK_SPLIT.ROOKIE)
		SetActive(self.tipNewTaskObj,false)
	elseif name == "btnWeekTip" then
		Log.Info("展示一个相对应的界面")
		UI_SHOW(UIDefine.UI_ItemDetail, 3, nil)
	elseif string.startswith(name,"rookie_btn") then
		local index = tonumber(string.match(name, "rookie_btn(%d+)"))
		self.rookieBtn_index = index
		self:FlushRookiePanel()
	elseif name == "rookieBox" then
		self:UpdataRookieRewad()
	elseif name == "btn_main" then	
		DailyTaskController:OnPanelType(TASK_SPLIT.MAIN)
		SetActive(self.tipNewTaskObj,false)
    end
end

function UI_DailyTask:GetRookieGift()
	local config = GlobalConfig:GetString(1505)
	local config2 = string.split(config,";")
	local rewards = {}
	for i, v in ipairs(config2) do
		local data = SplitStringToNum(v,"|")
		table.insert(rewards,data)
	end
	--local pos = self.ui.m_goRookieBox.transform.position
	--for num, v in ipairs(rewards) do
		--if v[1] <= ItemID._RESOURCE_MAX then
			--MapController:AddResourceBoomAnim(pos.x,pos.y, v[1], v[2])
			--NetUpdatePlayerData:AddResource(PlayerDefine[tonumber(v[1])],tonumber(v[2]))
		--else
			----if num == 5 then
			--self.bubble:FlyItem(v[1],v[2],pos)
			----end
		--end
	--end
	UI_SHOW(UIDefine.UI_ItemRookieFinishReward,rewards)
	NetDailyTaskData:SetRookieBoxRewardActive(true)
	self:Close()
	
end

function UI_DailyTask:UpdataRookieRewad()
	SetActive(self.ui.m_goRookieBoxTips,not self.ui.m_goRookieBoxTips.activeSelf)
end


function UI_DailyTask:InitRookieRewardBox()
	
	local rookie_iteminfo = GlobalConfig:GetString(1505)
	local iteminfo = string.split(rookie_iteminfo, ";")
	self.rookieRewardComp = {}
	for k, v in pairs(iteminfo) do
		local t = { }
		local info = string.split(v,"|")
		t.id = info[1]
		t.count = info[2]
		table.insert(self.rookieRewardComp,t)
	end
	self:UpdateRookieRewardBox(self.rookieRewardComp)
end

function UI_DailyTask:UpdateRookieRewardBox(iteminfo)
	local ImgReBg = GET_UI(self.uiGameObject, "ImgReBg", "RectTransform")
	local itemCount = ImgReBg.childCount
	local info_t = iteminfo
	SetActive(ImgReBg,true)
	--table.sort(info_t, function(a, b)
			--return a.id < b.id
		--end)
	for i = 1, itemCount do
		local item = GET_UI(ImgReBg, "Image"..i, "RectTransform")
		local num = GET_UI(item, "Num"..i, TP(UEUI.Text))
		local icon = GET_UI(item,"item"..i,TP(UEUI.Image))
		if i <= #info_t then
			local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item,info_t[i].id)
			SetUIImage(icon,info_t[i].specialImg or info_t[i].img or itemConfig.icon_b, false)
			num.text = info_t[i].count or " "
			SetActive(item.gameObject, true)
		else
			SetActive(item.gameObject, false)
		end
	end
end

function UI_DailyTask:FlushRookieButtonState()
	for k, v in pairs(self.rookieBtns) do
		v.light_img.gameObject:SetActive( self.rookieBtn_index == v.index)
		v.text.text = LangMgr:GetLang(154)..v.level
		v.lockText.text = LangMgr:GetLang(154)..v.level
		local curLevel = NetUpdatePlayerData:GetLevelMess()
		if v2n(curLevel) < v2n(v.level) then
			v.lock.gameObject:SetActive(true)
			RemoveUIComponentEventCallback(v["obj"],UEUI.Button)
		else
			v.lock.gameObject:SetActive(false)
		end
	end
end

function UI_DailyTask:UpdateButtonHitState(notGetCount ,week_notGetCount, rookie_Count)
	self.ui.m_txtDailyRed.text = notGetCount;
	self.dailyRedObj:SetActive(notGetCount > 0);

	self.ui.m_txtWeekRed.text = week_notGetCount;
	self.weekRedObj:SetActive(week_notGetCount > 0);

	self.ui.m_txtRookieRed.text = rookie_Count;
	self.rookieRedObj:SetActive(rookie_Count > 0);

	self:UpdateRookieLevelButtonRed();
end

function UI_DailyTask:ChangePanelState(_type)
	if self.viewType == TASK_VIEW_TYPE.ONLY_ROOKIE then
		self.btn_DailyObj:SetActive(false);
		self.btn_WeekObj:SetActive(false);
	elseif self.viewType == TASK_VIEW_TYPE.ONLY_DAILY then
		self.btn_RookieObj:SetActive(false);
	elseif self.viewType == TASK_VIEW_TYPE.ONLY_MAIN then
		self.btn_DailyObj:SetActive(false);
		self.btn_WeekObj:SetActive(false);
		self.btn_RookieObj:SetActive(false);
	end

	self:UpdateButtonState(_type)
	self:UpdatePanelState(_type)
end

function UI_DailyTask:UpdateButtonState(_type)
	if _type == TASK_SPLIT.DAILY then
		self.mainLightObj:SetActive(false)
		self.dailyLightObj:SetActive(true)
		self.weekLightObj:SetActive(false)
		self.rookieLightObj:SetActive(false)

		self.btn_DailyObj:SetActive(true)
		self.ui.m_txtTitle.text = LangMgr:GetLang(1037)
	elseif _type == TASK_SPLIT.WEEK then
		self.mainLightObj:SetActive(false)
		self.dailyLightObj:SetActive(false)
		self.weekLightObj:SetActive(true)
		self.rookieLightObj:SetActive(false)

		self.btn_WeekObj:SetActive(true)
		self.ui.m_txtTitle.text = LangMgr:GetLang(8087)
	elseif _type == TASK_SPLIT.ROOKIE then
		self.mainLightObj:SetActive(false)
		self.dailyLightObj:SetActive(false)
		self.weekLightObj:SetActive(false)
		self.rookieLightObj:SetActive(true)

		self.btn_RookieObj:SetActive(true)
		self.ui.m_txtTitle.text = LangMgr:GetLang(170000)
	elseif _type == TASK_SPLIT.MAIN then
		self.mainLightObj:SetActive(true)
		self.dailyLightObj:SetActive(false)
		self.weekLightObj:SetActive(false)
		self.rookieLightObj:SetActive(false)

		self.btn_mainObj:SetActive(true)
		self.ui.m_txtTitle.text = LangMgr:GetLang(170037)
	end
end
function UI_DailyTask:UpdatePanelState(_type)
	if _type == TASK_SPLIT.DAILY then
		self.mainRoot:SetActive(false);
		self.dailyRoot:SetActive(true)
		self.weekRoot:SetActive(false)
		self.rookieRoot:SetActive(false)

		self.viewPortObj:SetActive(true)
		self.rookieviewPortObj:SetActive(false)
	elseif _type == TASK_SPLIT.WEEK then
		self.mainRoot:SetActive(false);
		self.dailyRoot:SetActive(false)
		self.weekRoot:SetActive(true)
		self.rookieRoot:SetActive(false)

		self.viewPortObj:SetActive(true)
		self.rookieviewPortObj:SetActive(false)
	elseif _type == TASK_SPLIT.ROOKIE then
		self.mainRoot:SetActive(false);
		self.dailyRoot:SetActive(false)
		self.weekRoot:SetActive(false)
		self.rookieRoot:SetActive(true)

		self.viewPortObj:SetActive(false)
		self.rookieviewPortObj:SetActive(true)
	elseif _type == TASK_SPLIT.MAIN then
		self.mainRoot:SetActive(true);
		self.dailyRoot:SetActive(false);
		self.weekRoot:SetActive(false);
		self.rookieRoot:SetActive(false);

		self.viewPortObj:SetActive(false);
		self.rookieviewPortObj:SetActive(false);
	end
end


function UI_DailyTask:MoveToNextReward()
	local levelConfig = NetDailyTaskData:get_RookieTaskInfos(self.rookieBtn_index)
	--local content = GetChild(self.uiGameObject.transform,"state/rookie_panel/rookieviewPort",UE.Transform)
	local index = 0
	for k, v in pairs(levelConfig) do
		if v.taskState == DAY_TASK_STATE.FINISHED_NOT_REWARD then
			index = v2n(k-1)
			local y = index*192
			--Log.Error("index",index,"y",y)
			--self.rookie_slider.contentRect.transform.y = 0
			DOLocalMoveY(self.rookie_slider.contentRect.transform,
				v2n(y),0.2,nil,Ease.Linear)
			--.localPosition.y
			return
		end
	end
end

function UI_DailyTask:AutoClose()
	self:CloseCheck()
end

function UI_DailyTask:CloseCheck()
	if self.bubble.isAni then return end
	if not self.isCheckClose and self.bubble:IsHaveItem() then
		self.isCheckClose = true;
		self.bubble:Close()
	else
		self.isCheckClose = true;
		self:Close()
	end
end

function UI_DailyTask:setMainData(list)
	if list ~= nil then
		self.main_slider:SetData(list);
	end
	self.isFristIn = true;
end






























----------------------------------------主界面--END---------------------------------

---------item template--------------------------------------
function dailyTaskItem:OnInit(transform,is_rookie)
	self.stateNormalBg = "Sprite/ui_task/taskbox1.png"
	self.stateFinishBg = "Sprite/ui_task/taskbox2.png"
	self.itemNormalBg = "Sprite/ui_public/windows-listbox1.png"
	self.itemNormalBtnBg = "Sprite/ui_public/button1-blue.png"
	self.itemFinishBtnBg = "Sprite/ui_public/button1-green.png"
	
	self.twoNumSprint = "Sprite/ui_public/weektask_icon_points.png"
	
	--背景图
    self.imgBg = transform:GetComponent(typeof(UEUI.Image))
	--
	self.canvasGroup = transform:GetComponent(typeof(UE.CanvasGroup))
	--任务Icon
    self.imgTarget = transform:Find("taskBg/imgTarget"):GetComponent(typeof(UEUI.Image))
	--任务进度
    self.txtPercent = transform:Find("taskBg/numPercent"):GetComponent(typeof(UEUI.Text))
	self.go = transform:Find("taskBg/imgGo") 
	self.imgFinish = transform:Find("imgFinish")
	--任务标题
    self.txtContent = transform:Find("txtTargetContent"):GetComponent(typeof(UEUI.Text))
	--任务描述
	if is_rookie then
		self.txtDes = transform:Find("txtDes"):GetComponent(typeof(UEUI.Text))
	end
	--奖励文字
    self.txtReward = transform:Find("txtReward"):GetComponent(typeof(UEUI.Text))
	--奖励Icon
    self.imgRewardIcon = transform:Find("txtReward/imgIcon"):GetComponent(typeof(UEUI.Image))
	--奖励数量文字
    self.txtRewardNum = transform:Find("txtReward/imgIcon/txtNum"):GetComponent(typeof(UEUI.Text))
	--第二个奖励
	self.twoRewardIcon = transform:Find("txtReward/imgIcon_2"):GetComponent(typeof(UEUI.Image))
	self.twoRewardNum = transform:Find("txtReward/imgIcon_2/txtNum"):GetComponent(typeof(UEUI.Text))
    --按钮背景图
	self.btnImg = transform:Find("btnGet"):GetComponent(typeof(UEUI.Image))
	--按钮
    self.btnGetReward = transform:Find("btnGet"):GetComponent(typeof(UEUI.Button))
	--按钮的文字
    self.btnContent = transform:Find("txtBtnContent"):GetComponent(typeof(UEUI.Text))
	self.txtObj = transform:Find("txtBtnContent").gameObject
	--设置奖励静态文字
    self.txtReward.text = LangMgr:GetLang(16)
    self.maskGraphics = transform:GetComponentsInChildren(typeof(UEUI.MaskableGraphic),true)
    self.grayed = false
    local function onClick()
        local pos = self.btnGetReward.transform.position
        DailyTaskController:onItemClick(self.data.taskId,pos)
    end
    self.btnGetReward.onClick:AddListener(onClick)
end

function dailyTaskItem:SetType(type)
	self.type = type
end

--更新任务奖励的显示
function dailyTaskItem:SetRewardInfo(targetSpr,targetContent,rewardSpr,count,task_type,txtDes)
    if not count then
        self.imgRewardIcon.color = colorFade
        self.txtRewardNum.text = ""
        local col = self.txtReward.color
        col.a = 1
        self.txtReward.color=col
    else
        self.imgRewardIcon.color = colorNormal
        local col = self.txtReward.color
        col.a = 1
        self.txtReward.color=col
		local function funcW1(spr)
			self.imgRewardIcon.sprite = spr
		end
		ResMgr:LoadAssetAsync(rewardSpr,AssetDefine.LoadType.Sprite,funcW1)
        self.imgRewardIcon:SetNativeSize()
        self.txtRewardNum.text = tostring(count)
    end
	local function funcW2(spr)
		self.imgTarget.sprite = spr
	end
	ResMgr:LoadAssetAsync(targetSpr,AssetDefine.LoadType.Sprite,funcW2)
	
    self.txtContent.text = targetContent
	if self.txtDes then
		self.txtDes.text = txtDes
	end
	if task_type == 1 then
		self.txtReward.text = LangMgr:GetLang(7103)
	else
		self.txtReward.text = LangMgr:GetLang(16)
	end 
end

--第二个奖励部分
function dailyTaskItem:SetTwoRewardInfo(count)
	if not count then
		self.twoRewardIcon.color = colorFade
		self.twoRewardNum.text = ""
	else
		self.twoRewardIcon.color = colorNormal
		local function funcW(spr)
			self.twoRewardIcon.sprite = spr
		end
		ResMgr:LoadAssetAsync(self.twoNumSprint,AssetDefine.LoadType.Sprite,funcW)
		
		--self.twoRewardIcon:SetNativeSize()
		self.twoRewardNum.text = tostring(count)
	end
end

function dailyTaskItem:BlingBg()
	--开始 255，,255，,255，,255 FFFFFF 结束：255，,250，206，,255 FFFACE
	local FuncCall = function()
		DOKill(self.imgBg)
		self.imgBg.color = Color.New(1 , 1 , 1 , 1)
	end
	FuncCall()
	local endValue = Color.New(255/255, 250/255, 206/255, 255/255)
	local duration = 0.5
	local tween = self.imgBg:DOColor(endValue,duration):SetEase(Ease.Unset):SetLoops(3,LoopType.Yoyo)
	local loopCall = function()
		DOKill(self.imgBg)
		local curColor = self.imgBg.color
		self.imgBg:DOColor(Color.New(1 , 1 , 1 , 1),0.2):SetEase(Ease.Unset):SetAutoKill()
	end
	tween:OnComplete(loopCall)	
end

--更新任务的状态
function dailyTaskItem:UpdateItemShowState(_data)
	self.data = _data
	self:UpdateScrollItem(self.data)
end


--更新条目的状态
function dailyTaskItem:SetState(state,btnSpr,bgSpr,look_for)
    self.state = state
    self:SetGray(state==DAY_TASK_STATE.FINISHED_REWARDED)
    local str = nil
    self.btnImg.color = colorNormal
    if state == DAY_TASK_STATE.NOT_FINISH then
		--未完成
		self.canvasGroup.alpha = 1
        str = LangMgr:GetLang(30)
    elseif state == DAY_TASK_STATE.FINISHED_NOT_REWARD then
		--完成未领取
		self.canvasGroup.alpha = 1
        str = LangMgr:GetLang(17)
    elseif state == DAY_TASK_STATE.FINISHED_REWARDED then
		--已经完成并且领取
		self.canvasGroup.alpha = 0.5
        str =LangMgr:GetLang(1042)
        self.btnImg.material = nil
        self.btnContent.material = nil
        self.btnImg.color = colorFade
    end
    
	if look_for == FindType.HideFind and state == DAY_TASK_STATE.NOT_FINISH then
		self.btnContent.text = " "
	else
		self.btnContent.text = str
	end
	
	local function funcWbtn(spr)
		self.btnImg.sprite = spr
	end
	ResMgr:LoadAssetAsync(btnSpr,AssetDefine.LoadType.Sprite,funcWbtn)
	
	--local function funcWspr(spr)
		--self.imgBg.sprite = spr
	--end
	--ResMgr:LoadAssetAsync(bgSpr,AssetDefine.LoadType.Sprite,funcWspr)
    
   

	local hex = (btnSpr == self.itemNormalBtnBg) and "0069AB" or "247700"
	UnifyOutline(self.txtObj,hex)
end

function dailyTaskItem:SetGray(isGray)
	SetActive(self.imgFinish,isGray)
    --if self.grayed ==isGray then
        --return
    --end
    --self.grayed = isGray
    --local mat =nil
    --if isGray then
        --mat = grayMat
    --end
    --for i=0,self.maskGraphics.Length-1 do
        --self.maskGraphics[i].material = mat
    --end
end
--更新任务进度
function dailyTaskItem:SetCompleteCount(count,total)
    local formatStr = nil
    if count < total then
		SetActive(self.go,false)
        formatStr = "%d/%d"--"<color=#8a1609>%d</color>/%d"
    else
		SetActive(self.go,true)
        formatStr = "%d/%d" --"<color=#398213>%d/%d</color>"
    end
    self.txtPercent.text = string.format(formatStr,count,total)
end

function dailyTaskItem:UpdateScrollItem(data)
	local conf
	if self.type == TASK_SPLIT.ROOKIE then
		conf = ConfigMgr:GetDataByID(ConfigDefine.ID.rookie_task,data.taskId)
	else
		conf = ConfigMgr:GetDataByID(ConfigDefine.ID.daily_task,data.taskId)
	end
	
	--更新状态部分
	local curState = data.taskState
	local BtnSpr = nil
	local BgSpr = nil
	SetActive(self.btnGetReward,true)
	if curState == DAY_TASK_STATE.NOT_FINISH then
		--未完成
		BtnSpr = self.itemNormalBtnBg
		BgSpr = self.itemNormalBg
		if conf.look_for == FindType.HideFind then
			SetActive(self.btnGetReward,false) 
		else
			SetActive(self.btnGetReward,true)
		end
	elseif curState == DAY_TASK_STATE.FINISHED_NOT_REWARD then
		--已完成未领取
		BtnSpr = self.itemFinishBtnBg
		BgSpr = self.itemNormalBg
	else
		--已完成已领取
		BtnSpr = self.itemNormalBtnBg
		BgSpr = self.itemNormalBg
	end

	self:SetState(curState,BtnSpr,BgSpr,conf.look_for)
	--更新进度部分
	self:SetCompleteCount(data.completed,data.completecount)
end

function dailyTaskItem:UpdateData(data,index)
	--这里的data是一条网络数据
    if data then
		local conf
		if self.type == TASK_SPLIT.ROOKIE then
			conf = ConfigMgr:GetDataByID(ConfigDefine.ID.rookie_task,data.taskId)
		else
			conf = ConfigMgr:GetDataByID(ConfigDefine.ID.daily_task,data.taskId)
		end
		local TargetSprite = conf.icon--ResMgr:LoadAssetSync(,AssetDefine.LoadType.Sprite)
		local TargetName = LangMgr:GetLang(conf.lang_id)
		local RewardSpr = nil
		local RewardCount = conf.num
		local twoCount = conf.two_num
		local task_type = conf.task_type
		local txtDes = LangMgr:GetLang(conf.describe_lid)
		if conf.reward_type then
			RewardSpr = ConfigMgr:GetDataByID(ConfigDefine.ID.item,conf.reward_type).img
			--local path = ConfigMgr:GetDataByID(ConfigDefine.ID.item,conf.reward_type).img
			--RewardSpr = ResMgr:LoadAssetSync(path,AssetDefine.LoadType.Sprite)
		end
		--更新显示相关
        self:SetRewardInfo(TargetSprite,TargetName,RewardSpr,RewardCount,task_type,txtDes,conf.look_for)
		
		self:SetTwoRewardInfo(twoCount)
		
		self:UpdateScrollItem(data)
    end
    self.data = data
end

function dailyTaskItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function dailyTaskItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function dailyTaskItem:onDestroy()
    self.btnGetReward.onClick:RemoveAllListeners()
    UEGO.Destroy(self.transform.gameObject)
end






















function mainTaskItem:OnInit(transform)
	self.bg = self.transform:GetComponent(typeof(UEUI.Image));
	self.icon = transform:Find("itemBg/icon"):GetComponent(typeof(UEUI.Image));
	self.numTxt = transform:Find("itemBg/txtBg/numTxt"):GetComponent(typeof(UEUI.Text));
	self.nameTxt = transform:Find("nameTxt"):GetComponent(typeof(UEUI.Text));

	self.icon_1 = transform:Find("rewardObj/icon_1"):GetComponent(typeof(UEUI.Image));
	self.numTxt_1 = transform:Find("rewardObj/icon_1/numTxt_1"):GetComponent(typeof(UEUI.Text));
	self.icon_2 = transform:Find("rewardObj/icon_2"):GetComponent(typeof(UEUI.Image));
	self.numTxt_2 = transform:Find("rewardObj/icon_2/numTxt_2"):GetComponent(typeof(UEUI.Text));
	self.icon_3 = transform:Find("rewardObj/icon_3"):GetComponent(typeof(UEUI.Image));
	self.numTxt_3 = transform:Find("rewardObj/icon_3/numTxt_3"):GetComponent(typeof(UEUI.Text));

	self.showBtn = transform:Find("itemBg/showBtn"):GetComponent(typeof(UEUI.Button));
	self.getBtn = transform:Find("getBtn"):GetComponent(typeof(UEUI.Button));

	self.bgSp = "Sprite/ui_public/windows-list.png"--ResMgr:LoadAssetSync(, AssetDefine.LoadType.Sprite);
	self.bgSp2 = "Sprite/ui_public/windows-list4.png"--ResMgr:LoadAssetSync(,AssetDefine.LoadType.Sprite);

	local function onClick()
        UI_SHOW(UIDefine.UI_Task, self.id);
    end
    self.showBtn.onClick:AddListener(onClick);

    local function onClick2()
        FindController.LookFindItem(self.look,self.lookData)
		AudioMgr:Play(42)

        --如果是城堡类型，自动修复进度
        self:RepairCastle()
        UI_CLOSE(UIDefine.UI_DailyTask);
    end
    self.getBtn.onClick:AddListener(onClick2)
end

function mainTaskItem:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec;
end

function mainTaskItem:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition;
end

function mainTaskItem:UpdateData(id, index)
	if id == nil then return end;
	self.id = id;
	
	if NetTaskData:IsNowRandom(id) then
		
		local function funcWbg2(spr)
			self.bg.sprite = spr;
		end
		ResMgr:LoadAssetAsync(self.bgSp2,AssetDefine.LoadType.Sprite,funcWbg2)
	else
		local function funcWbg(spr)
			self.bg.sprite = spr;
		end
		ResMgr:LoadAssetAsync(self.bgSp,AssetDefine.LoadType.Sprite,funcWbg)
	end

	local data = NetTaskData:GetTaskItem(id);
	SetImageSprite(self.icon, data.icon, false);
	self.nameTxt.text = LangMgr:GetLang(data.conditions_lid);

	local target = NetTaskData:GetTarget(id);
    self.numTxt.text =  NetTaskData:GetProgressById(id) .. "/".. NetTaskData:GetTarget(id);

    local list = NetTaskData:GetReward(id);
    local nameStr;
    for k, v in ipairs(list) do
    	nameStr = "icon_" .. k;
    	SetImageSprite(self[nameStr], v.img, false);
    	SetActive(self[nameStr], true);

    	nameStr = "numTxt_" .. k;
    	self[nameStr].text = v.count;
    end

    local len = #list;
    if len < 3 then
    	for i = len + 1, 3 do
    		nameStr = "icon_" .. i;
    		SetActive(self[nameStr], false);
    	end
    end

    if data.look_for == nil or data.look_for == 0 then
    	SetActive(self.showBtn.gameObject, false);
    else
    	self.look = data.look_for
    	self.lookData = data.look_for_value
    end
end

function mainTaskItem:onDestroy()
	self.showBtn.onClick:RemoveAllListeners();
	self.getBtn.onClick:RemoveAllListeners();
	UEGO.Destroy(self.transform.gameObject);
end

function mainTaskItem:RepairCastle()
    local taskInfo = NetTaskData:GetTaskInfoById(self.id)
    if not taskInfo then
        return
    end
    if nil == taskInfo.targetItem then
        return
    end
    local type_use = ItemConfig:GetTypeUse(taskInfo.targetItem)
    if nil == type_use then
        return
    end
    if type_use == ItemUseType.Castle and taskInfo.conditions_type == 1 then
        local mapId = NetUpdatePlayerData.playerInfo.curMap
        if IsHomeMap(mapId) then
            local num = NetMapNoteData:GetNoteCount(mapId,NetMapNoteData.ID.item_has,taskInfo.targetItem)
            if num > 0 then
                local listOut = MapController:GetItemById(taskInfo.targetItem,1)
				if nil == listOut then
					return
				end
                if listOut[1] then
                    EventMgr:Dispatch(EventID.MAP_ITEM_NEW, mapId, taskInfo.targetItem,false,listOut[1])
                end
            end
        end
    else
        return
    end
end


return UI_DailyTask

