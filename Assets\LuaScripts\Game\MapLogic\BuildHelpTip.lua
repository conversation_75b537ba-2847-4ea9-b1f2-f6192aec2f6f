local BuildHelpTip = {}
local M = BuildHelpTip

local prePath = "Prefab/Map/BuildHelpTip.prefab"

function M:Create(pos)
    local item = {}
    setmetatable(item,{__index = BuildHelpTip})
    ResMgr:LoadAssetWithCache(prePath, AssetDefine.LoadType.Instant, function(prefab)
        local newGo, newTrans = CreateGOAndTrans(prefab)
        item.go = newGo
        MapController:AddUIToWorld(newTrans)
        SetUIPos(newGo,pos.x,pos.y)
        item:Init()
    end)
    return item
end

--初始化
function M:Init()
    self.buildHelp = GetChild(self.go,"buildHelp")
    self.btnHelpBtn = GetChild(self.go,"buildHelp/btn")
    self.callback = nil
    AddUIComponentEventCallback(self.btnHelpBtn,UEUI.Button,function()
        if self.callback then
            self.callback()
        end
    end)
end

function M:SetItem(i)
    self.workerId = i
    
end

function M:Destroy()
    EventMgr:Remove(EventID.TOUCH_INTERRUPT,self.CloseDiamond,self)
    self.workerId = nil
    UEGO.Destroy(self.go)
end

function M:SetActive(active)
    SetActive(self.buildHelp,active)
end

function M:SetPos(pos)
    SetUIPos(self.go,pos.x,pos.y)
end

function M:AddClickFunc(callback)
    self.callback = callback
    
end

return M