---



---
local GuideController = Class()

function GuideController:ctor()
    self.m_GuideIdNow = 0
    self.m_ListGuideState = {}
    self.m_CacheGuideItem = {}

    self.m_TriggerWaitingInf = {}
	self.MarkList = {}
end

function GuideController:ClearGuide()
    self.m_GuideData = {}
end

function GuideController:LoadGuide(json)
    if json then
        if json["cur_id"] then
            self.m_GuideData["map_" .. MAP_ID_MAIN] = DeepCopy(json)
        else
            self.m_GuideData = DeepCopy(json)
        end
    end
end

function GuideController:SaveGuide()
    local save = {}
    save["cur_id"] = self.m_GuideIdNow
    save["flags"] = self.m_ListGuideState
    save["arr_grid"] = self:SaveCacheItem()
	save["arr_wait"] = self.m_TriggerWaitingInf
	if self.m_MapId ~= nil then 
    	self.m_GuideData["map_" .. self.m_MapId] = save
	else
		self.m_GuideData["map_" .. MapController.m_MapId] = save
	end
    return self.m_GuideData
end

function GuideController:ClearGuideByMap(mapId) -- 清除当前结束
	if not IsHomeZooMap(mapId) then
		if self.m_GuideData["map_" .. mapId] then
			self.m_GuideData["map_" .. mapId] = nil
		end
	end
end

function GuideController:ClearGuideByOtheMap(mapId) -- 清除了 1, 2 ,7 当前副本id 之外的其他id
	for k, v in pairs(self.m_GuideData) do
		local _mapId = string.match(k, "%d+")
		if not IsHomeZooMap(_mapId) and v2n(k) ~= v2n(mapId) then
			if self.m_GuideData["map_" .. _mapId] then
				self.m_GuideData["map_" .. _mapId] = nil
			end
		end
	end
end

function GuideController:SaveCacheItem()
    local list = {}
    for k, v in pairs(self.m_CacheGuideItem) do
        if v then
            list[k] = v.m_StrGrid
        end
    end
    return list
end

function GuideController:LoadCacheItem(json)
    if json == nil then
        return
    end
    self.m_CacheGuideItem = {}
    for k, v in pairs(json) do
        local x, y = GetGridByStr(v)
        self.m_CacheGuideItem[k] = MapController:GetItemByGridUnsafe(x, y)
    end
end

--function GuideController:CheckFixed20220217()
    --if NetUpdatePlayerData.playerInfo.time_create < 1645086469 then
        --for id, data in pairs(GuideConfig.m_GuideInf) do
            --if not self:IsGuideDone(id) then
                --local ret = self:IsGuideLimit(id, data)
                --if ret ~= 0 then
                    --self:SetGuideDone(id, math.abs(ret))
                --end
            --end
        --end
    --end
--end

function GuideController:LoadGuideByMap(mapId)
    local json = self.m_GuideData["map_" .. mapId]
    if json then
        self.m_GuideIdNow = json["cur_id"]
        self.m_ListGuideState = json["flags"]
        self:LoadCacheItem(json["arr_grid"])
		--self.m_TriggerWaitingInf = json["arr_wait"]
    else
        self.m_GuideIdNow = 0
        self.m_ListGuideState = {}
        self.m_CacheGuideItem = {}
		--self.m_TriggerWaitingInf = {}
    end
	self.m_TriggerWaitingInf = {}
   -- self:CheckFixed20220217()
end

function GuideController:RegisterEvents()
    EventMgr:Add(EventID.EVENT_GUIDE, self.EventGuide, self)
    EventMgr:Add(EventID.TRIGGER_GUIDE, self.TriggerGuide, self)
    EventMgr:Add(EventID.TRIGGER_GUIDE_WAITING, self.TriggerGuideWaiting, self)

    --Step events listener register
    local function onEventClickedItem(touchType, tNode)
        local maybeId = nil
        if tNode then
            maybeId = tNode.m_Id
            self:EventGuide(1, touchType, tNode)
        end
        if touchType == 1 then
            EventMgr:Dispatch(EventID.TRIGGER_GUIDE, 6, maybeId)
        end
    end
    EventMgr:Add(EventID.TOUCH_CLICKED, onEventClickedItem)

    local function onEventItemNew(mapId, newItemId, isFromMapInit, itemObj)
        self:EventGuide(2, newItemId, itemObj)
        EventMgr:Dispatch(EventID.TRIGGER_GUIDE, 2, newItemId)
		EventMgr:Dispatch(EventID.TRIGGER_GUIDE, 21, newItemId,itemObj.m_GridX,itemObj.m_GridY)
    end
    EventMgr:Add(EventID.MAP_ITEM_NEW, onEventItemNew)

    local function onEventHeroMagic(heroId)
        self:EventGuide(6)
    end
    EventMgr:Add(EventID.HERO_CAST_OVER, onEventHeroMagic)

    local function onEventMoveItem(item, gx, gy)
        self:EventGuide(7, item, gx, gy)
    end
    EventMgr:Add(EventID.MAP_ITEM_MOVE_GRID, onEventMoveItem)
	local function onEventDelItem(item, gx, gy)
		self:EventGuide(101, gx, gy)
	end
	EventMgr:Add(EventID.MAP_ITEM_DEL, onEventDelItem)
    local function onEventUISHOW(uiName, view)
        self:EventGuide(9, uiName, view)
    end
    EventMgr:Add(EventID.UI_SHOWED, onEventUISHOW)

    local function onEventGroupOpen(mapId, id, idx, count, config)
        self:EventGuide(10, mapId, id)
    end
    EventMgr:Add(EventID.OPEN_GROUP, onEventGroupOpen)

    local function onEventGroupMagicClicked(id)
        self:EventGuide(11, id)
    end
    EventMgr:Add(EventID.CLICKED_GROUP_MAGIC, onEventGroupMagicClicked)
	
	local function onEventFeedClicked(id)
		self:EventGuide(14)
	end
	EventMgr:Add(EventID.ANIMAL_EAT_EVENT, onEventFeedClicked)
	
	local function onUSE_BOOM(id)
		self:EventGuide(15, id)
	end
	EventMgr:Add(EventID.USE_BOOM , onUSE_BOOM)
	
	
	local function onClickGroup(mapId,groupId)
		self:EventGuide(16, mapId,groupId)
	end
	EventMgr:Add(EventID.CLICK_GROUP , onClickGroup)
	
    --Trigger event, guide open
    local function onTriggerMapUnlock(mapId, gId)
		self:EventGuide(18, mapId,gId)
		
        EventMgr:Dispatch(EventID.TRIGGER_GUIDE, 5, mapId, gId)
    end
    EventMgr:Add(EventID.MAP_CLOUD_UNLOCKED, onTriggerMapUnlock)

    --local function onEventLevelUp(lvId)
    --    EventMgr:Dispatch(EventID.TRIGGER_GUIDE_WAITING, 3, lvId)
    --end
    --EventMgr:Add(EventID.LEVEL_UP, onEventLevelUp)

    --other event
	
    local function onTouchMoveItem(state, obj)
        self:onTouchMoveItem(state, obj)
    end
    EventMgr:Add(EventID.MAP_ITEM_MOVE, onTouchMoveItem)
	
	local function onCLOSE_UI(uiName)
		self:EventGuide(21, uiName)
	end
	EventMgr:Add(EventID.CLOSE_UI , onCLOSE_UI)

end

function GuideController:InitGuide(mapId)
	
    local isCloseSplash = true

    self.m_MapId = mapId

    self:RegisterEvents()

    self.m_BlockWall = require "Game.MapLogic.TutorialBlockController".new()
	self.m_BlockCircle = require "Game.MapLogic.TutorialBlockCircleController".new()

    if self:IsGuideValid() then
        local guideId = self.m_GuideIdNow
        if guideId == 0 then
            local config = BackgroundConfig:GetConfigByMapId(mapId)
            if config then
                guideId = config["guide_id"]
            end
        end
        local inf = GuideConfig:GetGuideInfByID(guideId)
        if inf then
           local isReal = self:InsertGuide(guideId, true)
	        if isReal and inf.closeSplashType then
	            isCloseSplash = false
	        end
        else
            self.m_GuideIdNow = -1
        end
    else
        if self.m_ListGuideState then
            local maxId = self:MaxId()
            local inf = GuideConfig:GetGuideInfByID(maxId)
            if inf and not inf["not_max"] then
                self:CtrlMainUI(inf, 0)
            end
        else
            Log.Error("###### why guide nil ????")
        end
    end
    EventMgr:Dispatch(EventID.GUIDE_INIT, mapId)
    return isCloseSplash
end

function GuideController:MaxId()
    local maxId = 0
    for k, v in pairs(self.m_ListGuideState) do
        --why nil ?
		local config = GuideConfig:GetGuideInfByID(tonumber(k))
		if config and (not config.isTinyGame or config.isTinyGame ~= 3) then 
	        if tonumber(k) > maxId then
	            maxId = tonumber(k)
	        end
		end
    end
    return maxId
end

function GuideController:IsMaxId(id)
	for k, v in pairs(self.m_ListGuideState) do
		if tonumber(k) >= id then
		 	return true
		end
	end
	return false
end

function GuideController:InsertGuide(guideId, isFromGameStart, param1, param2)
    local tbParam = { [1] = guideId, [2] = isFromGameStart, [3] = param1, [4] = param2 }
    --if guideId == 26 or guideId == 27 then
    --    Log.Info("Y")
    --end
    local ret = self:IsGuideCanShow(guideId)
    if ret < 0 then
        if ret ~= -1 then
            self:SetGuideDone(guideId, math.abs(ret))
        end
        Log.Info("======IgnoreInsertGuide:", guideId, ret)
		return false
    else
        Log.Info("======InsertGuide:", guideId)
        NetPushViewData:PushView(PushDefine.Guide, tbParam)
		return true
    end
end

function GuideController:ShowGuide(guideId, isFromGameStart, param1, param2)
    if not Enable_Guide then
        return
    end
    local ret = self:IsGuideCanShow(guideId)
    if ret < 0 then
		self:CloseGuide(true)
        Log.Info("======IgnoreShowGuide:", guideId, ret)
        return
    end
    Log.Info("======ShowGuide:", guideId)
    --if guideId == 26 or guideId == 27 then
    --    Log.Info("Y")
    --end
    local inf = GuideConfig:GetGuideInfByID(guideId)
    if not inf then
        return
    end
	if inf.map_id and next(inf.map_id) then
		local inList = false
		for index, mapId in ipairs(inf.map_id) do
			if mapId == MapController.m_MapId then
				inList = true
				break
			end
		end
		if not inList then
			self:CloseGuide()
			return
		end
	end

    local function onShowGuide()
        self:DoCommand(0, inf)
        if UIMgr:ViewIsShow(UIDefine.UI_Guide) then
            self:DoGuide(inf, isFromGameStart, param1, param2)
        else
            UI_SHOW(UIDefine.UI_Guide, self, isFromGameStart, param1, param2)
        end
    end
	
	self:CheckCameraLock(guideId)
	
    self.m_GuideIdNow = guideId
    self.m_GuideInfNow = inf

    if isFromGameStart then
        if inf.jumpId then
            if inf.jumpId > 0 then
                self:ShowGuide(inf.jumpId, false)
                return
            elseif inf.jumpId == -1 then
                self:CtrlMainUI(inf)
                self:CloseGuide(true)
                return
            end
        end
        onShowGuide()
    else
        if inf.delay > 0 then
            local function onDelayDone()
                UIMgr:SetUILock(false, "guide_delay")
                onShowGuide()
            end
            TimeMgr:CreateTimer(self, onDelayDone, inf.delay, 1)
            UIMgr:SetUILock(true, "guide_delay")
        else
            onShowGuide()
        end
    end
end

function GuideController:CloseGuide(isJumpClose)

	
	-- UI_SHOW(UIDefine.UI_Hint, nil)--hint
	MapController:SetTouchLockState(0)
	if not self:IsTinyGameNotMap(GuideConfig:GetGuideInfByID(self.m_GuideIdNow))  then
	    self.m_BlockWall:SetGrids()
		self.m_BlockCircle:SetGrids()
		if self.m_GuideInfNow then
	    	MapController:ForceDisappearMarkArrow("for_tutorial", self.m_GuideInfNow.arrowCloseDelay)
		end
	end
	local tempId = self.m_GuideIdNow
    self.m_GuideIdNow = -1
    self.m_GuideInfNow = nil
    UI_CLOSE(UIDefine.UI_Guide)
    NetPushViewData:RemoveViewByIndex(PushDefine.Guide)
    NetPushViewData:CheckOtherView(true)

    if isJumpClose then
        MapController:StartCamera()
    end
	
	
	--引导结束后，检测是否在小游戏里，避免引导结束 调用ui_mainface的图标回屏幕
	if (Game.IsTinyGame or Game.IsNativeTinyGame) and TinyGameMgr:GetIsInTinyGame() == true then
		MapController:TinyGameSwitch(true)
	end
	--- 小游戏引导id + 有小游戏，就执行保存
	--local info = GuideConfig:GetGuideInfByID(tonumber(tempId))
	--if info and (info.isTinyGame == 1 or info.isTinyGame == 3) and TinyGameMgr:GetTinyGameID() > 0 and (TinyGameMgr:CheckNeedVersion() == true) then
		--Game.Save(1)
		----Log.Info(" *** CloseGuide() -> Game.Save(1)")
	--end
	
end

function GuideController:AfterUIOpen(uiObj, isFromStart, param1, param2)
    self:DoGuide(self.m_GuideInfNow, isFromStart, param1, param2)
end

function GuideController:IsTinyGameNotMap(inf)
	if not inf then 
		inf = GuideConfig:GetGuideInfByID(self.m_GuideIdNow)
	end
	if  inf.isTinyGame and inf.isTinyGame == 3 then 
		return true
	end
	return false
end

function GuideController:DoGuide(inf, isFromGameStart, param1, param2)
    self:DoCommand(1, inf)
	
	if not self:IsTinyGameNotMap(inf)  then
		self.m_BlockWall:SetGrids(inf.hollow, inf.cameraPos)
	end
	
	if not self:IsTinyGameNotMap(inf) then
		self.m_BlockCircle:SetGrids(inf.circleHollowInfo, inf.cameraPos)
	end

    local limitGrids = nil
    local lockInf = inf.mapLock
    if lockInf.state == 3 then
        if type(inf.hollow) == "table" then
            limitGrids = inf.hollow
        end

    elseif lockInf.state >= 4 and lockInf.state <= 5 then
        limitGrids = inf.mapLimit
        for k, v in pairs(limitGrids) do
            if v.id then
                local gx, gy = self:GetCacheItemGrid(v.id)
                v.GridX = gx
                v.GridY = gy
            end
        end
    end
	if inf.ClearUI == 1 then 
	 	NetPushViewData:ClearUIMainFace()
	elseif inf.ClearUI == 2 then
	 	NetPushViewData:ClearUIMainFace(false)
	elseif inf.ClearUI == 3 then
		 NetPushViewData:ClearUIMainFace(true,3)
	end
	--if not self:IsTinyGameNotMap(inf)  then
  		MapController:SetTouchLockState(lockInf.state, limitGrids, lockInf.passId)
	--end
    if inf.zoom then
        if inf.zoom >= 0 then
            if isFromGameStart then
                MapController:SetCameraSizeAnim(inf.zoom, nil)
            else
                MapController:SetCameraSizeAnim(inf.zoom, 1)
            end
        elseif inf.zoom == -1 then
            MapController:PlayCameraAnim(inf.zoom)
        end
    end

    self:CtrlMainUI(inf)

    self:SetHeroAction(inf)

    if inf.itemNew then
        for k, v in pairs(inf.itemNew) do
            if v.type == 0 then
                local item = MapController:GetItemByGridUnsafe(v.gx, v.gy)
                if item then
                    if item.m_Id ~= v.id then
                        item:destroyNode()
                        MapController:CreateItemById(v.gx, v.gy, v.id, ItemFromWhere.Guide)
                    end
                else
                    MapController:CreateItemById(v.gx, v.gy, v.id, ItemFromWhere.Guide)
                end

            elseif v.type == 1 then
                local list, count = MapController:GetItemById(v.id, 1)
                if count > 0 then
                    local item = list[1]
                    local gx = item.m_GridX
                    local gy = item.m_GridY
                    item:destroyNode()
                    MapController:CreateItemById(gx, gy, v.id2, ItemFromWhere.Guide)
                end

            elseif v.type == 2 then
                local gx, gy = self:GetCacheItemGrid(v.pid)
                if gx and gy then
                    MapController:CreateItemById(gx + v.gx, gy + v.gy, v.id, ItemFromWhere.Guide)
                end
            end
        end
    end

    self:LightUpGrids(inf, true)
    self:OrderGrids(inf)

    if inf.findList then
        local itemCameTo = nil
        for k, v in pairs(inf.findList) do
            if not itemCameTo then
                itemCameTo = v
            end
           table.insert(self.MarkList, MapController:MarkItem(v, inf.findArrow > 0))
        end
        if itemCameTo then
            MapController:FindItemById(itemCameTo)
        end
    end

    local function onDoUI()
        UI_UPDATE(UIDefine.UI_Guide, 14, inf)--tip

        UI_UPDATE(UIDefine.UI_Guide, 1, inf)--tip

        UI_UPDATE(UIDefine.UI_Guide, 2, inf)--finger

        UI_UPDATE(UIDefine.UI_Guide, 3, inf)--arrow

        UI_UPDATE(UIDefine.UI_Guide, 4, inf)--hint

        UI_UPDATE(UIDefine.UI_Guide, 5, inf)--mask

        UI_UPDATE(UIDefine.UI_Guide, 6, inf)--ui top

        UI_UPDATE(UIDefine.UI_Guide, 7, inf)--ui skip
		
		UI_UPDATE(UIDefine.UI_Guide, 12, inf)--ui skip

        --UI_SHOW(UIDefine.UI_Hint, inf.hintInf)--hint
		if inf.cartoon  then
		 	if inf.cartoon.type == 5 and inf.zoom and inf.zoom >= 0 then
				local function OnZoomDone()
					UI_UPDATE(UIDefine.UI_Guide, 11, inf.cartoon)
				end
				TimeMgr:CreateTimer(self, OnZoomDone, 0.5, 1)
			elseif inf.cartoon.type == 3 then
				UI_UPDATE(UIDefine.UI_Guide, 11, inf.cartoon)
			end
		end
        if inf.stepInf then
            if inf.stepInf.typeId == 0 then
                self:StepGuide(inf.id)
            end
        else
            self:StepGuide(inf.id)
        end
    end
    if inf.cameraPos then
        MapController:SetMoveCameraToPos(inf.cameraPos, onDoUI, true, "guide")
    else
        onDoUI()
    end

	--if v2n(inf.dot) == v2n(SpecialId.MoveHero) then --and inf.stepInf.param2 and inf.stepInf.param3
		--local titleObj = MapController:GetTileByGridUnsafe(inf.stepInf.param2,inf.stepInf.param3)
		--if titleObj then
			--titleObj:UpdateLightState(true)
		--end
	--end
	
    self:PlayGuideCartoon(inf.cartoon)
end

function GuideController:LightUpGrids(inf, isLight)
    if inf.lightUp then
        for k, v in pairs(inf.lightUp) do
            local item = MapController:GetItemByGridUnsafe(v.gx, v.gy)
            if item then
                if v.id then
                    if v.id == item.m_Id then
                        item:setEffBottomLightUp(isLight, true)
                    end
                else
                    item:setEffBottomLightUp(isLight, true)
                end
            end
        end
    end
end

function GuideController:OrderGrids(inf)
    if inf.orderChange then
        for k, v in pairs(inf.orderChange) do
            local item = MapController:GetItemByGridUnsafe(v.gx, v.gy)
            if item then
                local sortLayerId = SortingLayerInGame[v.orderName]
                item:setOrderLayer(sortLayerId, v.orderIdx, true)
            end
        end
    end
end

function GuideController:PlayGuideCartoon(cInf)
    if not cInf then
        return
    end

    if cInf.type == 0 then
        --ui
        UI_SHOW(cInf.path)
        Log.Info("****** show cartoon:", cInf.path)

    elseif cInf.type == 1 then
        --3d
        local function onObjLoaded(prefab)
            local go, trans = CreateGOAndTrans(prefab)
            trans:SetParent(MapController.m_TransFly)
            trans:SetLocalScale(1, 1, 1)
            if cInf.pid then
                local pos = self:GetCacheItemPosV3(cInf.pid)
                if pos then
                    trans:SetLocalPosition(pos.x, pos.y, pos.z)
                end
            else
                trans:SetLocalPosition(cInf.x, cInf.y, cInf.z)
            end
            self.m_GoCartoon3d = go
            --   MapController:HideItem(40, 53, true)
        end
        ResMgr:LoadAssetAsync(cInf.path, AssetDefine.LoadType.Instant, onObjLoaded)
    elseif cInf.type == 2 then
        local function onTalkState(state)
            self:StepGuide(self.m_GuideIdNow)
        end
        local log = NetPushViewData:SplitProcess(cInf.talk)
        UI_SHOW(UIDefine.UI_DialogView, nil, log, onTalkState)
        return
	elseif cInf.type == 3  then
		--UI_UPDATE(UIDefine.UI_Guide, 11, cInf)
        UIMgr:SetUILock(true,"UI_Guide")
		--MapController:SetTouchLockState(1)
		return
	elseif cInf.type == 5  then
		return
	elseif cInf.type == 4 then
		local function Cartoon4()
			self:StepGuide(self.m_GuideInfNow.id)
		end
		UI_SHOW(cInf.path,nil,Cartoon4)
		return
    end

    UIMgr:SetUILock(true, "guide_anim")
    local function onUnLock()
        UIMgr:SetUILock(false, "guide_anim")

        if cInf.type == 0 then
            --ui
            UI_CLOSE(cInf.path)

        elseif cInf.type == 1 then
            --3d
            UEGO.Destroy(self.m_GoCartoon3d)
            self.m_GoCartoon3d = nil
            --  MapController:HideItem(40, 53, false)
        end

        self:StepGuide(self.m_GuideIdNow)
    end
    TimeMgr:CreateTimer(self, onUnLock, cInf.duration, 1)
end

function GuideController:SkipGuide()
    local guideInf = self.m_GuideInfNow
    self:LightUpGrids(guideInf, false)

    local ids = guideInf.skipInf.skipIds
    for k, v in pairs(ids) do
        local id = tonumber(v)
        self:SetGuideDone(id, 999)

        local inf = GuideConfig:GetGuideInfByID(id)
        if inf then
            local dot = inf.dot
            if dot > 0 then
                NetNotification:NotifyStepDot(dot)
            end
            self:DoCommand(2, inf)
        end
    end

    local infUi = GuideConfig:GetGuideInfByID(guideInf.skipInf.uiStateId)
    if infUi then
        self:CtrlMainUI(infUi)
    end

    self:CloseGuide()
end

function GuideController:GetGuideState()
	if nil ~= self.m_ListGuideState then
		return self.m_ListGuideState
	end
end

function GuideController:StepGuide(id, param1, param2)
    if self.m_GuideIdNow == id then
        self:SetGuideDone(id, 1)

        if id == SpecialId.InitFreeBoxGuideId then
            NetInfoData:SetDataMess("FreeBoxInit", true)
        end
        TriggerGiftConfig:CheckOpenGuide(id)

        local guideInf = self.m_GuideInfNow

        local dot = guideInf.dot
        if dot > 0 then
            NetNotification:NotifyStepDot(dot)
        end
		Log.Info("GuideController__ guide_id:" .. id)
		local thinkTable = {["guide_id"] = id}
		SdkHelper:ThinkingTrackEvent(ThinkingKey.guide,thinkTable)

        self:LightUpGrids(guideInf, false)

        self:DoCommand(2, guideInf)

        local nextId = guideInf.nextId
        if nextId == -1 then
            self:CloseGuide()
        else
            if nextId == 0 then
                nextId = id + 1
            end
            self:ShowGuide(nextId, false, param1, param2)
        end
    end
end

function GuideController:EventGuide(typeId, param1, param2, param3, param4, param5)
    local guideInf = self.m_GuideInfNow
    if not guideInf then
        return
    end

    local stepInf = guideInf.stepInf
    if stepInf.typeId ~= typeId then
        return
    end

    --Click right object
    if typeId == 1 then
        if stepInf.param1 == param1 then
            local isRight = false
            if param1 == 1 then
                --TouchedItem
                if stepInf.param2 then
                    if param2 and stepInf.param2 == param2.m_Id then
                        --same item id
                        isRight = true
                    end
                else
                    isRight = true
                end
            else
                isRight = true
            end
            if isRight then
                self:StepGuide(guideInf.id)
            end
        end

        --new right item
    elseif typeId == 2 then
        if param1 == nil then
            return
        end
        for i = 1, 5 do
            local paramStep = stepInf["param" .. i]
            if paramStep then
                if paramStep == param1 then
                    self:CacheGuideItem(guideInf.id, param2)
                    self:StepGuide(guideInf.id, param2)
                    break
                end
            else
                break
            end
        end

        --tutorial ui clicked
    elseif typeId == 3 or typeId == 4 or typeId == 5 or typeId == 6 or typeId == 8  or typeId == 17 or typeId == 102 or typeId == 103 then
        self:StepGuide(guideInf.id)

    elseif typeId == 7 then
        if param1 == nil then
            return
        end
        if stepInf.param1 == param1.m_Id and stepInf.param2 == param2 and stepInf.param3 == param3 then
			------动画------
			if param1.m_Type == ItemUseType.HeroFly then
				--SetActive(param1.m_TransSpinePa,false)
				--DOLocalMoveY(param1.m_TransSpinePa.transform,1.5,0,function()
					--SetActive(param1.m_TransSpinePa,true)
					--DOLocalMoveY(param1.m_TransSpinePa.transform,0,0.8,function()
								MapController:PlayEffectAtGrid(param2,param3,2)
								--UI_UPDATE(UIDefine.UI_Guide, 10)
								DOScale(param1.m_TransSpinePa.transform, Vector3.New(0.5,0.5,0.5), 0.3, function ()
										DOScale(param1.m_TransSpinePa.transform, Vector3.New(0.4,0.4,0.4), 0.3, function ()
												
											local titleObj = MapController:GetTileByGridUnsafe(param2,param3)
											if titleObj then
												titleObj:UpdateLightState(false)
											end	
											self:StepGuide(guideInf.id,param2,param3)
										end, Ease.OutBack)
									end, Ease.OutBack)
						--end,Ease.InOutCirc)
				--end,Ease.Linear)
			-----------------
			else
				self:StepGuide(guideInf.id)
			end
        end
    elseif typeId == 9 or typeId == 21 then
        if string.equals(stepInf.param1, param1) then
            self:StepGuide(guideInf.id)
        end

    elseif typeId == 10 or typeId == 16 or typeId == 18 or typeId == 101  then
        if stepInf.param1 == param1 and stepInf.param2 == param2 then
            self:StepGuide(guideInf.id)
        end

    elseif typeId == 11 then
        if stepInf.param1 == param1 then
            self:StepGuide(guideInf.id)
        end

    elseif typeId == 12 or typeId == 13 or typeId == 15 or typeId == 19 or typeId == 20 then
        if param1 and param1 == stepInf.param1 then
            self:StepGuide(guideInf.id)
        end
	elseif typeId == 14  then
			self:StepGuide(guideInf.id)
	elseif typeId == 22 then
		if stepInf.param1 == param1 and  stepInf.param2 == param2 then
			self:StepGuide(guideInf.id)
		end
    end
end

function GuideController:TriggerGuide(triggerType, param1, param2,param3)
    if triggerType == nil then
        return
    end
    if triggerType == -1 then

        -- Log.Info("==C== ", self.m_TriggerWaitingInf)
        if not self.m_TriggerWaitingInf then
            return
        end
        for i, v in ipairs(self.m_TriggerWaitingInf) do
            self:TriggerGuide(v[1], v[2], v[3])
        end
        return
    end

    --if NetPushViewData:IsPlaying() then
    --    self:TriggerGuideWaiting(triggerType, param1, param2)
    --    return
    --end

    local guideId = self:GetTriggerGuideId(triggerType, param1, param2,param3)
    if guideId == -1 then
        return
    end
    self:InsertGuide(guideId, false)
	----移除
	--local temp = self.m_TriggerWaitingInf
	--for k, v in pairs(temp) do
		--if v[1] == triggerType and v[2] == param1 and v[3] == param2 then
			--table.remove(temp,k)
			--break
		--end
	--end
	--self.m_TriggerWaitingInf = temp
end

function GuideController:GetTriggerGuideId(triggerType, param1, param2,param3)
    if not triggerType or not param1 then
        return -1
    end

    local inf = GuideConfig:GetGuideTriggerMap(tostring(triggerType))
    if not inf then
        return -1
    end

    param1 = tostring(param1)
    for judgeStr, guideId in pairs(inf) do
        if not self:IsGuideDone(guideId) then
            if triggerType == 3 then
                if tonumber(judgeStr) == tonumber(param1) then
                    return guideId
                end

            elseif triggerType == 5 or triggerType == 11 or triggerType == 20 then
                local arr = string.split(judgeStr, '|')
                if arr[1] == param1 and arr[2] == tostring(param2) then
                    return guideId
                end

            elseif triggerType == 6 then
                local arr = string.split(judgeStr, '|')
                for _, vId in ipairs(arr) do
                    if vId == param1 then
                        return guideId
                    end
                end
			elseif triggerType == 21 then
				local arr = string.split(judgeStr, '|')
				if arr[1] == param1 then
					if arr[2] and arr[3] then
						if arr[2] == tostring(param2) and arr[3] == tostring(param3) then
							return guideId
						end
					end
				end
			elseif triggerType == 22 then
				local arr = string.split(judgeStr, '|')
                if arr[1] == param1 and arr[2] == tostring(param2) then
                    return guideId
                end
            elseif judgeStr == param1 then
                return guideId
            end
        end
    end
    return -1
end

function GuideController:CtrlMainUI(inf, fromType)
    if fromType and fromType == 0 then
        if inf.mainUINoRestore == 0 then
            UIMgr:RefreshAllMainFace(8, nil, inf.mainUIFlags)
        elseif inf.mainUINoRestore > 0 then
            local newInf = GuideConfig:GetGuideInfByID(inf.mainUINoRestore)
            if newInf then
                UIMgr:RefreshAllMainFace(8, nil, newInf.mainUIFlags)
            end
        end
    else
        UIMgr:RefreshAllMainFace(8, nil, inf.mainUIFlags)
    end
end

function GuideController:TriggerGuideWaiting(triggerType, param1, param2)
    Log.Info("====== trigger_w:", triggerType, param1, param2)
    self.m_TriggerWaitingInf = self.m_TriggerWaitingInf or {}
    table.insert(self.m_TriggerWaitingInf, { triggerType, param1, param2 })
end

function GuideController:SetGuideDone(id, type)
    self.m_ListGuideState[tostring(id)] = type
	
	for i, v in ipairs(self.MarkList) do
		MapController:ForceDisappearMarkArrow(v)
	end

	local guidInfo = GuideConfig:GetGuideInfByID(id)
	if guidInfo.map_id and #guidInfo.map_id > 1 then
		NetGlobalData.data.multMapGuideIds[v2s(id)] = 1
	end
end

function GuideController:IsGuideDone(id)
    if not Enable_Guide then
        return true
    end
    local isDone = self.m_ListGuideState[tostring(id)]
    if isDone and isDone > 0 then
        return true
    end

	if NetGlobalData.data.multMapGuideIds[v2s(id)] == 1 then
		return true
	end

    return false
end

function GuideController:IsGuideLimit(id, data)
    if not data then
        data = GuideConfig:GetGuideInfByID(tonumber(id))
        if not data then
            return -2
        end
    end
    if data.limitLv > 0 then
        local type = math.floor(data.limitLv / 1000)
        local lv = data.limitLv % 1000
        if type == 1 and lv < NetUpdatePlayerData:GetLevel() then
            return -3

        elseif type == 2 and lv < NetUpdatePlayerData:GetZOOLevel() then
            return -4

        --elseif type == 3 then
            --local portObj = MapController.m_MapPortFly["70000"]
            --if portObj and lv < portObj.portLevel then
                --return -5
            --end
        end
    end
	
	--- 小游戏引导id + 无小游戏，返回-5
	local info = GuideConfig:GetGuideInfByID(tonumber(id))
	if (info.isTinyGame == 1 or info.isTinyGame == 3)
		and (TinyGameMgr:GetTinyGameID() <= 0 or TinyGameMgr:CheckNeedVersion() == false)
	then
		--Log.Info(" *** IsGuideLimit() ->  return -5")
		return -5
	end
	
    return 0
end

function GuideController:IsGuideCanShow(id)
    if self:IsGuideDone(id) then
        return -1
    end
    return self:IsGuideLimit(id)
end

function GuideController:IsGuideValid()
    if self.m_GuideIdNow then
        return (self.m_GuideIdNow < 10000 and self.m_GuideIdNow >= 0)
    end
    return false
end

function GuideController:IsGuiding()
    if self.m_GuideInfNow then
        return true
    end
    return false
end

function GuideController:CacheGuideItem(guideId, itemObj)
    if itemObj then
        self.m_CacheGuideItem[tostring(guideId)] = itemObj
    end
end

function GuideController:GetCacheItemPosV3(guideId)
    if not guideId then
        return nil
    end
    local itemObj = self.m_CacheGuideItem[tostring(guideId)]
    if itemObj then
        return itemObj:getPositionVt3()
    end
    Log.Error("###### guide item not find ", guideId)
    return nil
end

function GuideController:GetCacheItemGrid(guideId)
    if not guideId then
        return nil
    end
    local itemObj = self.m_CacheGuideItem[tostring(guideId)]
    if itemObj then
        return itemObj.m_GridX, itemObj.m_GridY
    end
    return nil, nil
end

function GuideController:SetHeroAction(guideInf)
    if not guideInf.heroCtrlArr then
        return
    end
    for i, v in ipairs(guideInf.heroCtrlArr) do
        if v then
            HeroController:SetHeroMovingState(v.id, v.state, v.pos)
        end
    end
end

function GuideController:onTouchMoveItem(state, objItem)
    local guideInf = self.m_GuideInfNow
    if not guideInf then
        return
    end
    if guideInf.fingerInfo then
        if state == 0 then
            UI_UPDATE(UIDefine.UI_Guide, 10, guideInf)
        else
            UI_UPDATE(UIDefine.UI_Guide, 2, guideInf)
        end
    end
end

function GuideController:DoCommand(state, inf)
    if not inf or not inf.command then
        return
    end
    local cmd = inf.command[tostring(state)]
    if cmd then
        DebugConsole(cmd[1], cmd[2], cmd[3])
    end
end

-- 锁定摄像机，禁止移动
function GuideController:CheckCameraLock(guideID)
	--local cfg = ConfigMgr:GetDataByID(ConfigDefine.ID.global_setting, 1109)
	--if cfg then
		--local iLockCameraGuideID = v2n(cfg.value)
		--isLock = (iLockCameraGuideID and (iLockCameraGuideID > guideID)) or false
	--else
		--isLock = false
	--end
	
	--local isLock = false
	--if not self:IsCanMoveInGuide() then
		--isLock = true
	--end
	----Log.Error("isLock",isLock)
	--MapController:SetCameraMoveLock(isLock)
end
-----引导多少之前不能缩放
function GuideController:IsCanMoveInGuide()
	do
		return true
	end
	if NetUpdatePlayerData.playerInfo.curMap ~= MAP_ID_MAIN then
		return true
	end
	local cfg = ConfigMgr:GetDataByID(ConfigDefine.ID.global_setting, 1109)
	if self:IsMaxId(v2n(cfg.value)) then
		return true
	else
		return false
	end
end

function GuideController:GetCurGuide()
	return self.m_GuideIdNow
end

function GuideController:GetCurGuideInf()
	return self.m_GuideInfNow
end

return GuideController