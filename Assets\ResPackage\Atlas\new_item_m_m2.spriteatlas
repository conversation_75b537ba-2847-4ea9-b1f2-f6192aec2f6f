%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!687078895 &4343727234628468602
SpriteAtlas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: new_item_m_m2
  m_EditorData:
    serializedVersion: 2
    textureSettings:
      serializedVersion: 2
      anisoLevel: 0
      compressionQuality: 0
      maxTextureSize: 0
      textureCompression: 0
      filterMode: 1
      generateMipMaps: 0
      readable: 0
      crunchedCompression: 0
      sRGB: 1
    platformSettings:
    - serializedVersion: 3
      m_BuildTarget: DefaultTexturePlatform
      m_MaxTextureSize: 2048
      m_ResizeAlgorithm: 0
      m_TextureFormat: 50
      m_TextureCompression: 1
      m_CompressionQuality: 50
      m_CrunchedCompression: 0
      m_AllowsAlphaSplitting: 0
      m_Overridden: 0
      m_IgnorePlatformSupport: 0
      m_AndroidETC2FallbackOverride: 0
      m_ForceMaximumCompressionQuality_BC6H_BC7: 0
    packingSettings:
      serializedVersion: 2
      padding: 2
      blockOffset: 0
      allowAlphaSplitting: 0
      enableRotation: 0
      enableTightPacking: 0
      enableAlphaDilation: 0
    secondaryTextureSettings: {}
    variantMultiplier: 1
    packables:
    - {fileID: 2800000, guid: 0c02da90b46bd7b43aa2f7a9fc633a31, type: 3}
    - {fileID: 2800000, guid: a45cd1d5a35bd8f428a4af4898a31638, type: 3}
    - {fileID: 2800000, guid: 9d4cea9db2e860b4cbcf9d5bb18d31d2, type: 3}
    - {fileID: 2800000, guid: e4d7d9db9f715b14caf57181cf61d83f, type: 3}
    - {fileID: 2800000, guid: b77e98ad20d6f2c46b2767746eed998a, type: 3}
    - {fileID: 2800000, guid: 04c07438a175a464a92a35b23eb86b1f, type: 3}
    - {fileID: 2800000, guid: 2fb8baaac424d29468d124fdc80e05ca, type: 3}
    - {fileID: 2800000, guid: 9df35b5ea1191ae44baacc58204a37c5, type: 3}
    bindAsDefault: 0
    isAtlasV2: 0
    cachedData: {fileID: 0}
    packedSpriteRenderDataKeys: []
  m_MasterAtlas: {fileID: 0}
  m_PackedSprites: []
  m_PackedSpriteNamesToIndex: []
  m_RenderDataMap: {}
  m_Tag: new_item_m_m2
  m_IsVariant: 0
