local UI_UnionActivity = Class(BaseView)

local ItemBase = require("UI.Common.BaseSlideItem")
local Item = Class(ItemBase)

local BagBase = require("UI.BagItem")
local BagItem = Class(BagBase)

local OpenBowlingTipFun;

function UI_UnionActivity:OnInit()
    EventMgr:Add(EventID.CLOSE_CHILD_WINDOW, self.Close, self);
    EventMgr:Add(EventID.REFRESH_UNION_BOSS, self.OnRefreshBoss, self);
    self.deltaTime = 0;
    self.itemList = {};
end

function UI_UnionActivity:OnCreate(param)
    self.actList = ConfigMgr:GetData(ConfigDefine.ID.union_activity);
    table.sort(self.actList, function(a, b)
        return a.sort < b.sort;
    end)
    
    for i = 1, #self.actList do
        local item = self.itemList[i];
        if not item then
            item = Item.new()
            item:Init(UEGO.Instantiate(self.ui.m_goActItem.transform))
            table.insert(self.itemList, item);
        end
    end

    self.slider = require("UI.Common.SlideRect").new()
    self.slider:Init(self.ui.m_scrollview, 2)
    self.slider:SetItems(self.itemList, 0, Vector2.New(0, 0))
    
    self:SetIsUpdateTick(true);
    self:ResetUI();
    
    OpenBowlingTipFun = function(obj, showType, state)
        self:OnBowlingTip(obj, showType, state);
    end
end

function UI_UnionActivity:OnRefresh(param)
    
end

function UI_UnionActivity:onDestroy()
    EventMgr:Remove(EventID.CLOSE_CHILD_WINDOW, self.Close, self);
    EventMgr:Remove(EventID.REFRESH_UNION_BOSS, self.OnRefreshBoss, self);
end

function UI_UnionActivity:onUIEventClick(go,param)
    local name = go.name;
    if name == "m_goBowlingTip" then
        SetActive(self.ui.m_goBowlingTip, false);
    end
end

function UI_UnionActivity:TickUI(deltaTime)
    self.deltaTime = self.deltaTime + deltaTime;
    if self.deltaTime < 1 then return end
    self.deltaTime = self.deltaTime - 1;
    
    local needResetUI = false
    if self.slider ~= nil and self.slider.itemCount > 0 then
        for i = 1, self.slider.itemCount do
            local item = self.slider.items[i]
            if item.enable then
                if (item.OnTick ~= nil and item:OnTick()) or item:OnCheckActState() then
                    needResetUI = true
                end
                if item.OnTimer then
                    item:OnTimer();
                end
            end
        end
    end

    if needResetUI then
        self:ResetUI()
    end
end

function UI_UnionActivity:ResetUI()
    self.slider:SetData(self.actList);
    LeagueManager:OnRequestBossLoad(); -- 加载Boss信息
end

function UI_UnionActivity:OnRefreshBoss()
    for i = 1, #self.itemList do
        local item = self.itemList[i];
        if item.data.id == 2 then
            item:OnUpdateBoss();
            break;
        end
    end
end

function UI_UnionActivity:OnBowlingTip(obj, showType, state)
    local rankBg = GetChild(self.ui.m_goBowlingTip, "rankBg");
    local joinBg = GetChild(self.ui.m_goBowlingTip, "joinBg");
    if showType == 1 then
        local titleTxt = GetChild(rankBg, "titleBg/titleTxt", UEUI.Text);
        local rankTxt = GetChild(rankBg, "rankBg/rankTxt", UEUI.Text);
        local topTxt = GetChild(rankBg, "topBg/topTxt", UEUI.Text);
        local numTxt = GetChild(rankBg, "numBg/numTxt", UEUI.Text);
        
        titleTxt.text = state == 1 and LangMgr:GetLang(70000575) or LangMgr:GetLang(70000576);
        rankTxt.text = BowlingBattleManager:GetMyLeagueRank();
        topTxt.text = BowlingBattleManager:GetRankInLeague();
        numTxt.text = BowlingBattleManager:GetBowlingCount();
    end
    SetActive(rankBg, showType == 1);
    SetActive(joinBg, showType == 2);
    
    local showObj = showType == 1 and rankBg or joinBg;
    local pos = self.uiGameObject.transform:InverseTransformPoint(obj.transform.position);
    showObj.transform.localPosition = Vector3(pos.x - 465, pos.y, 0);
    SetActive(self.ui.m_goBowlingTip, true);
end


-------------------------------- BaseSlideItem --------------------------------
---
function Item:OnInit(transform)
    self.transform = transform.transform
    self.bg = GetChild(self.transform, "bg", UEUI.Image);
    self.tipBtn = GetChild(self.transform, "tipBtn");
    self.titleTxt = GetChild(self.transform, "titleTxt", UEUI.Text);
    self.stateTxt = GetChild(self.transform, "stateTxt", UEUI.Text);
    self.tipTxt = GetChild(self.transform, "tipTxt", UEUI.Text);
    self.rewardObj = GetChild(self.transform, "rewardObj");
    self.bowlingObj = GetChild(self.transform, "bowlingObj");
    self.redDot = GetChild(self.transform, "redDot");
    
    self.rewardList = {};
end

function Item:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function Item:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function Item:UpdateData(data, index)
    self.data = data;
    
    self.titleTxt.text = LangMgr:GetLang(data.name);
    
    local list = Split1(data.rewards, "|");
    local count = #list;
    local num = #self.rewardList;
    local len = count > num and count or num;
    for i = 1, len do
        local bagItem = self.rewardList[i];
        if not bagItem then
            bagItem = BagItem.new();
            bagItem:Create(self.rewardObj);
            bagItem:SetScale(0.71, 0.71);
            table.insert(self.rewardList, bagItem);
        end
        SetActive(bagItem.go, i <= count);

        if i <= count then
            bagItem:UpdateInfo(BagItemModule.new({ id = v2n(list[i]) }));
            bagItem:SetNumTxt("");
        end
    end
    
    self.tipTxt.text = "";
    SetActive(self.bowlingObj, data.id == 1);
    
    if data.id == 1 then
        self:OnUpdateBowling();
    elseif data.id == 2 then
        self:OnUpdateBoss();
    end

    RemoveUIComponentEventCallback(self.tipBtn, UEUI.Button);
    AddUIComponentEventCallback(self.tipBtn, UEUI.Button, function()
        if data.id == 1 then
            UI_SHOW(UIDefine.UI_BowlingBattleHelp);
        else
            UI_SHOW(UIDefine.UI_UnionBossRule, LangMgr:GetLang(70000380));
        end
    end)
end

function Item:OnUpdateBowling()
    SetImageSprite(self.bg, self.data.pic, false, function()
        SetActive(self.bg, true);
    end);
    
    local activity = LimitActivityController:GetActive(self.data.total_type);
    local state = activity.info.state
    local isJoin = BowlingBattleManager:IsJoin()
    local hasReward = (not BowlingBattleManager:IsMemberRankRewardReceived()) or (not BowlingBattleManager:IsLeagueRankRewardReceived()) or (not BowlingBattleManager:IsPlayerRankRewardReceived())
    if state == 0 then
        state = 2
    elseif state == 1 then
        if BowlingBattleManager:IsActivityEnd() then
            state = (isJoin and hasReward) and 4 or 2;
        end
    end
    
    local rankBtn = GetChild(self.bowlingObj, "rankBtn");
    local rewardBg = GetChild(self.bowlingObj, "rewardBg");
    SetActive(rankBtn, state == 1 or state == 3);
    SetActive(rewardBg, state == 4);
    
    self.OnTick = nil
    local btnTxt = GetChild(rankBtn, "btnTxt",UEUI.Text);
    if state == 1 then
        if isJoin then
            local count = BowlingBattleManager:GetNeedBowlingCntNextReward()
            self.stateTxt.text = LangMgr:GetLang(9285) .. count .. LangMgr:GetLang(9504);

            btnTxt.text = LangMgr:GetLang(70000575);

            self.OnTick = function()
                return activity:GetRemainingTime() <= 0 and BowlingBattleManager:IsActivityEnd();
            end
        else
            self.stateTxt.text = LangMgr:GetLang(70000301);
            btnTxt.text = LangMgr:GetLang(9311);
        end

        RemoveUIComponentEventCallback(rankBtn, UEUI.Button);
        AddUIComponentEventCallback(rankBtn, UEUI.Button, function()
            local showType = isJoin and 1 or 2;
            OpenBowlingTipFun(rankBtn, showType, state);
        end)
    elseif state == 3 then
        btnTxt.text = LangMgr:GetLang(70000576);

        RemoveUIComponentEventCallback(rankBtn, UEUI.Button);
        AddUIComponentEventCallback(rankBtn, UEUI.Button, function()
            OpenBowlingTipFun(rankBtn, 1, state);
        end)

        self.OnTick = function()
            local remainTime = activity:GetStartRemainingTime()
            self.stateTxt.text = LangMgr:GetLangFormat(70000302, TimeMgr:ConverSecondToString(remainTime));
            return remainTime <= 0;
        end
    else
        self.stateTxt.text = LangMgr:GetLang(70000308);
    end

    if self.OnTick then
        self.OnTick();
    end
    
    RemoveUIComponentEventCallback(self.bg, UEUI.Button);
    AddUIComponentEventCallback(self.bg, UEUI.Button, function()
        if state == 1 then
            if isJoin then
                UI_UPDATE(UIDefine.UI_Union, 6);
                BowlingBattleManager:TryOpen(false);
            else
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000592));
            end
        elseif state == 4 then
            UI_UPDATE(UIDefine.UI_Union, 6);
            BowlingBattleManager:TryOpen(false);
        elseif state == 2 then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000388));
        end
    end)
end

function Item:OnCheckActState()
    if self.data.total_type then
        local activity = LimitActivityController:GetActive(self.data.total_type);
        local state = activity.info.state;
        if self.lastActState == nil then
            self.lastActState = state;
        elseif self.lastActState ~= state then
            return true;
        end
    end
    return false;
end

function Item:OnUpdateBoss()
    local needHeroId = v2n(LeagueManager:GetConfigById(101));
    local unLockState = HeroManager:IsHeroActive(needHeroId);
    self.OnTimer = nil;
    if unLockState then
        if LeagueManager:GetBossActEndTime() > 0 then
            self.OnTimer = function()
                if LeagueManager:GetBossActEndTime() <= 0 then
                    self:OnUpdateBoss();
                end
            end
            self.stateTxt.text = LangMgr:GetLang(70000301);
        else
            local nextTime = LeagueManager.act_next_start_time;
            self.OnTimer = function()
                local showTime = nextTime - TimeZoneMgr:GetServerStampWithServerZone();
                if showTime <= 0 then
                    showTime = 0;
                    LeagueManager:OnRequestBossLoad(); -- 加载Boss信息
                end
                self.stateTxt.text = LangMgr:GetLangFormat(70000302, TimeMgr:CutBuyWorkTime(showTime));
            end
        end
        SetImageSprite(self.bg, self.data.pic, false, function()
            SetActive(self.bg, true);
        end);
    else
        SetImageSprite(self.bg, self.data.close_pic, false, function()
            SetActive(self.bg, true);
        end);
        self.tipTxt.text = LangMgr:GetLangFormat(70000304, ItemConfig:GetLangByID(needHeroId));
        self.stateTxt.text = "";
    end
    
    if self.OnTimer then
        self.OnTimer();
    end

    RemoveUIComponentEventCallback(self.bg, UEUI.Button);
    AddUIComponentEventCallback(self.bg, UEUI.Button, function()
        if unLockState then
            if LeagueManager:GetBossActEndTime() > 0 then
                UI_SHOW(UIDefine.UI_UnionBoss);
            else
                local nextTime = LeagueManager.act_next_start_time;
                local showTime = nextTime - TimeZoneMgr:GetServerStampWithServerZone();
                if showTime <= 300 then
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000651));
                else
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000388));
                end
            end
        else
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(70000390, ItemConfig:GetLangByID(needHeroId)));
        end
    end)
    SetActive(self.redDot, LeagueManager:CheckBossRedDot());
end

return UI_UnionActivity