local UI_NewSkiingCollection = Class(BaseView)

local List_CollectCell_halloween

function UI_NewSkiingCollection:OnInit()
    self.moveIndex = 0
end

function UI_NewSkiingCollection:OnCreate(activeId)
    self.activeId = activeId
    local prePath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_CollectCell_halloween")
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,function(obj)
        List_CollectCell_halloween = obj
        self:CreateItems()
        if self.moveIndex ~= 0 then
            self:ScrollToIdxVertical(self.moveIndex)
        end
    end)
end

function UI_NewSkiingCollection:OnRefresh(param)

end

function UI_NewSkiingCollection:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnClose" then
        self:Close()
    end
end

function UI_NewSkiingCollection:onDestroy()
    self.moveIndex = 0
end

--********************************业务逻辑********************************
function UI_NewSkiingCollection:ScrollToIdxVertical(cellIndex)
    local scrollRect = GetChild(self.uiGameObject, "bg/goodObj/scrollview", UEUI.ScrollRect)
    local itemContent = GET_UI(self.uiGameObject, "itemsContent", "RectTransform")

    DOKill(itemContent.transform)

    local function onDelayDone()
        local cellY = GET_UI_CHILD(itemContent, cellIndex - 1, "RectTransform")
        local finish = function()
            scrollRect.enabled = true
        end

        if cellY then
            local posY = -cellY.anchoredPosition.y
            scrollRect.enabled = false

            itemContent.anchoredPosition = Vector2.New(0, 0)

            local function toT(lerpValue)
                itemContent.anchoredPosition = Vector2.New(0, lerpValue)
            end

            DOLocalMoveY(itemContent.transform , posY , 0.5 ,finish, Ease.InOutSine):SetDelay(0.3)
        else
            DOLocalMoveY(itemContent.transform , 0 , 0.5 ,finish, Ease.InOutSine):SetDelay(0.3)
        end
    end
    -- TimeMgr:CreateTimer(UIDefine.UI_NewSkiingCollection, onDelayDone, 0.2, 1)	
end

function UI_NewSkiingCollection:CreateItems()
    local config = NewSkiingManager:GetCollectionByActiveId(self.activeId)
    if not config then
        return
    end
    
    local itemContent = GET_UI(self.uiGameObject, "itemsContent", "RectTransform")
    local isShow = false
    for i = 1, #config do
        isShow = true
        local child =  GET_UI_CHILD(itemContent , i - 1 , "RectTransform")
        if child == nil then

            child = UEGO.Instantiate(List_CollectCell_halloween)
            child.transform:SetParent(itemContent.transform)
            child.transform.localScale = Vector3.New(1,1,1)
            child.transform:SetLocalPosition(0, 0, 0)
        end

        SetActive(child , true)
        local title = GET_UI(child, "itemName", TP(UEUI.Text))
        local collectionConfig = CollectionItems:get_CollectData(config[i].collection_item)
        local titleName = collectionConfig.name_id
        local chainString = collectionConfig.material_id

        title.text = LangMgr:GetLang(titleName)
        self:CreateAllItems(child,chainString,i)
    end
   
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(itemContent)
end

function UI_NewSkiingCollection:CreateAllItems(obj,chainString,index)
    local goodsItemRect = GET_UI(obj, "goodsItem", "RectTransform") --自动扩容的部分

    local childCount = goodsItemRect.childCount
    for i = 0, childCount - 1 do
        local child =  GET_UI_CHILD(goodsItemRect , i , "RectTransform")
        SetActive(child , false)
    end
    --初始化 里面小的cell
    local matList = {}
    local items = string.split(chainString,'|')
    for k, v in ipairs(items) do
        table.insert(matList,tonumber(v))
    end

    for i = 1, #matList do
        local itemId = matList[i]
        local child =  GET_UI_CHILD(goodsItemRect , i - 1 , "RectTransform")
        if nil == child then
            local items = GET_UI(obj, "IconContent", "RectTransform")
            child = UEGO.Instantiate(items)
            child.transform:SetParent(goodsItemRect.transform)
            child.transform.localScale = Vector3.New(1,1,1)
            child.transform:SetLocalPosition(0, 0, 0)
        end
        SetActive(child, true)

        local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemId)
        if itemConfig == nil then
            return
        end
        local icon = GET_UI(child, "icon", TP(UEUI.Image))
        local name = GET_UI(child, "name", TP(UEUI.Text))
        local signType = GET_UI(child, "signType", TP(UEUI.Image))
        local rewardIcon = GET_UI(child, "rewardIcon", TP(UEUI.Image))
        local rewardCount = GET_UI(child, "count", TP(UEUI.Text))
        local Goodsreward = GET_UI(child, "Goodsreward", TP(UEUI.Button))
        local effect = GetChild(child , "Obj/effect_UI_xuanzhuang")
        local ItemsAni = GetComponent(Goodsreward, UE.Animation)
        local imgRow = GET_UI(child, "row", TP(UEUI.Image))
        local bgSpe = GET_UI(child, "specialBg", TP(UEUI.Image))
        local imgUnknow = GET_UI(child, "unknown", TP(UEUI.Image))
        local bg = GET_UI(child, "bg", TP(UEUI.Image))
        local iconAni = GetComponent(icon, UE.Animation)
        
        SetUIImage(icon, itemConfig.icon_b, false)
        name.text = LangMgr:GetLang(itemConfig.id_lang)

        local itemState = NetNewSkiingData:GetCollectionData(itemId)
        SetUIImageGray(icon, itemState == ITEM_STATE.UNLOCK)
        SetActive(Goodsreward, itemState == ITEM_STATE.CAN_REWARD)
        SetActive(effect , itemState == ITEM_STATE.CAN_REWARD)
        SetActive(icon, itemState ~= ITEM_STATE.CAN_REWARD)
        SetActive(imgUnknow, itemState == ITEM_STATE.UNLOCK)
        icon.color = Color.New(1, 1, 1, itemState == ITEM_STATE.UNLOCK and 0.5 or 1)
        
        if itemState == ITEM_STATE.CAN_REWARD then
            ItemsAni:Play()
            if self.moveIndex == 0 then
                self.moveIndex = index
            end
        end
        
        SetActive(signType, false)
        SetActive(imgRow, true)

        local isTop = i == #matList
        SetActive(imgRow, not isTop)
        SetActive(bgSpe , isTop)
        SetActive(bg , not isTop)
        
        if not isTop then --最高等级item
            SetActive(rewardIcon,false)
        end
        
        SetActive(child , true)
        
        local coinNum = itemConfig.unlock_coins or 0
        RemoveUIComponentEventCallback(Goodsreward, UEUI.Button)
        AddUIComponentEventCallback(Goodsreward, UEUI.Button, function(arg1,arg2)
            self:GetGoodsReward(Goodsreward, coinNum, itemId, itemConfig)
            SetActive(Goodsreward, false)
            SetActive(effect , false)
            SetActive(imgUnknow, false)
            SetActive(icon, true)
            iconAni:Play()
            NetNewSkiingData:SetCollectionData(itemId,ITEM_STATE.REWARED)
            UI_UPDATE(UIDefine.UI_NewSkiingMatch, NetNewSkiingData.UpdateType.RefreshCollection)

			local isOpen,active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.NewSkiing)
			if isOpen then
				UIMgr:Refresh(UIDefine.UI_MainFace, 9, { id = active.info.activeId, state = 2 })
			end
        end)
    end
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(goodsItemRect)
end

function UI_NewSkiingCollection:GetGoodsReward(obj, num, itemId, config)
    local flyPos = UIMgr:GetObjectScreenPos(obj.transform)
    MapController:AddResourceBoomAnim(flyPos.x, flyPos.y, ItemID.COIN, num , true)
    MapController:AddResourceJumpAnim(flyPos.x, flyPos.y, config, num)
    NetUpdatePlayerData:AddResource(PlayerDefine.Coin, num,true,nil,"UI_NewSkiingCollection")
end

return UI_NewSkiingCollection