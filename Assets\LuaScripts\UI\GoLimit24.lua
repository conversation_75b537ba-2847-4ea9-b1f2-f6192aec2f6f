local GoLimit24 = {}
local M = GoLimit24
local prePath = "Assets/ResPackage/Prefab/UI/GoLimit24.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.time = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
    self.red = GetChild(self.go,"doLimit/bg/goPoint")
    self.text_progress = GetChild(self.go,"doLimit/bg/Limit/text_progress",UEUI.Text)
	self.img = GetChild(self.go,"doLimit/bg/Limit/img",UEUI.Image)
	self.showDiceTips = GetChild(self.go,"doLimit/bg/AddDiceTips/view",UE.RectTransform)
    self.diceTipsPosY = self.showDiceTips.localPosition.y
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:RefreshUI()
        UI_SHOW(UIDefine.UI_RollDiceView)
    end)
    self.slider = GetChild(self.go,"doLimit/bg/Limit/Slider",UEUI.Slider)
    self.goDiceNum = GetChild(self.go,"goDiceNum/txt",UEUI.Text)
    self.goDiceNum.text = NetRollDiceData:GetDiceNum()
   
    EventMgr:Add(EventID.REFRESH_RED,self.SetRedShow,self)
    EventMgr:Add(EventID.BUY_ACT_GIFT,self.BuyActRefresh,self)
    EventMgr:Add(EventID.ACT_DOT_REFRESH,self.BuyActRefresh,self)
end
--init
function M:SetItem(param)
    self.id = param.id
    local activeInfo = LimitActivityController:GetActiveMessage(self.id)
	self.activeInfo = activeInfo
    self.active = activeInfo.form.activeMess
	self.totalType = param.totalType
    RollDiceManager:InitRoleDiceActive(activeInfo.info.activeId)
    -- self.totalType = param.totalType
    -- self.condition = param.condition
    SetImageSprite(self.icon,self.active.icon,false)
	-- SetImageSprite(self.img, ItemConfig:GetIcon(73006), false)

    local needPointAndGetRollNum = RollDiceManager:GetSettingConfigById(6).value:split("|")
    self.needPoint = tonumber(needPointAndGetRollNum[1])
    self.acquireRollNum = tonumber(needPointAndGetRollNum[2])

    local present = NetRollDiceData:GetDiceValue() / self.needPoint
    self.slider.value = present
    self.text_progress.text = math.floor(present * self.needPoint) .. "/" .. self.needPoint


	self:SetRedShow()
	-- self:SetRankTxt()
end
function M:ShowAddDiceTips()
    self.showDiceTips:DOKill()
    self.showDiceTips:DOScale(Vector3.one,0.5)
    local pos = self.showDiceTips.localPosition
    pos.y = self.diceTipsPosY
    self.showDiceTips.localPosition = pos
    self.showDiceTips:DOLocalMoveY(self.showDiceTips.localPosition.y + 30,1):SetLoops(6,LoopType.Yoyo):SetEase(Ease.InOutSine):OnComplete(function ()
        self:HideAddDiceTips()
    end)
end
function M:HideAddDiceTips()
    self.showDiceTips:DOKill()
    self.showDiceTips:DOScale(Vector3.zero,0.3):OnComplete(function ()
        local pos = self.showDiceTips.localPosition
        pos.y = self.diceTipsPosY
        self.showDiceTips.localPosition = pos
    end)
end
function M:RefreshUI()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end    
    self.hasLastSequence = false
    if Tween.IsTweening(self.showDiceTips) then
        self:HideAddDiceTips()
    end
    self:SetRedShow()
    self.goDiceNum.text = NetRollDiceData:GetDiceNum()
    local present = NetRollDiceData:GetDiceValue() / self.needPoint
    self.slider.value = present
    self.text_progress.text = math.floor(present * self.needPoint) .. "/" .. self.needPoint
   
    if Tween.IsTweening(self.img.transform) then
        self.img.transform:DOKill()
        self.img.transform.localScale = Vector3.one
    end
end
function M:SetProgress()
    local upgrageNum = RollDiceManager.upgrageNum

    local present = NetRollDiceData:GetDiceValue() / self.needPoint
    local tweenSpeed = 2
    if not upgrageNum then
        upgrageNum = 0
    end
    if self.sequence then
        self.sequence:Kill()
    end
    self.sequence = Tween.Sequence()
    
    self.sequence:SetAutoKill(true)
    if not self.hasLastSequence then
        self.sequence:PrependInterval(0.7)   
    end
    self.hasLastSequence = true
    self.img.transform:DOScale(Vector3.one * 1.2,0.2):SetDelay(0.7)
    if upgrageNum > 0 then
        for i = 1, upgrageNum, 1 do
            local speed
            if i == 1 then
                speed = (1 - self.slider.value) * tweenSpeed
            else
                speed = tweenSpeed
            end
            self.sequence:Append(self.slider:DOValue(1,speed))
            self.sequence:AppendCallback(function ()
                self:SetRedShow()
                self:ShowAddDiceTips()
                self.slider.value = 0
                RollDiceManager.upgrageNum = RollDiceManager.upgrageNum - 1
            end):OnUpdate(function ()
                local value = self.slider.value * self.needPoint
                self.text_progress.text = Mathf.Floor(value + 0.5) .. "/" .. self.needPoint
            end)
        end
    end
    self.sequence:Append(self.slider:DOValue(present,tweenSpeed * present):OnUpdate(function ()
        local value = self.slider.value * self.needPoint
        self.text_progress.text = Mathf.Floor(value + 0.5) .. "/" .. self.needPoint
    end))
    self.sequence:AppendCallback(function ()
        self.img.transform:DOScale(Vector3.one,0.2)
        self.hasLastSequence = false
    end)
end
function M:ChangState(id)
   
end
function M:ChangeValue()
    self:SetProgress()
	-- self:SetRankTxt()
end
---tick
function M:ChangeItem()
	if not self.activeInfo then
		return
	end
	local time = self.activeInfo:GetRemainingTime()
	if time > 0 then
		self.time.text = TimeMgr:CheckHMSNotEmpty(time)
	else
		self.time.text = LangMgr:GetLang(7077)
	end
end

function M:BuyActRefresh(totalType,payId)
    if totalType == nil then
        return
    end

    if totalType == ActivityTotal.RollDice then
        self:SetRedShow()
    end
end

function M:SetRedShow()
    SetActive(self.red,NetRollDiceData:GetDiceNum() > 0 or NetRollDiceData:HasCanReward() > 0 or not NetRollDiceData.data.isFrist or NetLimitActGift:IsShowRed(ActivityTotal.RollDice))
    self.goDiceNum.text = NetRollDiceData:GetDiceNum()
end

function M:Close()
	self.img.transform:DOKill()
	self.showDiceTips:DOKill()
    EventMgr:Remove(EventID.REFRESH_RED,self.SetRedShow,self)
    EventMgr:Remove(EventID.BUY_ACT_GIFT,self.BuyActRefresh,self)
    EventMgr:Remove(EventID.ACT_DOT_REFRESH,self.BuyActRefresh,self)
    UEGO.Destroy(self.go)
end

return M