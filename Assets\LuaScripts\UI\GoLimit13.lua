local GoLimit13 = {}
local M = GoLimit13

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit13.prefab"
local pre
local isFirstCheckMission = true

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"bg/icon",UEUI.Image)
    self.red =  GetChild(self.go,"bg/goPoint")
    --self.rank = GetChild(self.go,"bg/rank",UEUI.Text)
    self.scheduleText = GetChild(self.go,"bg/num",UEUI.Text)
    self.count = GetChild(self.go,"bg/CountDown/countTxt",UEUI.Text)
    self.roundNum = GetChild(self.go,"bg/roundNumImg/roundNum",UEUI.Text)
    self.slider = GetChild(self.go,"bg/Limit/Slider", UEUI.Slider)
    self.img = GetChild(self.go,"bg/Limit/img", UEUI.Image)
    self.text_progress = GetChild(self.go,"bg/Limit/text_progress", UEUI.Text)
    self.showRed = false
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
    self:RefreshToyRed()
    EventMgr:Add(EventID.REFRESH_TOY_RED,self.RefreshToyRed,self)
    EventMgr:Add(EventID.REFRESH_TOY_ROUND,self.RefreshRound,self)
    EventMgr:Add(EventID.BUY_ACT_GIFT,self.BuyActRefresh,self)
    EventMgr:Add(EventID.ACT_DOT_REFRESH,self.BuyActRefresh,self)
end

function M:BuyActRefresh(totalType,payId)
    if totalType == nil then
        return
    end
    if totalType == ActivityTotal.Toy then
        self:RefreshToyRed()
    end
end

function M:RefreshToyRed(totalType,payId)
    local isRed = NetActToyData:GetToyCollectionRed() or NetLimitActGift:IsShowRed(ActivityTotal.Toy)
    SetActive(self.red,isRed)
end

function M:SetItem(param)
    self.id = param.id
    self.totalType = param.totalType
    self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
	local active = self.activeInfo.form.activeMess
    self.info = self.activeInfo.info
	SetUIImage(self.icon,active.icon,false)
    self:RefreshRound()
	NetActToyData:SetDataByKey("activityId",param.id)
	self:ChangeValue()
	self:AutoSetActive()
end

function M:RefreshRound()
    local curRound = NetActToyData:GetRoundId()
    self.roundNum.text = curRound
    self:RefreshTargetNum()
end

function M:ChangeItem()
	local time = self.activeInfo:GetRemainingTime()
	if time <= 0 then 
		--self:ClickItemPush()
	end
    self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetRemainingTime())
end

function M:ChangeValue()
	local hasRed = NetActToyData:IsHaveRedPoint() or NetLimitActGift:IsShowRed(ActivityTotal.Toy)
	SetActive(self.red,hasRed)
	self:AutoSetActive()
    if isFirstCheckMission then
        isFirstCheckMission = false
        if NetActToyData:GetIsStartCompetition() == true
                and NetActToyData:OnMissionEndCheck() == true
        then
            local curRound = NetActToyData:GetRoundId()
            --local playerCount = NetActToyData:GetDataByKey("curScore")
            --local iMax = NetActToyData:GetTagetMax(curRound)
            local flag = NetActToyData:GetRoundRankReward()
            
            if flag <= curRound then --playerCount >= iMax and
                NetPushViewData:PushView(PushDefine.ActToy,tonumber(self.id))
            end
        end
    end
    self:RefreshTargetNum()
end

function M:AutoSetActive()
	if NetActToyData:CheckIsGetAllReward() then
		SetActive(self.go,false)
        -- 领完奖励入口消失同时地图上的物品转为金币
        NetActToyData:ChangeToWasteId(nil,true)
	else
		SetActive(self.go,true)
	end
end

function M:ClickItem(arg1)
	UI_SHOW(UIDefine.UI_ActToy,self.id)
	
end

function M:ClickItemPush()
	self.isPush = true

end

function M:Close()
    EventMgr:Remove(EventID.BUY_ACT_GIFT,self.BuyActRefresh,self)
    EventMgr:Remove(EventID.ACT_DOT_REFRESH,self.BuyActRefresh,self)
    UEGO.Destroy(self.go)
end

function M:RefreshTargetNum()
    local targetID, targetNum = NetActToyData:GetCurToyTargetItemID()
    local _, has = MapController:GetItemById(targetID)
    self.text_progress.text = has .. "/" .. targetNum
    self.slider.value = has / targetNum
    if targetID and targetID ~= 0 then
        SetUIImage(self.img, ItemConfig:GetIcon(targetID), false)
    end
end

return M