local GoLimit18 = {}
local M = GoLimit18

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit18.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init() 
    
	self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.count = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
    self.red = GetChild(self.go,"doLimit/bg/Red")
    self.nameTxt = GetChild(self.go,"doLimit/bg/nameTxt",UEUI.Text)

    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
    

end


function M:SetItem(param)
    self.id = param.id
    self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
    local active = self.activeInfo.form.activeMess
    self.totalType = param.totalType
    self.condition = param.condition
    self.nameTxt.text = LangMgr:GetLang(active.activity_title)
    SetImageSprite(self.icon,active.icon,false)
	
    self:SetRedShow(BowlingBattleManager:CheckRedPoint())

	self:ChangeItem()
	self:ChangState()

    --检查参加活动的情况
    BowlingBattleManager:CheckJoinState(function ()
        BowlingBattleManager:RefreshRedPoint()
    end,false)
end

function M:ChangState(id)
	
end

---通过 Refresh的方法调用
function M:ChangeValue()

end

---来自timer 的调用
function M:ChangeItem()
	local time =self.activeInfo:GetRemainingTime()
    self.count.text = TimeMgr:CheckHMSNotEmpty(time)
end

function M:SetRedShow(IsShow)
    SetActive(self.red,IsShow)
end

function M:ClickItem(arg1)
    local state = BowlingBattleManager:CanJoinActive()
    if state <= 0 then 
        BowlingBattleManager:TryOpen(true)
    elseif state == 1 then
        --没有工会
        UI_SHOW(UIDefine.UI_JoinLeagueDialog)
    else
        --没有资格
        -- UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(9306))
        UI_SHOW(UIDefine.UI_GoToChat)
    end
end

function M:Close()

    UEGO.Destroy(self.go)
end

return M