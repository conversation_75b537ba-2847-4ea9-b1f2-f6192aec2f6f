local GoLimit17 = {}
local M = GoLimit17
local timerId = "GoLimit17"

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit17.prefab"
local pre

local isFirstCheckMission = true

function M:Create(parent,loadCallBack)
	local item = {}
	setmetatable(item,{__index = M})
	item.loadCallBack = loadCallBack
 
	local function callBack(obj)
		local newGo = CreateGameObjectWithParent(obj,parent)
		item.go = newGo
		item.trans = newGo.transform
		item:Init()
		if item.loadCallBack then
			item.loadCallBack(item)
		end
	end
	ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

	--return item
end

function M:Init()
    self.state1 = GetChild(self.go,"doLimit/bg/state1")
    --self.state2 = GetChild(self.go,"doLimit/bg/state2")
    --self.state3 = GetChild(self.go,"doLimit/bg/state3")
    self.act = GetChild(self.go,"doLimit")
    self.bg = GetChild(self.go,"doLimit/bg",UEUI.Image)
    self.bgRect = GetComponent(self.bg.gameObject, UE.RectTransform)
    --self.scheduleImg = GetChild(self.state1,"scheduleImg")
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.countGo = GetChild(self.go,"doLimit/bg/CountDown")
    self.count = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
    --self.timeLang = GetChild(self.state2,"countDown",UEUI.Text)
    --self.needLevel = GetChild(self.state3,"Image/needLevel",UEUI.Text)
    self.intIcon = GetChild(self.state1,"icon",UEUI.Image)
    --self.effect = GetChild(self.go,"doLimit/effect")
    self.point = GetChild(self.go,"doLimit/bg/goPoint")
    --self.adImg = GetChild(self.go,"doLimit/bg/adImg")
    self.doAni =  GetChild(self.go,"doLimit",TweenAnim)
    self.limitGo = GetChild(self.state1,"Limit")
    self.normalGo = GetChild(self.state1,"Normal")
    self.arrowGo = GetChild(self.go,"arrow")
	self.pass_icon = GetChild(self.state1,"Normal/icon",UEUI.Image)
	self.pass_icon2 = GetChild(self.state1,"Limit/icon",UEUI.Image)
	self.goRound = GetChild(self.go,"doLimit/bg/goRound")
	self.txtRound = GetChild(self.goRound,"txt",UEUI.Text)
	
	--self.goEnergy = GetChild(self.go,"doLimit/bg/goEnergy")
    --self.adState = false
	--self.needPush = true
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
    EventMgr:Add(EventID.CHANGE_ACTIVE_STATE,self.ChangState,self)
    EventMgr:Add(EventID.ADD_INTEGRAL,self.PlayAni,self)
	
end

function M:PlayAni(id)
    if id == self.id then
        self.doAni:DORestart()
    end
end

function M:SetItem(param)
    self.id = param.id
    self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
    local active = self.activeInfo.form.activeMess
    self.totalType = param.totalType
    self.condition = param.condition
    local arrowShow = false
    if self.totalType == ActivityTotal.LimitIns then
        SetActive(self.limitGo,true)
        SetActive(self.normalGo,false)
        --self.progress = GetChild(self.limitGo,"Image/progress",UEUI.Image)
		self.progress = GetChild(self.limitGo,"Slider",UEUI.Slider)
        self.scheduleText = GetChild(self.limitGo,"Text",UEUI.Text)
		self.rankStageText = GetChild(self.limitGo,"icon/rank",UEUI.Text)
        if NetUpdatePlayerData.playerInfo.curMap == MAP_ID_MAIN then
            if LimitActivityController:GetIsFirstShow() then
                arrowShow = true
            end
        end
    else
        SetActive(self.limitGo,false)
        SetActive(self.normalGo,true)
        --self.progress = GetChild(self.normalGo,"Image/progress",UEUI.Image)
        self.progress = GetChild(self.normalGo,"Slider",UEUI.Slider)
        self.scheduleText = GetChild(self.normalGo,"Text",UEUI.Text)
		self.rankStageText = GetChild(self.normalGo,"icon/rank",UEUI.Text)
    end
	
    if arrowShow then
        SetActive(self.arrowGo,true)
        TimeMgr:CreateTimer(timerId,function()
            SetActive(self.arrowGo,false)
        end,30,1)
    else
        SetActive(self.arrowGo,false)
    end
    
    --self.needLevel.text = active.level
    self.adId = active.advertisement_id
    SetImageSprite(self.icon,active.icon,false)
	if active.pass_icon2 then
		SetImageSprite(self.pass_icon,active.pass_icon2,false)
		SetImageSprite(self.pass_icon2,active.pass_icon2,false)
	end
	--Log.Error("xxx",active.id,active.pass_icon2)
    self:ChangeItem()
    self:ChangState()
end

function M:ChangState(id)
    --刷新时要判断id
    if id and id ~= self.id then
		return
	end
	
	self:ChangeValue()
end

function M:ChangeValue()
    local active = self.activeInfo
    local actInfo = active.info
    if self.condition ~= actInfo.state then
        self.condition = actInfo.state
        --self:ChangState()
    end
	
	--活动第一次自动打开
	if NetFlowerCompetition:GetIsStartCompetition() == false then
		NetFlowerCompetition:ResetCompetitionData()
		NetFlowerCompetition:SetIsStartCompetition(true)
		
		NetPushViewData:PushView(PushDefine.FlowerCompetition, {self.activeInfo.info.activeId, true, true})

	end
	
	local iRound = NetFlowerCompetition:GetRoundId()
	if iRound > 0 then
		SetActive(self.goRound, true)
		self:SetRoundTxt(iRound)
	end
	
	local iFlowerCount = NetFlowerCompetition:GetFlowerCount(1)
	local Max = FlowerCompetitionConfig:GetTagetMax(iRound)
	
	if self.progress then
		--self.progress.fillAmount = NetFlowerCompetition:GetCollectCount() / Max
		--self.progress.fillAmount = iFlowerCount / Max
		self.progress.value = iFlowerCount / Max
	end
	
	--self.scheduleText.text = string.format("%d/%d", NetFlowerCompetition:GetCollectCount(), Max)
	self.scheduleText.text = string.format("%d/%d", iFlowerCount, Max)
	
	--self.rankStageText.text = self.activeInfo.info.energySch
	self.rankStageText.text = tostring(NetFlowerCompetition:GetSelfRank())

	self:CheckRedPoint()
	
	--账号登录，进入到主营地，鲜花数量大于等于当轮鲜花数（上次鲜花数满了，但关闭了游戏），自动打开
	if isFirstCheckMission == true
		and NetFlowerCompetition:GetIsStartCompetition() == true
		and NetFlowerCompetition:IsRoundEnd() == false
	then
		isFirstCheckMission = false
		if iFlowerCount >= Max then
			NetPushViewData:PushView(PushDefine.FlowerCompetition, {self.activeInfo.info.activeId, true})
			
		end
	end
	
	if NetFlowerCompetition:IsRoundEnd() then
		SetActive(self.go, false)
	end
	
end

function M:SetRoundTxt(value)
	self.txtRound.text = tostring(value)
end

function M:CountDown5M()
	local oneMinute = 60
	if self.activeInfo.info.totalType == ActivityTotal.LimitIns then
		local time = self.activeInfo:GetRemainingTime()
		if time < 1 then
			time = self.activeInfo:GetWaitTime()
		end
		--if time <= oneMinute*5 then
			--if not self.needPush then

			--else
				--self.needPush = false
			--end
		--end
		if time > oneMinute*5 or time <=0 then
			return 
		end
		local str = nil
		if time <= oneMinute*2 then
			if time == oneMinute then
				str = "1min"
			end
			if time == oneMinute*2 then
				str = "2min"
			end
			if time == oneMinute*1/2 then
				str = "30s"
			end
			if time <= 10 then
				str = math.floor(time).."s"
			end
			if str ~= nil then
				local text = string.format(LangMgr:GetLang(8118),str)
				local curMapID = NetUpdatePlayerData.playerInfo.curMap
				if not curMapID ~= 7 and not IsHomeMap(curMapID) then
					UIMgr:Show(UIDefine.UI_WidgetTip,text)
				end
			end
		end

	end
end

function M:ChangeItem()
    if self.condition == 2 then return end
    if self.condition == 1 then
        self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetRemainingTime())
		self:CountDown5M()
    elseif self.condition == 3 then
        self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetStartRemainingTime())
    elseif self.condition == 4 then
        self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetWaitTime())
		self:CountDown5M()
    end


end

function M:ClickItem(arg1)
	if NetFlowerCompetition:GetInAddFlowerAnim() == false then
		UI_SHOW(UIDefine.UI_FlowerCompetition, {self.activeInfo.info.activeId})
	end
end

function M:Close()
    TimeMgr:DeleteTimer(timerId)
    EventMgr:Remove(EventID.CHANGE_ACTIVE_STATE,self.ChangState,self)
    EventMgr:Remove(EventID.ADD_INTEGRAL,self.PlayAni,self)
    UEGO.Destroy(self.go)
end


function M:CheckRedPoint()
	--完成所有轮次
	if NetFlowerCompetition:IsRoundEnd() == true then
		SetActive(self.point, false)
		return
	end
	
	local iFlowerCount = NetFlowerCompetition:GetFlowerCount(1)
	local iTargetPrizeIndex = NetFlowerCompetition:GetTargetPrizeIndex()
	local iTagetPirze = FlowerCompetitionConfig:GetTagetById(NetFlowerCompetition:GetRoundId(), iTargetPrizeIndex)
	if iTagetPirze == nil then
		iTagetPirze = 0
	end
	if iFlowerCount == nil then
		iFlowerCount = 0
	end
	SetActive(self.point, (iFlowerCount >= iTagetPirze) )
end

return M