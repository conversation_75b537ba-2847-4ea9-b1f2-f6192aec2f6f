local UI_OneToOnePassport = Class(BaseView)
local PerProgress = 0.1
local RewardItemWidth = 160
local IconAnimName = "activity_itemicon"

function UI_OneToOnePassport:OnInit()
    self.activityId = 0         -- 活动 id
    self.activityItem = nil     -- 活动对象
    self.nowPer = 0             -- 当前进度
    self.rewardIndex = -1       -- 奖励进度
    self.itemList = {}          -- 奖励列表
    self.nowScore = 0
    self.rewardList = {}
    self.rewardCacheList = {}
    self.bubbleStatus = {}
end

function UI_OneToOnePassport:OnCreate()
    -- 设置活动 id 和活动对象
    local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.OneToOne)
    self.activityItem = activityItem
    if not self.activityItem then return end
    self.activityId = activityItem.info.activeId
    SetActive(self.ui.m_goRewardItem, false)
    -- 初始刷新数据和界面
    self:UpdateActivityData()
    self:LoadRewardItem()
    self:UpdateLayout()
    self:SetIsUpdateTick(true)
end

function UI_OneToOnePassport:OnRefresh(type)
    self:UpdateActivityData()
    self:UpdateLayout()
    self:UpdateAllList()
    -- 解锁 VIP
    if type == 1 then
        EffectConfig:CreateEffect(141, 0, 0, 0, self.ui.m_goEffectBg.transform, function()
            self:SortOrderAllCom(true)
        end)
		UI_UPDATE(UIDefine.UI_OneToOneView)
    end
end

function UI_OneToOnePassport:TickUI(deltaTime)
    local time = self:GetActiveTime()
    if time and time > 0 then
        self.ui.m_txtTime.text = TimeMgr:BaseTime(time, 3, 2)
    else
        self.ui.m_txtTime.text = LangMgr:GetLang(7077)
    end
end

function UI_OneToOnePassport:onDestroy()
    self.activityId = nil
    self.activityItem = nil
    self.nowPer = nil
    self.rewardIndex = nil
    self.itemList = nil
    self.nowScore = nil
    self.rewardList = nil
    self.rewardCacheList = nil
    self.bubbleStatus = nil
    self.clickclose = nil
    self:SetIsUpdateTick(false)
end

function UI_OneToOnePassport:onUIEventClick(go)
    local name = go.name
    if name ~= "m_btnRewardTip" and name ~= "m_btnReward" then
        if self.isOpenRewardTip then
            self.isOpenRewardTip = false
            SetActive(self.ui.m_goFinalRewardTip, self.isOpenRewardTip)
        end
    end
    -- 点击关闭按钮
    if name == "btn_close" then
        self:Close()
    -- 高级奖励详情
    elseif name == "m_btnTag" then
        local imagePath = "Sprite/ui_activity_tongxingzheng/txz_jiangli2.png"
        UI_SHOW(UIDefine.UI_BuyPassPortTip, {["imagePath"] = imagePath, ["des"] = 8024})
    -- 豪华奖励详情
    elseif name == "m_btnTag3" then
        local imagePath = "Sprite/ui_activity_tongxingzheng/txz_jiangli3.png"
        UI_SHOW(UIDefine.UI_BuyPassPortTip, {["imagePath"] = imagePath, ["des"] = 4024})
    -- 购买高级奖励
    elseif name == "m_btnUnlock" then
        self:BuyVip()
    -- 购买豪华奖励
    elseif name == "m_btnVip3" then
        self:BuyVip3()
    -- 物品详情弹窗
    elseif name == "btnTips" then
        self:ShowCommonTips(go)
    -- 付费物品详情弹窗
    elseif name == "btnLock" then
        self:ShowBuyCommonVip(go)
    -- 领取奖励
    elseif name == "btnReward" then
        self:PlayRewardAnim()
    -- 通关图腾奖励
    elseif name == "m_btnRewardTip" or name == "m_btnReward" then
        self.isOpenRewardTip = not self.isOpenRewardTip
        SetActive(self.ui.m_goFinalRewardTip, self.isOpenRewardTip)
    end
end

function UI_OneToOnePassport:AutoClose()
    if self:IsHaveBubble() then
        self:DoCloseAnim()
        return
    end
    self:Close()
end

function UI_OneToOnePassport:onEscape()
    self:AutoClose()
end

--- 加载通行证奖励物品
function UI_OneToOnePassport:LoadRewardItem()
    local itemListRes = self.ui.m_goRewardItem
    -- 获取奖励数据
    local formTable =  self.activityItem:GetForm()
    local itemCount  = GetTableLength(formTable.ordinary)
    local oData = formTable.ordinary
    local vData = formTable.vipReward
    local v3Data = formTable.vipReward3
    -- 设置列表宽度
    self.ui.m_rtransContent.sizeDelta = Vector2.New(itemCount * RewardItemWidth, self.ui.m_rtransContent.sizeDelta.y)
    -- 生成奖励列表
    for i = 1, itemCount do
        local go = UEGO.Instantiate(itemListRes)
        SetActive(go, true)
        if itemListRes then
            go.name = i
            AddChild(self.ui.m_panel, go)
        end
        local list = {}
        if vData and oData and v3Data then
            list = {{v2n(oData[i][1]), v2n(oData[i][2])},
                    {v2n(vData[i][1]), v2n(vData[i][2])},
                    {v2n(v3Data[i][1]), v2n(v3Data[i][2])}}
        else
            list = {{0, 0}, {0, 0}, {0, 0}}
        end
        local nowPer = i * PerProgress
        if self:IsHaveReward(nowPer, i) then
            self.rewardIndex = i
        end
        table.insert(self.itemList, list)
        self:UpdateItemList(go, list, i, true)
    end
end

--- 是否可领取奖励
--- @param progress number 当前进度
--- @param index number 当前索引
--- @return boolean state 奖励状态
function UI_OneToOnePassport:IsHaveReward(progress, index)
    local indexList = self:GetScheduleList()
    if CompareNum(progress, self.nowPer) then
        if self.activityItem:IsBuyVip() then
            if indexList[2] < index then
                return true
            end
        end
        if self.activityItem:IsBuyVip3() then
            if indexList[3] < index then
                return true
            end
        end
        if indexList[1] < index then
            return true
        end
    end
    return false
end

--- 获取领奖进度
--- @return table schedule 三个等级的进度
function UI_OneToOnePassport:GetScheduleList()
    local oIndex = self.activityItem:GetSchedule()
    local vIndex = self.activityItem:GetVIPSchedule()
    local v3Index = self.activityItem:GetVIP3Schedule()
    return {oIndex, vIndex, v3Index}
end

--- 再次刷新奖励列表
function UI_OneToOnePassport:UpdateAllList()
    local count = self.ui.m_panel.transform.childCount
     for i = 1, count do
         local listGo = GetChild(self.ui.m_panel, v2s(i))
         local list = self.itemList[i]
         local nowPer = i * PerProgress
         if self:IsHaveReward(nowPer, i) then
             self.rewardIndex = i
         end
         self:UpdateItemList(listGo, list , i, false)
     end
 end

--- 刷新奖励列表
--- @param listGo any 奖励物品
--- @param list any 奖励数据列表
--- @param index number 当前索引
--- @param isInit boolean 是否初始化
function UI_OneToOnePassport:UpdateItemList(listGo, list, index, isInit)
    if not listGo or not list then
        return
    end
    local nowPer = index * PerProgress
    local indexList = self:GetScheduleList()
    local energySch = self:GetNowEnergySch()
    self.ui.m_txtLevel.text = tostring(energySch)
    for i = 1, 3 do
        local itemGo = GetChild(listGo, "Item" .. i)
        local itemKey = (index - 1) * 2 + i
        local bubbleType = itemKey % 2 == 0 and  2 or 1
        if itemGo then
            local nodeList  = self:GetItemGoNodeList(itemGo)
            SetActive(itemGo, true)
            SetActive(nodeList.imgProgress, index == 1)
            if nodeList.ImgLock then
                SetActive(nodeList.ImgLock, false)
            end
            SetActive(nodeList.effect, false)
            SetActive(nodeList.rewardTag, false)
            SetActive(nodeList.btnReward, false)
            SetActive(nodeList.btnTips, true)
            SetActive(nodeList.level_bg, true)
            SetActive(nodeList.level_image, false)
            SetActive(nodeList.kelinqu, false)
            local isPlayAnim = false
            local isHaveBubble = self:IsHaveBubble(bubbleType)
            if CompareNum(nowPer, self.nowPer) then
                -- 未领取
                if index > indexList[i] and not isHaveBubble then
                    SetActive(nodeList.effect, true)
                    SetActive(nodeList.btnReward, true)
                    isPlayAnim = true
                    SetActive(nodeList.btnTips, false)
                    SetActive(nodeList.kelinqu, true)
                -- 已领取
                else
                    SetActive(nodeList.rewardTag, true)
                    SetActive(nodeList.kelinqu, false)
                end
                SetActive(nodeList.level_bg, false)
                SetActive(nodeList.level_image, true)
            end
            nodeList.level_text.text = index
            local itemList = self.itemList[index][i]
            local itemId = v2n(NetSeasonActivity:GetChangeItemId(itemList[1]))
            local itemCount = itemList[2]
            local imagePath = ItemConfig:GetIcon(itemId)
            if imagePath then
                SetImageSprite(nodeList.itemIcon, imagePath, false)
                nodeList.txtNum.text = string.format("x%d", itemCount)
            else
                SetActive(itemGo, false)
            end
            -- 未买 VIP
            if i == 2 and nodeList.ImgLock and not self.activityItem:IsBuyVip() then
                SetActive(nodeList.ImgLock, true)
                SetActive(nodeList.effect, false)
                SetActive(nodeList.rewardTag, false)
                SetActive(nodeList.kelinqu, false)
                isPlayAnim = false
            elseif i == 3 and nodeList.ImgLock and not self.activityItem:IsBuyVip3() then
                SetActive(nodeList.ImgLock, true)
                SetActive(nodeList.effect, false)
                SetActive(nodeList.rewardTag, false)
                SetActive(nodeList.kelinqu, false)
                isPlayAnim = false
            end
            SetControlExpand(nodeList.btnTips, itemId, 0)
            SetControlExpand(nodeList.btnReward, itemId, 0)
            SetControlExpand(nodeList.btnReward, i, 1)
            SetControlExpand(nodeList.btnLock, i, 0)
            if isPlayAnim then
                if not self.rewardCacheList[v2s(itemKey)] then
                    local itemData = {itemId, itemGo, itemCount}
                    if itemId >= ItemID._RESOURCE_MAX then
                        table.insert(self.rewardList, itemData)
                    end
                    self.rewardCacheList[v2s(itemKey)] = itemId
                end
                nodeList.iconAnim:Play(IconAnimName)
            end

            if isInit then
                self:InitAllBtnClick(nodeList.btnReward, nodeList.btnTips, nodeList.btnLock)
            end
        end
    end
end

--- 获取奖励物品组件
--- @param itemGo any 奖励物品
--- @return table list 组件列表
function UI_OneToOnePassport:GetItemGoNodeList(itemGo)
    local list = {}
    list["imgProgress"] = GetChild(itemGo, "imgProgress",          UEUI.Image)
    list["effect"]      = GetChild(itemGo, "ImgEffect",            UEUI.Image)
    list["itemIcon"]    = GetChild(itemGo, "itemIcon",             UEUI.Image)
    list["energyImg"]   = GetChild(itemGo, "bgImg/energyImg",      UEUI.Image)
    list["level_bg"]    = GetChild(itemGo, "level_bg/level_bg",    UEUI.Image)
    list["level_image"] = GetChild(itemGo, "level_bg/level_image", UEUI.Image)
    list["kelinqu"]   	= GetChild(itemGo, "kelinqu",              UEUI.Image)
    list["rewardTag"]   = GetChild(itemGo, "rewardTag",            UEUI.Image)
    list["ImgLock"]     = GetChild(itemGo, "ImgLock",              UEUI.Image)
    list["txtNum"]      = GetChild(itemGo, "txtNum",               UEUI.Text)
    list["rewardNum"]   = GetChild(itemGo, "bgImg/rewardNum",      UEUI.Text)
    list["level_text"]  = GetChild(itemGo, "level_bg/level_text",  UEUI.Text)
    list["btnReward"]   = GetChild(itemGo, "btnReward",            UEUI.Button)
    list["btnTips"]     = GetChild(itemGo, "btnTips",              UEUI.Button)
    list["btnLock"]     = GetChild(itemGo, "ImgLock/btnLock",      UEUI.Button)
    list["iconAnim"]    = GetChild(itemGo, "itemIcon",             UE.Animation)
    return list
end

--- 初始化所有按钮点击事件
--- @param ... any 所有按钮组件
function UI_OneToOnePassport:InitAllBtnClick(...)
    local btnList = table.pack(...)
    for _, btn in ipairs(btnList) do
        RemoveUIComponentEventCallback(btn, UEUI.Button)
        AddUIComponentEventCallback(btn, UEUI.Button, function(go, param)
            self:onUIEventClick(go, param)
        end)
    end
end

--- 购买高级奖励
function UI_OneToOnePassport:BuyVip()
    self:ControlArrow(false)
    if self.activityId then
        UI_SHOW(UIDefine.UI_BuyPassPort, self.activityId)
    end
end

--- 购买豪华奖励
function UI_OneToOnePassport:BuyVip3()
    self:ControlArrow(false)
    if self.activityId then
        UI_SHOW(UIDefine.UI_BuyPass3Port, self.activityId)
    end
end

--- 控制箭头显示
--- @param isShow boolean 是否显示
function UI_OneToOnePassport:ControlArrow(isShow)
    if isShow == false then
        self.ui.m_scrollview.onValueChanged:RemoveAllListeners()
    end
    SetActive(self.ui.m_imgArrow.gameObject, false)
end

--- 更新界面布局
function UI_OneToOnePassport:UpdateLayout()
    local type, value = self.activityItem:GetPayTypeAndValue()
    local type3, value3 = self.activityItem:GetPayType3AndValue()
    -- 钻石购买（高级奖励）
    if type == 1 then
        SetActive(self.ui.m_imgDiamond, true)
        self.ui.m_txtDiamond.text = value
    -- 付费购买（高级奖励）
    elseif type == 2 then
        SetActive(self.ui.m_imgDiamond, false)
        local payConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.payment, value)
        self.ui.m_txtDiamond.text = LangMgr:GetLang(payConfig["price_langid"])
    end
    -- 钻石购买（豪华奖励）
    if type3 == 1 then
        SetActive(self.ui.m_imgRePrice3, true)
        self.ui.m_txtRePrice3.text = value3
    -- 付费购买（豪华奖励）
    elseif type3 == 2 then
        SetActive(self.ui.m_imgRePrice3, false)
        local payConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.payment, value3)
        self.ui.m_txtRePrice3.text = LangMgr:GetLang(payConfig["price_langid"])
    end
    -- 刷新通行证积分进度
    local energySch  = self:GetNowEnergySch()
    local nextScore = v2n(self.activityItem.form.exps[energySch])
    local showNowScore = self.nowScore
    if showNowScore > nextScore then
        showNowScore = nextScore
    end
    self.ui.m_txtScore.text = string.format("%d/%d", showNowScore, nextScore)
    self.ui.m_slider3.value = self.nowScore / nextScore
    -- self.ui.m_imgProgress.fillAmount = self:GetNowPer()
    -- 刷新高级奖励购买按钮
    local isShow = self.activityItem:IsBuyVip()
    if isShow == nil then isShow = false end
    SetActive(self.ui.m_btnUnlock.gameObject, not isShow)
    SetActive(self.ui.m_btnTag.gameObject, not isShow)
    SetActive(self.ui.m_goVipTag, isShow)
    -- 刷新豪华奖励购买按钮
    local isShow3 = self.activityItem:IsBuyVip3()
    if isShow3 == nil then isShow3 = false end
    SetActive(self.ui.m_btnVip3.gameObject, not isShow3)
    SetActive(self.ui.m_btnTag3.gameObject, not isShow3)
    SetActive(self.ui.m_goVipTag3, isShow3)

    local tex = self.ui.m_goVipTag.transform:GetComponent(typeof(UEUI.Text))
    tex.text = LangMgr:GetLang(4019)
    tex = self.ui.m_goVipTag3.transform:GetComponent(typeof(UEUI.Text))
    tex.text = LangMgr:GetLang(4019)

    local showLangId = self.activityItem:IsActivityEnd() and 1042 or 64
    self.ui.m_txtInfo.text = LangMgr:GetLang(showLangId)

    UIRefreshLayout(self.ui.m_panelVip)
    UIRefreshLayout(self.ui.m_panel)
    UIRefreshLayout(self.ui.m_btnUnlock)
    -- UIRefreshLayout(self.ui.m_imgProgress)
    UIRefreshLayout(self.contentRectTrans)
    UIRefreshLayout(self.ui.m_goBtnParent)
end

--- 获取当前等级
--- @return number 当前等级
function UI_OneToOnePassport:GetNowEnergySch()
    local maxCount = self.activityItem.form.max
    local schedule = self.activityItem.info.energySch + 1
    schedule = math.min(schedule, maxCount)
    return schedule
end

--- 获取当前进度
--- @return number 当前进度
function UI_OneToOnePassport:GetNowPer()
    local progress          = 0
    local energySch         = self:GetNowEnergySch()
    local nowMaxScore       = v2n(self.activityItem.form.exps[energySch])
    local per = self.nowScore * 1.0 / nowMaxScore * 0.1
    progress = progress + (energySch - 1)  * PerProgress + per
    return progress
end

--- 更新活动数据
function UI_OneToOnePassport:UpdateActivityData()
    self.nowScore = v2n(self.activityItem.info.integral)
    self.nowPer   = self:GetNowPer()
end

--- 显示物品详情弹窗
--- @param go any 物品
function UI_OneToOnePassport:ShowCommonTips(go)
    if not go then
        Log.Error(" ---go is nil ----------- in function ShowCommonTips")
        return
    end
    local itemId = GetControlExpand(go, 0)
    if not itemId then
        Log.Error(" ---itemId is nil ----------- in function ShowCommonTips")
        return
    end
    UI_SHOW(UIDefine.UI_ItemTips, itemId)
end

--- 显示物品详情弹窗
--- @param go any 物品
function UI_OneToOnePassport:ShowBuyCommonVip(go)
    if not go then
        Log.Error(" ---go is nil ----------- in function ShowBuyCommonVip")
        return
    end
    local btnLock = GetControlExpand(go,0)
    if not btnLock then
        Log.Error(" ---itemId is nil ----------- in function ShowBuyCommonVip")
        return
    end
    if btnLock == 2 then
        UI_SHOW(UIDefine.UI_BuyPassPort, self.activityId)
    elseif btnLock == 3 then
        UI_SHOW(UIDefine.UI_BuyPass3Port, self.activityId)
    end
end

--- 播放奖励云动画
function UI_OneToOnePassport:PlayRewardAnim()
    AudioMgr:Play(48)
    -- 先发奖励，根据领奖进度检查图腾奖励
    self:RewardAll()
    self.rCount = GetTableLength(self.rewardList)
    if self.rCount <= 0  then
        if self:IsHaveBubble() then
            self:DoCloseAnim()
        else
            self:RewardLogic()
        end
        return
    end
    self:EnableTouch(false)
    self.index = 0
    self.tagnum1 = 1
    local InfinityItems = {}
    local curMapID = NetUpdatePlayerData.playerInfo.curMap
    for i = 1, self.rCount do
        local itemId = self.rewardList[i][1]
        local itemCount = self.rewardList[i][3]
        local itemdata = ItemConfig:GetDataByID(itemId)
        local back_id = itemdata.back_id or MAP_ID_HOME_UNKNOW
        if itemdata.type_use == ItemUseType.Infinity then
            local item = {}
            item.itemID = itemId
            item.itemCount = itemCount
            table.insert(InfinityItems, item)
        else
            -- 积分不放进气泡里
            if v2n(itemId) == 73000 then
                back_id = curMapID
            end
            if back_id == MAP_ID_HOME_UNKNOW and IsHomeMap(curMapID) then
                self:AddTogoTarget(self.ui.m_goTarget, i, itemId, self.tagnum1)
                self.tagnum1 = self.tagnum1 + 1
            elseif back_id == curMapID or back_id == -1 then
                self:AddTogoTarget(self.ui.m_goTarget, i, itemId, self.tagnum1)
                self.tagnum1 = self.tagnum1 + 1
            else
                self:AddTogoTarget(self.ui.m_goTarget, i, itemId, self.tagnum1)
                self.tagnum1 = self.tagnum1 + 1
            end
        end
    end
    if not IsTableEmpty(InfinityItems) then
        local param = {}
        param.type = 15
        param.rewards = InfinityItems
        UI_SHOW(UIDefine.UI_Recharge, param, nil, false)
    end

    if self.activityItem:IsBuyVip() then
        self.bubbleStatus[1] = 1
        self.bubbleStatus[2] = 1
    else
        self.bubbleStatus[1] = 1
    end
    -- 检查图腾奖励
    -- local souvenirRewardStr = self.souvenirRewardStr
    -- if not IsNilOrEmpty(souvenirRewardStr) then
    --     souvenirRewardStr = string.sub(souvenirRewardStr, 1, string.len(souvenirRewardStr) - 1)
    --     local rewardTable = string.split(souvenirRewardStr, "|")
    --     local itemID = v2n(rewardTable[1])
    --     local index = self.tagnum1
    --     if index > 5 then index = 5 end
    --     local rewardGo = GetChild(self.ui.m_goTarget, "panel/pos" .. index, UEUI.Image)
    --     SetActive(rewardGo, true)
    --     local icon = ItemConfig:GetIcon(v2n(itemID))
    --     SetImageSprite(rewardGo, icon, false)
    --     GameUtil.SetLocalScale(rewardGo.transform, 0.1, 0.1, 0.1)
    --     DOScale(rewardGo.transform, 1.2, 0.5, function()
    --         DOScale(rewardGo.transform, 1.0, 0.5)
    --     end)
    -- end

    if self.tagnum1>1 then
        SetActive(self.ui.m_panelUI, false)
        SetActive(self.ui.m_goGetTarget, true)
    end
    local targetRT = GetComponent(self.ui.m_goTarget, UE.RectTransform)
    if targetRT then
        targetRT.position = Vector3.New()
    end
end

--- 奖励逻辑
function UI_OneToOnePassport:RewardLogic()
    SetActive(self.ui.m_goTarget, false)
    SetActive(self.ui.m_panelUI, false)
    self:ControlArrow(false)
    -- self:RewardAll()
    UI_UPDATE(UIDefine.UI_OneToOneView)
    self:Close()
end

--- 发放所有奖励
function UI_OneToOnePassport:RewardAll()
    local rewardStr = ""
    if self.rewardIndex <= 0 then
        return
    end
    local oIndex = self.activityItem:GetSchedule()--普通奖励的领取情况
    local vIndex = self.activityItem:GetVIPSchedule()--vip奖励的领取情况
    local v3Index = self.activityItem:GetVIP3Schedule()
    local indexList = {oIndex, vIndex, v3Index}

    local pass_id = {}
    local vip_pass_id = {}
    local vip3_pass_id = {}
    for i = 1, #indexList do
        if (i == 2 and self.activityItem:IsBuyVip()) or (i == 3 and self.activityItem:IsBuyVip3()) or i == 1 then
            for index, v in ipairs(self.itemList) do
                if index > indexList[i] and index <= self.rewardIndex then
                    local cId = v2n(NetSeasonActivity:GetChangeItemId(v[i][1]))
                    if cId < ItemID._RESOURCE_MAX then
                        NetUpdatePlayerData:AddResource(PlayerDefine[v[i][1]], v[i][2], nil, nil, "UI_OneToOnePassport")
                        MapController:AddResourceBoomAnim(0, 0, v[i][1], v[i][2])
                    else
                        local count = v[i][2]
                        local itemStr = v2s(cId) .. "|" .. v2s(count) .. ";"
                        if i == 1 then
                            table.insert(pass_id, v2n(index))
                        elseif i == 2 then
                            table.insert(vip_pass_id, v2n(index))
                        elseif i == 3 then
                            table.insert(vip3_pass_id, v2n(index))
                        end
                        rewardStr = rewardStr .. itemStr
                    end
                end
            end
        end
    end

    self.activityItem:SetSchedule(self.rewardIndex)
    if self.activityItem:IsBuyVip() then
        self.activityItem:SetVipSchedule(self.rewardIndex)
    end
    if self.activityItem:IsBuyVip3() then
        self.activityItem:SetVip3Schedule(self.rewardIndex)
    end

    -- 检查图腾奖励
    -- local souvenirRewardStr = NetOneToOneData:CheckPassportCompleteReward()
    -- self.souvenirRewardStr = souvenirRewardStr
    -- if not IsNilOrEmpty(souvenirRewardStr) then
    --     rewardStr = rewardStr .. souvenirRewardStr
    -- end

    rewardStr = string.sub(rewardStr, 1, string.len(rewardStr) - 1)

    if not IsNilOrEmpty(rewardStr) then
        MapController:SendRewardToMap(rewardStr, nil, nil, nil, "UI_OneToOnePassport", nil, true)
    end

    -- 通行证埋点
    if next(pass_id) ~= nil then
        for _, v in pairs(pass_id) do
            local thinkTable = {["battle_passfreelv"] = v}
            SdkHelper:ThinkingTrackEvent(ThinkingKey.battle_pass, thinkTable)
        end
    end
    if next(vip_pass_id) ~= nil then
        for _, v in pairs(vip_pass_id) do
            local thinkTable = {["battle_passiap1lv"] = v}
            SdkHelper:ThinkingTrackEvent(ThinkingKey.battle_pass, thinkTable)
        end
    end
    if next(vip3_pass_id) ~= nil then
        for _, v in pairs(vip3_pass_id) do
            local thinkTable = {["battle_passiap2lv"] = v}
            SdkHelper:ThinkingTrackEvent(ThinkingKey.battle_pass, thinkTable)
        end
    end
end

--- 是否有奖励气泡
--- @param nowIndex number 当前索引
--- @return boolean state 是否有奖励气泡
function UI_OneToOnePassport:IsHaveBubble(nowIndex)
    for i, v in ipairs(self.bubbleStatus) do
        if not nowIndex then
            if v > 0 then
                return true
            end
        end

        if nowIndex == i and v > 0 then
            return true
        end
    end
    return false
end

-- 播放关闭动画
function UI_OneToOnePassport:DoCloseAnim()
    if self.clickclose then
        return nil
    else
        self.clickclose = true
    end
    local atherv2 = MapController:GetFlyPos(FlyId.Air)
    atherv2.x = atherv2[1]
    atherv2.y = atherv2[2]
    local animnum = 0 -- 限制最后一个抵达的的动画播完后进行下一步
    if self.tagnum1 > 1 then
        animnum = animnum + 1
        TimeMgr:CreateTimer(UIDefine.UI_OneToOnePassport, function()
            EffectConfig:CreateEffect(136, atherv2.x, atherv2.y, 0, UIMgr.layers[UILayerType.Top])
            local targetRT2 = GetComponent(self.ui.m_goTarget, UE.RectTransform)
            DOLocalMoveY(targetRT2.transform, targetRT2.localPosition.y + 200, 0.2, function()
                DOLocalMoveY(targetRT2.transform, atherv2.y, 0.5, function()
                end, Ease.InQuad)
            end, Ease.OutQuad)
            DOLocalMoveX(targetRT2.transform, targetRT2.localPosition.x - 150, 0.1, function()
                DOLocalMoveX(targetRT2.transform, atherv2.x, 0.6, function()
                end, Ease.InQuad)
            end, Ease.OutQuad)
            DOScale(targetRT2.transform, 0.8, 0.3, function()
                DOScale(targetRT2.transform, 0, 0.5, function()
                    animnum = animnum - 1
                    if animnum <= 0 then
                        self:RewardLogic()
                    end
                end, Ease.InQuad)
            end, Ease.InQuad)
        end, (animnum - 1) * 0.3, 1)
    end
end

--- 是否可点击
--- @param isEnable boolean 是否可点击
function UI_OneToOnePassport:EnableTouch(isEnable)
    self.isPlayAnim = not isEnable
    self.ui.m_scrollview.enabled = isEnable
end

--- 添加到奖励云
--- @param Target any 父节点
--- @param i any 索引
--- @param itemId any 物品 ID
--- @param index any 数量索引
function UI_OneToOnePassport:AddTogoTarget(Target, i, itemId, index)
    if not Target.gameObject.activeSelf then
        SetActive(Target, true)
    end

    local function CloseUI()
        if self.activityItem:IsBuyVip() then
            self.bubbleStatus[1] = 1
            self.bubbleStatus[2] = 1
        else
            self.bubbleStatus[1] = 1
        end
        self:EnableTouch(true)
        self.rewardList = {}
    end
    local bubbleAnim    = GetChild(Target, "bubble", UE.Animation)
    local panel         = GetChild(Target, "panel")
    local cCount        = panel.transform.childCount
    local itemGo        = self.rewardList[i][2]
    local nodeList      = self:GetItemGoNodeList(itemGo)
    SetActive(nodeList.effect, false)
    nodeList.iconAnim:Stop(IconAnimName)
    SetActive(nodeList.rewardTag, true)
    SetActive(nodeList.kelinqu, false)
    SetUIImageGray(nodeList.itemIcon, true)
    bubbleAnim:Play("animation_Getitems")
    if self.rCount < cCount then
        self:PlayRewardFinish(panel, i, index, self.rCount)
        if index == self.rCount then
            CloseUI()
        end
    else
        if index <= cCount then
            self:PlayRewardFinish(panel, i, index, self.rCount, function()
                if self.rCount == cCount then
                    CloseUI()
                end
            end)
        elseif i >= self.rCount then
            CloseUI()
        end
    end

    self.isPlayAnim = false
end

--- 播放奖励动画完成
--- @param panel any 父节点
--- @param i any 索引
--- @param index any 数量索引
--- @param count any 数量
--- @param callBack any 回调
function UI_OneToOnePassport:PlayRewardFinish(panel, i, index, count, callBack)
    local rewardGo = GetChild(panel, "pos" .. index, UEUI.Image)
    local itemId = self.rewardList[i][1]

    SetActive(rewardGo, true)
    GameUtil.SetLocalScale(rewardGo.transform, 0.1, 0.1, 0.1)
    SetImageSprite(rewardGo, ItemConfig:GetIcon(itemId), false)
    DOScale(rewardGo.transform, 1.2, 0.5, function()
        DOScale(rewardGo.transform, 1.0, 0.5, function()
            if index >= count then
                self:EnableTouch(true)
            end
            LuaCallback(callBack)
        end)
    end)
end

--- 获取活动剩余时间
--- @return integer time 剩余时间
function UI_OneToOnePassport:GetActiveTime()
    local condition = self.activityItem.info.state
    local time = 0
    if condition == 1 then
        time = self.activityItem:GetRemainingTime()
    elseif condition == 3 then
        time = self.activityItem:GetStartRemainingTime()
    elseif condition == 4 then
        time = self.activityItem:GetWaitTime()
    end
    return time
end

return UI_OneToOnePassport