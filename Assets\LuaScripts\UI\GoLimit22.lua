local GoLimit22 = {}
local M = GoLimit22
local prePath = "Assets/ResPackage/Prefab/UI/GoLimit22.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.count = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
    self.red = GetChild(self.go,"doLimit/bg/goPoint")
    self.text_progress = GetChild(self.go,"doLimit/bg/Limit/text_progress",UEUI.Text)
	self.imgProgress = GetChild(self.go,"doLimit/bg/Limit/img",UEUI.Image)
	self.txtRank = GetChild(self.go,"doLimit/goRank/txt",UEUI.Text)
	
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        UI_SHOW(UIDefine.UI_SkiingMatch)
    end)
    self.slider = GetChild(self.go,"doLimit/bg/Limit/Slider",UEUI.Image)
  
end
--init
function M:SetItem(param)
    self.id = param.id
	NetSkiingMatchData.data.activeId = param.id
    local activeInfo = LimitActivityController:GetActiveMessage(self.id)
	self.activeInfo = activeInfo
    self.active = activeInfo.form.activeMess
    self.totalType = param.totalType
    self.condition = param.condition
    SetImageSprite(self.icon,self.active.icon,false)
	SetImageSprite(self.imgProgress, ItemConfig:GetIcon(73006), false)

    self.rewardConfig = SkiingMatchManager:GetAllRewardCfgByActiveId(self.id)
    if NetSkiingMatchData.data.rewardLevel + 1 > #self.rewardConfig then
        self.text_progress.text = LangMgr:GetLang(5003)
    else
        local nextPoint = self.rewardConfig[NetSkiingMatchData.data.rewardLevel + 1].need_point
        local present = NetSkiingMatchData.data.rewardPoint / nextPoint
        self.slider.fillAmount = present
        self.text_progress.text = math.floor(present * nextPoint) .. "/" .. nextPoint
        -- self:SetProgress()
    end
	self:SetRedShow()
	self:SetRankTxt()
end

function M:SetProgress()
    local upgrageNum = NetSkiingMatchData.upgradeLevel
    if not upgrageNum then
        upgrageNum = 0
    end
    self.slider:DOKill()
    local startLevel = NetSkiingMatchData.data.rewardLevel - upgrageNum
    local nextLevel = startLevel + 1
    local isFullLevel = false
    if nextLevel > #self.rewardConfig then
        isFullLevel = true
    end
    if not isFullLevel then
        local nextPoint = self.rewardConfig[nextLevel].need_point
        if upgrageNum > 0 then
            upgrageNum = upgrageNum - 1
            self.slider:DOFillAmount(1,0.5):OnComplete(function ()
                NetSkiingMatchData.upgradeLevel = NetSkiingMatchData.upgradeLevel - 1
                if upgrageNum > 0 then
                    self.slider.fillAmount = 0
                    nextLevel = nextLevel + 1
                    nextPoint = self.rewardConfig[nextLevel].need_point
                    self.slider:DOFillAmount(1,0.5):SetLoops(upgrageNum,LoopType.Restart):SetEase(Ease.Linear):OnUpdate(function ()
                        self.text_progress.text = Mathf.Floor(self.slider.fillAmount * nextPoint + 0.5)  .. "/" .. nextPoint
                    end):OnComplete(function ()
                        nextLevel = nextLevel + upgrageNum
                        if nextLevel > #self.rewardConfig then
                            isFullLevel = true
                            self.text_progress.text = LangMgr:GetLang(5003)
                            self.slider:DOKill()
                            NetSkiingMatchData.upgradeLevel = 0
                            return
                        end
                        nextPoint = self.rewardConfig[nextLevel].need_point
                        local persent = NetSkiingMatchData.data.rewardPoint / nextPoint
                        self.slider.fillAmount = 0
                        self.slider:DOFillAmount(persent,0.5 * persent):SetEase(Ease.Linear):OnUpdate(function ()
                            self.text_progress.text = Mathf.Floor(self.slider.fillAmount * nextPoint + 0.5)  .. "/" .. nextPoint
                        end)
                        NetSkiingMatchData.upgradeLevel = 0
                    end)
                else
                    nextLevel = nextLevel + 1
                    if nextLevel > #self.rewardConfig then
                        isFullLevel = true
                        self.text_progress.text = LangMgr:GetLang(5003)
                        self.slider:DOKill()
                        return
                    end
                    if not isFullLevel then
                        local nextPoint = self.rewardConfig[nextLevel].need_point
                        local persent = NetSkiingMatchData.data.rewardPoint / nextPoint
                        self.slider.fillAmount = 0
                        self.slider:DOFillAmount(persent,0.5 * persent):SetEase(Ease.Linear):OnUpdate(function ()
                            self.text_progress.text = Mathf.Floor(self.slider.fillAmount * nextPoint + 0.5)  .. "/" .. nextPoint
                        end)
                    end
                end
            end):SetEase(Ease.Linear):OnUpdate(function ()
                self.text_progress.text = Mathf.Floor(self.slider.fillAmount * nextPoint + 0.5)  .. "/" .. nextPoint
            end):SetDelay(0.7)
        else
            local persent = NetSkiingMatchData.data.rewardPoint / nextPoint
            self.slider:DOFillAmount(persent,0.5 * persent):SetEase(Ease.Linear):OnUpdate(function ()
                self.text_progress.text = Mathf.Floor(self.slider.fillAmount * nextPoint + 0.5)  .. "/" .. nextPoint
            end):SetDelay(0.7)
        end
        
    else
        self.text_progress.text = LangMgr:GetLang(5003)
    end
end
function M:ChangState(id)

end
function M:ChangeValue()
    self:SetProgress()
	self:SetRedShow()
	self:SetRankTxt()
end
---tick
function M:ChangeItem()
	if not self.activeInfo then
		return
	end
	
	local time = self.activeInfo:GetRemainingTime()
	if time > 0 then
		self.count.text = TimeMgr:CheckHMSNotEmpty(time)
	else
		self.count.text = "0"
	end
end
function M:SetRedShow()
	local isShow = SkiingMatchManager:IsShowRed()
    SetActive(self.red,isShow)
end

function M:Close()
    TimeMgr:DeleteTimer("GoLimit22")
    UEGO.Destroy(self.go)
end

function M:SetRankTxt()
	self.txtRank.text = tostring(NetSkiingMatchData:GetCurRank()) or ""
end


return M