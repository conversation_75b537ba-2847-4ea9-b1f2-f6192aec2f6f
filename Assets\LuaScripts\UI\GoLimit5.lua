local GoLimit5 = {}
local M = GoLimit5

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit5.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init() 
    --self.state2 = GetChild(self.go,"doLimit/bg/state2")
    self.state3 = GetChild(self.go,"doLimit/bg/state3")
    self.act = GetChild(self.go,"doLimit")
    self.bg = GetChild(self.go,"doLimit/bg",UEUI.Image)
    self.bgRect = GetComponent(self.bg.gameObject, UE.RectTransform)
    self.icon = GetChild(self.go,"doLimit/bg/eptmy/icon",UEUI.Image)
    self.countGo = GetChild(self.go,"doLimit/bg/CountDown")
	self.count = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
	self.Red = GetChild(self.go,"doLimit/bg/Red")
    --self.timeLang = GetChild(self.state2,"countDown",UEUI.Text)
    --self.needLevel = GetChild(self.state3,"Image/needLevel",UEUI.Text)
    self.doAni =  GetChild(self.go,"doLimit",TweenAnim)

    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
    EventMgr:Add(EventID.CHANGE_ACTIVE_STATE,self.ChangState,self)
    EventMgr:Add(EventID.ADD_INTEGRAL,self.PlayAni,self)

end

function M:SetRedShow(IsShow)
	if IsShow then
		SetActive(self.Red,IsShow)
	else
		SetActive(self.Red,NetSavingBank:RedIsShow())
	end
end

function M:PlayAni(id)
    if id == self.id then
        self.doAni:DORestart()
    end
end

function M:SetItem(param)
    self.id = param.id
    self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
    local active = self.activeInfo.form.activeMess
    self.totalType = param.totalType
    self.condition = param.condition
    --self.needLevel.text = active.level
    SetImageSprite(self.icon,active.icon,false)
    self:ChangeItem()
    self:ChangState()
end

function M:ChangState(id)
    --刷新时要判断id
    if id and id ~= self.id then return end
    local condition = self.condition
    if condition == 1 then
        SetActive(self.state3,false)
        SetActive(self.countGo,true)
        --self.timeLang.text = LangMgr:GetLang(7075)
    --elseif condition == 2 then
    --    SetActive(self.state3,true)
    --    SetActive(self.countGo,false)
    --else
    --    SetActive(self.state3,false)
    --    SetActive(self.countGo,true)
    --    if condition == 3 then
    --        self.timeLang.text = LangMgr:GetLang(7073)
    --    else
    --        self.timeLang.text = LangMgr:GetLang(7074)
    --    end
    end
end

function M:ChangeValue()

end

function M:ChangeItem()
    --if self.condition == 2 then return end
    if self.condition == 1 then
		local time =self.activeInfo:GetRemainingTime()
        self.count.text = TimeMgr:CheckHMSNotEmpty(time)
		
    --elseif self.condition == 3 then
    --    self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetStartRemainingTime())
    --elseif self.condition == 4 then
    --    self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetWaitTime())
    end
end

function M:ClickItem(arg1)
    if self.condition == 1 or self.condition == 4 then
		if self.activeInfo.form.activeMess.total_type == ActivityTotal.DiyGift then
			UI_SHOW(UIDefine.UI_DiyGift)
		else
			UI_SHOW(UIDefine.UI_SavingBank,self.id)
		end
		
    elseif self.condition == 2 then
        UI_SHOW(UIDefine.UI_WidgetTip,LangMgr:GetLangFormat(7076,self.activeInfo.form.activeMess.level))
    elseif self.condition == 3 then
        UI_SHOW(UIDefine.UI_UpcomingEvents,self.id)
    end
end

function M:Close()
    EventMgr:Remove(EventID.CHANGE_ACTIVE_STATE,self.ChangState,self)
    EventMgr:Remove(EventID.ADD_INTEGRAL,self.PlayAni,self)

    UEGO.Destroy(self.go)
end

return M