local UI_LeagueChatGift = Class(BaseView)

local BubbleItem = require("UI.BubbleItem");
local LeagueChatHelpPeople = require ("UI.LeagueChatHelpPeople");

local tableViewV = nil

local tb_GiftPeopleView = {}

local str_timerKey_GiftPeopleView = string.format("%s_%s", UIDefine.UI_LeagueChatGift, "GiftPeople")

local str_key_Id_Gift = "playerId"

function UI_LeagueChatGift:OnInit()
	self.isFristIn = true;
	self.isCheckClose = false
    EventMgr:Add(EventID.UNION_ID_CHANGE, self.CheckClose, self);
	EventMgr:Add(EventID.REFRESH_CHAT_ONLYRELOADVIEW,self.ReloadView,self)
end

function UI_LeagueChatGift:OnCreate(param)
	tableViewV = GetChild(self.ui.m_goChargeGift, "goSlideRect", CS.Mosframe.TableView)
	
	--创建头像
	local headNode = GetChild(self.ui.m_goItem, "headNode")
	CreateCommonHead(headNode.transform,0.8)
	
	self.bubbleItem = BubbleItem.new("UI_LeagueChatGift");
    self.bubbleItem:Init(self.uiGameObject, {x = -350, y = 800}, function()
        self:Close();
    end);
	
	self:onRefreshList()
end

function UI_LeagueChatGift:OnRefresh(param)
    
end

function UI_LeagueChatGift:onDestroy()
	if not self.isCheckClose and self.bubbleItem and self.bubbleItem:IsHaveItem() then
		self.isCheckClose = true;
		self.bubbleItem:SetCloseCall(nil);
		self.bubbleItem:CloseImmediately();
	end
	
	EventMgr:Remove(EventID.REFRESH_CHAT_ONLYRELOADVIEW,self.ReloadView,self)
	EventMgr:Remove(EventID.UNION_ID_CHANGE, self.CheckClose, self);
	
	self:onClearGiftPeopleView()
	tableViewV = nil
end

function UI_LeagueChatGift:onUIEventClick(go,param)
    local name = go.name

	if name == "closeBtn" then
		self:CheckClose()
	end
end

function UI_LeagueChatGift:AutoClose()
    self:CheckClose();
end

function UI_LeagueChatGift:CheckClose()
    if not self.isCheckClose and self.bubbleItem and self.bubbleItem:IsHaveItem() then
        self.isCheckClose = true;
        self.bubbleItem:Close();
    else
        self.isCheckClose = true;
        self:Close();
    end
end

function UI_LeagueChatGift:onRefreshList()
	self:onClearGiftPeopleView()
	
	self:initEmptyState()
	
	self:LoadTableView_Gift()
end

function UI_LeagueChatGift:initEmptyState()
	local giftCount = LeagueChatManager:GetLenChargeGiftList()
	SetActive(self.ui.m_goNoChargeGift, giftCount <= 0)
end

function UI_LeagueChatGift:onClearGiftPeopleView()
	for k, v in pairs(tb_GiftPeopleView) do
		if v.timer then
			TimeMgr:DestroyTimer(str_timerKey_GiftPeopleView, v.timer)
		end
		if v.cp then
			v.cp:onDestroy()
			v.cp = nil
		end
	end
	tb_GiftPeopleView = {}
end

function UI_LeagueChatGift:ReloadView()
	--is_OnlyRefreshGiftView = true
	tableViewV:ReloadData()
end

function UI_LeagueChatGift:LoadTableView_Gift()
	local iCount = LeagueChatManager:GetLenChargeGiftList()
	tableViewV.GetItemCount = function (num)
		return iCount
	end
	tableViewV.GetItemGo = function(obj)
		return self.ui.m_goItem;
	end
	tableViewV.UpdateItemCell = function(idx, obj)
		self:LoadCellData_Gift(idx, obj)
	end

	if self.isFristIn then
		self.isFristIn = false;

		local data;
		local selfPlayerID = NetUpdatePlayerData:GetPlayerInfo().id;
		local iGiftCount = v2n(ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 6).value) + LeagueManager:GetScienceAdd(SCIENCE_TYPE.UNION_GIFT);
		local index = iCount;
		for i = 1, iCount do
			data = LeagueChatManager:GetChargeGiftSortByIndex(i);

			local isSelfRewarded = false;
			for k, v in pairs(data.getPIds) do
				if v2n(k) == selfPlayerID then
					isSelfRewarded = true;
					break;
				end
			end

			if not isSelfRewarded and GetTableLength(data.getPIds) < iGiftCount then
				index = i;
				break;
			end
		end
		tableViewV:InitTableViewByIndex(index - 1);
	end
end

function UI_LeagueChatGift:LoadCellData_Gift(index, obj)
	local tId = index + 1
	local realId = v2n(obj.name)
	
	local childObj
	for i = 1, obj.transform.childCount do
		childObj = obj.transform:GetChild(i-1).gameObject
		SetActive(childObj, false)
	end
	
	local selfPlayerID = NetUpdatePlayerData:GetPlayerInfo().id
	local chatSort = LeagueChatManager:GetChargeGiftSortByIndex(tId)
	local useObj
	
	if v2n(chatSort[str_key_Id_Gift]) ~= selfPlayerID then
		useObj = GetChild(obj, "m_gotChargeGiftCell_Other")
	else
		useObj = GetChild(obj, "m_gotChargeGiftCell_Self")
	end
	SetActive(useObj, true)

	local imgHead = GetChild(useObj, "imgHeadBg/imgHead", UEUI.Image)
	local headBtn = GetChild(useObj, "imgHeadBg/imgHead", UEUI.Button)
	local goChatBg = GetChild(useObj, "imgChatBg")
	local imgRolePositon = GetChild(useObj, "imgChatBg/imgRolePositon", UEUI.Image)
	local txtName = GetChild(useObj, "imgChatBg/goTop/txtName", UEUI.Text)
	local txtRolePositon = GetChild(useObj, "imgChatBg/imgRolePositon/txtRolePositon", UEUI.Text)

	SetImageSprite(imgHead, FriendManager:GetHeadIcon(chatSort["icon"]), false)

	local isMyself = v2n(chatSort[str_key_Id_Gift]) == selfPlayerID
	local playerData = chatSort
	local headNode = GetChild(obj, "headNode")
	local customHeadObj = GetChild(obj,"headNode/CustomHead")
	SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border,function()
		if not isMyself then
			FriendManager:ShowPlayerById(v2n(chatSort[str_key_Id_Gift]))
		end
	end)
	SetActive(headNode,true)
	local nodeRect = GetComponent(headNode,UE.RectTransform)
	nodeRect.anchoredPosition = isMyself  and Vector2.New(375,35) or Vector2.New(-371,24)
	
	
	txtName.text = chatSort["name"] or ""

	local iLeagueDuty = chatSort["leagueDuty"] or 3
	if iLeagueDuty < 3 then
		local _, imgPath = LeagueManager:GetDutyByType(iLeagueDuty);
		SetImageSprite(imgRolePositon, imgPath, false)
	end
	SetActive(imgRolePositon.gameObject, (iLeagueDuty < 3))
	if iLeagueDuty < 3 then
		local str = {LangMgr:GetLang(9263), LangMgr:GetLang(9264)}
		txtRolePositon.text = str[iLeagueDuty]
	end
	
	local txtCountDown = GetChild(useObj, "imgChatBg/goTop/txtCountDown", UEUI.Text)
	local txtTips = GetChild(useObj, "imgChatBg/goTop/txtTips", UEUI.Text)
	
	local btnReward = GetChild(useObj, "imgChatBg/btnReward", UEUI.Button)
	local txtReward = GetChild(btnReward, "txtReward", UEUI.Text)
	
	local goRewarded = GetChild(useObj, "imgChatBg/btnRewarded")
	local txtRewarded = GetChild(goRewarded, "txtRewarded", UEUI.Text)
	
	local goFull = GetChild(useObj, "imgChatBg/btnFull")
	local txtFull = GetChild(goFull, "txtFull", UEUI.Text)
	
	--local TableViewH = GetChild(useObj, "imgChatBg/goSlideRect", CS.Mosframe.TableView)
	local goContent = GetChild(useObj, "imgChatBg/goSlideRect/Viewport/Content")
	
	local applyTime = v2n(chatSort["time"]) or 0
	local OverDeltaTime = v2n(ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 15).value) or 0
	local curTime = TimeMgr:GetServerTimestamp()
	local t = (applyTime + OverDeltaTime) - curTime

	txtCountDown.text = TimeMgr:CheckHMSNotEmpty(t)

	--txtTips.text = LangMgr:GetLang(9113)
	local strGiftName = " "
	if chatSort["paymentId"] then
		local cfgGift = ConfigMgr:GetDataByID(ConfigDefine.ID.payment, v2n(chatSort["paymentId"]))
		if cfgGift then
			strGiftName = LangMgr:GetLang(v2n(cfgGift.pay_name)) or " "
		end
	end
	txtTips.text = LangMgr:GetLangFormat(9113, strGiftName)
	
	txtReward.text = LangMgr:GetLang(17)
	txtFull.text = LangMgr:GetLang(17)
	txtRewarded.text = LangMgr:GetLang(71)
	
	if not tb_GiftPeopleView[realId] then
		tb_GiftPeopleView[realId] = {}
	end

	local iIndex = 1
	local tbGiftPIds = chatSort["getPIds"]
	tb_GiftPeopleView[realId].tbGiftPIds = {}
	for k, v in pairs(tbGiftPIds) do
		tb_GiftPeopleView[realId].tbGiftPIds[iIndex] = {}
		tb_GiftPeopleView[realId].tbGiftPIds[iIndex].id = v2n(k)
		tb_GiftPeopleView[realId].tbGiftPIds[iIndex].icon = v2n(v)

		iIndex = iIndex + 1
	end

	local iGiftCount = v2n(ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 6).value) + LeagueManager:GetScienceAdd(SCIENCE_TYPE.UNION_GIFT);
	if not tb_GiftPeopleView[realId].cp then
		tb_GiftPeopleView[realId].cp = LeagueChatHelpPeople.new()
		tb_GiftPeopleView[realId].cp:OnInit()
		
		local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_LeagueChatHeadCell")
		local HeadObj = ResMgr:LoadAssetSync(assetPath, AssetDefine.LoadType.Instant)
		tb_GiftPeopleView[realId].cp:SetCellGo(HeadObj)
	end
	tb_GiftPeopleView[realId].cp:SetPlayerID(v2n(chatSort[str_key_Id_Gift]))
	--tb_GiftPeopleView[realId].cp:SetParentIndex(tId)
	--tb_GiftPeopleView[realId].cp:SetTableViewH(TableViewH)
	tb_GiftPeopleView[realId].cp:SetCount(iGiftCount)
	tb_GiftPeopleView[realId].cp:SetType(2)
	tb_GiftPeopleView[realId].cp:SetGift(v2n(chatSort["unionGiftId"]))
	tb_GiftPeopleView[realId].cp:SetPeopleData(tb_GiftPeopleView[realId].tbGiftPIds)
	--tb_GiftPeopleView[realId].cp:SetCellGo(self.ui.m_goHeadShow)
	tb_GiftPeopleView[realId].cp:SetPeopleParent(goContent)

	
	local isSelfRewarded = false --自己是否已经领取过
	for k, v in pairs(tb_GiftPeopleView[realId].tbGiftPIds) do
		if v.id == selfPlayerID then
			isSelfRewarded = true
			break
		end
	end

	local isFull = (GetTableLength(tb_GiftPeopleView[realId].tbGiftPIds) >= iGiftCount)

	SetActive(btnReward.gameObject, not isSelfRewarded and not isFull)
	SetActive(goFull, not isSelfRewarded and isFull)
	SetActive(goRewarded, isSelfRewarded)
	
	RemoveUIComponentEventCallback(btnReward, UEUI.Button)
	if isSelfRewarded == false then
		AddUIComponentEventCallback(btnReward, UEUI.Button, function(go, param)
			local giftId = v2n(param[1])
			local unionGiftId = v2n(param[2])
			local itemNum = 1

			local function callback(unionGiftId)
				if self.ui == nil then return end

				local itemNum = 1
				if unionGiftId < ItemID._RESOURCE_MAX then
					NetUpdatePlayerData:AddResource(PlayerDefine[unionGiftId], itemNum, nil, nil, "union_help_complete")
				else
					self.bubbleItem:FlyItem(unionGiftId, itemNum, go.transform.position);
					Game.Save(2);
				end
			end
			LeagueChatManager:RewardGiftItem(giftId, unionGiftId, callback)
		end, { chatSort["giftId"], chatSort["unionGiftId"] })
	end

	
	tb_GiftPeopleView[realId].cp:ReloadPeopleView()

	local index = 1;
	local list = tb_GiftPeopleView[realId].tbGiftPIds;
	for i = 1, iGiftCount do
		if not list[i] then
			index = i;
			break;
		end
	end
	self:MoveItemIndex(goContent, index);
	--if tb_GiftPeopleView[realId].timer then
		--TimeMgr:DestroyTimer(str_timerKey_GiftPeopleView , tb_GiftPeopleView[realId].timer)
	--end

	--tb_GiftPeopleView[realId].timer = TimeMgr:CreateTimer(str_timerKey_GiftPeopleView,
		--function(param)
			--local id = param[1]
			--tb_GiftPeopleView[id].cp:LoadTableView_People()
		--end,
		--0.1, 1, tId)
	
	--RemoveUIComponentEventCallback(headBtn,UEUI.Button)
	--AddUIComponentEventCallback(headBtn, UEUI.Button, function(arg1,arg2)
	--	if v2n(chatSort[str_key_Id_Gift]) ~= selfPlayerID then
	--		FriendManager:ShowPlayerById(v2n(chatSort[str_key_Id_Gift]))
	--	end
	--end)
end

function UI_LeagueChatGift:MoveItemIndex(goContent, index)
    local totalWidth = 780;
    local cellWidth = 105;
    local offsetX = index * cellWidth - totalWidth;
    if offsetX > 0 then
        DOLocalMoveX(goContent.transform, -offsetX, 0.2, nil, Ease.Linear);
    end
end

return UI_LeagueChatGift