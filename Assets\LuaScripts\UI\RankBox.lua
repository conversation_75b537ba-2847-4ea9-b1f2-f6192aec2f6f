local RankBox = Class()

function RankBox:ctor(parent)
	self.parent = parent
end

function RankBox:UpdateItem(targetObj, rewardStr)
	self.bg = GET_UI(self.BoxGo, "bg", TP(UEUI.Image))
	SetActive(self.BoxGo, true)

	local pos = UIMgr:GetUIPosByWorld(targetObj.transform.position)
	self.BoxGo.transform.localPosition = pos
	self.BoxGo.transform.localScale = Vector3.New(1, 1, 1)
	rewardStr = NetSeasonActivity:GetChangeItemId(rewardStr)
	local rewardConfig = rewardStr
	local cfgList = string.split(rewardConfig, ";")
	local lenth = #cfgList
	local items = GET_UI(self.BoxGo, "rewardList", "RectTransform")
	local itemLen = items.childCount
	for i = 1, itemLen do
		local item = GetChild(items, "item" .. i)
		local rewardImg = GetChild(item, "icon",  UEUI.Image)
		local num       = GetChild(item, "m_txtNum", UEUI.Text)
		if i <= lenth then
			SetActive(rewardImg.transform.parent, true)
			local info = string.split(cfgList[i], "|")
			local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, v2n(info[1]))
			SetImageSync(rewardImg, itemConfig.icon_b, false)
			num.text = "x"..info[2]

			if self.onClick then
				RemoveUIComponentEventCallback(item)
				AddUIComponentEventCallback(item, UEUI.Button, function(arg1,arg2)
					if self.onClick then
						self.onClick(itemConfig.id)
					end
				end)
			end

		else
			SetActive(rewardImg.transform.parent, false)
		end
	end
	UIRefreshLayout(self.bg)
	UIRefreshLayout(items)

	local bgWidth = (84 * lenth + 30 * (lenth - 1) + 60) / 2;
	local halfWidth = UIWidth / 2;
	if pos.x + bgWidth > halfWidth then
		SetUIPos(self.bg, -(pos.x + bgWidth - halfWidth), 30);
	elseif pos.x - bgWidth < -halfWidth then
		SetUIPos(self.bg, -(pos.x - bgWidth + halfWidth), 30);
	end
end

function RankBox:IsInRankBoxRect(touchPos)
	if self.bg then
		local region = GetComponent(self.bg,UE.RectTransform)
		return UE.RectTransformUtility.RectangleContainsScreenPoint(region,touchPos,UIMgr.uiCamera)
	end
	return false
end

function RankBox:InitUI(style,onClick,callBack)
	if not self.parent then
		Log.Error("RankBox Parent is nil")
		return
	end

	self.onClick = onClick
	self.loadCallBack = callBack

	if self.BoxGo == nil then
		self.BoxGo = FineFromParent(self.parent, "Box")

		local pName = "ListRankReward"
		if style then
			pName = pName..style
		end

		local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, pName)
		ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant,function(obj)
			self.BoxGo = UEGO.Instantiate(obj)
			self.BoxGo.transform:SetParent(self.parent.transform)
			self.BoxGo.transform.localScale = Vector3.New(1, 1, 1)
			self.BoxGo.name = "Box"
			if self.loadCallBack then
				self.loadCallBack()
			end
		end)
	end
end

function RankBox:Destory()
	if self.BoxGo then
		UEGO.Destroy(self.BoxGo)
		self.BoxGo = nil
	end
end

function RankBox:SetCanvasOrder(value)
	if self.boxCanvas == nil then
		self.boxCanvas = GetComponent(self.BoxGo, TP(UE.Canvas))
	end
	if self.boxCanvas then
		--local tSortingOrder = leftTopCanvas.sortingOrder
		self.boxCanvas.sortingOrder = value
	end
end

function RankBox:Close()
	SetActive(self.BoxGo, false)
end

return RankBox