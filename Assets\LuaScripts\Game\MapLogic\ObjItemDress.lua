
local ObjItem = require "Game.MapLogic.ObjItem"
local ObjItemDress = Class(ObjItem)
local M = ObjItemDress

function M:onSaveInf(inf)
	local data = {}
	data["useSkinId"] = self.useSkinId
	--data["exp"] = self.m_Exp
	--data["lv"] = self.m_Lv
	--data["ts"] = self.m_Timestamp
	--data["rewardRankItem"] = self.m_RankItem
	--data["drop"] = self.m_ObjDrop:Save()
	inf["newDress"] = data
end


function M:onInit(inf)
	self.useSkinId = 0
	self.skinDecorateTrans = nil
	self.skinDefDecorateTrans = nil
	self.tileSkinedList = {}
	self.m_OrderLayerId = SortingLayerInGame.WORLD_TILE
	self:setOrderLayer(SortingLayerInGame.WORLD_TILE)
	if inf then
		local data = inf["newDress"]
		if data then
			self.useSkinId = data["useSkinId"]
			--self.m_Exp = data["exp"]
			--self.m_Lv = data["lv"]
			--self.m_Timestamp = data["ts"]
			--self.m_RankItem = data["rewardRankItem"]
			--self.m_ObjDrop:Load(data["drop"])
		end
	end
	if nil ~= self.m_Config.id_use4 then
		self.skin_type = v2n(self.m_Config.id_use4)
	else
		Log.Error(self.m_Id,".id_use is nil ###")
	end
	--if tonumber(self.m_Id) < 15201 then
		--self.skin_type = NetHandbook.ID.Floor
	--else
		--self.skin_type = NetHandbook.ID.Island
	--end

	-- EventMgr:Add(EventID.ALL_LOAD_END, self.OnAllLoadEnd, self)
	-- EventMgr:Add(EventID.ALL_VISIT_LOAD_END, self.OnAllLoadEnd, self)
end


function M:ChangeSkin(skinId,icon_path,isPreview)
	if skinId < 0 then return end

	local skincfg = ConfigMgr:GetDataByID(ConfigDefine.ID.skin_collection,  skinId)

	local is_have = NetHandbook:IsHaveSkin(self.skin_type,skinId) or MapControllerVisit:IsVisit()
	if isPreview then
		is_have = true
	end
	if is_have then
		if not isPreview then
			self.useSkinId = skinId
		end
		EventMgr:Dispatch(EventID.MAP_FLOOR_DRESS)
		self:LoadSkin(skincfg,icon_path,isPreview)
	end
end


function M:HeightShow(IsShow)
	if IsShow then
		self.OldLayer = self.m_OrderLayerId
		self:setOrderLayer(SortingLayerInGame.UI_TOP)
		self:SetDecorateLayer(SortingLayerInGame.UI_TOP)
	else
		if self.OldLayer ~= nil then
			self:setOrderLayer(self.OldLayer)
			self:ResetDecorateLayer()
		end
	end
end

function M:SetDecorateLayer(layerID,order)
	if not layerID then
        layerID = self.m_OrderLayerId
    end
    if not order then
        order = self.m_OrderIdx
    end

	--设置层级
	local renders = self.m_TransSpinePa:GetComponentsInChildren(TP(UE.Renderer),true)
	for i = 0, renders.Length - 1 do
		if not self.CacheDecorateLayerData then
			self.CacheDecorateLayerData = {}
		end
		self.CacheDecorateLayerData[renders[i]] = {[1] = renders[i].sortingLayerID,[2] = renders[i].sortingOrder}
		SetRendererOrder(renders[i], layerID, order)
	end
end

function M:ResetDecorateLayer()
	local renders = self.m_TransSpinePa:GetComponentsInChildren(TP(UE.Renderer),true)
	for i = 0, renders.Length - 1 do
		if self.CacheDecorateLayerData and self.CacheDecorateLayerData[renders[i]] then
			local cache = self.CacheDecorateLayerData[renders[i]]
			SetRendererOrder(renders[i], cache[1], cache[2])
		end
	end
end

function M:ResetSkin()
	local mess
	if self.useSkinId > 0 then
		mess = SkinCollectConfig:GetDataByID(self.useSkinId)
	else
		mess = SkinCollectConfig:GetDefaultSkin(self.m_Id)
	end
	EventMgr:Dispatch(EventID.MAP_FLOOR_DRESS)
	self:LoadSkin(mess)
end

function M:LoadSkin(config,icon_path,isPreview)
	if not icon_path then
		icon_path = config.skin_icon
	end
	
	ResMgr:LoadAssetAsync(icon_path,AssetDefine.LoadType.Sprite,function(spr)
		SetSprite3D(self.m_SpItem, spr)
	end)

	--默认装饰
	if config.default_decorate then
		if self.skinDefDecorateTrans then
			UEGO.Destroy(self.skinDefDecorateTrans.gameObject)
			self.skinDefDecorateTrans = nil
		end
		ResMgr:LoadAssetAsync(config.default_decorate,AssetDefine.LoadType.Instant,function(prefab)
			if prefab then
				local go,trans = CreateGOAndTrans(prefab)
				go.name = "skinDefDecorate"
				trans:SetParent(self.m_TransSpinePa)
				trans:SetLocalPosition(0,-0.7,0)
				trans:SetLocalScale(1,1,1)

				self:ResetTransSpinePa()

				self.skinDefDecorateTrans = trans

			end
		end)
	else
		if self.skinDefDecorateTrans then
			SetActive(self.skinDefDecorateTrans,false)
		end
	end

	--装饰
	if config.skin_decorate then
		if self.skinDecorateTrans then
			UEGO.Destroy(self.skinDecorateTrans.gameObject)
			self.skinDecorateTrans = nil
		end
		ResMgr:LoadAssetAsync(config.skin_decorate,AssetDefine.LoadType.Instant,function(prefab)
			if prefab then
				local go,trans = CreateGOAndTrans(prefab)
				trans:SetParent(self.m_TransSpinePa)
				trans:SetLocalPosition(0,-0.7,0)--因为prefab预设体中图片默认就是-0.7 这里挂件与图片对齐
				trans:SetLocalScale(1,1,1)

				self:ResetTransSpinePa()

				self.skinDecorateTrans = trans
				self:ShowSkinDecorate(config.id,self.skinDecorateTrans,isPreview)
			end
		end)
	else
		if self.skinDecorateTrans then
			SetActive(self.skinDecorateTrans,false)
		end
	end

	--设置层级
	if isPreview then
		self:SetDecorateLayer(SortingLayerInGame.UI_TOP, 600)
	else
		self:ResetDecorateLayer()
	end

	if config.default_decorate or config.skin_decorate then
		SetActive(self.m_TransSpinePa.gameObject,true)
	else
		SetActive(self.m_TransSpinePa.gameObject,false)
	end
	

	local res = self:SetTileSkin(config)
	if not res then
		self:ResetAllTileSkin()
	end

end

function M:ResetTransSpinePa()
	local vtOff = GetVector3ByStr(self.m_Config["offset"])
	if vtOff then
		self.m_TransSpinePa:SetLocalPosition(vtOff.x, vtOff.y, vtOff.z)
	else
		self.m_TransSpinePa:SetLocalPosition(0,0,0)
	end

	local vtScale = GetVector3ByStr(self.m_Config["scale"])
	if vtScale then
		self.m_TransSpinePa:SetLocalScale(vtScale.x, vtScale.y, vtScale.z)
	else
		self.m_TransSpinePa:SetLocalScale(1,1,1)
	end
end

function M:SetTileSkin(config)
	
	if not config then return end

	local path = "Assets/ResPackage/Sprite/tile/tile_back%d.png"

	if not config.tile_skin or not config.tile_area then
		return
	end

	local skin = string.split(config.tile_skin,'|')

	if not skin or not skin[1] or not skin[2] then
		return 
	end

	self.tileSkinedList = string.split(config.tile_area,';')
	for index, value in ipairs(self.tileSkinedList) do
		local data = MapController.m_MapGroupData[value]
		if data.arr then
			for i, v in ipairs(data.arr) do
				if v and v[1] and v[2] then
					MapController:SetMapTileBg(v[1], v[2],string.format(path,skin[1]),string.format(path,skin[2]))
					local objTile = MapController:GetTileByGridUnsafe(v[1], v[2])
					if objTile then
						objTile:UpdateGrayState()
					end
				end
			end
		end
	end

	return true
end

function M:ResetAllTileSkin()
	if self.tileSkinedList then
		for index, value in ipairs(self.tileSkinedList) do
			local data = MapController.m_MapGroupData[value]
			if data.arr then
				for i, v in ipairs(data.arr) do
					if v and v[1] and v[2] then
						MapController:SetMapTileBg(v[1], v[2])
						local objTile = MapController:GetTileByGridUnsafe(v[1], v[2])
						if objTile then
							objTile:UpdateGrayState()
						end
					end
				end
			end
		end
	end
end

function M:ShowSkinDecorate(skinId,trans,isPreview)

	if isPreview then
		local childCount = trans.childCount
		for i = 0, childCount - 1  do
			local child = trans:GetChild(i)
			SetActive(child,true)
		end
		return
	end

	local childCount = trans.childCount
	for i = 0, childCount - 1  do
		local child = trans:GetChild(i)
		SetActive(child,false)
	end

	local list = NetHandbook:GetSkinDecorate(skinId)
	if not list then return end

	for index, value in ipairs(list) do
		local skinDecorateCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.skin_decorate,  value)
		if skinDecorateCfg and skinDecorateCfg.skin_layer then
			local decorate = GetChild(trans,skinDecorateCfg.skin_layer)
			if decorate then
				SetActive(decorate,true)
			end
		end
	end
end

function M:onLoaded()
	if self.useSkinId <=0 then return end
	local config = ConfigMgr:GetDataByID(ConfigDefine.ID.skin_collection,self.useSkinId)
	if config then
		self:ChangeSkin(self.useSkinId,config.skin_icon)
	end
end
function M:onEnable()
	--override in child
end
function M:onDestroy()
	--override in child
	-- EventMgr:Remove(EventID.ALL_LOAD_END, self.OnAllLoadEnd, self)
	-- EventMgr:Remove(EventID.ALL_VISIT_LOAD_END, self.OnAllLoadEnd, self)
	self.CacheDecorateLayerData = nil
end
function M:onDrag(state)
	--override in child
end
function M:onDelete()
	--override in child
end
function M:onGridChange(gridX, gridY, state, from)
	--override in child
end

function M:AddDressArrow()
	local function onLoaded(prefab)
		if prefab then
			local newGo, newTrans = CreateGOAndTrans(prefab)
			newTrans:SetParent(self.m_TransWidgetTop)
			newTrans:SetLocalScale(1, 1, 1)
			--if self.m_Offset then
			--newTrans:SetLocalPosition(self.m_Offset.x, self.m_Offset.y, 0)
			--else
			newTrans:SetLocalPosition(0, 0, 0)
			newTrans.localRotation = Quaternion.Euler(0,0,0)
			--end

			--if self:getNodeRota() then
			--newTrans.localEulerAngles = Vector3.New(0, 0, self:getNodeRota())
			--end

			newTrans.name = "dress_arrow"
			local renderer = SearchChild(newTrans, "offset/m_sp_arrow", SpriteRenderer)
			DOLocalMoveYLoop(renderer.transform, 0.3, 0.5, LoopType.Yoyo, Ease.InOutSine)
			self.m_RenderArrow = renderer
		end
	end
	ResMgr:LoadAssetWithCache("Assets/ResPackage/Prefab/Map/dress_arrow.prefab", AssetDefine.LoadType.Instant, onLoaded)
end

function M:onClickPos(touchPos)
	local itemPos = MapController:GetWorldPosByTouch(touchPos)
	self.worldPos = itemPos
end


function M:onClicked()
	local skin_list = NetHandbook:GetHaveSkinByItemId(self.skin_type,self.m_Id)
	if next(skin_list) == nil then return end
	if #skin_list > 1 then
		local function onFun()
			UI_SHOW(UIDefine.UI_SkinCollection,self,self.skin_type)
		end--m_Vt2Pos.x m_Vt2Pos.y
		if self.skin_type == NetHandbook.ID.Coastline then
			local tempT = DeepCopy(self)
			tempT.m_Vt2Pos.x = self.worldPos.x
			tempT.m_Vt2Pos.y = self.worldPos.y
			ClearController:Show(ClearType.Dress, tempT,onFun)
		else
			ClearController:Show(ClearType.Dress, self,onFun)
		end				
	end	
	if self.m_RenderArrow then
		UEGO.Destroy(self.m_RenderArrow.gameObject)
		self.m_RenderArrow = nil
	end
end
function M:onExtinct()
	--override in child
end
function M:onCommand(cmd)
	--override in child
end
function M:OnRefreshState()
	--override in child
end
function M:OnSetEffGray()

end
return M