%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!687078895 &4343727234628468602
SpriteAtlas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: ui_ship
  m_EditorData:
    serializedVersion: 2
    textureSettings:
      serializedVersion: 2
      anisoLevel: 0
      compressionQuality: 0
      maxTextureSize: 0
      textureCompression: 0
      filterMode: 1
      generateMipMaps: 0
      readable: 0
      crunchedCompression: 0
      sRGB: 1
    platformSettings:
    - serializedVersion: 3
      m_BuildTarget: DefaultTexturePlatform
      m_MaxTextureSize: 2048
      m_ResizeAlgorithm: 0
      m_TextureFormat: 50
      m_TextureCompression: 1
      m_CompressionQuality: 50
      m_CrunchedCompression: 0
      m_AllowsAlphaSplitting: 0
      m_Overridden: 0
      m_IgnorePlatformSupport: 0
      m_AndroidETC2FallbackOverride: 0
      m_ForceMaximumCompressionQuality_BC6H_BC7: 0
    packingSettings:
      serializedVersion: 2
      padding: 2
      blockOffset: 0
      allowAlphaSplitting: 0
      enableRotation: 0
      enableTightPacking: 0
      enableAlphaDilation: 0
    secondaryTextureSettings: {}
    variantMultiplier: 1
    packables:
    - {fileID: 2800000, guid: 4480ea91dc892d544a81a782d3a060c5, type: 3}
    - {fileID: 2800000, guid: 043777b3153923d4f9720ab1aa81202c, type: 3}
    - {fileID: 2800000, guid: 38ed9fa715975d741ba39f9ec946bb73, type: 3}
    - {fileID: 2800000, guid: 4dfab15852d831d4f9b620c37f1b451e, type: 3}
    - {fileID: 2800000, guid: 5c19735e48cc1504b89ad67fc0cda786, type: 3}
    - {fileID: 2800000, guid: 422436ce6c1bee649a9b655f0e603c4a, type: 3}
    - {fileID: 2800000, guid: 27dfa4676e0073b44960fc90be4c757d, type: 3}
    - {fileID: 2800000, guid: 85f99d7b1aada614a8fe21415c14335f, type: 3}
    - {fileID: 2800000, guid: 47399ba2893031b4f9611cd8045e17c6, type: 3}
    - {fileID: 2800000, guid: 1b2f811f87f607745a1dff2676042b9f, type: 3}
    bindAsDefault: 0
    isAtlasV2: 0
    cachedData: {fileID: 0}
    packedSpriteRenderDataKeys: []
  m_MasterAtlas: {fileID: 0}
  m_PackedSprites: []
  m_PackedSpriteNamesToIndex: []
  m_RenderDataMap: {}
  m_Tag: ui_ship
  m_IsVariant: 0
