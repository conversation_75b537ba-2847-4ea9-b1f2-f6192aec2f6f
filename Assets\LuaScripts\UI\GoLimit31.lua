local GoLimit31 = {}
local M = GoLimit31
local prePath = "Assets/ResPackage/Prefab/UI/GoLimit31.prefab"
local pre
--新滑雪活动
function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.count = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
    self.red = GetChild(self.go,"doLimit/bg/goPoint")
    self.text_progress = GetChild(self.go,"doLimit/bg/Limit/text_progress",UEUI.Text)
	self.imgProgress = GetChild(self.go,"doLimit/bg/Limit/img",UEUI.Image)
    self.goRank = GetChild(self.go,"doLimit/goRank")
	self.txtRank = GetChild(self.go,"doLimit/goRank/txt",UEUI.Text)
	
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
		if self.activeInfo and self.activeInfo:IsActivityEnd() then
			NetNewSkiingData:CheckEndPush(self.id)
			return
		end	
        UI_SHOW(UIDefine.UI_NewSkiingMatch)
    end)
    self.slider = GetChild(self.go,"doLimit/bg/Limit/Slider",UEUI.Slider)
end
--init
function M:SetItem(param)
    self.id = param.id
    local activityItem = NewSkiingManager:GetActivityItem()
    if not activityItem then
        return
    end
    
	self.activeInfo = activityItem
    self.active = activityItem.form.activeMess
    self.totalType = param.totalType
    self.condition = param.condition
    SetImageSprite(self.icon,self.active.icon,false)
	SetImageSprite(self.imgProgress, "Sprite/ui_public/Resource_jifen_huoxuelicheng.png", false)

    self:SetProgress()
	self:SetRedShow()
	self:SetRankTxt()

    EventMgr:Add(EventID.REFRESH_SKIING_RANK,self.SetRankTxt,self)
    EventMgr:Add(EventID.USE_SLEDGE_SUCCESS,self.OnUseSledgeSuccess,self)
	EventMgr:Add(EventID.REFRESH_SKIING_SCORE,self.OnRefreshScore,self)
    NewSkiingManager:UpdateRankData(5)
end

function M:SetProgress()
	local ratio,curDis,sumDis = NewSkiingManager:GetPassPortProgressIncludeOverflow()

    self.text_progress.text = curDis.."/"..sumDis

    ratio = (ratio >1) and 1 or ratio
    self.slider.value = ratio
end

function M:ChangState(id)

end
function M:ChangeValue()
    self:SetRedShow()
end
---tick
function M:ChangeItem()
	if not self.activeInfo then
		return
	end
	
	local time = self.activeInfo:GetRemainingTime()
    --self.count.text = (time > 0) and TimeMgr:CheckHMSNotEmpty(time) or "0"
	self.count.text = (time > 0) and TimeMgr:CheckHMSNotEmpty(time) or LangMgr:GetLang(7077)
	
end

--成功使用雪车
function M:OnUseSledgeSuccess()
    self:SetProgress()
    self:SetRankTxt()
end

function M:OnRefreshScore()
	self:SetProgress()
    self:SetRankTxt()
end

function M:SetRedShow()
	local isShow = NewSkiingManager:IsShowRed()
    SetActive(self.red,isShow)
end

function M:Close()
    TimeMgr:DeleteTimer("GoLimit31")
    EventMgr:Remove(EventID.REFRESH_SKIING_RANK,self.SetRankTxt,self)
    EventMgr:Remove(EventID.USE_SLEDGE_SUCCESS,self.OnUseSledgeSuccess,self)
	EventMgr:Remove(EventID.REFRESH_SKIING_SCORE,self.OnRefreshScore,self)
    UEGO.Destroy(self.go)
end

function M:SetRankTxt()
    local rank = NetNewSkiingData:GetMyRank()
    self.txtRank.text = tostring(rank) or ""
    SetActive(self.goRank, rank > 0)
end

return M