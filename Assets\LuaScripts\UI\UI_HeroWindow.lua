local UI_HeroWindow = Class(BaseView)
local HeroStarAttr = require("UI.HeroStarAttr")

function UI_HeroWindow:OnInit()
    self.selectType = -1;
    self.selectIndex = 0;
    self.heroItemList = {};
    self.togObjList = {};
    self.isInit = true;
end

function UI_HeroWindow:OnCreate(param)
    local list = HeroManager:GetHeroKindList();
    for i = 1, #list do
        local obj = self.togObjList[i];
        if obj == nil then
            obj = CreateGameObjectWithParent(self.ui.m_goTog, self.ui.m_goTogList);
            SetActive(obj, true);
            table.insert(self.togObjList, obj);
        end

        if i == 1 then
            local nameTxt = GetChild(obj, "bg/m_txtAuto58006010");
            local selectTxt = GetChild(obj, "selectBg/m_txtAuto58006010");
            SetActive(nameTxt, true);
            SetActive(selectTxt, true); 
        else
            local icon = GetChild(obj, "bg/icon", UEUI.Image);
            local selectSp = GetChild(obj, "selectBg/selectSp", UEUI.Image);
            local iconStr = HeroManager:GetHeroKindIcon(list[i]);
            SetUIImage(icon, iconStr, true);
            SetUIImage(selectSp, iconStr, true);
            SetActive(icon, true);
            SetActive(selectSp, true);
        end

        AddUIComponentEventCallback(obj, UEUI.Toggle, function(go, param)
            if go.isOn then
                self:OnSelectType(list[i]);
            end
        end);
    end
    
    self:OnSelectType(HERO_KIND.ALL);
	self:GuideToHero(0.1)
    self.heroAttr = HeroStarAttr.new()
    self.heroAttr:Create(self.ui.m_goStarAttr,true, function()
        self:UpdateHeroStarAttr()
    end)
    self:SortOrderAllCom(true)
    EventMgr:Add(EventID.UPDATE_HERO_SUM_STAR, self.UpdateHeroStarAttr, self)
end

function UI_HeroWindow:GuideToHero(delay)
	if not UIMgr.uiHeroWindowTargetHero or UIMgr.uiHeroWindowTargetHero <= 0 then
		return
	end

	if not delay then
		delay = 0
	end

	local heroId = UIMgr.uiHeroWindowTargetHero
	local heroIdText = UIMgr.uiHeroWindowTargetHeroText
	UIMgr.uiHeroWindowTargetHeroText = 0
	UIMgr.uiHeroWindowTargetHero = 0
	local heroVoTemp = nil
	local indexTemp = 0
	local item = nil
	for index, heroVo in ipairs(self.heroVoList) do
		if heroVo.heroId == heroId then
			heroVoTemp = heroVo
			indexTemp = index
			item = self:GetHeroItem(index)
		end
	end
	
    local function GuideCallback()
        UI_CLOSE(UIDefine.UI_GuideMask)
		self:SelectHero(heroVoTemp,indexTemp)
    end
	if not item then
		return
	end
	local hasSet = false
	
	AddDOTweenNumberDelay(0,1,0.1,delay,function (num)
		if not hasSet then
			local moveMaxOffset = math.max(self.ui.m_scrollview.content.rect.height - self.ui.m_scrollview.viewport.rect.height,0)
			local moveOffset = -item.root.transform.anchoredPosition.y-(377/2)
			if moveOffset > moveMaxOffset then
				moveOffset = moveMaxOffset
			end
			SetUIPos(self.ui.m_scrollview.content,0,moveOffset)
		end
	end,function ()
		local posScreen = UIMgr:GetUIPosByWorld(item.root.transform.position)
		local centerPos = {posScreen.x,posScreen.y}
		UI_SHOW(UIDefine.UI_GuideMask, {
			{2, 0, 160},                -- 遮罩类型和大小
			centerPos,                 -- 遮罩位置
			{5, 5},                    -- 遮罩按钮大小
			0.5,                       -- 缩放动画的时长
			function() GuideCallback() end,   -- 点击回调
			nil,   -- 箭头位置
			{-0.5, centerPos[2] > 0 and -2.5 or 2.5, heroIdText},                    -- 对话框位置和内容
			"Sprite/new_hero_0/headFrame_1.png",   -- 对话框头像
			nil,
		})
	end)
end

function UI_HeroWindow:OnRefresh(_type, param)
    if _type == 1 then -- 英雄列表刷新
        self:OnUpdateUI();
	elseif _type == 2 then
		self:GuideToHero()
    end
end

function UI_HeroWindow:onDestroy()
    self.isInit = false;
    EventMgr:Remove(EventID.UPDATE_HERO_SUM_STAR, self.UpdateHeroStarAttr, self)
    if self.heroAttr then
        self.heroAttr:Destroy()
    end
end

function UI_HeroWindow:onUIEventClick(go, param)
    local name = go.name
    if name == "closeBtn" then
        self:Close();
    elseif name == "recruitBtn" then
        UI_SHOW(UIDefine.UI_LotteryCenter)
    elseif name == "honorBtn" then
        UI_SHOW(UIDefine.UI_HeroHonorWindow);
    elseif name == "dressBtn" then
        UI_SHOW(UIDefine.UI_DecorateBuff)
    elseif name == "bagBtn" then
        UI_SHOW(UIDefine.UI_BagView)
    end
end

function UI_HeroWindow:OnVisibleChange(isVisible)
    if not isVisible then
        local item = self:GetHeroItem(self.selectIndex);
        if item and item.bg then
            local bg = item.bg;
            bg.transform.localScale = Vector3(1, 1, 1);
            bg.color = GetColorByHex("ffffff");
        end
    else
        if self.isInit then
            self:OnUpdateUI();
        end
    end
end

function UI_HeroWindow:IsIgnoreAnim()
    return true;
end

function UI_HeroWindow:OnSelectType(_type)
    if self.selectType == _type then return end
    self.selectType = _type;
    
    self.ui.m_scrollview:StopMovement();
    SetUIPos(self.ui.m_scrollview.content, 0, 0);
    self:OnUpdateUI();
end

function UI_HeroWindow:OnUpdateUI()
    self.heroVoList = HeroManager:GetHeroVoList(self.selectType, true, true);
    local hotHeroVoList = HeroManager:GetHotUnlockListByKind(self.selectType);
    for i = 1, #hotHeroVoList do
        table.insert(self.heroVoList, hotHeroVoList[i]);
    end
    
    local count = #self.heroVoList;
    local num = #self.heroItemList;
    local len = count > num and count or num;
    for i = 1, len do
        if i <= count then
            self:OnUpdateHeroItem(i, self.heroVoList[i]);
        end
        self:SetHeroItemEnable(i, i <= count);
    end
    
    SetActive(self.ui.m_goHonorRed, HeroManager:GetHeroHonorAllRed());
end

function UI_HeroWindow:GetHeroItem(index)
    local item = self.heroItemList[index];
    if not item then
        local obj = CreateGameObjectWithParent(self.ui.m_goHeroItem, self.ui.m_scrollview.content);
        item = { root = obj, bg = GetChild(obj, "bg", UEUI.Image),
                 heroImg = GetChild(obj, "bg/heroBg/heroImg", UEUI.Image),
                 frameBg = GetChild(obj, "bg/frameBg", UEUI.Image),
                 careerImg = GetChild(obj, "bg/careerImg", UEUI.Image),
                 teamImg = GetChild(obj, "bg/teamImg"),
                 teamTxt = GetChild(obj, "bg/teamImg/teamTxt", UEUI.Text),
                 levelTxt = GetChild(obj, "bg/levelTxt", UEUI.Text),
                 kindImg = GetChild(obj, "bg/levelTxt/kindImg", UEUI.Image),
                 upImg = GetChild(obj, "bg/upImg", UEUI.Image),
                 starObj = GetChild(obj, "bg/starObj"),
                 noneObj = GetChild(obj, "bg/noneObj"),
                 kindSp = GetChild(obj, "bg/noneObj/kindSp", UEUI.Image),
                 noneTxt = GetChild(obj, "bg/noneObj/m_txtAuto58006008", UEUI.Text),
                 chipProgressBg = GetChild(obj, "bg/noneObj/chipProgressBg"),
                 chipProgress = GetChild(obj, "bg/noneObj/chipProgressBg/chipProgress", UEUI.Image),
                 chipSp = GetChild(obj, "bg/noneObj/chipProgressBg/chipSp", UEUI.Image),
                 combindTxt = GetChild(obj, "bg/noneObj/chipProgressBg/combindTxt", UEUI.Text),
                 redDot = GetChild(obj, "bg/redDot"),
        }
        table.insert(self.heroItemList, item);
    end
    return item;
end

function UI_HeroWindow:SetHeroItemEnable(index, val)
    local item = self:GetHeroItem(index);
    if item then
        SetActive(item.root, val);
    end
end

function UI_HeroWindow:OnUpdateHeroItem(index, heroVo)
    local item = self:GetHeroItem(index);
    if not item or not heroVo then return end
    
    local quality = heroVo:GetHeroQuality();
    SetUIImage(item.bg, "Sprite/ui_slg_jueseyangcheng/yangcheng_ka" .. quality .. ".png", false);
    SetUIImage(item.frameBg, "Sprite/ui_slg_jueseyangcheng/yangcheng_ka" .. quality .. "_1.png", false);
    SetUIImage(item.teamImg, "Sprite/ui_slg_jueseyangcheng/yangcheng_jiaobiao" .. quality .. ".png", false);
    
    SetUIImage(item.heroImg, heroVo:GetHeroHead(), false, function()  
        SetActive(item.heroImg, true);
    end);
    SetUIImage(item.careerImg, HeroManager:GetHeroCareerIcon(heroVo:GetHeroCareer()), true);
    SetUIImage(item.kindImg, HeroManager:GetHeroKindIcon2(heroVo:GetHeroKind()), true);
    
    item.levelTxt.text = LangMgr:GetLang(154) .. heroVo.level;

    local isActive = heroVo:GetHeroActive();
    if isActive then
        local star, order = heroVo:GetHeroStarOrder();
        for i = 1, 5 do
            local img = GetChild(item.starObj, "starSp" .. i, UEUI.Image);
            if i <= star then
                SetUIImage(img, "Sprite/ui_slg_jueseyangcheng/yangcheng_star5.png", false);
            elseif i == star + 1 then
                SetUIImage(img, "Sprite/ui_slg_jueseyangcheng/yangcheng_star" .. order .. ".png", false);
            else
                SetUIImage(img, "Sprite/ui_slg_jueseyangcheng/yangcheng_star0.png", false);
            end
        end
    else
        local isCombine = heroVo:GetHeroIsCombine();
        local isHotCombine = false;
        if isCombine then
            local hasCount = BagManager:GetBagItemCount(heroVo:GetHeroPiece());
            local needCount = heroVo:GetHeroCombine();
            item.chipProgress.fillAmount = hasCount > needCount and 1 or hasCount / needCount;
            item.combindTxt.text = hasCount .. "/" .. needCount;
            
            SetUIImage(item.chipSp, "Sprite/ui_slg_jueseyangcheng/yangcheng_liebiao_suipian" .. quality .. ".png", false);
        else
            local maxNum, curNum = heroVo:GetHeroHotProgress();
            if maxNum > 0 then
                maxNum = maxNum + 1; -- 加上角色本身
                item.chipProgress.fillAmount = curNum > maxNum and 1 or curNum / maxNum;
                item.combindTxt.text = curNum .. "/" .. maxNum;
                isHotCombine = true;
            end
        end
        SetUIImage(item.kindSp, HeroManager:GetHeroKindIcon2(heroVo:GetHeroKind()), true);
        --SetActive(item.noneTxt, not isCombine and not isHotCombine);
        SetActive(item.chipProgressBg, isCombine or isHotCombine);
        SetActive(item.chipSp, isCombine);
    end
    SetActive(item.noneObj, not isActive);
    SetActive(item.starObj, isActive);
    SetActive(item.levelTxt, isActive);

    RemoveUIComponentEventCallback(item.bg, UEUI.Button);
    AddUIComponentEventCallback(item.bg, UEUI.Button, function(arg1, arg2)
		self:SelectHero(heroVo,index)
    end);
    
    SetActive(item.redDot, HeroManager:GetSingleHeroRed(heroVo.heroId));
    SetActive(item.upImg, isActive and heroVo:GetHeroUpStarRed());
end

function UI_HeroWindow:SelectHero(heroVo,index)
	self.selectIndex = index;
	UI_SHOW(UIDefine.UI_HeroDevelopWindow, heroVo.heroId, self.heroVoList, index);
end

--更新英雄星级属性加成
function UI_HeroWindow:UpdateHeroStarAttr()
    local nowCount,nowList = HeroManager:GetStarAttrList()
    self.heroAttr:ShowHeroAttr(nowCount,nowList)
end

return UI_HeroWindow