-- 好友聊天界面

local UI_FriendView = Class(BaseView)

--region ----------------------------------------- 定义变量 -----------------------------------------

-- item 固定高度的循环列表
local SlideRect = require("UI.Common.SlideRect")
local ItemBase = require("UI.Common.BaseSlideItem")

-- item 动态高度的循环列表
local ChatScrollRect = require("UI.Common.ChatScrollRect")
local ChatScrollItem = require("UI.Common.ChatScrollItem")
local ChatItemOther = Class(ChatScrollItem)  -- 他人
local ChatItemMe = Class(ChatScrollItem)     -- 自己
local ChateInviteOther = Class(ChatScrollItem)	 -- 公会邀请
local ChateInviteMe = Class(ChatScrollItem)	 -- 公会邀请
local AnnounceOther = Class(ChatScrollItem)	 -- 公告邀请

local ChateInviteBossOther = Class(ChatScrollItem)	    -- 联盟Boss召集
local ChateInviteBossMe = Class(ChatScrollItem)	        -- 联盟Boss召集
local ChateBossDamageOther = Class(ChatScrollItem)	    -- 联盟Boss伤害分享
local ChateBossDamageMe = Class(ChatScrollItem)	        -- 联盟Boss伤害分享
local ChateArenaShareOther = Class(ChatScrollItem)	    -- 竞技场战报分享
local ChateArenaShareMe = Class(ChatScrollItem)	        -- 竞技场战报分享
local ChateTrainShareOther = Class(ChatScrollItem)	    -- 联盟火车分享
local ChateTrainShareMe = Class(ChatScrollItem)	        -- 联盟火车分享
local ChateTrainRob = require("UI.Chat.ChatCellTrainRob")	-- 联盟火车分享

local ArenaWinImg = "Sprite/ui_slg_jingjisai/zb_qipao1_jingji_world_share_shengli.png"
local ArenaFailImg = "Sprite/ui_slg_jingjisai/zb_qipao1_jingji_world_share_shibai.png"
local ArenaWinBgImg = "Sprite/ui_slg_jingjisai/zb_qipao1_jingji_world_share_kuang_shengli.png"
local ArenaFailBgImg = "Sprite/ui_slg_jingjisai/zb_qipao1_jingji_world_share_kuang_shibai.png"

local GoSlgHeroItem = require("UI.GoSlgHeroItem")

local ShowAttitude = require("UI.ShowAttitude")

local ItemShowNums = 8      -- item 数量
local FriendItemShowNum = 8 -- 好友 item 数量
local MAX_WIDTH = 720       -- 聊天气泡最大宽度
local CurPlayerID           -- 当前选中的玩家 ID
local CurSelectedFriend     -- 当前选中的好友
local CurClickType          -- 当前选中的页签

local FriendItem = Class(ItemBase)   -- 好友
local rankItem = Class(FriendItem)
local unionItem = Class(FriendItem)

local ChannelItem = Class(ItemBase)  -- 频道
local BlackListItem = Class(ItemBase)   --黑名单列表

local BubbleItem = require("UI.BubbleItem")
local BubbleCollectItem 

local oldChatContentHeight = 0
local chatViewportHeight = 0

local CHANNEL_PLAYER_MAX = 500

local EmojiNum = 40

local emojiFilter1 = {
    0,0,0,0,0,0,0,0,
    0,0,1,0,0,0,0,0,
    0,1,1,1,1,1,1,0,
    0,0,0,0,0,0,0,0,
    0,0,0,
}

local emojiFilter2 = {
    0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,1,
    1,1,1,1,1,1,1,1,
    1,1,1,
}

--endregion -------------------------------------- 定义变量 -----------------------------------------

function UI_FriendView:OnInit()

end

function UI_FriendView:OnCreate(param, viewType)
    -- 初始化变量
    self.hasSystemMessage = false   -- 是否有系统消息
	self.canRequest = true
	self.friendData = {}
	self.acceptData = {}
	self.requestData = {}
	self.rankData = {}
	self.sendTime = 0
	self.receiveTime = 0
	self.friendRankItems = {}
	self.rankItems = {}
	self.myId = NetUpdatePlayerData:GetPlayerInfo().id

    self.chatInputField = GetComponent(self.ui.m_goInputField,CS.TMPro.TMP_InputField)
    self.isChatCD = false
    self.emojiGoList = {}
    self.isShowEmoji = false
    self.emojiPage = 1
    self.isShowChannelList = false
    oldChatContentHeight = 0
    chatViewportHeight = 0

    self.curChatChannel = 0     --  当前聊天类型频道

    self.chatRect = GetComponent(self.ui.m_scrollviewChat, UE.RectTransform)
    self.isShowBtnMask = false
    self.isInPrivateChat = false -- 是否私聊中

    --创建头像
    local headNode = GetChild(self.ui.m_goItemFriend, "headNode")
    CreateCommonHead(headNode.transform,0.8)
    local blackHeadNode = GetChild(self.ui.m_goBlackListItem, "headNode")
    CreateCommonHead(blackHeadNode.transform, 0.73)
    
    -- 初始化界面
    self:InitPanel()

    -- 请求好友列表
    NetFriendData:RequestFriendList(FRIEND_REQUEST_TYPE.Friend, function (data)
        if self.clickType == FRIEND_VIEW_TYPE.Friend then
            local friendList = NetFriendData.friendList
            --if CurPlayerID == nil then
            --    self:SelectFirstItem()
            --end
            --
            if param then
                self:SelectChatFriend(param)
                --self:SetFriendListData(friendList)
                self:SelectPrivateChat(param)
            else
                self:SetFriendListData(friendList)
            end
        end
    end)

    -- 请求最近联系
    NetFriendData:RequestFriendList(FRIEND_REQUEST_TYPE.Recently, function (data)

    end)

    -- 请求黑名单
    NetFriendData:RequestFriendList(FRIEND_REQUEST_TYPE.Blacklist, function (data)

    end)

    -- 自己申请好友
    NetFriendData:RequestFriendList(FRIEND_REQUEST_TYPE.AddFriend, function (data)

    end)

    -- 他人申请好友
    NetFriendData:RequestFriendList(FRIEND_REQUEST_TYPE.OtherRequest, function (data)
        self:RefreshRedPoint()
    end)

	EventMgr:Add(EventID.DELETE_FRIEND,self.RefreshFriendListByDelect,self)
	EventMgr:Add(EventID.AGREE_FRIEND,self.RefreshFriendList,self)
	EventMgr:Add(EventID.REFUSE_FRIEDN,self.RefreshAcceptList,self)

	EventMgr:Add(EventID.CHAT_PUSH_MSG,self.PushMsg,self)
	EventMgr:Add(EventID.CHECK_NEW_MSG,self.CheckIsNewMsg,self)
	EventMgr:Add(EventID.TRANSLATE_CALL_BACK,self.TranslateCallBack,self)
	EventMgr:Add(EventID.REFRESH_CHAT_RED,self.RefreshChatRed,self)
	--EventMgr:Add(EventID.REWARD_IN_BUBBLE_ITEM, self.RewardItemInBubble, self)
	--EventMgr:Add(EventID.SEND_ENERGY,self.SendEnergyEvent,self)
	local robNum = NetFriendData.data.robNum or 0
	local maxNum = FriendManager:GetConfigById(201)
	local num = NetFriendData:GetMaxWateringNum() - NetFriendData:GetWateringNum()
	self.ui.m_txtTodayStealing2.text = num.."/"..FriendManager:GetConfigById(201)
    self.ui.m_txtWaterCount.text = num .. "/" .. FriendManager:GetConfigById(201)
	self.ui.m_txtBtnChatTxt.text = LangMgr:GetLang(127)
	-- self:OnRefreshRed()
	--local is_red = NetFriendData:GetAppleRed()
	--self:InitBubble()
	UI_SHOW(UIDefine.UI_BubbleCollectItem)
	BubbleCollectItem = UIMgr:GetUIItem(UIDefine.UI_BubbleCollectItem)
    -- 默认选中的页签
    if viewType then
        self:OnClickHorTab(viewType)
    else
        local openType = self:GetDefaultOpenType()
		self:OnClickHorTab(openType)
    end
	local thinkTable = {
		["open_home"] = 1,
	}
	SdkHelper:ThinkingTrackEvent(ThinkingKey.open_gam, thinkTable)

    self:SetIsUpdateTick(true)

    self:RefreshRedPoint()
    self:RefreshChatRedPoint()
    self.defaultEmojiIndex = 1
    self:UpdataEmojiInfo()
    self:UpdataEmojiPageInfo()
    
	SetActive(self.ui.m_btnFacebook,not Game.IsTinyGame)
end

function UI_FriendView:GetDefaultOpenType()
    local openType = self.clickType and self.clickType or FRIEND_VIEW_TYPE.Chat

    local isHasFriend = NetFriendData:HasFriendMessageRed()
    if isHasFriend then
        openType = FRIEND_VIEW_TYPE.Friend
        return openType
    end
    
    local isAnnounceNew = ChatManager:GetAnnounceMentHasNew()
    if isAnnounceNew then
        openType = FRIEND_VIEW_TYPE.Chat
        return openType
    end
    return openType
end

function UI_FriendView:TickUI(delta)
    --local chatCD = ChatManager:GetChatCD()
    --local timeNow = TimeMgr:GetServerTime()
    --if chatCD > timeNow then
    --    self.isChatCD = true
    --    local time = chatCD - timeNow
    --    local str = string.format("(%s)",time)
    --    self.ui.m_txtBtnChatTxt.text = LangMgr:GetLang(127) .. str
    --else
    --    self.isChatCD = false
    --    self.ui.m_txtBtnChatTxt.text = LangMgr:GetLang(127)
    --end
end

function UI_FriendView:OnRefresh(type, param, auto, obj)
    -- 刷新好友列表
    if type == 1 then
        if self.clickType == FRIEND_VIEW_TYPE.Blacklist then
            local blacklist = NetFriendData.blacklist
            --self:SetFriendListData(blacklist, FRIEND_VIEW_TYPE.Blacklist)
            self:SetBlackListData(blacklist)
            --self:SelectFirstItem()
            --SetUIBtnGrayAndEnable(self.ui.m_btnCancelBlacklist, true)
        elseif self.clickType == FRIEND_VIEW_TYPE.Friend then
            local friendList = NetFriendData.friendList
            self:SetFriendListData(friendList)
            --self:SelectFirstItem()

            SetActive(self.ui.m_scrollviewChat, false)
            SetActive(self.ui.m_goSendMessage, false)
            SetActive(self.ui.m_goNoFriend, false)

            if #NetFriendData.friendList > 0 then
                --SetActive(self.ui.m_scrollviewChat, true)
                --SetActive(self.ui.m_goSendMessage, true)
            elseif #NetFriendData.friendList == 0 then
                --SetActive(self.ui.m_goNoFriend, true)
            end
        end
    elseif type == 2 then
        self:SendEnergyEvent(param, auto, obj)
    elseif type == 3 then
        self:GetEnergyEvent(param, auto, obj)
    elseif type == 4 then
        self:OnClickHorTab(FRIEND_VIEW_TYPE.Request,false)
    -- 刷新选中的玩家
    elseif type == 5 then
        -- 选中屏蔽名单的玩家
        if self.clickType == FRIEND_VIEW_TYPE.Blacklist then
            --self:RefreshBlacklistPlayer(param)
        else
            self:SelectPrivateChat(param)
        end
    -- 刷新红点
    elseif type == 6 then
        self:RefreshRedPoint()
	elseif type == 7 then
		if self.clickType == FRIEND_VIEW_TYPE.Friend then
			self:HighlightSideBarBtn(self.clickType)
		end
		self:RefreshRedPoint()
    -- 刷新好友 item，不重置列表数据
    elseif type == 8 then
        if self.friendItems then
            for i = 1, #self.friendItems, 1 do
                self.friendItems[i]:Refresh()
            end
        end
        if CurSelectedFriend and CurSelectedFriend.data then
            local remark = NetFriendData:GetFriendRemark(CurSelectedFriend.data.playerId)
            self.ui.m_txtFriendTop.text = remark or CurSelectedFriend.data.name
            self.ui.m_txtFriendTopRemark.text = CurSelectedFriend.data.name
            SetActive(self.ui.m_txtFriendTopRemark, not IsNilOrEmpty(remark))
        end
    -- 跳转到聊天对象
    elseif type == 9 then
        self:SelectChatFriend(param)
    -- 删除好友后刷新列表，定位到被删好友的上一个位置
    elseif type == 10 then
        if self.clickType == FRIEND_VIEW_TYPE.Blacklist then
            local blacklist = NetFriendData.blacklist
            self:SetFriendListData(blacklist, FRIEND_VIEW_TYPE.Blacklist)
            --self:SelectFirstItem()
            --SetUIBtnGrayAndEnable(self.ui.m_btnCancelBlacklist, true)
        elseif self.clickType == FRIEND_VIEW_TYPE.Friend then
            local friendList = NetFriendData.friendList
            self:SetFriendListData(friendList, FRIEND_VIEW_TYPE.Friend, param)
            -- self:SelectTargetItem(param)

            SetActive(self.ui.m_scrollviewChat, false)
            SetActive(self.ui.m_goSendMessage, false)
            SetActive(self.ui.m_goNoFriend, false)

            if #NetFriendData.friendList > 0 then
                SetActive(self.ui.m_scrollviewChat, true)
                --SetActive(self.ui.m_goSendMessage, true)
            elseif #NetFriendData.friendList == 0 then
                --SetActive(self.ui.m_goNoFriend, true)
            end
        end
    elseif type == 33 then
        self:RefreshRedPoint()
    elseif type == 34 then
        self:UpdataLeagueAnnouncemen()
    elseif type == 35 then
        self:RefreshChangeChannel()
    elseif type == 36 then
        self:RefreshAnnounceChatRedPoint()
    elseif type == 301 then
        self:RefreshChatRedPoint()
        self:RefreshAnnounceChatRedPoint()
        UI_UPDATE(UIMgr:GetNowMainUI(),301)
    elseif type == 100 then
        if BubbleCollectItem then
            BubbleCollectItem:SetCallBack(param)
        end
        self:isBubbleReward()
	elseif type == 5100 then
		local isJoin = LeagueManager:IsJoinUnion()
		if not isJoin then
			SetActive(self.ui.m_goNoUnion,true)
			SetActive(self.ui.m_btnAddLeague,not LeagueManager:IsJoinUnion())
		end
    elseif type == 40 then
        self:OnClickChannelTab(FRIEND_VIEW_TYPE.LeagueAnnouncement)
    end
end

function UI_FriendView:SelectPrivateChat(param)
    self:OpenPrivateChat(true)
    self.isInPrivateChat = true
    local remark = NetFriendData:GetFriendRemark(param.playerId)
    self.ui.m_txtFriendTop.text = remark or param.name
    self.ui.m_txtFriendTopRemark.text = param.name
    SetActive(self.ui.m_txtFriendTopRemark, not IsNilOrEmpty(remark))

    local dataList = ChatManager:UpdateFriendMessageByID(param.playerId)
    local isEmpty = IsTableEmpty(dataList)
    self:RequestFriendChatList(param.playerId, isEmpty)
end

function UI_FriendView:onDestroy()
    UIMgr:RefreshAllMainFace(51)
    self.hasSystemMessage = nil
    self.blackHeadNode = nil
    CurPlayerID = nil
    CurSelectedFriend = nil

    self:CloseOther()

    self.bubble = nil

    self.myUnionData = nil
    self.friendData = nil
    self.acceptData = nil
    self.requestData = nil
    self.rankData = nil
    self.friendSlider = nil
    EventMgr:Remove(EventID.DELETE_FRIEND,self.RefreshFriendList,self)
    EventMgr:Remove(EventID.AGREE_FRIEND,self.RefreshFriendList,self)
    EventMgr:Remove(EventID.REFUSE_FRIEDN,self.RefreshAcceptList,self)
    EventMgr:Remove(EventID.CHAT_PUSH_MSG,self.PushMsg,self)
    EventMgr:Remove(EventID.CHECK_NEW_MSG,self.CheckIsNewMsg,self)
    EventMgr:Remove(EventID.TRANSLATE_CALL_BACK,self.TranslateCallBack,self)
    EventMgr:Remove(EventID.REFRESH_CHAT_RED,self.RefreshChatRed,self)

    --EventMgr:Remove(EventID.REWARD_IN_BUBBLE_ITEM, self.RewardItemInBubble, self)

    for i = 1, #self.friendItems do
        self.friendItems[i]:onDestroy()
    end

    for i=1,#self.friendRankItems do
        self.friendRankItems[i]:onDestroy()
    end
    for i=1,#self.rankItems do
        self.rankItems[i]:onDestroy()
    end

	if self.tweenExtensionPanel then
		self.tweenExtensionPanel:Kill()
		self.tweenExtensionPanel = nil
	end
end

function UI_FriendView:onUIEventClick(go)
    local name = go.name
    SetActive(self.ui.m_goChannelPopup, false)
    if name ~= "m_btnEmoji" and name ~= "m_btnEmojiPage1" and name ~= "m_btnEmojiPage2" then
        if self.isShowEmoji then
            self.isShowEmoji = false
            SetActive(self.ui.m_goEmojiRoot,false)
        end
        if self.isShowBtnMask then
            self.isShowBtnMask = false
            SetActive(self.ui.m_btnMask,false)
        end
    end
    if name ~= "m_btnSwitchChannel" then
        if self.isShowChannelList then
            self.isShowChannelList = false
            SetActive(self.ui.m_goChannelPopup,false)
        end
    end


	if name ~= "m_btnUnionInviteSend" and name~= "m_btnOpenExtensionPanel" then
		self:ShowExtensionPanel(false,true)
	end
        
    -- 关闭按钮
    if name == "m_btnClose" then
        if self.isInPrivateChat then
            self.isInPrivateChat = false
            self:OpenPrivateChat(false)
            self:OnClickHorTab(self.clickType)
        else
            self:isBubbleReward()
        end
    -- 侧边栏聊天按钮
    elseif name == "m_btnTabChat" then
        self:OnClickHorTab(FRIEND_VIEW_TYPE.Chat)
    -- 侧边栏好友按钮
    elseif name == "m_btnTabFriend" then
        self:OnClickHorTab(FRIEND_VIEW_TYPE.Friend)
    -- 侧边栏最近联系按钮
    elseif name == "m_btnTabRecently" then
        self:OnClickHorTab(FRIEND_VIEW_TYPE.Recently)
    -- 侧边栏屏蔽名单按钮
    elseif name == "m_btnTabBlacklist" then
        self:OnClickHorTab(FRIEND_VIEW_TYPE.Blacklist)
    -- 系统消息按钮
    elseif name == "m_btnSystemMessages" then
        self:OnClickChannelTab(FRIEND_VIEW_TYPE.SystemMessages)
    -- 世界频道按钮
    elseif name == "m_btnWorldChannel" then
        self:OnClickChannelTab(FRIEND_VIEW_TYPE.WorldChannel)
    -- 自选语言按钮
    elseif name == "m_btnOptionalLanguage" then
        self:OnClickChannelTab(FRIEND_VIEW_TYPE.OptionalLanguage)
    -- 联盟聊天按钮
    elseif name == "m_btnLeagueChat" then
        self:OnClickChannelTab(FRIEND_VIEW_TYPE.LeagueChat)
    -- 联盟公告按钮
    elseif name == "m_btnLeagueAnnouncement" then
        self:OnClickChannelTab(FRIEND_VIEW_TYPE.LeagueAnnouncement)
    -- 添加好友按钮
    elseif name == "m_btnAddFriend" then
        UI_SHOW(UIDefine.UI_FriendAdd)
    -- 帮助按钮
    elseif name == "m_btnHelp" then
        UI_SHOW(UIDefine.UI_FriendHelp)
    -- 切换频道按钮
    elseif name == "m_btnSwitchChannel" then
        --if self.isShowChannelList then
        --    self.isShowChannelList = false
        --    SetActive(self.ui.m_goChannelPopup,false)
        --    return
        --end
        --self.isShowChannelList = true
        --SetActive(self.ui.m_goChannelPopup,true)
        ---- 切换频道
        --if self.currentChannel == FRIEND_VIEW_TYPE.WorldChannel or self.currentChannel == FRIEND_VIEW_TYPE.OptionalLanguage then
        --    local chatType
        --    if self.currentChannel == FRIEND_VIEW_TYPE.WorldChannel then
        --        chatType = CHAT_TYPE_ENUM.WORLD
        --    elseif self.currentChannel == FRIEND_VIEW_TYPE.OptionalLanguage then
        --        chatType = CHAT_TYPE_ENUM.AREA
        --    end
        --    local function requestCallBack()
        --        self:RefreshChannelList(chatType)
        --    end
        --    local langID = LangMgr:GetLangId()
        --    local param = {}
        --    param.type = chatType
        --    param.lan = langID
        --    ChatManager:RequestAllChatChannelList(param,requestCallBack)
        --end
        UI_SHOW(UIDefine.UI_ChatChangeChannelTips,self.currentChannel)
    -- 好友更多操作按钮
    elseif name == "m_btnFriendTop" then
        -- 请求玩家信息，打开玩家档案界面
        if CurPlayerID then
            NetFriendData:QueryUserInfo(CurPlayerID, function (data)
                UI_SHOW(UIDefine.UI_FriendMore, data)
            end)
            --FriendManager:ShowPlayerById(CurPlayerID);
        end
    -- 拜访好友按钮
    elseif name == "m_btnVisit" then
        if CurPlayerID then
            FriendManager:VisitMapData(CurPlayerID, MAP_ID_MAIN)
        end
    -- 一键赠送按钮
    elseif name == "m_btnAllSend" then
        self:SendEnergyOneClick()
    -- 一键领取按钮
    elseif name == "m_btnAutoAccept" then
        self:GetEnergyOneClick()
    -- 取消屏蔽按钮
    elseif name == "m_btnCancelBlacklist" then
        NetFriendData:RemoveBlacklist(tostring(CurPlayerID))
        --SetUIBtnGrayAndEnable(self.ui.m_btnCancelBlacklist, false)
    elseif name == "m_btnRankVisit" then
        --FriendManager:VisitMapData(-1)
        UI_UPDATE(UIDefine.UI_FriendView, 100,
            function()
                FriendManager:VisitMapData(-1)
            end)
    elseif name == "m_btnNoFriend" then
        UI_UPDATE(UIDefine.UI_FriendView,4)
    elseif name == "m_btnNoAccept" then
        UI_UPDATE(UIDefine.UI_FriendView,4)
    elseif name == "m_btnAutoAgree" then
        FriendManager:SendAgreeAuto()
    elseif name == "m_btnAutoRefuse" then
        FriendManager:SendRefuseAuto()
    elseif name == "m_btnDailyReward" then
        local daily_type = 0
        if FRIEND_VIEW_TYPE.FuBenRank == self.clickType then
            daily_type = FRIEND_RANK_TAB_TYPE.FUBEN
        elseif FRIEND_VIEW_TYPE.ZooRank == self.clickType then
            daily_type = FRIEND_RANK_TAB_TYPE.ZOO	
        elseif FRIEND_VIEW_TYPE.UnionRank == self.clickType then
            daily_type = FRIEND_RANK_TAB_TYPE.LEAGUE
        end
        NetFriendData:SetDailyRewardState(daily_type)
        local item_id,item_num = FriendManager:GetDailyRewardConfig()
        --self:RewardItemInBubble({[1] = item_id,[2] = self.ui.m_btnDailyReward,[3] = item_num})
        --EventMgr:Dispatch(EventID.REWARD_IN_BUBBLE_ITEM, {item_id, self.ui.m_btnDailyReward, item_num})
        if BubbleCollectItem then
            BubbleCollectItem:RewardItemInBubble({item_id, self.ui.m_btnDailyReward, item_num})
        end
        UI_UPDATE(UIDefine.UI_FriendView,8)
        UI_UPDATE(UIDefine.UI_MainFace, 33)
        EventMgr:Dispatch(EventID.FRIEND_DAILY_REWARD)
	elseif name == "m_btnFacebook" then
		UI_SHOW(UIDefine.UI_InviteFriendMain)

    elseif name == "m_btnSendChat" then
        if self.isChatCD then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(53241123))
            return
        end
        if self.chatInputField.text == "" then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(165))
            return
        end
        local content = self.chatInputField.text
        if string.len(content) > 512 then
            UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(53241107))
            return
        end
        local chatCDConfig = ChatManager:GetChatSettingByID(5)
        local timeNow = TimeMgr:GetServerTime()
        local setCD = v2n(chatCDConfig) + timeNow
        ChatManager:SetChatCD(setCD)
        self:SendChatMsg(CHAT_MSG_TYPE.NORMAL)
    elseif name == "m_btnNewMsg" then
        self.chatSlider:MoveToPosition(nil,0)
    elseif name == "m_btnEmoji" then
        --self.chatInputField.text = "<sprite=0>"
        self.isShowEmoji = not self.isShowEmoji
        SetActive(self.ui.m_goEmojiRoot,self.isShowEmoji)
        self.isShowBtnMask = not self.isShowBtnMask
        SetActive(self.ui.m_btnMask,self.isShowBtnMask)
	elseif name == "m_btnOpenExtensionPanel" then
		if self.ui.m_goChatExtensionPanel.activeSelf then
			--折叠
			self:ShowExtensionPanel(false,true)
		else
			
			if self.currentChannel == FRIEND_VIEW_TYPE.LeagueChat then
				UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(5008))
				return
			end

			--展开
			self:ShowExtensionPanel(true,true)
			
			--邀请按钮只能在 相应的频道发放
			if self.currentChannel == FRIEND_VIEW_TYPE.WorldChannel 
			or self.currentChannel == FRIEND_VIEW_TYPE.OptionalLanguage 
			or self.currentChannel == FRIEND_VIEW_TYPE.Recently 
			or self.currentChannel == FRIEND_VIEW_TYPE.Friend
			then
				SetActive(self.ui.m_goUnionInvite,true)
				--判断职位权限
				local myDuty = LeagueManager:GetMyLeagueDuty()
				local pos_config = LeagueManager:GetUnionPosById(myDuty)
				
				local sendAble = pos_config and pos_config.invite and pos_config.invite == 1
				SetActive(self.ui.m_btnUnionInviteSend,sendAble)
				SetActive(self.ui.m_btnUnionInviteSendDisable,not sendAble)
			else
				--频道不符合
				SetActive(self.ui.m_goUnionInvite,false)
			end

		end
	elseif name == "m_btnUnionInviteSendDisable" then
		self:ShowExtensionPanel(false,true)
		UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(53241116))
	elseif name == "m_btnUnionInviteSend" then
		self:ShowExtensionPanel(false,true)
		--发送公会邀请

		--聊天CD
		if self.isChatCD then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(53241036))
            return
        end
		--判断职位权限
		local myDuty = LeagueManager:GetMyLeagueDuty()
		local pos_config = LeagueManager:GetUnionPosById(myDuty)
		if not (pos_config and pos_config.invite and pos_config.invite == 1) then
			UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(53241116))
		end

		--判断频道TODO

		local chatCDConfig = ChatManager:GetChatSettingByID(5)
        local timeNow = TimeMgr:GetServerTime()
        local setCD = v2n(chatCDConfig) + timeNow
        ChatManager:SetChatCD(setCD)

		self:SendChatMsg(CHAT_MSG_TYPE.UNION_INVITE)
    elseif name == "m_btnAnnounce" then
        UI_SHOW(UIDefine.UI_LeagueAnnounceView)
	elseif name == "btnAddLeague" or name == "m_btnAddLeague" then
		--跳转加入团队
		if not LeagueManager:IsOpenUnion() then
			--公会未开放
			local openLevel = LeagueManager:GetConfigById(2)
			openLevel = v2n(openLevel)
			UIMgr:Show(UIDefine.UI_WidgetTip, string.format(LangMgr:GetLang(9205),openLevel) )
			return
		end
		self:Close()
		UI_SHOW(UIDefine.UI_Union, 2)
    -- 删除好友按钮
    elseif name == "m_btnDeleteFriend" then
        UI_SHOW(UIDefine.UI_FriendDelete)
    elseif name == "m_btnEmojiPage1" then
        self:ChangeEmojiPage(1)
    elseif name == "m_btnEmojiPage2" then
        self:ChangeEmojiPage(2)
    elseif name == "m_btnMask" then
        self.isShowBtnMask = false
        SetActive(self.ui.m_btnMask, false)
    end
end

function UI_FriendView:ShowExtensionPanel(show,animation)
	if self.ui.m_goChatExtensionPanel.activeSelf == show then
		return
	end
    self.isShowBtnMask = show
    SetActive(self.ui.m_btnMask,self.isShowBtnMask)
	local tf = self.ui.m_goChatExtensionPanel.transform
	if not tf then 
		return
	end
	
	if self.tweenExtensionPanel then
		self.tweenExtensionPanel:Kill()
		self.tweenExtensionPanel = nil
	end
	if animation then
		
		
		if show then
			SetActive(tf,true)
			self.tweenExtensionPanel = AddDOTweenNumberDelay(0,1,0.2,0,function (num)
                tf:SetLocalScale(1,num,1)
            end)

		else
			SetActive(tf,true)
			self.tweenExtensionPanel = AddDOTweenNumberDelay(1,0,0.2,0,function (num)
				SetUIForceRebuildLayout(tf.parent)
                tf:SetLocalScale(1,num,1)
            end,function ()
				SetActive(tf,false)
			end)
		end
	else
		if show then
			tf:SetLocalScale(1,1,1)
		end
		SetActive(tf,show)
	end
end

--- 初始化界面
function UI_FriendView:InitPanel()
    CreateCommonHead(GetChild(self.ui.m_goChatCellOther,"headbg").transform,0.75)
    CreateCommonHead(GetChild(self.ui.m_goChatCellMe,"headbg").transform,0.75)
    CreateCommonHead(GetChild(self.ui.m_goChatCellInviteOther,"headbg"),0.75)
    CreateCommonHead(GetChild(self.ui.m_goChatCellInviteMe,"headbg"),0.75)
    CreateCommonHead(GetChild(self.ui.m_goAnnounceOther,"headbg"),0.75)
    CreateCommonHead(GetChild(self.ui.m_goChatCellInviteBossOther,"headbg"),0.75)
    CreateCommonHead(GetChild(self.ui.m_goChatCellInviteBossMe,"headbg"),0.75)    
    CreateCommonHead(GetChild(self.ui.m_goChatCellBossDamageOther,"headbg"),0.75)
    CreateCommonHead(GetChild(self.ui.m_goChatCellBossDamageMe,"headbg"),0.75)
    CreateCommonHead(GetChild(self.ui.m_goChatCellArenaOther,"headbg"),0.75)
    CreateCommonHead(GetChild(self.ui.m_goChatCellArenaMe,"headbg"),0.75)
    CreateCommonHead(GetChild(self.ui.m_goChatCellTrainOther,"headbg"),0.75)
    CreateCommonHead(GetChild(self.ui.m_goChatCellTrainMe,"headbg"),0.75)
    -- 好友列表滚动视图
    self.friendSlider = SlideRect.new()
    self.friendSlider:Init(self.ui.m_scrollviewFriend, 2)

    self.friendItems = {}
    for i = 1, FriendItemShowNum do
        self.friendItems[i] = FriendItem.new()
        self.friendItems[i]:Init(UEGO.Instantiate(self.ui.m_goItemFriend.transform))
        self.friendItems[i]:SetType(FRIEND_VIEW_TYPE.Friend)
    end
    self.friendSlider:SetItems(self.friendItems, 0, Vector2.New(0, 0))

    -- 刷新赠送/领取体力次数
    self:RefreshSendTimes()
    self:RefreshReveiveTimes()

    -- 聊天频道滚动视图
    self.channelSlider = SlideRect.new()
    self.channelSlider:Init(self.ui.m_scrollviewChannel, 2)

    self.channelItems = {}
    for i = 1, ItemShowNums do
        self.channelItems[i] = ChannelItem.new()
        self.channelItems[i]:Init(UEGO.Instantiate(self.ui.m_goChannelItem.transform))
    end
    self.channelSlider:SetItems(self.channelItems, 0, Vector2.New(0, 20))

    -- 聊天列表滚动视图
    self.chatSlider = ChatScrollRect.new()
    local scrollParam = {
        paddingBottom = 70
    }
    self.chatSlider:Init(self.ui.m_scrollviewChat, scrollParam)

    -- 私聊滚动视图
    self.privateChatSlider = ChatScrollRect.new()
    local privateScrollParam = {
        paddingBottom = 70
    }
    self.privateChatSlider:Init(self.ui.m_scrollviewPrivateChat, privateScrollParam)

    local chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChatItemOther.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellOther.transform))
    end
    self.chatSlider:SetItems("ChatItemOther", chatItems)

    chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChatItemMe.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellMe.transform))
    end
    self.chatSlider:SetItems("ChatItemMe", chatItems)

	chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChateInviteOther.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellInviteOther.transform))
    end
    self.chatSlider:SetItems("ChateInviteOther", chatItems)
    
	chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = AnnounceOther.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goAnnounceOther.transform))
    end
    self.chatSlider:SetItems("m_goAnnounceOther", chatItems)

	chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChateInviteMe.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellInviteMe.transform))
    end
    self.chatSlider:SetItems("ChateInviteMe", chatItems)

    chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChateInviteBossOther.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellInviteBossOther.transform))
    end
    self.chatSlider:SetItems("ChateInviteBossOther", chatItems)
    
    chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChateInviteBossMe.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellInviteBossMe.transform))
    end
    self.chatSlider:SetItems("ChateInviteBossMe", chatItems)

    chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChateBossDamageOther.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellBossDamageOther.transform))
    end
    self.chatSlider:SetItems("ChateBossDamageOther", chatItems)

    chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChateBossDamageMe.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellBossDamageMe.transform))
    end
    self.chatSlider:SetItems("ChateBossDamageMe", chatItems)

    chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChateArenaShareOther.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellArenaOther.transform))
    end
    self.chatSlider:SetItems("ChateArenaShareOther", chatItems)

    chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChateArenaShareMe.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellArenaMe.transform))
    end
    self.chatSlider:SetItems("ChateArenaShareMe", chatItems)

    chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChateTrainShareOther.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellTrainOther.transform))
    end
    self.chatSlider:SetItems("ChateTrainShareOther", chatItems)

    chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChateTrainShareMe.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellTrainMe.transform))
    end
    self.chatSlider:SetItems("ChateTrainShareMe", chatItems)    
    
    chatItems = {}
    for i = 1, ItemShowNums do
        chatItems[i] = ChateTrainRob.new()
        chatItems[i]:Init(UEGO.Instantiate(self.ui.m_goChatCellTrainRob.transform))
    end
    self.chatSlider:SetItems("ChateTrainRob", chatItems)

    -- 屏蔽好友滚动视图
    self.blackListSlider = SlideRect.new()
    self.blackListSlider:Init(self.ui.m_scrollviewPlayerBlacklist, 2)

    self.blackListItems = {}
    for i = 1, FriendItemShowNum do
        self.blackListItems[i] = BlackListItem.new()
        self.blackListItems[i]:Init(UEGO.Instantiate(self.ui.m_goBlackListItem.transform))
    end
    self.blackListSlider:SetItems(self.blackListItems, 0, Vector2.New(0, 0))

    self.uiDrag = self.ui.m_scrollviewChat:GetComponent(typeof(CS.UIDrag))
    local startPosition = 0
    self.uiDrag.m_BeginDrag = function ()

    end
    self.uiDrag.m_EndDrag = function ()
        local endPosition
        if self.scrollType == 1 then
            endPosition = self.ui.m_scrollviewChat.horizontalNormalizedPosition
        else
            endPosition = self.ui.m_scrollviewChat.verticalNormalizedPosition
        end
        self:OnChatViewDragEnd(endPosition)
    end

    -- 输入框字符限制提示
    self.chatInputField.onValueChanged:AddListener(function (value)
        local length = utf8.len(value)
        if length >= self.chatInputField.characterLimit then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(53241107))
        end
        local content = self.chatInputField.text
        SetActive(self.ui.m_btnSendChat,string.len(content) > 0)
        SetActive(self.ui.m_btnOpenExtensionPanel,string.len(content) == 0)
    end)
end

function UI_FriendView:OnChatViewDragEnd(param)
    if self.currentChannel == FRIEND_VIEW_TYPE.LeagueChat then
        if param >= 1 then
            self:RequestLeagueChatList(true)
        end
    end

    if self.clickType == FRIEND_VIEW_TYPE.Friend then
        if param >= 1 and CurPlayerID then
            self:RequestFriendChatList(CurPlayerID, true)
        end
    end
end

--- 刷新选中屏蔽名单的玩家
--- @param data table 玩家数据
function UI_FriendView:RefreshBlacklistPlayer(data)
    -- 玩家名称
    self.ui.m_txtName.text = data.name
    -- 语言
    self.ui.m_txtLang.text = FriendManager:GetLanguage(data)
    -- 头像
    --local headIconPath = FriendManager:GetHeadIcon(data.headId)
    --if headIconPath then
    --    SetImageSprite(self.ui.m_imgHead, headIconPath, false)
    --end
    
    SetHeadAndBorderByGo(self.blackHeadNode,data.headId,data.border)
    --customHeadObj.transform.localPosition = Vector3.zero
    
    -- 段位
    local rankIconPath = FriendManager:GetRankStageIcon(data.rankStage)
    if rankIconPath then
        SetImageSprite(self.ui.m_imgRank, rankIconPath, false)
    end
    -- 玩家 ID
    self.ui.m_txtMyID.text = data.playerId
    -- 等级
    self.ui.m_txtMyLevel.text = "Lv." .. data.level
    -- 全服排名
    local rank = NetFriendData:GetBlacklistRank(data.playerId)
    if rank == nil or rank == 0 then
        NetFriendData:QueryUserInfo(data.playerId, function (friend)
            NetFriendData:UpdateBlacklistRank(data.playerId, friend.rank)
            if friend.rank == nil or friend.rank == 0 then
                self.ui.m_txtMyRankAll.text = LangMgr:GetLang(9056)
            else
                self.ui.m_txtMyRankAll.text = friend.rank
            end
        end)
    else
        self.ui.m_txtMyRankAll.text = rank
    end
end

function UI_FriendView:OnRefreshRed()
	local apple_red = NetFriendData:GetAppleRed()
	local bind_Red = NetInviteFriendData:CanGetBindFbReward()
	-- SetActive(self.ui.m_goTabFriendRed,apple_red or receive_red or bind_Red)
	--UI_UPDATE(UIDefine.UI_MainFace, 33, nil)
	local chat_red = NetLeagueData:GetReceiveChatRed()
	SetActive(self.ui.m_goTabChatRed,chat_red)
end

--region ----------------------------------------- 侧边栏 -----------------------------------------

--- 点击侧边栏页签
--- @param type number 页签类型
function UI_FriendView:OnClickHorTab(type, noRequest)
    local clickType = type or 1
    self.clickType = clickType
	self.currentChannel = self.clickType
    CurClickType = clickType
    CurPlayerID = nil

    self:HighlightSideBarBtn(clickType)

    SetActive(self.ui.m_goSwitchChannel, false)
    --SetActive(self.ui.m_goFriendTop, false)
    SetActive(self.ui.m_goBlacklistTop, false)
    SetActive(self.ui.m_goRecentlyTop, false)

    SetActive(self.ui.m_scrollviewChat, false)
    SetActive(self.ui.m_goNoSystemMessage, false)
    SetActive(self.ui.m_goNoBlacklist, false)
    SetActive(self.ui.m_goNoBlacklistEmpty, false)
    SetActive(self.ui.m_goRecentlyEmpty, false)
    SetActive(self.ui.m_goNoRecently, false)
    SetActive(self.ui.m_goNoFriend, false)

    SetActive(self.ui.m_goPlayerBlacklist, false)

    SetActive(self.ui.m_goSendMessage, false)
	SetActive(self.ui.m_goChatExtensionPanel, false)
    SetActive(self.ui.m_goNoUnion, false)
    SetActive(self.ui.m_goNoAnnouncement, false)
    SetActive(self.ui.m_btnAnnounce, false)

    SetActive(self.ui.m_btnVisit, false)
    SetActive(self.ui.m_btnDeleteFriend, false)
    SetActive(self.ui.m_scrollviewFriend, false)
    
    -- 选择聊天
    if clickType == FRIEND_VIEW_TYPE.Chat then
        SetActive(self.ui.m_goSwitchChannel, true)
        local isAnnounceNew = ChatManager:GetAnnounceMentHasNew()
        local isAreaNew = ChatManager:GetAreaRedPoint()
        local isUnionNew = ChatManager:GetUnionRedPoint()
        local openChannel = FRIEND_VIEW_TYPE.WorldChannel
        if isAnnounceNew then
            openChannel = FRIEND_VIEW_TYPE.LeagueAnnouncement
        elseif isAreaNew then
            openChannel = FRIEND_VIEW_TYPE.OptionalLanguage
        elseif isUnionNew then
            openChannel = FRIEND_VIEW_TYPE.LeagueChat
        end
        self:RefreshWorldChatRedPoint()
        self:RefreshAreaChatRedPoint()
        self:RefreshLeagueChatRedPoint()
        self:RefreshAnnounceChatRedPoint()
        -- 选择当前的聊天频道
        self:OnClickChannelTab(openChannel)
    -- 选择好友
    elseif clickType == FRIEND_VIEW_TYPE.Friend then
        SetActive(self.ui.m_scrollviewFriend, true)
        if #NetFriendData.friendList > 0 then
            --SetActive(self.ui.m_scrollviewChat, true)
            --SetActive(self.ui.m_goSendMessage, true)
            SetActive(self.ui.m_btnVisit, true)
        elseif #NetFriendData.friendList == 0 then
            --SetActive(self.ui.m_goNoFriend, true)
        end
        SetActive(self.ui.m_btnDeleteFriend, true)
        local thinkTable = {
            ["click_friend"] = 1,
        }
        SdkHelper:ThinkingTrackEvent(ThinkingKey.open_gam, thinkTable)

        self:SetFriendListData(NetFriendData.friendList, FRIEND_VIEW_TYPE.Friend)

        --self:SelectFirstItem()
    -- 选择最近联系
    elseif clickType == FRIEND_VIEW_TYPE.Recently then
        SetActive(self.ui.m_scrollviewFriend, true)
        if not noRequest then
            -- 请求最近联系
            NetFriendData:RequestFriendList(FRIEND_REQUEST_TYPE.Recently, function (data)
                if self.clickType == FRIEND_VIEW_TYPE.Recently then
                    self:SetFriendListData(NetFriendData.recentlyList, FRIEND_VIEW_TYPE.Recently)
                    --self:SelectFirstItem()
                end
            end)
        end

        self:SetFriendListData(NetFriendData.recentlyList, FRIEND_VIEW_TYPE.Recently)
        --self:SelectFirstItem()

        if #NetFriendData.recentlyList > 0 then
            --SetActive(self.ui.m_goFriendTop, true)
            SetActive(self.ui.m_btnVisit, true)
            --SetActive(self.ui.m_scrollviewChat, true)
            --SetActive(self.ui.m_goSendMessage, true)
			SetActive(self.ui.m_goChatExtensionPanel, false)
        elseif #NetFriendData.recentlyList == 0 then
            --SetActive(self.ui.m_goRecentlyTop, true)
            --SetActive(self.ui.m_goNoRecently, true)
        end
    -- 选择屏蔽名单
    elseif clickType == FRIEND_VIEW_TYPE.Blacklist then
        --SetActive(self.ui.m_goBlacklistTop, true)

        --self:SetFriendListData(NetFriendData.blacklist, FRIEND_VIEW_TYPE.Blacklist)
        self:SetBlackListData(NetFriendData.blacklist)
        --self:SelectFirstItem()

        if #NetFriendData.blacklist > 0 then
            SetActive(self.ui.m_goPlayerBlacklist, true)
        elseif #NetFriendData.blacklist == 0 then
            --SetActive(self.ui.m_goNoBlacklist, true)
        end
    end

	if FRIEND_VIEW_TYPE.Accept == clickType then
		NetFriendData:SetAppleRed(false)
		-- self:OnRefreshRed()
	end
	self:CloseOther(clickType)
end

--- 侧边栏按钮高亮
--- @param clickType number 点击类型
function UI_FriendView:HighlightSideBarBtn(clickType)
    -- 选择聊天
    local selectChat = clickType == FRIEND_VIEW_TYPE.Chat
    SetActive(self.ui.m_btnTabChatLight, selectChat)
    SetActive(self.ui.m_goChat, selectChat)

    -- 选择好友
    local selectFriend = clickType == FRIEND_VIEW_TYPE.Friend
    SetActive(self.ui.m_btnTabFriendLight, selectFriend)

    -- 选择最近联系
    local selectRecently = clickType == FRIEND_VIEW_TYPE.Recently
    SetActive(self.ui.m_btnTabRecentlyLight, selectRecently)

    -- 选择屏蔽名单
    local selectBlacklist = clickType == FRIEND_VIEW_TYPE.Blacklist
    SetActive(self.ui.m_btnTabBlacklistLight, selectBlacklist)

    if clickType == FRIEND_VIEW_TYPE.Friend or clickType == FRIEND_VIEW_TYPE.Recently
        or clickType == FRIEND_VIEW_TYPE.Blacklist then
        SetActive(self.ui.m_goFriend, true)
    else
        SetActive(self.ui.m_goFriend, false)
    end

    SetActive(self.ui.m_btnAddFriend, selectFriend)
    SetActive(self.ui.m_goFriendBottom, selectFriend)
	local showFB = self:RefreshFbItem(selectFriend)

    local rect = GetComponent(self.ui.m_scrollviewFriend, UE.RectTransform)
    if clickType == FRIEND_VIEW_TYPE.Friend then
        rect.offsetMax = Vector2.New(540, showFB and -120 or 0)
        rect.offsetMin = Vector2.New(-540, 0)
        if self.friendSlider then
            self.friendSlider.viewportHeight = rect.rect.height
        end
    elseif clickType == FRIEND_VIEW_TYPE.Recently or clickType == FRIEND_VIEW_TYPE.Blacklist then
        rect.offsetMax = Vector2.New(540, 90)
        rect.offsetMin = Vector2.New(-540, 0)
        if self.friendSlider then
            self.friendSlider.viewportHeight = rect.rect.height
        end
    end
end

function UI_FriendView:RefreshFbItem(selectFriend)
	local show = false
	if selectFriend and not NetInviteFriendData:HasReceivedBindFbReward() then
		
		local bindState = SdkHelper:IsBinding();
		if bindState == 1 or bindState == 0 then
			show = true
			local btn_login = GetChild(self.ui.m_goItemFacebook,"btn_login",UEUI.Button)
			local btn_getReward = GetChild(self.ui.m_goItemFacebook,"btn_getReward",UEUI.Button)
			local itemIcon = GetChild(self.ui.m_goItemFacebook,"imgReward",UEUI.Image)
			local itemNum = GetChild(self.ui.m_goItemFacebook,"txtReward",UEUI.Text)

			if btn_login then btn_login.onClick:RemoveAllListeners() end
			if btn_getReward then btn_getReward.onClick:RemoveAllListeners() end

			local rewardStr = FriendManager:GetChatSettingByID(6,true)
			if rewardStr then
				local arr = Split1(rewardStr)
				local itemId = v2n(arr[1])
				local itemCnt = v2n(arr[2]) or 0
				if itemId then
					SetImageSprite(itemIcon,ItemConfig:GetIcon(itemId),false)
				end
				itemNum.text = itemCnt
			end

			-- 已经绑定过Fb 可以领奖
			if bindState == 1 then
				-- 已经绑定过Fb 可以领奖
				SetActive(btn_login, false)
				SetActive(btn_getReward, true)

				local listener = function()
					--领奖
					NetInviteFriendData:SendBindFbReward()
				end
				if btn_getReward then btn_getReward.onClick:AddListener(listener) end
			elseif bindState == 0 then
				-- 还没有绑定过
				SetActive(btn_login, true)
				SetActive(btn_getReward, false)

				local listener = function()
					--FB 登录
					SdkHelper:BindingDirect(1)
				end
				if btn_login then btn_login.onClick:AddListener(listener) end
			end
		else
			--已经绑定过 但是不是FB
			show = false
		end
	else
		show = false
	end
    show = false
	SetActive(self.ui.m_goItemFacebook, show)
	return show
end

--endregion -------------------------------------- 侧边栏 -----------------------------------------

--region ----------------------------------------- 聊天频道 -----------------------------------------

--- 点击聊天频道
--- @param type any
function UI_FriendView:OnClickChannelTab(type)
    local clickType = type or FRIEND_VIEW_TYPE.SystemMessages
    self.currentChannel = clickType
    
    if self.currentChannel == FRIEND_VIEW_TYPE.WorldChannel then
        self.curChatChannel = ChatManager:GetWorldChatChannel()
    elseif self.currentChannel == FRIEND_VIEW_TYPE.OptionalLanguage then
        self.curChatChannel = ChatManager:GetAreaChatChannel()
    end
    self:RefreshWorldChatChannelInfo()
    self:RefreshAreaChatChannelInfo()
    self:HighlightChatBtns(clickType)
    self:CloseNewMsg()
    SetActive(self.ui.m_scrollviewChat, false)
    SetActive(self.ui.m_goNoSystemMessage, false)
    SetActive(self.ui.m_goSendMessage, false)
    SetActive(self.ui.m_goNoUnion, false)
    SetActive(self.ui.m_goNoAnnouncement, false)
    SetActive(self.ui.m_btnAnnounce, false)

    -- 选择系统消息
    if clickType == FRIEND_VIEW_TYPE.SystemMessages then
        self:RefreshSystemInfo()
    -- 选择世界频道 选择自选语言 
    elseif clickType == FRIEND_VIEW_TYPE.WorldChannel or clickType == FRIEND_VIEW_TYPE.OptionalLanguage then
        self:RefreshChatList()
        --选择联盟聊天
    elseif clickType == FRIEND_VIEW_TYPE.LeagueChat then
        SetActive(self.ui.m_goSendMessage, true)
        SetActive(self.ui.m_scrollviewChat, true)
        self:RequestLeagueChatList()
        -- 选择联盟公告
    elseif clickType == FRIEND_VIEW_TYPE.LeagueAnnouncement then

        self:RequestLeagueAnnouncemen()
    end
end

--- 聊天频道高亮
--- @param clickType number 点击类型
function UI_FriendView:HighlightChatBtns(clickType)
    self.chatInputField.text = ""
    -- 选择系统消息
    SetActive(self.ui.m_btnSystemMessagesLight, clickType == FRIEND_VIEW_TYPE.SystemMessages)
    -- 选择世界频道
    SetActive(self.ui.m_btnWorldChannelLight, clickType == FRIEND_VIEW_TYPE.WorldChannel)
    -- 选择自选语言
    SetActive(self.ui.m_btnOptionalLanguageLight, clickType == FRIEND_VIEW_TYPE.OptionalLanguage)
    -- 选择联盟聊天
    SetActive(self.ui.m_btnLeagueChatLight, clickType == FRIEND_VIEW_TYPE.LeagueChat)
    -- 选择联盟公告
    SetActive(self.ui.m_btnLeagueAnnouncementLight, clickType == FRIEND_VIEW_TYPE.LeagueAnnouncement)
end

--- 聊天频道系统消息
function UI_FriendView:RefreshSystemInfo()
    SetActive(self.ui.m_imgSwitchChannel,false)
    SetActive(self.ui.m_btnSwitchChannel,false)
    self.ui.m_txtSwitchChannel.text = LangMgr:GetLang(53241028)
    local datas = self:GetSystemMessageList()
    self.hasSystemMessage = table.count(datas) > 0
    SetActive(self.ui.m_scrollviewChat, self.hasSystemMessage)
    SetActive(self.ui.m_goNoSystemMessage, not self.hasSystemMessage)

    self.chatSlider:SetDatas(datas,true)
    local chatContent = self.ui.m_scrollviewChat.content
    local chatViewport = self.ui.m_scrollviewChat.viewport
    SetUIForceRebuildLayout(chatContent)
    oldChatContentHeight = chatContent.rect.height
    chatViewportHeight = chatViewport.rect.height
end

function UI_FriendView:GetSystemMessageList()
    local SystemMessages = NetAnnounceData:GetDataByKey("sysLogCache")
    local list = {}
    for i, v in ipairs(SystemMessages) do
        local tempT = deepcopy(v)
        tempT.isOther = true
        tempT.chatType = FRIEND_VIEW_TYPE.SystemMessages
        local name = tempT.isOther and "ChatItemOther" or "ChatItemMe"
        tempT.name = name
        local lineHight,lineWidth = self:ComputeChatCellHight(tempT)
        local tempTrans 
        if v.isOther then
            tempTrans = self.ui.m_goChatCellOther.transform
        else
            tempTrans = self.ui.m_goChatCellMe.transform
        end
        local titleHeight = tempTrans.sizeDelta.y
        tempT.hight = titleHeight + lineHight + 50
        tempT.lineHight = lineHight
        tempT.lineWidth = lineWidth
        table.insert(list,tempT)
    end
    return list
end

-- 世界聊天频道信息
function UI_FriendView:RefreshWorldChatChannelInfo()
    local worldChannel = ChatManager:GetWorldChatChannel()
    if worldChannel ~= 0 then
        SetActive(self.ui.m_imgSwitchChannel,true)
        SetActive(self.ui.m_btnSwitchChannel,true)
        self.ui.m_txtWorldChannel.text = LangMgr:GetLang(53241030) .. LangMgr:GetLangFormat(53241031,worldChannel)
        self.ui.m_txtWorldChannelLight.text = LangMgr:GetLang(53241030) .. LangMgr:GetLangFormat(53241031,worldChannel)
    end
end

-- 区域聊天频道信息
function UI_FriendView:RefreshAreaChatChannelInfo()
    local areaChannel = ChatManager:GetAreaChatChannel()
    if areaChannel ~= 0 then
        local lanName = self:GetPlayerLanName()
        self.ui.m_txtOptionalLanguage.text = lanName .. "\n" .. LangMgr:GetLangFormat(53241031,areaChannel)
        self.ui.m_txtOptionalLanguageLight.text = lanName .. "\n" .. LangMgr:GetLangFormat(53241031,areaChannel)
    end
end

--- 聊天频道世界聊天
function UI_FriendView:RefreshChatList()
    local chatContent = self.ui.m_scrollviewChat.content
    oldChatContentHeight = chatContent.rect.height
    local m_txtDefalt = GetComponent(self.ui.m_goPlaceholder.gameObject,CS.TMPro.TextMeshProUGUI)
    m_txtDefalt.text = LangMgr:GetLang(53241035)

    SetActive(self.ui.m_goSendMessage, true)
    SetActive(self.ui.m_scrollviewChat, true)

    SetActive(self.ui.m_imgSwitchChannel,true)
    SetActive(self.ui.m_btnSwitchChannel,true)
    local chatChannel = self.curChatChannel
    local chatType
    if self.currentChannel == FRIEND_VIEW_TYPE.WorldChannel then
        chatType = CHAT_TYPE_ENUM.WORLD
        self.ui.m_txtSwitchChannel.text = LangMgr:GetLang(53241030) .. LangMgr:GetLangFormat(53241032,self.curChatChannel)
    elseif self.currentChannel == FRIEND_VIEW_TYPE.OptionalLanguage then
        chatType = CHAT_TYPE_ENUM.AREA
        local lanName = self:GetPlayerLanName()
        self.ui.m_txtSwitchChannel.text = lanName .. LangMgr:GetLangFormat(53241032,self.curChatChannel)
    end
    if chatType then
        self:RequestWorldOrAreaChatList(chatType)
        chatContent = self.ui.m_scrollviewChat.content
        oldChatContentHeight = chatContent.rect.height
    end
end

--- 聊天频道联盟聊天
function UI_FriendView:RequestWorldOrAreaChatList(chatType)
    self.chatSlider:ClearDatas()
    local channelId = self.curChatChannel
    local function RequestChatListCallBack(isMoveEnd)
        local data_list = ChatManager:GetChatCacheByTypeAndChannel(chatType,channelId)
        local datas = self:GetChatList(data_list)
        if IsTableEmpty(datas) then
            return
        end
        self.chatSlider:SetDatas(datas,isMoveEnd)
    end
    
    local data_list = ChatManager:GetChatCacheByTypeAndChannel(chatType,channelId)
    local timeAt = 0
    if  not IsTableEmpty(data_list) then
        local count = table.count(data_list)
        local lastData = data_list[count]
        timeAt = lastData.chat.timestamp
    end
    
    local langID = LangMgr:GetLangId()
    
    local param = {}
    param.type = chatType
    param.lastAt = timeAt
    param.channelId = channelId
    param.lan = langID
    ChatManager:RequestChatChannelList(param,RequestChatListCallBack)
end

function UI_FriendView:GetPlayerLanName()
    local langID = LangMgr:GetLangId()
    local datalist = ConfigMgr:GetData(ConfigDefine.ID.language_config)
    local name = ""
    for k, v in pairs(datalist) do
        if v.on_off == "1" then
            if langID == v.unityid then
                name = v.name
            end
        end
    end
    return name
end

--- 聊天频道联盟聊天
function UI_FriendView:RequestLeagueChatList(isDrag)
    self.ui.m_txtSwitchChannel.text = LangMgr:GetLangFormat(53241038)
    SetActive(self.ui.m_imgSwitchChannel,false)
    SetActive(self.ui.m_btnSwitchChannel,false)
    if not isDrag then
        self.chatSlider:ClearDatas()
    end
    local function RefreshLeagueChatList(isMoveEnd)
        local data_list = ChatManager:GetChatCacheByTypeAndChannel(CHAT_TYPE_ENUM.UNION)
        local datas = self:GetChatList(data_list)
        if IsTableEmpty(datas) then
            self.chatSlider:ClearDatas()
            return
        end
        self.chatSlider:SetDatas(datas,isMoveEnd)
    end
    local isJoin = LeagueManager:IsJoinUnion()
    if not isJoin then
        SetActive(self.ui.m_goSendMessage, false)
        SetActive(self.ui.m_scrollviewChat, false)
        SetActive(self.ui.m_goNoUnion,true)
        return
    end
    local isChatContentEnd = ChatManager:GetUnionChatIsEnd()
    if isChatContentEnd then
        return
    end
    local data_list = ChatManager:GetChatCacheByTypeAndChannel(CHAT_TYPE_ENUM.UNION)
    local timeAt = 0
    if isDrag and not IsTableEmpty(data_list) then
        local lastData = data_list[1]
        timeAt = lastData.chat.timestamp
    end
    local param = {}
    param.type = CHAT_TYPE_ENUM.UNION
    param.lastAt = timeAt
    ChatManager:RequestChatChannelList(param,RefreshLeagueChatList)
end

function UI_FriendView:GetAnnounceDataList()
    local data_list = ChatManager:GetAnnounceList()
    local list = {}
    for i, v in ipairs(data_list) do
        local tempT = deepcopy(v)
        tempT.isOther = true
        tempT.isLeagueAnnounce = true
        tempT.chatType = FRIEND_VIEW_TYPE.LeagueAnnouncement
        local name = "m_goAnnounceOther" 
        tempT.name = name
        local lineHight,lineWidth = self:GetCellSizeByMessageType(tempT)
        local tempTrans = self.ui.m_goAnnounceOther.transform
        local titleHeight = tempTrans.sizeDelta.y
        tempT.hight = titleHeight + lineHight 
        tempT.lineHight = lineHight
        tempT.lineWidth = lineWidth
        table.insert(list,tempT)
    end
    return list
end

function UI_FriendView:UpdataLeagueAnnouncemen()
    if self.currentChannel ~= FRIEND_VIEW_TYPE.LeagueAnnouncement then
        return 
    end
    local data_list = ChatManager:GetAnnounceList()
    if IsTableEmpty(data_list) then
        SetActive(self.ui.m_goNoAnnouncement,true)
    else
        SetActive(self.ui.m_goNoAnnouncement,false)
    end
    local datas = self:GetAnnounceDataList()
    if IsTableEmpty(datas) then
		self.chatSlider:ClearDatas()
        return
    end
    SetActive(self.ui.m_scrollviewChat, true)
    self.chatSlider:SetDatas(datas,true)
end

--- 联盟公告
function UI_FriendView:RequestLeagueAnnouncemen()
    self.ui.m_txtSwitchChannel.text = LangMgr:GetLang(53241040)
    local data_list = ChatManager:GetAnnounceList()
    self.chatSlider:ClearDatas()
    SetActive(self.ui.m_goSendMessage, false)
    SetActive(self.ui.m_scrollviewChat, true)
    SetActive(self.ui.m_imgSwitchChannel,false)
    SetActive(self.ui.m_btnSwitchChannel,false)
    if IsTableEmpty(data_list) then
        local function RefreshLeagueChatList()
            local datas = self:GetAnnounceDataList()
            if IsTableEmpty(datas) then
                SetActive(self.ui.m_goNoAnnouncement,true)
                return
            end
            SetActive(self.ui.m_scrollviewChat, true)
            SetActive(self.ui.m_goNoAnnouncement,false)
            self.chatSlider:SetDatas(datas,true)
        end
        local param = {}
        param.relation_id = 0
        param.last_id = 0
        ChatManager:RequestLeagueAnnouncement(param,RefreshLeagueChatList)
    else
        local datas = self:GetAnnounceDataList()
        self.chatSlider:SetDatas(datas,true)
    end
    --
    SetActive(self.ui.m_btnAddLeague,not LeagueManager:IsJoinUnion())
    local myDuty = LeagueManager:GetMyLeagueDuty();
    if myDuty ~= -1 then
        local posConfig = LeagueManager:GetUnionPosById(myDuty);
        if posConfig and posConfig.notice == 1 then
            SetActive(self.ui.m_btnAnnounce, true)
        end
    end

end

function UI_FriendView:GetChatList(dataList)
    local list = {}
    local showTime = 0
    for i, v in ipairs(dataList) do
        local tempT = deepcopy(v)
        local tempTrans
        if v.isOther then
            tempTrans = self.ui.m_goChatCellOther.transform
        else
            tempTrans = self.ui.m_goChatCellMe.transform
        end
        if i == 1 then
            tempT.isShowTime = true
            showTime = v.chat.timestamp
        elseif v.chat.timestamp > (showTime + 10 * 60) then
            tempT.isShowTime = true
            showTime = v.chat.timestamp
        else
            tempT.isShowTime = false
        end
        local name 
        if v.chat.messageType == CHAT_MSG_TYPE.NORMAL then
            name = v.isOther and "ChatItemOther" or "ChatItemMe"
        elseif v.chat.messageType == CHAT_MSG_TYPE.UNION_INVITE then
			name = v.isOther and "ChateInviteOther" or "ChateInviteMe"
        elseif v.chat.messageType == CHAT_MSG_TYPE.UNION_INVITE_BOSS then
            name = v.isOther and "ChateInviteBossOther" or "ChateInviteBossMe"        
        elseif v.chat.messageType == CHAT_MSG_TYPE.UNION_BOSS_SHARE or v.chat.messageType == CHAT_MSG_TYPE.WORLD_BOSS_SHARE then
            name = v.isOther and "ChateBossDamageOther" or "ChateBossDamageMe"  
        elseif v.chat.messageType == CHAT_MSG_TYPE.ARENA_REPORT_SHARE then
            name = v.isOther and "ChateArenaShareOther" or "ChateArenaShareMe"      
        elseif v.chat.messageType == CHAT_MSG_TYPE.TRADE_TRAIN_INFO then
            name = v.isOther and "ChateTrainShareOther" or "ChateTrainShareMe"
        elseif v.chat.messageType == CHAT_MSG_TYPE.TRADE_TRAIN_ROB then
            name = "ChateTrainRob"
        elseif v.isLeagueAnnounce then
            name = v.name
		end
        if name then
            tempT.name = name
            tempT.isOther = v.isOther
            tempT.chatType = self.currentChannel
            tempT.content = v.chat.content
            tempT.player = v.player
            tempT.league = v.league
            tempT.chat = v.chat
            local titleHeight = tempTrans.sizeDelta.y
            local lineHight,lineWidth = self:GetCellSizeByMessageType(tempT,v.chat.messageType)

            tempT.hight = titleHeight + lineHight + 30
            tempT.lineHight = lineHight
            tempT.lineWidth = lineWidth

            table.insert(list,tempT)
        end
    end
    return list
end

function UI_FriendView:GetCellSizeByMessageType(data,msgType)
	if msgType == CHAT_MSG_TYPE.UNION_INVITE then
		return 330,MAX_WIDTH
    elseif msgType == CHAT_MSG_TYPE.UNION_INVITE_BOSS then
        return 250,MAX_WIDTH   
    elseif msgType == CHAT_MSG_TYPE.UNION_BOSS_SHARE or msgType == CHAT_MSG_TYPE.WORLD_BOSS_SHARE then
        return 445,MAX_WIDTH 
    elseif msgType == CHAT_MSG_TYPE.ARENA_REPORT_SHARE then
        return 320,MAX_WIDTH
    elseif msgType == CHAT_MSG_TYPE.TRADE_TRAIN_INFO then
        return 350,625
	else
		return self:ComputeChatCellHight(data)
	end
end

function UI_FriendView:ComputeChatCellHight(data)
    --计算高度
    local tempTrans
    if data.name == "ChatItemOther" then
        tempTrans = self.ui.m_goChatCellOther.transform
    elseif data.name == "ChatItemMe" then
        tempTrans = self.ui.m_goChatCellMe.transform
    elseif data.name == "m_goAnnounceOther" then
        tempTrans = self.ui.m_goAnnounceOther.transform
    end
    local textTMP = GetChild(tempTrans,"bubble/Text",CS.TMPro.TMP_Text)
    textTMP.enableWordWrapping = true
    textTMP.text = data.content
    
    local translateTextTMP 
    local layoutElement2 
    if data.isTranslated then
        translateTextTMP = GetChild(tempTrans,"bubble/Text/translate_txt",CS.TMPro.TMP_Text)
        layoutElement2 = GetComponent(translateTextTMP,CS.UnityEngine.UI.LayoutElement)
        translateTextTMP.enableWordWrapping = true
        translateTextTMP.text = data.translateContent
    end
    local lineHight = 50            -- 翻译按钮默认50高
    if data.name == "ChatItemMe" then
        lineHight = 0
    end
    local lineWidth = 0
    local announceTitleHeight = 0
    local announceLineSpecing = 0
    local announceSenderHeight = 0
    if data.name == "m_goAnnounceOther" then
        local announceTitleGo = GetChild(tempTrans,"bubble/imgTitleBg",UE.RectTransform)
        local txtSenderGo = GetChild(tempTrans,"bubble/txtSender",UE.RectTransform)
        announceTitleHeight = announceTitleGo.sizeDelta.y
        announceLineSpecing = 50
        announceSenderHeight = txtSenderGo.sizeDelta.y + 50 + 50
    end
    local layoutElement = GetComponent(textTMP,CS.UnityEngine.UI.LayoutElement)
    
    if textTMP.preferredWidth < MAX_WIDTH then
        textTMP.enableWordWrapping = false
        layoutElement.preferredWidth = textTMP.preferredWidth
        
        SetUIForceRebuildLayout(textTMP.gameObject)
        lineWidth = textTMP.preferredWidth + 15    -- 文本到边框距离30 左右对称 30/2
        lineHight = lineHight + textTMP.preferredHeight + 20-- 加上上下文本到边框的距离

        if data.isTranslated then
            if translateTextTMP.preferredWidth > lineWidth and translateTextTMP.preferredWidth < MAX_WIDTH then
                translateTextTMP.enableWordWrapping = false
                layoutElement2.preferredWidth = translateTextTMP.preferredWidth
                lineWidth = translateTextTMP.preferredWidth
            elseif translateTextTMP.preferredWidth > MAX_WIDTH then
                translateTextTMP.enableWordWrapping = true
                layoutElement2.preferredWidth = MAX_WIDTH
                lineWidth = MAX_WIDTH
            end
            SetUIForceRebuildLayout(translateTextTMP.gameObject)
            lineHight = lineHight + translateTextTMP.preferredHeight + 30 -- 翻译文本和文本间隔
        end
    else
        textTMP.enableWordWrapping = true
        layoutElement.preferredWidth = MAX_WIDTH
        SetUIForceRebuildLayout(textTMP.gameObject)
        
        lineWidth = MAX_WIDTH
        lineHight = lineHight + textTMP.preferredHeight + 20 -- 加上上下文本到边框的距离

        if data.isTranslated then
            translateTextTMP.enableWordWrapping = true
            layoutElement2.preferredWidth = MAX_WIDTH
            SetUIForceRebuildLayout(translateTextTMP.gameObject)
            lineHight = lineHight + translateTextTMP.preferredHeight + 30 -- 翻译文本和文本间隔
        end
    end
    lineHight = lineHight + announceTitleHeight + announceLineSpecing + announceSenderHeight + 30 -- 单元之间的间距20
    return lineHight,lineWidth
end

--- 聊天频道区域聊天
function UI_FriendView:RefreshAreaChat()
    
end

function UI_FriendView:SendChatMsg(messageType)
    local chatType 
    if self.currentChannel == FRIEND_VIEW_TYPE.WorldChannel then
        chatType = CHAT_TYPE_ENUM.WORLD
    elseif self.currentChannel == FRIEND_VIEW_TYPE.OptionalLanguage then
        chatType = CHAT_TYPE_ENUM.AREA
    elseif self.currentChannel == FRIEND_VIEW_TYPE.LeagueChat then
        chatType = CHAT_TYPE_ENUM.UNION
    end

    local params = {}
    params.type = chatType
    params.messageType = messageType
    local inputStr = self.chatInputField.text
    inputStr = string.gsub(inputStr, "\n", "")
    inputStr = string.gsub(inputStr, "\r", "")
    local isFlag,checkStr = SensitiveWordsMgr:IsContainsSensitiveWord(inputStr)
    if isFlag then
        params.content = checkStr
    else
        params.content = inputStr
    end
    
	-- 选中好友或最近联系页签
	if (self.clickType == FRIEND_VIEW_TYPE.Friend
		or self.clickType == FRIEND_VIEW_TYPE.Recently)
		and CurPlayerID then
		params.type = CHAT_TYPE_ENUM.PRIVATE
		params.targetId = CurPlayerID
	end

	if messageType == CHAT_MSG_TYPE.UNION_INVITE then
		ChatManager:SendChatMsg(params)
	else
		params.content = inputStr
		ChatManager:SendChatMsg(params)
		self.chatInputField.text = ""
	end
end

function UI_FriendView:PushMsg(msg,isMoveEnd)
    -- 聊天类型 ：世界 区域 联盟聊天
    local curChatType
    local msgChatType = msg.chat.type
    if msgChatType == CHAT_TYPE_ENUM.WORLD  then
        curChatType = FRIEND_VIEW_TYPE.WorldChannel
    elseif msgChatType == CHAT_TYPE_ENUM.AREA then
        curChatType = FRIEND_VIEW_TYPE.OptionalLanguage
    elseif msgChatType == CHAT_TYPE_ENUM.UNION then
        curChatType = FRIEND_VIEW_TYPE.LeagueChat
    elseif msgChatType == CHAT_TYPE_ENUM.PRIVATE then
        curChatType = self.currentChannel

        local msgPlayerID = msg.player.id
        local myID = NetUpdatePlayerData:GetPlayerInfo().id

        local canEnqueue
        -- 自己发送的消息
        if v2n(msgPlayerID) == v2n(myID) then
            canEnqueue = true
        end

        -- 选中的好友发送的消息
        if v2n(msgPlayerID) == v2n(CurPlayerID) then
            canEnqueue = true
        end

        self:RefreshFriendItems()
        self:RefreshRedPoint()

        if not canEnqueue then return end
    end

    if curChatType ~= self.currentChannel then
        return
    end

    local chatData = deepcopy(msg)
    if msg.chat.messageType == CHAT_MSG_TYPE.NORMAL then
        chatData.name = msg.isOther and "ChatItemOther" or "ChatItemMe"
    elseif msg.chat.messageType == CHAT_MSG_TYPE.UNION_INVITE then
		chatData.name = msg.isOther and "ChateInviteOther" or "ChateInviteMe"
    elseif msg.chat.messageType == CHAT_MSG_TYPE.UNION_INVITE_BOSS then
        chatData.name = msg.isOther and "ChateInviteBossOther" or "ChateInviteBossMe"
    elseif msg.chat.messageType == CHAT_MSG_TYPE.UNION_BOSS_SHARE or msg.chat.messageType == CHAT_MSG_TYPE.WORLD_BOSS_SHARE then
        chatData.name = msg.isOther and "ChateBossDamageOther" or "ChateBossDamageMe" 
    elseif msg.chat.messageType == CHAT_MSG_TYPE.ARENA_REPORT_SHARE then
        chatData.name = msg.isOther and "ChateArenaShareOther" or "ChateArenaShareMe"
    elseif msg.chat.messageType == CHAT_MSG_TYPE.TRADE_TRAIN_INFO then
        chatData.name = msg.isOther and "ChateTrainShareOther" or "ChateTrainShareMe"
    elseif msg.chat.messageType == CHAT_MSG_TYPE.TRADE_TRAIN_ROB then
        chatData.name = "ChateTrainRob"
    end
    if chatData.name == nil then
        return
    end
	chatData.chatType = self.currentChannel
	chatData.content = msg.chat.content

    local endPosition = self.ui.m_scrollviewChat.verticalNormalizedPosition
    local isScorllEnd = false
    if endPosition <= 0.2 then
        isScorllEnd = true
    end
    
    --计算高度
    local tempTrans
    if msg.isOther then
        tempTrans = self.ui.m_goChatCellOther.transform
    else
        tempTrans = self.ui.m_goChatCellMe.transform
    end
    
    local titleHeight = tempTrans.sizeDelta.y
	local lineHight,lineWidth = self:GetCellSizeByMessageType(chatData,chatData.chat.messageType)

    chatData.hight = titleHeight + lineHight + 30
    chatData.lineHight = lineHight
    chatData.lineWidth = lineWidth
    
    local needMoveEnd = isScorllEnd or isMoveEnd
    if needMoveEnd then
        
        local chatChannel
        local chatType
        if self.currentChannel == FRIEND_VIEW_TYPE.WorldChannel then
            chatType = CHAT_TYPE_ENUM.WORLD
            chatChannel = ChatManager:GetWorldChatChannel()
        elseif self.currentChannel == FRIEND_VIEW_TYPE.OptionalLanguage then
            chatType = CHAT_TYPE_ENUM.AREA
            chatChannel = ChatManager:GetAreaChatChannel()
        end
        local chatList = ChatManager:GetChatCacheByTypeAndChannel(chatType,chatChannel)
        if not IsTableEmpty(chatList) then
            local dataIndex = table.count(chatList)
            ChatManager:SetChatNewCacheIsRead(chatType,chatChannel,dataIndex)
        end
    end

    self.chatSlider:Enqueue(chatData,needMoveEnd)
    self:CheckIsNewMsg(chatData)
end

--{content :翻译内容, dataIndex ：第几个，chatType :聊天类型, chatChannel ：频道}
function UI_FriendView:TranslateCallBack(param)
    local chatType = param.chatType
    local chatChannel = param.chatChannel
    local chatList
    local dataIndex = param.dataIndex
    local data --= chatList[dataIndex]
    if chatType == CHAT_TYPE_ENUM.WORLD or chatType == CHAT_TYPE_ENUM.AREA then
        chatList = ChatManager:GetChatCacheByTypeAndChannel(chatType,chatChannel)
    elseif chatType == CHAT_TYPE_ENUM.PRIVATE then
        chatList = ChatManager:UpdateFriendMessageByID(param.playerId)
    elseif chatType == CHAT_TYPE_ENUM.UNION then
        chatList = ChatManager:GetChatCacheByTypeAndChannel(CHAT_TYPE_ENUM.UNION)
    elseif chatType == CHAT_TYPE_ENUM.SYSTEM then
        chatList = self:GetSystemMessageList()
    elseif param.isAnnounce then
        chatList = ChatManager:GetAnnounceList()
    end
    if not chatList then
        return
    end
    if chatType == CHAT_TYPE_ENUM.WORLD or chatType == CHAT_TYPE_ENUM.AREA 
            or chatType == CHAT_TYPE_ENUM.PRIVATE or chatType == CHAT_TYPE_ENUM.UNION then
        for i, v in ipairs(chatList) do
            if v.chat.id == dataIndex then
                data = v
            end
        end
    elseif chatType == CHAT_TYPE_ENUM.SYSTEM or param.isAnnounce then
        data = chatList[dataIndex]
    end
    if not data then
        return
    end
    local translateContent = param.content
    data.translateContent = translateContent
    data.isTranslated = true

    local datas 
    if chatType == CHAT_TYPE_ENUM.WORLD or chatType == CHAT_TYPE_ENUM.AREA or chatType == CHAT_TYPE_ENUM.UNION
            or chatType == CHAT_TYPE_ENUM.PRIVATE then
        datas = self:GetChatList(chatList)
    elseif param.isAnnounce then
        datas = self:GetAnnounceDataList()
    end
    self.chatSlider:SetDatas(datas)
    --self.chatSlider:UpdateData(data,dataIndex)
end

function UI_FriendView:CheckIsNewMsg(msg,isRead)
    local chatContent = self.ui.m_scrollviewChat.content
    local chatViewport = self.ui.m_scrollviewChat.viewport
    SetUIForceRebuildLayout(chatContent)
    local nowContentHeight = chatContent.rect.height
    local nowViewportHeight = chatViewport.rect.height

    if nowContentHeight < nowViewportHeight then
        return
    end
    if nowContentHeight > oldChatContentHeight then
        if isRead then
            oldChatContentHeight = oldChatContentHeight + msg.hight
        end
        SetActive(self.ui.m_goNewMsgRoot,true)
        local chatChannel
        local chatType
        if self.currentChannel == FRIEND_VIEW_TYPE.WorldChannel then
            chatType = CHAT_TYPE_ENUM.WORLD
            chatChannel = ChatManager:GetWorldChatChannel()
        elseif self.currentChannel == FRIEND_VIEW_TYPE.OptionalLanguage then
            chatType = CHAT_TYPE_ENUM.AREA
            chatChannel = ChatManager:GetAreaChatChannel()
        end
        local count = ChatManager:GetChatNewCacheCount(chatType,chatChannel)
        if count > 0 then
            local str = LangMgr:GetLangFormat(53241033,count)
            self.ui.m_txtNewMsg.text = str
			UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_btnNewMsg.transform)
            SetActive(self.ui.m_goNewMsgRoot,true)
        else
            SetActive(self.ui.m_goNewMsgRoot,false)
        end
    else
        SetActive(self.ui.m_goNewMsgRoot,false)
    end
end

function UI_FriendView:CloseNewMsg()
    SetActive(self.ui.m_goNewMsgRoot,false)
end

function UI_FriendView:UpdataEmojiInfo()
    local emojiFilter = self.defaultEmojiIndex == 1 and emojiFilter1 or emojiFilter2
    local isLock = self.defaultEmojiIndex == 2 and not NetGlobalData:GetIsUnLockEmoji()
    for i = 1, EmojiNum do

        if emojiFilter[i] == 1 then

            if self.emojiGoList[i] == nil then
                self.emojiGoList[i] = {}
                self.emojiGoList[i].obj = CreateGameObjectWithParent(self.ui.m_goEmojiItem, self.ui.m_goEmojiItemCount)
            end
            SetActive(self.emojiGoList[i].obj, i == 24 or not isLock)
            self.emojiGoList[i].btn = GetChild(self.emojiGoList[i].obj, "btn", UEUI.Button)
            self.emojiGoList[i].text = GetChild(self.emojiGoList[i].obj, "Text")
            self.emojiGoList[i].lock = GetChild(self.emojiGoList[i].obj, "lock",UEUI.Image)
            self.emojiGoList[i].tmp_text = GetComponent(self.emojiGoList[i].text, CS.TMPro.TextMeshProUGUI)
            self.emojiGoList[i].tmp_text.text = string.format("<sprite=%s>", i - 1)
            RemoveUIComponentEventCallback(self.emojiGoList[i].btn, UEUI.Button)
            AddUIComponentEventCallback(self.emojiGoList[i].btn, UEUI.Button, function(arg1, arg2)
                self:CloseEmojiPanel()
                local oldStr = self.chatInputField.text
                self.chatInputField.text = oldStr .. self.emojiGoList[i].tmp_text.text
            end)
        else
            if self.emojiGoList[i] ~= nil and self.emojiGoList[i].obj then
                SetActive(self.emojiGoList[i].obj, false)
            end
        end
    end
end

function UI_FriendView:CloseEmojiPanel()
    self.isShowEmoji = false
    SetActive(self.ui.m_goEmojiRoot,false)
end

function UI_FriendView:UpdataEmojiPageInfo()
    SetActive(self.ui.m_imgEmojiPageLight1,self.defaultEmojiIndex == 1)
    SetActive(self.ui.m_imgEmojiPageLight2,self.defaultEmojiIndex == 2)
end

function UI_FriendView:ChangeEmojiPage(page)
    if self.defaultEmojiIndex == page then
        return
    end
    self.defaultEmojiIndex = page
    self:UpdataEmojiInfo()
    self:UpdataEmojiPageInfo()
end

function UI_FriendView:RefreshChannelList(chatType)
    local channelData = self:GetChannelDataList(chatType)
    self.channelSlider:SetData(channelData)
end

function UI_FriendView:GetChannelDataList(chatType)
    local dataList = {}
    local serverDatas = ChatManager:GetChannelListByType(chatType)
    for i, v in ipairs(serverDatas) do
        table.insert(dataList,{data = v,chatType = chatType})
    end
    return dataList
end

-- 切换频道
function UI_FriendView:RefreshChangeChannel()
    if self.isShowChannelList then
        self.isShowChannelList = false
        SetActive(self.ui.m_goChannelPopup,false)
    end    
    if self.currentChannel == FRIEND_VIEW_TYPE.WorldChannel then
        self.curChatChannel = ChatManager:GetWorldChatChannel()
    elseif self.currentChannel == FRIEND_VIEW_TYPE.OptionalLanguage then
        self.curChatChannel = ChatManager:GetAreaChatChannel()
    end
    self:RefreshWorldChatChannelInfo()
    self:RefreshAreaChatChannelInfo()
    self:RefreshChatList()
end

function UI_FriendView:RefreshChatRed(param)
    if param == CHAT_TYPE_ENUM.WORLD then
        self:RefreshWorldChatRedPoint()
    elseif param == CHAT_TYPE_ENUM.AREA then
        self:RefreshAreaChatRedPoint()
    elseif param == CHAT_TYPE_ENUM.UNION then
        self:RefreshLeagueChatRedPoint()
    end
    self:RefreshChatRedPoint()
end

--endregion -------------------------------------- 聊天频道 -----------------------------------------

function UI_FriendView:CloseOther(clickType)
	if FRIEND_VIEW_TYPE.JoinLeauge ~= clickType then
		UI_CLOSE(UIDefine.UI_JoinUnion)
	end
	if FRIEND_VIEW_TYPE.MyUnion ~= clickType then
		UI_CLOSE(UIDefine.UI_MyUnion)
	end
	if FRIEND_VIEW_TYPE.Chat ~= clickType then
		UI_CLOSE(UIDefine.UI_LeagueChat)
	end
	if FRIEND_VIEW_TYPE.CreatLeauge ~= clickType then
		UI_CLOSE(UIDefine.UI_CreatLeagueView)
	end
end

function UI_FriendView:AddMyself(friendList)
	local hasMyself = false
	local newFriendList = friendList
	for k, v in pairs(newFriendList) do
		if v2n(v.playerId) == v2n(self.myId) then
			hasMyself = true
			break
		end
	end
	
	if not hasMyself then
		local friend = FriendModule.new(self.myId,
			NetUpdatePlayerData:GetPlayerInfo().name,
			NetUpdatePlayerData:GetPlayerInfo().head,
			NetUpdatePlayerData:GetPlayerInfo().levelid,
			0,--v.exp
			LangMgr:GetLangId(),--v.lan
			NetUpdatePlayerData:GetPlayerInfo().rankStage,
			99999999999999,--v.lastApi
			0,--v.gift
			99999999999999,--v.giftSendTime
			0)--v.giftReceiveTime
		friend.myself = true 
		table.insert(newFriendList,friend)
	end
	return newFriendList
end

function UI_FriendView:RefreshFriendListByDelect(delectIndex)
	if self.ui == nil then
		return;
	end
	local friendlist,applyFriendList = FriendManager:GetDataList()
	self.ui.m_txtfriendCount.text = #friendlist .."/"..FriendManager:GetConfigById(102)
	self.ui.m_txtfriendCount_light.text = #friendlist .."/"..FriendManager:GetConfigById(102)
	local datat = self:SortFriendList(friendlist)
	self.friendSlider:SetData(datat,delectIndex)
	self.friendData = datat

	SetActive(self.ui.m_goNotHaveFriend.gameObject,table.count(datat) <= 1)
	SetActive(self.ui.m_goHaveFriend.gameObject,table.count(datat) > 1)
end

function UI_FriendView:RefreshFriendList(isAuto)
	local friendlist,applyFriendList = FriendManager:GetDataList()
	self.ui.m_txtfriendCount.text = #friendlist .."/"..FriendManager:GetConfigById(102)
	self.ui.m_txtfriendCount_light.text = #friendlist .."/"..FriendManager:GetConfigById(102)
	self:SetFriendListData(friendlist)
	
	if isAuto then
        if self.AcceptSlider then
		self.AcceptSlider:SetData(applyFriendList)
        end
		self.acceptData = applyFriendList
	end
end

function UI_FriendView:RefreshAcceptList(isAuto)
	local _,applyFriendList = FriendManager:GetDataList()
	
	if isAuto then
        if self.AcceptSlider then
		self.AcceptSlider:SetData(applyFriendList)
        end
		self.acceptData = applyFriendList
	end
end

--region ----------------------------------------- 赠送/领取体力 -----------------------------------------

--- 刷新体力赠送次数
function UI_FriendView:RefreshSendTimes()
    -- 已赠送的次数
    self.sendTime = math.max(NetFriendData.data.sendGift, NetFriendData.send_used_num)
    NetFriendData.data.sendGift = self.sendTime
    -- 可赠送的次数
    local todaySendMax = FriendManager:GetConfigById(103)
    local sendRemainCount = math.max(math.floor(todaySendMax - self.sendTime), 0)
    self.ui.m_txtTodaySend2.text = sendRemainCount .. "/" .. todaySendMax
end

--- 刷新体力领取次数
function UI_FriendView:RefreshReveiveTimes()
    -- 已领取的次数
    self.receiveTime = math.max(NetFriendData.data.reGift, NetFriendData.reward_used_num)
    NetFriendData.data.reGift = self.receiveTime
    -- 可领取的次数
    local todayReceiveMax = FriendManager:GetConfigById(104)
    local receiveRemainCount = math.max(math.floor(todayReceiveMax - self.receiveTime), 0)
    self.ui.m_txtTodayReceive2.text = receiveRemainCount .. "/" .. todayReceiveMax
end

--- 一键赠送体力
function UI_FriendView:SendEnergyOneClick()
    if FriendManager:IsMaxSendTimes() then
        UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(9042))
        return
    end
    if not NetFriendData:HasFriendSend() then
        UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(9074))
        return
    end
    local function BtnAllSend(objJson)
        self.ui.m_btnAllSend.interactable = true
        --埋点
        local thinkTable = {
            ["Friend"] = "quickGift",
        }
        SdkHelper:ThinkingTrackEvent(ThinkingKey.Home, thinkTable)
        if objJson and objJson["data"] then
            UI_UPDATE(UIDefine.UI_FriendView, 2, objJson, true)
        end
    end
    NetFriendData:GiveFriend(0, BtnAllSend)
    self.ui.m_btnAllSend.interactable = false
    self:SetBtnGrey(1)
end

--- 一键领取体力
function UI_FriendView:GetEnergyOneClick()
    if FriendManager:IsMaxReceiveTimes() then
        UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(9041))
        return
    end
    if not NetFriendData:HasFriendAccept() then
        UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(9073))
        return
    end
    local function BtnAllAccept(objJson)
        self.ui.m_btnAutoAccept.interactable = true
        --埋点
        local thinkTable = {
            ["Friend"] = "quickCollect",
        }
        SdkHelper:ThinkingTrackEvent(ThinkingKey.Home, thinkTable)
        if objJson and objJson["data"] then
            UI_UPDATE(UIDefine.UI_FriendView, 3, objJson, true)
        end
    end
    NetFriendData:ReceiveFriend(0, BtnAllAccept)
    self.ui.m_btnAutoAccept.interactable = false
    self:SetBtnGrey(2)
end

--- 赠送体力事件
--- @param objJson table 后端返回的数据
--- @param auto boolean 是否一键赠送
function UI_FriendView:SendEnergyEvent(objJson, auto, obj)
    local arr = FriendManager:GetConfigById(110)
    arr = string.split(arr,"|")
    local sendNum = 1
    -- 一键赠送体力
    if auto then
        local function friendBack(data)
            if next(arr) ~= nil then
                local itemId = v2n(arr[1])
                if nil ~= objJson["data"] then
                    sendNum = table.count(objJson["data"])
                    for _, value in pairs(objJson["data"]) do
                        NetFriendData:UpdateFriendSend(value, 1)
                    end
                end
                local count = v2n(arr[2])*sendNum
                local pos = UIRectPosFit(self.ui.m_btnAllSend)
                MapController:AddResourceBoomAnim(pos[1], pos[2], itemId, count, true)
                NetUpdatePlayerData:AddResource(PlayerDefine[itemId],count,nil,nil,"UI_FriendView")
            end
            self:SetFriendListData(data)
            self:RefreshSendTimes()
        end
        NetFriendData:RequestFriendList(FRIEND_REQUEST_TYPE.Friend, friendBack)
    -- 单个赠送体力
    else
        if objJson["data"] and objJson["info"] then
            local giftSendTime = objJson["info"].time
            local playerId = objJson["data"][1]
            for k, v in pairs(self.friendData) do
                if v2n(v.playerId) == v2n(playerId) then
                    v.giftSendTime = giftSendTime
                    v.send = 1
                end
            end
            NetFriendData:UpdateFriendSend(playerId, 1)
            if next(arr) ~= nil then
                local itemId = v2n(arr[1])
                local count = v2n(arr[2])*sendNum
                NetFriendData.data.sendGift = NetFriendData.data.sendGift + 1
                local x = 0
                local y = 0
                if obj then
                    local pos = UIRectPosFit(obj)
                    x = pos[1]
                    y = pos[2]
                end
                MapController:AddResourceBoomAnim(x, y, itemId, count, true)
                NetUpdatePlayerData:AddResource(PlayerDefine[itemId],count,nil,nil,"UI_FriendView")
            end
            self.friendSlider.datas = self.friendData
            --self:SetFriendListData(self.friendData)
            self:RefreshSendTimes()
        end
    end
end

--- 领取体力事件
--- @param objJson table 后端返回的数据
--- @param auto boolean 是否一键领取
function UI_FriendView:GetEnergyEvent(objJson, auto, obj)
    local arr = FriendManager:GetConfigById(105)
    arr = string.split(arr,"|")
    -- 一键领取体力
    if auto then
        local function friendBack(data)
            if next(arr) ~= nil then
                local itemId = v2n(arr[1])
                --local friendNum = table.count(objJson["data"]["Friend"]) or 1
                --一个跟多个返回的ID字段名不一样
                local friendNum = 1
                if nil ~= objJson["data"] then
                    friendNum = table.count(objJson["data"])
                    for _, value in pairs(objJson["data"]) do
                        NetFriendData:UpdateFriendReward(value, 0)
                    end
                end
                local count = v2n(arr[2])*friendNum
                local pos = UIRectPosFit(self.ui.m_btnAutoAccept)
                MapController:AddResourceBoomAnim(pos[1], pos[2], itemId, count, true)
                NetUpdatePlayerData:AddResource(PlayerDefine[itemId],count,nil,nil,"UI_FriendView")
            end
            self:SetFriendListData(data)
            self:RefreshReveiveTimes()
            local receive_red = FriendManager:IsHaveReceiveRed(self.friendSlider.datas)
            NetFriendData:SetReceiveGiftRed(receive_red)
        end
        NetFriendData:RequestFriendList(FRIEND_REQUEST_TYPE.Friend, friendBack)
    -- 单个领取体力
    else
        local giftReceiveTime = objJson["info"].time
        local playerId = objJson["data"][1]
        for k, v in pairs(self.friendData) do
            if v2n(v.playerId) == v2n(playerId) then
                v.giftReceiveTime = giftReceiveTime
                v.gift = 0
                v.reward = 0
            end
        end
        NetFriendData:UpdateFriendReward(playerId, 0)
        if next(arr) ~= nil then
            local itemId = v2n(arr[1])
            local friendNum = 1
            local count = v2n(arr[2])*friendNum
            NetFriendData.data.reGift = NetFriendData.data.reGift + 1
            local x = 0
            local y = 0
            if obj then
                local pos = UIRectPosFit(obj)
                x = pos[1]
                y = pos[2]
            end
            MapController:AddResourceBoomAnim(x, y, itemId, count, true)
            NetUpdatePlayerData:AddResource(PlayerDefine[itemId],count,nil,nil,"UI_FriendView")
        end	
        self.friendSlider.datas = self.friendData
        --self:SetFriendListData(self.friendData)
        self:RefreshReveiveTimes()
        local receive_red = FriendManager:IsHaveReceiveRed(self.friendSlider.datas)
        NetFriendData:SetReceiveGiftRed(receive_red)
    end
end

--endregion -------------------------------------- 赠送/领取体力 -----------------------------------------

----- bubble -------
--function UI_FriendView:RewardItemInBubble(param)
--	local itemid = param[1]
--	local obj = param[2]
--	local num = param[3]
--	self.bubble.isAni = true
--	self:BubbleByIndexAnim(itemid, obj, num)
--end
--
--function UI_FriendView:InitBubble()
--	self.bubble = BubbleItem.new("UI_FriendView")
--	self.bubble:Init(self.uiGameObject,{x = -677, y = 426},
--		function()
--			EventMgr:Dispatch(EventID.UI_FRIEND_CLOSE)
--			self:Close()
--		end,
--		false)
--end
----item飞到左上角气泡的动画
--function UI_FriendView:BubbleByIndexAnim(itemid, obj, num)
--	--气泡里面显示当前买的(除金币外都显示)
--	self:playPopAnim(obj, itemid, num)
--end
--
--function UI_FriendView:playPopAnim(obj, item_id, num)
--	self.bubble:FlyItem(item_id, num, obj.transform.position)
--end

function UI_FriendView:TryClose()
	self:isBubbleReward()
end

function UI_FriendView:isBubbleReward()
	local function CloseUI()
		self:CreateScheduleFun(function()
			EventMgr:Dispatch(EventID.UI_FRIEND_CLOSE)
			local leagueId,quitTime = LeagueManager:GetMyLeagueId()
			SdkHelper:UserSet(UserSet.team_time,v2n(leagueId) > 0 and 1 or 0)
			self:Close()
		end, 0.1, 1)
	end

	--if not self.bubble then
	if not BubbleCollectItem then
		CloseUI()
	end

	--if self.bubble.isAni then return end
	if BubbleCollectItem:GetBubbleIsAnim() then
		return
	end

	--if self.bubble:IsHaveItem() then
	--self.bubble:Close()
	--else
	--CloseUI()
	--end
	BubbleCollectItem:CheckClose()
	CloseUI()
end

-- 一键申请的视图表现
function UI_FriendView:SetAllInReqView()
	for k, v in pairs(self.requestData) do
		v.isInReq = true
	end

	local btn
	for k, v in pairs(self.requestItems) do
		btn = v.transform:Find("btn_request"):GetComponent(typeof(UEUI.Button))
		SetUIBtnGrayAndEnable(btn.gameObject, false)
	end
end

--region ----------------------------------------- 好友列表 -----------------------------------------

--- 设置好友列表数据
function UI_FriendView:SetFriendListData(data, clickType, selectIndex)
    if not self.ui then return end

    -- 刷新好友列表
    local friendList = self:SortFriendList(data)
    for i = 1, #self.friendItems, 1 do
        self.friendItems[i]:SetType(self.clickType)
    end
    if selectIndex then
        self.friendSlider:SetData(friendList, selectIndex)
        for i = 1, #self.friendItems, 1 do
            if self.friendItems[i].index == selectIndex then
                self.friendItems[i]:SelectItem()
            end
        end
    else
        self.friendSlider:SetData(friendList)
    end

    self.friendData = friendList

    local friendCount = table.count(friendList)

    if self.clickType == FRIEND_VIEW_TYPE.Blacklist then
        SetActive(self.ui.m_goNotHaveFriend, false)
        SetActive(self.ui.m_goNoBlacklistEmpty, friendCount == 0)
        SetActive(self.ui.m_goHaveFriend, friendCount >= 1)

        --SetActive(self.ui.m_goNoBlacklist, friendCount == 0)
        SetActive(self.ui.m_goPlayerBlacklist, friendCount >= 1)
    elseif self.clickType == FRIEND_VIEW_TYPE.Recently then
        SetActive(self.ui.m_goNotHaveFriend, false)
        SetActive(self.ui.m_goRecentlyEmpty, friendCount == 0)
        SetActive(self.ui.m_goHaveFriend, friendCount >= 1)
    else
        SetActive(self.ui.m_goNotHaveFriend, friendCount == 0)
        SetActive(self.ui.m_goHaveFriend, friendCount >= 1)
        --SetActive(self.ui.m_goFriendTop, friendCount >= 1)
    end

    local friendCountMax = FriendManager:GetConfigById(102)
    --local curTitle = LangMgr:GetLangFormat(53241046, friendCount, friendCountMax)
    --if type == FRIEND_VIEW_TYPE.Friend then
    --    curTitle = LangMgr:GetLangFormat(53241046, friendCount, friendCountMax)
    --elseif type == FRIEND_VIEW_TYPE.Recently then
    --    curTitle = LangMgr:GetLangFormat(53241047, friendCount, friendCountMax)
    --elseif type == FRIEND_VIEW_TYPE.Blacklist then
    --    curTitle = LangMgr:GetLangFormat(53241048, friendCount, friendCountMax)
    --end
    --self.ui.m_txtFriendNum.text = curTitle
    SetActive(self.ui.m_txtFriendNum,self.clickType == FRIEND_VIEW_TYPE.Friend)
    SetActive(self.ui.m_txtFriendRecentlyNum,self.clickType == FRIEND_VIEW_TYPE.Recently)
    SetActive(self.ui.m_txtFriendBlacklistNum,self.clickType == FRIEND_VIEW_TYPE.Blacklist)
    self.ui.m_txtFriendNum.text = LangMgr:GetLangFormat(53241046, friendCount, friendCountMax)
    self.ui.m_txtFriendRecentlyNum.text = LangMgr:GetLangFormat(53241047, friendCount, friendCountMax)
    self.ui.m_txtFriendBlacklistNum.text = LangMgr:GetLangFormat(53241048, friendCount, friendCountMax)
end

function UI_FriendView:SetBlackListData(data)
    local friendList = self:SortFriendList(data)
    local friendCount = table.count(friendList)
    local friendCountMax = FriendManager:GetConfigById(102)
    SetActive(self.ui.m_txtFriendNum,false)
    SetActive(self.ui.m_txtFriendRecentlyNum,false)
    SetActive(self.ui.m_txtFriendBlacklistNum,true)
    SetActive(self.ui.m_goNotHaveFriend, false)
    SetActive(self.ui.m_goNoBlacklistEmpty, friendCount == 0)
    SetActive(self.ui.m_goHaveFriend, friendCount >= 1)
    self.ui.m_txtFriendBlacklistNum.text = LangMgr:GetLangFormat(53241048, friendCount, friendCountMax)
    self.blackListSlider:SetData(friendList)
end

--- 好友列表排序
--- @param list table 好友列表
--- @return table list 排序后的好友列表
function UI_FriendView:SortFriendList(list)
    -- 最近联系按照发消息的时间戳排序
    if self.clickType == FRIEND_VIEW_TYPE.Recently then
        local tempT = DeepCopy(list)
        table.sort(tempT, function(a, b)
            if a.recentAt and b.recentAt then
                return v2n(a.recentAt) > v2n(b.recentAt)
            end
            return false
        end)
        return tempT
    end

    local tempT = DeepCopy(list)
    -- local tempT = self:AddMyself(tempT)
    table.sort(tempT, function(a, b)
            return v2n(a.lastApi) > v2n(b.lastApi)
        end)
    for k, v in pairs(tempT) do
        v.sortIndex = k
    end

    local onLineT = {}
    local offLineT = {}
    for k, v in pairs(tempT) do
        local isOnline = FriendManager:IsOnLine(v)
        if isOnline then
            local t = v
            table.insert(onLineT,t)
        else
            local t = v
            table.insert(offLineT,t)
        end
    end

    -- 在线好友人数
    self.friendOnlineNum = #onLineT

    -- 在线好友列表
    if table.count(onLineT) > 1 then
        table.sort(onLineT, function(a, b)
            if v2n(a.lastApi) == v2n(b.lastApi) then
                return v2n(a.sortIndex) < v2n(b.sortIndex)
            end
            return v2n(a.lastApi) > v2n(b.lastApi)
        end)
    end

    -- 离线好友列表
    if table.count(offLineT) > 1 then
        table.sort(offLineT, function(a, b)
            if v2n(a.lastApi) == v2n(b.lastApi) then
                return v2n(a.sortIndex) < v2n(b.sortIndex)
            end
            return v2n(a.lastApi) > v2n(b.lastApi)
        end)
    end

    -- 合并在线和离线列表
    table.insertto(onLineT, offLineT)

    for k, v in pairs(onLineT) do
        if v2n(v.playerId) == self.myId then
            local t = v
            table.remove(onLineT, k)
            table.insert(onLineT, 1, t)
            break
        end
    end

    -- 总好友人数
    self.friendAllNum = #onLineT

    return onLineT
end

--- 选中第一个好友
function UI_FriendView:SelectFirstItem()
    if self.friendItems then
        for i = 1, #self.friendItems, 1 do
            if self.friendItems[i].index == 1 then
                self.friendItems[i]:SelectItem()
            end
        end
    end
end

--- 选中目标好友
--- @param playerID number|string 玩家 ID
function UI_FriendView:SelectTargetItem(playerID)
    if IsTableNotEmpty(self.friendSlider.datas) then
        local index
        for key, value in pairs(self.friendSlider.datas) do
            if v2n(value.playerId) == v2n(playerID) then
                index = key
            end
        end
        if index then
            self.friendSlider.currentIndex = 0
            self.friendSlider:MoveToIndex(index, 0.5, function ()
                CurPlayerID = nil
                for i = 1, #self.friendItems, 1 do
                    if self.friendItems[i].index == index then
                        self.friendItems[i]:ClickChat()
                    end
                end
            end)
        end
    end
end

--- 赠送和领取按钮置灰
--- @param type number 类型
function UI_FriendView:SetBtnGrey(type)
    if self.friendItems then
        for i = 1, #self.friendItems, 1 do
            self.friendItems[i]:SetBtnGray(type, true)
        end
    end
end

--- 刷新好友列表 item
function UI_FriendView:RefreshFriendItems()
    if self.friendItems then
        for i = 1, #self.friendItems, 1 do
            self.friendItems[i]:Refresh()
        end
    end
end

function FriendItem:OnInit(transform)
    self.transform = transform
    self.bg = GetChild(transform, "bg", UEUI.Image)

    self.playerName = GetChild(transform, "txt_name", UEUI.Text)

    self.customHeadObj = GetChild(self.transform.gameObject,"headNode/CustomHead")
    -- 头像按钮
    self.btnHead = GetChild(transform, "head/img_head", UEUI.Button)
    self.btnHead.onClick:AddListener(function ()
        -- 请求玩家信息，打开玩家档案界面
        NetFriendData:QueryUserInfo(self.data.playerId, function (data)
            UI_SHOW(UIDefine.UI_FriendInfo, data, self.data.sortIndex)
        end)
    end)

    -- item 按钮
    self.btnItem = GetChild(transform, "btnItem", UEUI.Button)
    self.btnItem.onClick:AddListener(function ()
        if self.type == FRIEND_VIEW_TYPE.Recently then
            self:ClickChat()
        else
            self:SelectItem()
        end
    end)

    -- 赠送体力按钮
    self.btnSend = GetChild(transform, "friend/btns/btnSend", UEUI.Button)
    self.imgSendEnergy = GetChild(transform, "friend/btns/btnSend/imgEnergy", UEUI.Image)
    self.imgSendIcon = GetChild(transform, "friend/btns/btnSend/imgSend", UEUI.Image)

    -- 领取体力按钮
    self.btnAccept = GetChild(transform, "friend/btns/btnAccept", UEUI.Button)
    self.imgAcceptEnergy = GetChild(transform, "friend/btns/btnAccept/imgEnergy", UEUI.Image)
    self.imgAcceptIcon = GetChild(transform, "friend/btns/btnAccept/imgAdd", UEUI.Image)

    -- 私聊按钮
    self.btnChat = GetChild(transform, "friend/btns/btnChat", UEUI.Button)

	-- 添加好友
	self.btnAdd = GetChild(transform, "fbFriend/btn_add", UEUI.Button)
	self.goHasInvitedFb = GetChild(transform, "fbFriend/goHasInvitedFb")

	self.goFriendPanel = GetChild(transform, "friend")
	self.goFbFriendPanel = GetChild(transform, "fbFriend")

    self.leagueIcon = GetChild(self.transform, "img_league", UEUI.Image)
    self.leagueName = GetChild(self.transform, "txt_name2", UEUI.Text)

    -- 消息红点
    self.redPoint = GetChild(transform, "redPoint")

    self.lastContent = GetChild(transform, "txt_content", UEUI.Text)
    self.lastContentTime = GetChild(transform, "txt_contentTime", UEUI.Text)
end

--- 选中 item
function FriendItem:SelectItem()
    --NetFriendData:SetFriendMessageRed(self.data.playerId, false)
    --SetActive(self.redPoint, NetFriendData:GetFriendMessageRed(self.data.playerId))
    --UI_UPDATE(UIDefine.UI_FriendView, 6)
    --
    ------ 点击已选中的玩家
    ----if CurPlayerID == v2n(self.data.playerId) then
    ----    return
    ----end
    ---- 选中点击的玩家
    --CurPlayerID = v2n(self.data.playerId)
    --if CurSelectedFriend then
    --    SetActive(CurSelectedFriend.bg, false)
    --end
    --CurSelectedFriend = {
    --    bg = self.bg,
    --    data = self.data,
    --}
    ----SetActive(self.bg, true)
    --
    --UI_UPDATE(UIDefine.UI_FriendView, 5, self.data)
    FriendManager:ShowPlayerById(self.data.playerId);
end

function FriendItem:ClickChat()
    NetFriendData:SetFriendMessageRed(self.data.playerId, false)
    SetActive(self.redPoint, NetFriendData:GetFriendMessageRed(self.data.playerId))
    UI_UPDATE(UIDefine.UI_FriendView, 6)

    ---- 点击已选中的玩家
    --if CurPlayerID == v2n(self.data.playerId) then
    --    return
    --end
    -- 选中点击的玩家
    CurPlayerID = v2n(self.data.playerId)
    if CurSelectedFriend then
        SetActive(CurSelectedFriend.bg, false)
    end
    CurSelectedFriend = {
        bg = self.bg,
        data = self.data,
    }
    --SetActive(self.bg, true)

    UI_UPDATE(UIDefine.UI_FriendView, 5, self.data)
end

--- 赠送和领取按钮置灰
--- @param type number 类型
--- @param flag boolean 状态
function FriendItem:SetBtnGray(type, flag)
    -- 赠送按钮
    if type == 1 then
        SetUIBtnGrayAndEnable(self.btnSend.gameObject, not flag)
        SetUIImageGray(self.imgSendEnergy, flag)
        SetUIImage(self.imgSendIcon, "Sprite/ui_liaotian/liaotian_haoyou_share_hui.png", false)
    -- 领取按钮
    elseif type == 2 then
        SetUIBtnGrayAndEnable(self.btnAccept.gameObject, not flag)
        SetUIImageGray(self.imgAcceptEnergy, flag)
        SetUIImage(self.imgAcceptIcon, "Sprite/ui_liaotian/liaotian_haoyou_jia_hui.png", false)
    end
end

function FriendItem:SetType(type)
	self.type = type
end

function FriendItem:SetBtnEvent()
	if self.type == FRIEND_VIEW_TYPE.Request then
		
	end
end

function FriendItem:OnClick(data,curStageId)
	--if nil == data.id or nil == curStageId then
		--return
	--end
	--if curStageId > data.id then

	--elseif curStageId == data.id then
		--self:EnterStage(curStageId)
	--elseif curStageId < data.id then
		--UIMgr:Show(UIDefine.UI_WidgetTip,LangMgr:GetLang(93))
	--end
end

function FriendItem:UpdateData(data, index)
	if self.type == FRIEND_VIEW_TYPE.UnionRank then
		self:onUpdateDate(data,index)
		return
	end

    self.data = data
    self.index = index

    local isSelect = CurPlayerID == v2n(self.data.playerId)
    --SetActive(self.bg, isSelect)
    if isSelect then
        CurSelectedFriend = {
            bg = self.bg,
            data = self.data,
        }

        NetFriendData:SetFriendMessageRed(self.data.playerId, false)
    end

    -- 玩家名称
    local remark = NetFriendData:GetFriendRemark(self.data.playerId)
    self.playerName.text = remark or data.name

	local name2 = self.transform:Find("txt_name2"):GetComponent(typeof(UEUI.Text))
	name2.text = " "

	--local head = self.transform:Find("head/img_head"):GetComponent(typeof(UEUI.Image))
	--local HeadIcon = FriendManager:GetHeadIcon(data.headId)
	--if HeadIcon then
	--	SetImageSprite(head,HeadIcon,false)
	--end
    SetHeadAndBorderByGo(self.customHeadObj,data.headId,data.border,function()
        -- 请求玩家信息，打开玩家档案界面
        NetFriendData:QueryUserInfo(self.data.playerId, function (data)
            UI_SHOW(UIDefine.UI_FriendInfo, data, self.data.sortIndex)
        end)
    end)
    self.customHeadObj.transform.localPosition = Vector3.zero
    

	local FBIcon = self.transform:Find("head/iconFB")
	if self.data.isFB then
		SetActive(FBIcon,true)
	else
		SetActive(FBIcon,false)
	end

	local rank = self.transform:Find("img_rank"):GetComponent(typeof(UEUI.Image))
	local rankIcon = FriendManager:GetRankStageIcon(data.rankStage)
	-- SetImageSprite(rank,rankIcon,false)
	local txt_level = self.transform:Find("head/txt_level"):GetComponent(typeof(UEUI.Text))
	txt_level.text = data.level
	
	local txt_online = self.transform:Find("txt_online"):GetComponent(typeof(UEUI.Text))
    local isOnLine, str = FriendManager:IsOnLine(data, "8c8c8c")
	txt_online.text = str
	
	local txt_lang = self.transform:Find("txt_lang"):GetComponent(typeof(UEUI.Text))
	txt_lang.text = FriendManager:GetLanguage(data)

    local leagueId = self.data.leagueId
    if not IsNilOrEmpty(leagueId) then
        SetActive(self.leagueName,true)
        SetActive(self.leagueIcon,true)
        --联盟名称
        self.leagueName.text = self.data.leagueName
        --联盟图标
        SetImageSprite(self.leagueIcon, LeagueManager:GetUnionImageById(self.data.leagueIcon), false);
    else
        SetActive(self.leagueName,false)
        SetActive(self.leagueIcon,false)
    end

	self:onUpdateDate(data,index)

    SetActive(self.redPoint, NetFriendData:GetFriendMessageRed(self.data.playerId))
    
    -- 最近联系人最近内容
    SetActive(self.lastContent,self.type == FRIEND_VIEW_TYPE.Recently)
    SetActive(self.lastContentTime,self.type == FRIEND_VIEW_TYPE.Recently)
    if self.type == FRIEND_VIEW_TYPE.Recently then
        local lastContent = NetFriendData:GetFriendLastMessage(self.data.playerId)
        if lastContent then
            self.lastContent.text = lastContent.content
            self.lastContentTime.text = TimeMgr:StampToDateDes(lastContent.timeStamp)
        else
            self.lastContent.text = ""
            self.lastContentTime.text = ""
        end
        
        SetActive(self.leagueName,false)
        SetActive(self.leagueIcon,false)
    end
end

function FriendItem:onUpdateDate(data,index)
	self:ShowSort(index)
	local btn = self.transform:Find("friend/btns/btn_visit"):GetComponent(typeof(UEUI.Button))
    SetActive(btn.gameObject, not CurClickType == FRIEND_VIEW_TYPE.Recently) 
    SetActive(self.btnChat, not CurClickType == FRIEND_VIEW_TYPE.Recently) 
	if data.myself then
		SetActive(btn.gameObject,false)
	else
		--local imgEmoji = self.transform:Find("friend/btn_visit/imgEmoji"):GetComponent(typeof(UEUI.Image))
		--SetImageSprite(imgEmoji,FriendManager:GetFriendlinessEmoticon(data.playerId),false)
		-- SetActive(btn.gameObject,true)
	end

	if btn then
		RemoveUIComponentEventCallback(btn,UEUI.Button)
		AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
				--FriendManager:VisitMapData(data.playerId)
				UI_UPDATE(UIDefine.UI_FriendView, 100,
					function()
						FriendManager:VisitMapData(data.playerId)
					end)

			end)
	end
    
    SetActive(self.btnSend.gameObject, CurClickType == FRIEND_VIEW_TYPE.Friend)

    self:SetBtnGray(1, true)

    if self.btnSend then
        RemoveUIComponentEventCallback(self.btnSend,UEUI.Button)
        if data.send and data.send == 0 then
            AddUIComponentEventCallback(self.btnSend, UEUI.Button, function()
                if FriendManager:IsMaxSendTimes() then
                    UIMgr:Show(UIDefine.UI_WidgetTip,LangMgr:GetLang(9042))
                else
                    local function BtnSend(objJson)
                        local thinkTable = {
                            ["Friend"] = "gift",
                        }
                        SdkHelper:ThinkingTrackEvent(ThinkingKey.Home, thinkTable)
                        UI_UPDATE(UIDefine.UI_FriendView, 2, objJson, false, self.btnSend)
                        RemoveUIComponentEventCallback(self.btnSend,UEUI.Button)
                        AddUIComponentEventCallback(self.btnSend, UEUI.Button, function()
                            UIMgr:Show(UIDefine.UI_WidgetTip,LangMgr:GetLang(9058))
                        end)
                    end
                    NetFriendData:GiveFriend(self.data.playerId, BtnSend)
                    self:SetBtnGray(1, true)
                end
            end)
            self:SetBtnGray(1, false)
        else
            AddUIComponentEventCallback(self.btnSend, UEUI.Button, function()
                UIMgr:Show(UIDefine.UI_WidgetTip,LangMgr:GetLang(9058))
            end)
            self:SetBtnGray(1, true)
        end
    end

    self:SetBtnGray(2, true)

    if self.btnAccept then
        RemoveUIComponentEventCallback(self.btnAccept,UEUI.Button)
        if data.reward and data.reward == 1 then
            AddUIComponentEventCallback(self.btnAccept, UEUI.Button, function()
                if FriendManager:IsMaxReceiveTimes() then
                    UIMgr:Show(UIDefine.UI_WidgetTip,LangMgr:GetLang(9041))
                else
                    local arr = FriendManager:GetConfigById(105)
                    arr = string.split(arr,"|")
                    if next(arr) ~= nil then
                        local itemId = v2n(arr[1])
                        local count = v2n(arr[2])
                        local function BtnGet(objJson)
                            local thinkTable = {
                                ["Friend"] = "collect",
                            }
                            SdkHelper:ThinkingTrackEvent(ThinkingKey.Home, thinkTable)
                            UI_UPDATE(UIDefine.UI_FriendView, 3, objJson, false, self.btnAccept)
                            --MapController:AddResourceBoomAnim(btn_accept.transform.position.x, btn_accept.transform.position.x,itemId,count)
                            --NetUpdatePlayerData:AddResource(PlayerDefine[itemId],count,nil,nil,"UI_FriendView")
                            RemoveUIComponentEventCallback(self.btnAccept,UEUI.Button)
                            AddUIComponentEventCallback(self.btnAccept, UEUI.Button, function()
                                UIMgr:Show(UIDefine.UI_WidgetTip,LangMgr:GetLang(9064))
                            end)
                        end
                        NetFriendData:ReceiveFriend(self.data.playerId, BtnGet)
                        self:SetBtnGray(2, true)
                    end
                end
            end)
            SetActive(self.btnAccept.gameObject, true)
            self:SetBtnGray(2, false)
        else
            local _,todayAccept = FriendManager:IsCanReceive(data)
            if todayAccept then
                AddUIComponentEventCallback(self.btnAccept, UEUI.Button, function(arg1,arg2)
                    UIMgr:Show(UIDefine.UI_WidgetTip,LangMgr:GetLang(9064))
                end)
                self:SetBtnGray(2, true)
            else
                SetActive(self.btnAccept.gameObject,false)
            end
        end
    end

	if self.btnAdd then
		RemoveUIComponentEventCallback(self.btnAdd,UEUI.Button)
		if data.isFB and data.isFB == FACEBOOK_FRIEND_STATE.ONEY_FACEBOOK_FRIEND then
			if data.apply == 1 then
				--已经申请了
				SetActive(self.goHasInvitedFb,true)
				SetActive(self.btnAdd,false)
			else
				SetActive(self.goHasInvitedFb,false)
				SetActive(self.btnAdd,true)

				AddUIComponentEventCallback(self.btnAdd, UEUI.Button, function(arg1,arg2)
					NetFriendData:ApplyFriend(data.playerId,function (dataBack)
						if dataBack then
							data.apply = 1

							SetActive(self.goHasInvitedFb,true)
							SetActive(self.btnAdd,false)

						end
					end)
				end)
			end
		end
	end

    if self.btnChat then
        RemoveUIComponentEventCallback(self.btnChat,UEUI.Button)
        AddUIComponentEventCallback(self.btnChat, UEUI.Button, function(arg1,arg2)
            self:ClickChat()
        end)
    end

    if data.playerId == NetUpdatePlayerData:GetPlayerInfo().id then
        SetActive(self.btnAccept.gameObject,false)
        SetActive(self.btnSend.gameObject,false)
    end

    if CurClickType == FRIEND_VIEW_TYPE.Blacklist then
        SetActive(self.btnAccept.gameObject, false)
        SetActive(self.btnSend.gameObject, false)
    end

	if data.isFB and data.isFB == FACEBOOK_FRIEND_STATE.ONEY_FACEBOOK_FRIEND and CurClickType == FRIEND_VIEW_TYPE.Friend then
		SetActive(self.goFriendPanel,false)
		SetActive(self.goFbFriendPanel,true)
	else
		SetActive(self.goFriendPanel,true)
		SetActive(self.goFbFriendPanel,false)
	end
end

function FriendItem:Refresh()
    if self.data then
        self:UpdateData(self.data, self.index)
    end
end

function FriendItem:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec
end

function FriendItem:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition
end

function FriendItem:onDestroy()
	UEGO.Destroy(self.transform.gameObject)
end

function rankItem:onUpdateDate(data,index)
	if index == nil then
		return
	end
	self:ShowSort(index)
	local txt_level = self.transform:Find("head/txt_level"):GetComponent(typeof(UEUI.Text))
	
	local rankTypeText = LangMgr:GetLang(9008)
	local rank_type = self.transform:Find("txt_rank_type"):GetComponent(typeof(UEUI.Text))
	rank_type.text = rankTypeText
	local league_icon = self.transform:Find("league_icon"):GetComponent(typeof(UEUI.Image))
	SetActive(league_icon,false)
	if self.type == FRIEND_VIEW_TYPE.FuBenRank or self.type == FRIEND_VIEW_TYPE.ZooRank then
		local name2 = self.transform:Find("txt_name2"):GetComponent(typeof(UEUI.Text))
		name2.text = self.data.leagueName
		if self.data.leaguePic then
			SetActive(league_icon,true)
			SetUIImage(league_icon,LeagueManager:GetUnionImageById(self.data.leaguePic),false)
		end
		
		local lang_str_1 = LangMgr:GetLang(9124)
		local lang_str_2 = LangMgr:GetLang(9126)
		local rank_type_str = ""
		local rank_str = ""
		if self.type == FRIEND_VIEW_TYPE.FuBenRank then
			rank_type_str = lang_str_1
			rank_str = data.fSkin
		else
			rank_type_str = lang_str_2
			rank_str = data.zooCount
		end
		rank_type.text = rank_type_str
		txt_level.text = rank_str
	end
end

function unionItem:onUpdateDate(data,index)
	local union_name = self.transform:Find("union_name"):GetComponent(typeof(UEUI.Text))
	union_name.text = data.name

	local union_img = self.transform:Find("union_img"):GetComponent(typeof(UEUI.Image))
	SetUIImage(union_img,LeagueManager:GetUnionImageById(data.pic),false)
	self:ShowSort(index)
	
	local union_pepple = self.transform:Find("Image/union_pepple"):GetComponent(typeof(UEUI.Text))
	union_pepple.text = v2n(data.members) .."/"..v2n(data.membersMax)
	
	local union_level = self.transform:Find("Image/union_level"):GetComponent(typeof(UEUI.Text))
	union_level.text = v2n(data.level)
	
	local union_lanague = self.transform:Find("union_lanague"):GetComponent(typeof(UEUI.Text))
	union_lanague.text = FriendManager:GetLanguage(data)
	
	local btn = union_img.transform:GetComponent(typeof(UEUI.Button))
	if btn then
		RemoveUIComponentEventCallback(btn,UEUI.Button)
		AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
			LeagueManager:ShowLeagueAbout(data.id)
		end)	
	end
end

function FriendItem:ShowSort(index)
	local sortIndex = index
	local txt_rank = self.transform:Find("rank/txt_rank"):GetComponent(typeof(UEUI.Text))
	txt_rank.text = sortIndex

	local img_rank = self.transform:Find("rank/img_rank"):GetComponent(typeof(UEUI.Image))
	if sortIndex <= 3 then
		local sprite = "Assets/ResPackage/Sprite/ui_friend/haoyou_win1_paihang"..sortIndex..".png"
		SetActive(img_rank.gameObject,true)
		SetImageSprite(img_rank,sprite,false)
	else
		SetActive(img_rank.gameObject,false)
	end
end

--endregion -------------------------------------- 好友列表 -----------------------------------------

--region ----------------------------------------- 频道列表 -----------------------------------------

function ChannelItem:OnInit(transform)
    self.channelType = 0
    self.transform = transform
    self.txtChannelName = GetChild(transform, "bg/txt_name", UEUI.Text)
    self.imgChannelState = GetChild(transform, "bg/img_state", UEUI.Image)
    self.bg = GetChild(transform, "bg", UEUI.Button)
    self.itemBg = GetChild(transform, "bg", UEUI.Image)
end

function ChannelItem:UpdateData(data, index)
    self.data = data.data
    self.chatType = data.chatType
    self.index = index
    
    local curNum = self.data.num
    local imgPath = "Sprite/ui_liaotian/liaotian_point_green.png"
    if curNum > (CHANNEL_PLAYER_MAX * 0.8) then
        imgPath = "Sprite/ui_liaotian/liaotian_point_red.png"
    end
    SetImageSprite(self.imgChannelState,imgPath,false)

    local imgPath2 = "Sprite/ui_liaotian/liaotian_qiehuan_dikuang3.png"
    local curChannelId 
    if self.chatType == CHAT_TYPE_ENUM.WORLD then
        curChannelId = ChatManager:GetWorldChatChannel()
    elseif self.chatType == CHAT_TYPE_ENUM.AREA then
        curChannelId = ChatManager:GetAreaChatChannel()
    end
    if curChannelId == index then
        imgPath2 = "Sprite/ui_liaotian/liaotian_qiehuan_dikuang4.png"
    end
    SetImageSprite(self.itemBg,imgPath2,false)
    self.txtChannelName.text = LangMgr:GetLangFormat(53241032,index)

    local function requestCallBack()
        UI_UPDATE(UIDefine.UI_FriendView, 35)
    end
    
    RemoveUIComponentEventCallback(self.bg,UEUI.Button)
    AddUIComponentEventCallback(self.bg,UEUI.Button,function(arg1,arg2)
        if self.chatType ~= 0 then
            local param = {}
            param.type = self.chatType
            param.channelId = index
            local langID = LangMgr:GetLangId()
            param.lan = langID
            ChatManager:RequestChatChannel(param,requestCallBack)
        end

    end)
end

function ChannelItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function ChannelItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function ChannelItem:onDestroy()

end

--endregion -------------------------------------- 频道列表 -----------------------------------------

--region ----------------------------------------- 聊天列表 -----------------------------------------

function ChatItemOther:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)
    self.chatBg = GetChild(self.transform, "bubble", UEUI.Image)
    self.textTMP = GetChild(self.transform, "bubble/Text", CS.TMPro.TMP_Text)
    self.translateTextTMP = GetChild(self.transform,"bubble/Text/translate_txt",CS.TMPro.TMP_Text)
    self.line = GetChild(self.transform,"bubble/Text/translate_txt/line",UE.RectTransform)

    self.hor_layout = GetChild(self.transform, "hor_layout")
    self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.headbg = GetChild(self.transform, "headbg", UEUI.Image)
    self.playerHead = GetChild(self.transform, "headbg/head", UEUI.Image)
	--self.playerHead = GetChild(self.transform, "CustomHead/imgHeadBorder/imgHead", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)
    self.btnTranslate = GetChild(self.transform,"bubble/btn_Translate",UEUI.Button)
    self.imgTranslate = GetChild(self.transform,"bubble/btn_Translate",UEUI.Image)

	self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)

    self.btnPlayerHead = GetChild(self.transform, "headbg/head", UEUI.Button)
end

function ChatItemOther:OnUpdateData(dataIndex,data)
    local chatData = data.chat
    local chatType = data.chatType or FRIEND_VIEW_TYPE.Chat
    self.textTMP.enableWordWrapping = true
    self.translateTextTMP.enableWordWrapping = true
    local contentStr = data.content
    if chatType == FRIEND_VIEW_TYPE.SystemMessages then
        contentStr = string.gsub(data.content, "<[^>]+>", "")
    end
    local _chatType
    local chatChannel
    if chatType == FRIEND_VIEW_TYPE.WorldChannel then
        _chatType = CHAT_TYPE_ENUM.WORLD
        chatChannel = ChatManager:GetWorldChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.OptionalLanguage then
        _chatType = CHAT_TYPE_ENUM.AREA
        chatChannel = ChatManager:GetAreaChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.LeagueChat then
        _chatType = CHAT_TYPE_ENUM.UNION
        chatChannel = CHAT_TYPE_ENUM.UNION
    elseif chatType == FRIEND_VIEW_TYPE.SystemMessages then
        _chatType = CHAT_TYPE_ENUM.SYSTEM
    end

    if CurClickType == FRIEND_VIEW_TYPE.Friend or CurClickType == FRIEND_VIEW_TYPE.Recently then
        _chatType = CHAT_TYPE_ENUM.PRIVATE
    end

    if chatChannel or chatType == FRIEND_VIEW_TYPE.LeagueChat then
        local isNew = ChatManager:GetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
        if isNew then
            ChatManager:SetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
            EventMgr:Dispatch(EventID.CHECK_NEW_MSG,data,true)
            EventMgr:Dispatch(EventID.REFRESH_CHAT_RED,_chatType,true)
        end
    end

    self.textTMP.text = contentStr
    if data.translateContent then
        SetActive(self.translateTextTMP,true)
        self.translateTextTMP.text = data.translateContent
    else
        SetActive(self.translateTextTMP,false)
    end
    
    local layoutElement = GetComponent(self.textTMP, CS.UnityEngine.UI.LayoutElement)
    local layoutElement2 = GetComponent(self.translateTextTMP, CS.UnityEngine.UI.LayoutElement)

    if self.textTMP.preferredWidth < MAX_WIDTH then
        self.textTMP.enableWordWrapping = false
        self.translateTextTMP.enableWordWrapping = false
        if self.translateTextTMP.preferredWidth > self.textTMP.preferredWidth and self.translateTextTMP.preferredWidth < MAX_WIDTH then
            self.translateTextTMP.enableWordWrapping = false
            layoutElement2.preferredWidth = self.textTMP.preferredWidth
        elseif self.translateTextTMP.preferredWidth > MAX_WIDTH then
            self.translateTextTMP.enableWordWrapping = true
            layoutElement2.preferredWidth = MAX_WIDTH
        end
        --layoutElement.preferredWidth = self.textTMP.preferredWidth
        --layoutElement2.preferredWidth = self.translateTextTMP.preferredWidth
        SetUIForceRebuildLayout(self.textTMP.gameObject)
        SetUIForceRebuildLayout(self.translateTextTMP.gameObject)
    else
        self.textTMP.enableWordWrapping = true
        self.translateTextTMP.enableWordWrapping = true
        layoutElement.preferredWidth = MAX_WIDTH
        layoutElement2.preferredWidth = MAX_WIDTH
        SetUIForceRebuildLayout(self.textTMP.gameObject)
        SetUIForceRebuildLayout(self.translateTextTMP.gameObject)
    end
    self.bg.sizeDelta = Vector2.New(data.lineWidth + 50, data.lineHight - 30) -- 高度里包含了每个单元之间的间距
    self.line.sizeDelta = Vector2.New(data.lineWidth - 10, 4)
    SetActive(self.line ,true)
    
    local isShow = chatType ~= FRIEND_VIEW_TYPE.SystemMessages
    SetActive(self.rankImg,isShow)
    SetActive(self.unionImg,isShow)
    SetActive(self.unionName,isShow)
    local isNeedTranslate = true
    SetActive(self.btnTranslate,true)
    if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end
    if chatType == FRIEND_VIEW_TYPE.SystemMessages then
		local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
		SetHeadAndBorderByGo(customHeadObj)
        SetActive(self.btnTranslate,false)
        --SetImageSprite(self.playerHead,"Sprite/new_hero/headFrame_1.png",false)
        self.playerName.text = LangMgr:GetLang(53241028)
        SetActive(self.chatTime,true)
        self.chatTime.text = TimeMgr:StampToDateDes(data.time)
    end
    if data.player then
        SetActive(self.hor_layout,true)
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name
        local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerData.icon)
        if headConfig then
            SetImageSprite(self.playerHead,headConfig["icon"],false)
        end
        --local playerLan = playerData.lan
        --local langID = LangMgr:GetLangId()
        --if v2n(playerLan) ~= v2n(langID) then
        --    isNeedTranslate = true
        --end
        
        local imgPath = "Sprite/ui_liaotian/liaotian_gonggao_fanyi.png"
        if data.isTranslated then
            imgPath = "Sprite/ui_liaotian/liaotian_gonggao_fanyi2.png"
        end
        SetUIImage(self.imgTranslate,imgPath,false)
        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        self.btnHeadBorder = GetChild(customHeadObj, "imgHeadBorder", UEUI.Button)
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border,function() end)

		SetChatBorder(self.imgChatBorder,playerData.chatBorder,false)
		self.textTMP.color = GetChatBorderTextColor(playerData.chatBorder,false)
    else
        SetActive(self.hor_layout,false)
        SetChatBorder(self.imgChatBorder,110,false)
        self.textTMP.color = GetChatBorderTextColor(110,false)
    end
    if data.league then
        local leagueData = data.league
        if not IsNilOrEmpty(leagueData.icon) then
            SetImageSprite(self.unionImg, LeagueManager:GetUnionImageById(leagueData.icon), false)
            SetActive(self.unionImg, true)
        else
            SetActive(self.unionImg, false)
        end
        self.unionName.text = leagueData.name
    end

    local function TranslateCallBack(translateContent)
        EventMgr:Dispatch(EventID.TRANSLATE_CALL_BACK, {content = translateContent, dataIndex = data.chat.id,--dataIndex,
            chatType = _chatType, chatChannel = chatChannel, playerId = data.player and data.player.id or 0})
    end

    RemoveUIComponentEventCallback(self.btnTranslate,UEUI.Button)
    AddUIComponentEventCallback(self.btnTranslate,UEUI.Button,function(arg1,arg2)
        if isNeedTranslate then
            if data.isTranslated then
                return
            end 
            AnnounceManager:SendTranslateInfo(contentStr,true,TranslateCallBack)
        end
    end)

    if self.btnHeadBorder then
        RemoveUIComponentEventCallback(self.btnHeadBorder, UEUI.Button)
        AddUIComponentEventCallback(self.btnHeadBorder, UEUI.Button, function()
            if data.player and not IsNilOrEmpty(data.player.id) then
                FriendManager:ShowPlayerById(data.player.id)
            end
        end)
    end
end

function ChatItemOther:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

-------------------- ChatItemMe --------------------------------
function ChatItemMe:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)
    self.textTMP = GetChild(self.transform, "bubble/Text", CS.TMPro.TMP_Text)

    self.hor_layout = GetChild(self.transform, "hor_layout")
    self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.playerHead = GetChild(self.transform, "headbg/head", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

	self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)
end

function ChatItemMe:OnUpdateData(dataIndex,data)
    self.textTMP.enableWordWrapping = true
    self.textTMP.text = data.content
    local layoutElement = GetComponent(self.textTMP, CS.UnityEngine.UI.LayoutElement)
    if self.textTMP.preferredWidth < MAX_WIDTH then
        self.textTMP.enableWordWrapping = false
        layoutElement.preferredWidth = self.textTMP.preferredWidth
		SetUIForceRebuildLayout(self.textTMP.gameObject)
    else
        self.textTMP.enableWordWrapping = true
        layoutElement.preferredWidth = MAX_WIDTH
		SetUIForceRebuildLayout(self.textTMP.gameObject)
    end
    self.bg.sizeDelta = Vector2.New(data.lineWidth + 50, data.lineHight)

    if data.player then
        SetActive(self.hor_layout,true)
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name
        local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerData.icon)
        if headConfig then
            SetImageSprite(self.playerHead,headConfig["icon"],false)
        end
        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border)

		SetChatBorder(self.imgChatBorder,playerData.chatBorder,true)
		self.textTMP.color = GetChatBorderTextColor(playerData.chatBorder,true)
    else
        SetActive(self.hor_layout,false)
    end
    local leagueData = data.league
    if leagueData and leagueData.name  ~= "" then
        SetActive(self.unionImg,true)
        SetActive(self.unionName,true)
        SetImageSprite(self.unionImg,LeagueManager:GetUnionImageById(leagueData.icon),false)
        self.unionName.text = leagueData.name
    else
        SetActive(self.unionImg,false)
        SetActive(self.unionName,false)
    end
    if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end
end

function ChatItemMe:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

-------------------- ChateInviteOther --------------------------------
function ChateInviteOther:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)
	
    self.hor_layout = GetChild(self.transform, "hor_layout")
	self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.playerHead = GetChild(self.transform, "headbg/head", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

	self.imgLeagueIcon = GetChild(self.transform,"bubble/imgLeagueIcon",UEUI.Image)
	self.txtTitle = GetChild(self.transform,"bubble/txtTitle",UEUI.Text)
	self.txtLeagueText = GetChild(self.transform,"bubble/txtLeagueText",UEUI.Text)
	self.txtLeagueLang = GetChild(self.transform,"bubble/txtLeagueLang",UEUI.Text)
	self.txtLeagueName = GetChild(self.transform,"bubble/txtLeagueName",UEUI.Text)
	self.txtMember = GetChild(self.transform,"bubble/txtMember",UEUI.Text)
	self.imgIcon2 = GetChild(self.transform,"bubble/icon2",UEUI.Image)

	self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)

	self.btnInvite = GetChild(self.transform,"bubble/btnInvite",UEUI.Button)
	
end

function ChateInviteOther:OnUpdateData(dataIndex,data)
    local chatData = data.chat
    local chatType = data.chatType or FRIEND_VIEW_TYPE.Chat
    local contentStr = data.content
    if chatType == FRIEND_VIEW_TYPE.SystemMessages then
        contentStr = string.gsub(data.content, "<[^>]+>", "")
    end
    local _chatType
    local chatChannel 
    if chatType == FRIEND_VIEW_TYPE.WorldChannel then
        _chatType = CHAT_TYPE_ENUM.WORLD
        chatChannel = ChatManager:GetWorldChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.OptionalLanguage then
        _chatType = CHAT_TYPE_ENUM.AREA
        chatChannel = ChatManager:GetAreaChatChannel()
    end
    if chatChannel then
        local isNew = ChatManager:GetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
        if isNew then
            ChatManager:SetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
            EventMgr:Dispatch(EventID.CHECK_NEW_MSG,data,true)
            EventMgr:Dispatch(EventID.REFRESH_CHAT_RED,_chatType,true)
        end
    end
    
    local isShow = chatType ~= FRIEND_VIEW_TYPE.SystemMessages
    SetActive(self.rankImg,isShow)
    SetActive(self.unionImg,isShow)
    SetActive(self.unionName,isShow)
    local isNeedTranslate = true
    self.txtTitle.text = LangMgr:GetLang(9344)
	local leagueId = 0
    if data.player then
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name
        local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerData.icon)
        if headConfig then
            SetImageSprite(self.playerHead,headConfig["icon"],false)
        end
        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border,function()
            if playerData and not IsNilOrEmpty(playerData.id) then
                FriendManager:ShowPlayerById(playerData.id)
            end
        end)
		
		SetChatBorder(self.imgChatBorder,playerData.chatBorder,false)
		local color = GetChatBorderTextColor(playerData.chatBorder,false)
		self.txtLeagueText.color = color
    else
        SetActive(self.hor_layout,false)
    end
    if data.league then
        local leagueData = data.league
        SetImageSprite(self.unionImg,LeagueManager:GetUnionImageById(leagueData.icon),false)
        self.unionName.text = leagueData.name
		
    end

	if data.content then
		local invitedata = Json.decode(data.content)
		
		if invitedata and invitedata.league then
			local league = invitedata.league

			leagueId = league.id

			SetImageSprite(self.imgLeagueIcon,LeagueManager:GetUnionImageById(league.icon),false)
			self.txtLeagueName.text = league.name
			self.txtLeagueLang.text = FriendManager:GetLanguage(league);
			self.txtLeagueText.text = LangMgr:GetLang(53241130) --league.declaration or ""
			if (v2n(league.num) or 0) >= (v2n(league.max_num) or 0) then
				self.txtMember.text = league.num.."/"..league.max_num
			else
				self.txtMember.text = string.format("<color=#%s>%s</color>", "FF8B00", league.num).."/"..league.max_num
			end
		end

	end

	if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end
	
	RemoveUIComponentEventCallback(self.btnInvite,UEUI.Button)
    AddUIComponentEventCallback(self.btnInvite,UEUI.Button,function(arg1,arg2)

		if not LeagueManager:IsOpenUnion() then
			--公会未开放
			local openLevel = LeagueManager:GetConfigById(2)
			openLevel = v2n(openLevel)
			UIMgr:Show(UIDefine.UI_WidgetTip, string.format(LangMgr:GetLang(9205),openLevel) )
			return
		end
		
		if LeagueManager:IsJoinUnion() then
			--已有公会
			UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(53241121))
			return
		end
        LeagueManager:ShowLeagueAbout(leagueId)
    end)
end

function ChateInviteOther:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

-------------------- ChateInviteMe --------------------------------
function ChateInviteMe:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)
	
    self.hor_layout = GetChild(self.transform, "hor_layout")
	self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.playerHead = GetChild(self.transform, "headbg/head", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

	self.imgLeagueIcon = GetChild(self.transform,"bubble/imgLeagueIcon",UEUI.Image)
	self.txtTitle = GetChild(self.transform,"bubble/txtTitle",UEUI.Text)
	self.txtLeagueText = GetChild(self.transform,"bubble/txtLeagueText",UEUI.Text)
	self.txtLeagueLang = GetChild(self.transform,"bubble/txtLeagueLang",UEUI.Text)
	self.txtLeagueName = GetChild(self.transform,"bubble/txtLeagueName",UEUI.Text)
	self.txtMember = GetChild(self.transform,"bubble/txtMember",UEUI.Text)
	self.imgIcon2 = GetChild(self.transform,"bubble/icon2",UEUI.Image)

	self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)
end

function ChateInviteMe:OnUpdateData(dataIndex,data)
    local chatData = data.chat
    local chatType = data.chatType or FRIEND_VIEW_TYPE.Chat
    local contentStr = data.content
    if chatType == FRIEND_VIEW_TYPE.SystemMessages then
        contentStr = string.gsub(data.content, "<[^>]+>", "")
    end
    local _chatType
    local chatChannel 
    if chatData then
        chatChannel = chatData.channel
    end
    if chatType == FRIEND_VIEW_TYPE.WorldChannel then
        _chatType = CHAT_TYPE_ENUM.WORLD
    elseif chatType == FRIEND_VIEW_TYPE.OptionalLanguage then
        _chatType = CHAT_TYPE_ENUM.AREA
    end
    if chatChannel then
        local isNew = ChatManager:GetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
        if isNew then
            ChatManager:SetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
            EventMgr:Dispatch(EventID.CHECK_NEW_MSG,data,true)
            UI_UPDATE(UIDefine.UI_FriendView, 35)
        end
    end
    
    local isShow = chatType ~= FRIEND_VIEW_TYPE.SystemMessages
    SetActive(self.rankImg,isShow)
    SetActive(self.unionImg,isShow)
    SetActive(self.unionName,isShow)
    local isNeedTranslate = true
    self.txtTitle.text = LangMgr:GetLang(9344)
    if data.player then
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name
        local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerData.icon)
        if headConfig then
            SetImageSprite(self.playerHead,headConfig["icon"],false)
        end
        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border)

		SetChatBorder(self.imgChatBorder,playerData.chatBorder,true)
		local color = GetChatBorderTextColor(playerData.chatBorder,false)
		self.txtLeagueText.color = color
    else
        SetActive(self.hor_layout,false)
    end
    if data.league then
        local leagueData = data.league
        SetImageSprite(self.unionImg,LeagueManager:GetUnionImageById(leagueData.icon),false)
        self.unionName.text = leagueData.name
		
    end

	if data.content then
		local invitedata = Json.decode(data.content)
		
		if invitedata and invitedata.league then
			local league = invitedata.league
			SetImageSprite(self.imgLeagueIcon,LeagueManager:GetUnionImageById(league.icon),false)
			self.txtLeagueName.text = league.name
			self.txtLeagueLang.text = FriendManager:GetLanguage(league);
			self.txtLeagueText.text = LangMgr:GetLang(53241130) --league.declaration or ""
			if (v2n(league.num) or 0) >= (v2n(league.max_num) or 0) then
				self.txtMember.text = league.num.."/"..league.max_num
			else
				self.txtMember.text = string.format("<color=#%s>%s</color>", "FF8B00", league.num).."/"..league.max_num
			end
		end

	end

	if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end
    
end

function ChateInviteMe:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

--region -------------------- AnnounceOther 公告 --------------------------------
local Attitude = {
    None = 0;--未表态
    Like = 1;--支持
    Dislike = 2;--不支持
    AllSelect = 3;--支持&不支持
}

function AnnounceOther:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)
    self.chatBg = GetChild(self.transform, "bubble", UEUI.Image)

    self.hor_layout = GetChild(self.transform, "hor_layout")
    self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.headbg = GetChild(self.transform, "headbg", UEUI.Image)
    self.playerHead = GetChild(self.transform, "headbg/head", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

    self.txttitleName = GetChild(self.transform,"bubble/imgTitleBg/txttitleName",UEUI.Text)
    self.txtSender = GetChild(self.transform,"bubble/txtSender",UEUI.Text)
    self.imgTitleBg = GetChild(self.transform,"bubble/imgTitleBg",UEUI.Image)
    
    self.textTMP = GetChild(self.transform,"bubble/Text",CS.TMPro.TMP_Text)
    self.translateTextTMP = GetChild(self.transform,"bubble/Text/translate_txt",CS.TMPro.TMP_Text)
    self.line = GetChild(self.transform,"bubble/Text/translate_txt/line",UE.RectTransform)

    self.btn_Translate = GetChild(self.transform,"bubble/btn_Translate",UEUI.Button)
    self.imgTranslate = GetChild(self.transform,"bubble/btn_Translate",UEUI.Image)
    self.btn_Delete = GetChild(self.transform,"bubble/btn_Delete",UEUI.Button)

	self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)
    self.btn_Discuss = GetChild(self.transform,"bubble/btn_Discuss",UEUI.Button)

    self.downNode = GetChild(self.transform,"bubble/down")
    self.m_btnLike = GetChild(self.downNode,"attitude/btnLike",UEUI.Button)
    self.m_btnLikeMask = GetChild(self.downNode,"attitude/btnLikeMask",UEUI.Button)
    self.m_txtLikeCount = GetChild(self.downNode,"attitude/txtLikeCount",UEUI.Text)
    self.m_btnDislike = GetChild(self.downNode,"attitude/btnDislike",UEUI.Button)
    self.m_btnDislikeMask = GetChild(self.downNode,"attitude/btnDislikeMask",UEUI.Button)
    self.m_txtDislikeCount = GetChild(self.downNode,"attitude/txtDislikeCount",UEUI.Text)
    self.discussCount = GetChild(self.downNode,"discussCount",CS.TMPro.TMP_Text)

    self.myAttitude = Attitude.None
end

function AnnounceOther:OnUpdateData(dataIndex,data)
    SetActive(self.rankImg,false)
    self.txttitleName.text = LangMgr:GetLang(53241040)
    self.playerName.text = data.sender_name

    self.textTMP.enableWordWrapping = true
    self.translateTextTMP.enableWordWrapping = true
    
    local contentStr = data.content
    self.textTMP.text = contentStr
    if data.translateContent then
        SetActive(self.translateTextTMP,true)
        self.translateTextTMP.text = data.translateContent
    else
        SetActive(self.translateTextTMP,false)
    end
    
    local layoutElement = GetComponent(self.textTMP, CS.UnityEngine.UI.LayoutElement)
    local layoutElement2 = GetComponent(self.translateTextTMP, CS.UnityEngine.UI.LayoutElement)
    SetUIForceRebuildLayout(self.textTMP.gameObject)
    SetUIForceRebuildLayout(self.translateTextTMP.gameObject)
    if self.textTMP.preferredWidth < MAX_WIDTH then
        self.textTMP.enableWordWrapping = false
        layoutElement.preferredWidth = self.textTMP.preferredWidth

        self.translateTextTMP.enableWordWrapping = false
        if self.translateTextTMP.preferredWidth > MAX_WIDTH then
            self.translateTextTMP.enableWordWrapping = true
            layoutElement2.preferredWidth = MAX_WIDTH
        else
            layoutElement2.preferredWidth = self.translateTextTMP.preferredWidth
        end
    else
        self.textTMP.enableWordWrapping = true
        self.translateTextTMP.enableWordWrapping = true
        layoutElement.preferredWidth = MAX_WIDTH
        layoutElement2.preferredWidth = MAX_WIDTH
    end
    local bgWidth = 810
  
    self.bg.sizeDelta = Vector2.New(bgWidth, data.lineHight + 30)
    self.line.sizeDelta = Vector2.New(bgWidth - 100, 4)
    
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, data.sender_icon)
    --if headConfig then
    --    SetImageSprite(self.playerHead,headConfig["icon"],false)
    --end
    if data.isNew then
        ChatManager:SetAnnounceMentIsRead(dataIndex)
        UI_UPDATE(UIDefine.UI_FriendView, 36)
        UI_UPDATE(UIMgr:GetNowMainUI(),301)
    end
	SetChatBorder(self.imgChatBorder,data.sender_chatBorder,false)
	self.textTMP.color = GetChatBorderTextColor(data.sender_chatBorder,false)

    if data.sender_icon then
        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,data.sender_icon,data.sender_border)
    end

    local lederName ,leagueName = LeagueManager:GetMyLeagueLeaderDetail()
    self.txtSender.text = TimeMgr:StampToDateDes(data.create_at) .. " " .. string.format("[%s]%s",leagueName,data.sender_name)
    local isNeedTranslate = true
    --if data.sender_lan then
    --    local senderLan = data.lan
    --    local langID = LangMgr:GetLangId()
    --    
    --    if v2n(senderLan) ~= v2n(langID) then
    --        isNeedTranslate = true
    --    end
    --end
    SetActive(self.btn_Translate,true)
    --local imgPath = "Sprite/ui_liaotian/liaotian_gonggao_fanyi1.png"
    --if data.isTranslated then
    --    imgPath = "Sprite/ui_liaotian/liaotian_gonggao_fanyi2.png"
    --end
    --SetUIImage(self.imgTranslate,imgPath,false)

    SetUIImage(self.imgTranslate,data.isTranslated and "Sprite/ui_liaotian/liaotian_gonggao_fanyi2.png" or "Sprite/ui_liaotian/liaotian_gonggao_fanyi.png")
    
    local function TranslateCallBack(translateContent)
        EventMgr:Dispatch(EventID.TRANSLATE_CALL_BACK, {content = translateContent, dataIndex = dataIndex,
                                                        isAnnounce = true})
    end
    RemoveUIComponentEventCallback(self.btn_Translate,UEUI.Button)
    AddUIComponentEventCallback(self.btn_Translate,UEUI.Button,function(arg1,arg2)
        if isNeedTranslate then
            if data.isTranslated then
                return
            end
            AnnounceManager:SendTranslateInfo(contentStr,true,TranslateCallBack)
        end
    end)

    RemoveUIComponentEventCallback(self.btn_Delete,UEUI.Button)
    AddUIComponentEventCallback(self.btn_Delete,UEUI.Button,function(arg1,arg2)
        local function onOk()
            local params = {}
            params.id = data.id
            ChatManager:RequestDeleteLeagueAnnouncement(params)
        end
        UI_SHOW(UIDefine.UI_TipsTop, LangMgr:GetLang(53241122), onOk)
    end)

    RemoveUIComponentEventCallback(self.btn_Discuss,UEUI.Button)
    AddUIComponentEventCallback(self.btn_Discuss,UEUI.Button,function(arg1,arg2)
        ChatManager:RequestDiscussDetail(tonumber(data.id),0,function(info)
            UI_SHOW(UIDefine.UI_LeagueDiscuss,data,info)
        end)
    end)
    
    self.discussCount.text = LangMgr:GetLangFormat(53241044,math.floor(data.children))
    --------------------------评论相关--------------------------
    self:ShowCount(data.upvote,data.downvote)
    self:CheckAttitudeBtnActive(data.self_vote)
    
    local attitudeList = {
        ["btnLike"] = self.m_btnLike;
        ["btnLikeMask"] = self.m_btnLikeMask;
        ["btnDislike"] = self.m_btnDislike;
        ["btnDislikeMask"] = self.m_btnDislikeMask;
    }

    for k,v in pairs(attitudeList) do
        RemoveUIComponentEventCallback(v,UEUI.Button)
        AddUIComponentEventCallback(v,UEUI.Button,function(arg1,arg2)
            self:BindAttitudeFunc(k,data.id)
        end)
    end
end

--绑定表态事件
function AnnounceOther:BindAttitudeFunc(name,managerId)
    local showAttitude = false
    local attitude = Attitude.None
    if name == "btnLike" then
        attitude = Attitude.Like
        showAttitude = true
    elseif name == "btnLikeMask" then
        --attitude = Attitude.None
        --showAttitude = true
    elseif name == "btnDislike" then
        attitude = Attitude.Dislike
        showAttitude = true
    elseif name == "btnDislikeMask" then
        --attitude = Attitude.None
        --showAttitude = true
    end

    if showAttitude then
        local isCanSend,time = ChatManager:IsCanSendAttitude()
        if not isCanSend then
            --"发送冷却中"
            UI_SHOW(UIDefine.UI_WidgetTip,LangMgr:GetLangFormat(53241135, time))
            return
        end
        
        if managerId and managerId ~= -1 then
            --向后端发送表态数据
            if self.myAttitude == Attitude.Like and attitude == Attitude.Dislike then
                attitude = Attitude.AllSelect
            elseif self.myAttitude == Attitude.Dislike and attitude == Attitude.Like then
                attitude = Attitude.AllSelect
            end
            attitude = v2n(attitude)
            ChatManager:VoteAttitude(managerId,attitude,function(info)
                self:CheckAttitudeBtnActive(attitude)
                self:ShowCount(info.upvote,info.downvote)
                ChatManager:SetAttitudeCD()
            end)
        end
    end
end

--判断表态相关按钮状态显隐
function AnnounceOther:CheckAttitudeBtnActive(attitude)
    attitude = v2n(attitude)
    SetActive(self.m_btnLike,attitude == Attitude.None or attitude == Attitude.Dislike)
    SetActive(self.m_btnLikeMask,attitude == Attitude.Like or attitude == Attitude.AllSelect)
    SetActive(self.m_btnDislike,attitude == Attitude.None or attitude == Attitude.Like)
    SetActive(self.m_btnDislikeMask,attitude == Attitude.Dislike or attitude == Attitude.AllSelect)
    self.myAttitude = attitude
end

--展示喜欢和不喜欢的数量
function AnnounceOther:ShowCount(likeCount,dislikeCount)
    self.m_txtLikeCount.text = likeCount
    self.m_txtDislikeCount.text = dislikeCount
end

function AnnounceOther:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end
--endregion ------------------------------------- AnnounceOther 公告 --------------------------------

--endregion -------------------------------------- 聊天列表 -----------------------------------------

--region ----------------------------------------- 红点 -----------------------------------------

function UI_FriendView:RefreshRedPoint()
    if not self.ui then return end
    -- 他人请求添加好友的数量
    local otherRequestCount = table.count(NetFriendData.otherRequestList)
    SetActive(self.ui.m_goRedPoint, otherRequestCount > 0)
    self.ui.m_txtRedPointNum.text = otherRequestCount

    -- 领取体力红点
    local hasGift = NetFriendData:HasFriendAccept()
    local isReceiveMax = FriendManager:IsMaxReceiveTimes()
    SetActive(self.ui.m_goAutoReveiveRed, hasGift and not isReceiveMax)

    -- 有好友私聊消息
    local hasFriendRed, hasStrangerRed = NetFriendData:HasFriendMessageRed()

    -- 好友页签红点
    local showFriendTabRedPoint = otherRequestCount > 0 or (hasGift and not isReceiveMax) or hasFriendRed or NetInviteFriendData:CanGetBindFbReward()
    SetActive(self.ui.m_goTabFriendRed, showFriendTabRedPoint)

    -- 最近联系页签红点
    SetActive(self.ui.m_goTabRecentlyRed, hasStrangerRed)
end

function UI_FriendView:RefreshChatRedPoint()
    if not self.ui then return end

    local worldChatType = CHAT_TYPE_ENUM.WORLD
    local worldChannel = ChatManager:GetWorldChatChannel()
    local worldRedCount = ChatManager:GetChatNewCacheCount(worldChatType,worldChannel)

    local areaRedCount = 0
    if worldRedCount <= 0 then
        local areaChatType = CHAT_TYPE_ENUM.AREA
        local areaChannel = ChatManager:GetAreaChatChannel()
        local playerLan = LangMgr:GetLangId()
        local key = playerLan * 10000 + areaChannel
        areaRedCount = ChatManager:GetChatNewCacheCount(areaChatType, key)
    end

    local unionRedCount = ChatManager:GetChatNewCacheCount(CHAT_TYPE_ENUM.UNION)

    local isHas,chatRedCount = ChatManager:GetAnnounceMentHasNew()

    SetActive(self.ui.m_goTabChatRed,worldRedCount > 0 or areaRedCount > 0 or unionRedCount > 0 or chatRedCount > 0)
end

-- 世界聊天红点
function UI_FriendView:RefreshWorldChatRedPoint()
    local worldChatType = CHAT_TYPE_ENUM.WORLD
    local worldChannel = ChatManager:GetWorldChatChannel()
    local worldRedCount = ChatManager:GetChatNewCacheCount(worldChatType,worldChannel)
    SetActive(self.ui.m_goWorldRedPoint,worldRedCount > 0)
    if worldRedCount > 99 then
        worldRedCount = 99 .. "+"
    end
    self.ui.m_txtWorldRedPointNum.text = worldRedCount
end

-- 区域聊天红点
function UI_FriendView:RefreshAreaChatRedPoint()
    local areaChatType = CHAT_TYPE_ENUM.AREA
    local areaChannel = ChatManager:GetAreaChatChannel()
    local playerLan = LangMgr:GetLangId()
    local key = playerLan * 10000 + areaChannel
    local areaRedCount = ChatManager:GetChatNewCacheCount(areaChatType, key)
    SetActive(self.ui.m_goAreaRedPoint,areaRedCount > 0)
    if areaRedCount > 99 then
        areaRedCount = 99 .. "+"
    end
    self.ui.m_txtAreaRedPointNum.text = areaRedCount
end

-- 团队聊天红点
function UI_FriendView:RefreshLeagueChatRedPoint()
    local leagueChatType = CHAT_TYPE_ENUM.UNION
    local leagueRedCount = ChatManager:GetChatNewCacheCount(leagueChatType)
    SetActive(self.ui.m_goLeagueRedPoint,leagueRedCount > 0)
    if leagueRedCount > 99 then
        leagueRedCount = 99 .. "+"
    end
    self.ui.m_txtLeagueRedPoint.text = leagueRedCount
end

-- 公告红点
function UI_FriendView:RefreshAnnounceChatRedPoint()
    local isHas,areaRedCount = ChatManager:GetAnnounceMentHasNew()
    SetActive(self.ui.m_goAnnounceRedPoint,isHas)
    self.ui.m_txtAnnounceRedPoint.text = areaRedCount
end

--endregion -------------------------------------- 红点 -----------------------------------------

--region ----------------------------------------- 好友私聊 -----------------------------------------

--- 好友私聊消息列表
function UI_FriendView:RequestFriendChatList(targetId, isRequest)
    SetActive(self.ui.m_scrollviewChat,false)
    -- 清屏
    if v2n(CurPlayerID) ~= v2n(targetId) then
        self.chatSlider:ClearDatas()
        --self.privateChatSlider:ClearDatas()
    end
    
    -- 初次请求，时间戳默认为 0
    local timeAt = 0
    local needMoveEnd = not isRequest

    -- 已拉取过私聊消息，发送最上面那条消息的时间戳，再次拉取历史私聊消息
    local dataList = ChatManager:UpdateFriendMessageByID(targetId)
    if not IsTableEmpty(dataList) then
        local lastData = dataList[1]
        timeAt = lastData.chat.timestamp
    end

    if isRequest then
        -- 请求私聊消息列表
        local param = {}
        param.type = CHAT_TYPE_ENUM.PRIVATE
        param.lastAt = timeAt
        param.targetId = targetId

        ChatManager:RequestChatChannelList(param, function ()
            -- 使用最新的消息缓存列表
            local dataList = ChatManager:UpdateFriendMessageByID(targetId)
            local datas = self:GetChatList(dataList)
            SetActive(self.ui.m_scrollviewChat,true)
            if IsTableEmpty(datas) then
                self.chatSlider:ClearDatas()
                return
            end
            -- 初次请求，要滑动到最底
            if timeAt == 0 then
                needMoveEnd = true
            end
            self.chatSlider:SetDatas(datas, needMoveEnd)
        end)
    else
        local datas = self:GetChatList(dataList)
        SetActive(self.ui.m_scrollviewChat,true)
        if IsTableEmpty(datas) then
            self.chatSlider:ClearDatas()
            return
        end
        self.chatSlider:SetDatas(datas, needMoveEnd)
    end
end

--- 选择要聊天的好友
--- @param data table 目标 ID
function UI_FriendView:SelectChatFriend(data)
    local index
    if IsTableNotEmpty(self.friendSlider.datas) then
        for key, value in pairs(self.friendSlider.datas) do
            if v2n(value.playerId) == v2n(data.playerId) then
                index = key
            end
        end
    end

    -- 列表里有这个好友
    if index then
        --self:SelectTargetItem(data.playerId)
    -- 列表里没有这个陌生人
    else
        -- 切换到最近联系
        --self:OnClickHorTab(FRIEND_VIEW_TYPE.Recently, true)
        self.clickType = FRIEND_VIEW_TYPE.Recently
        -- 深拷贝最近联系列表
        local temp = deepcopy(NetFriendData.recentlyList)
        local hasPlayer
        for _, value in pairs(temp) do
            if v2n(value.playerId) == v2n(data.playerId) then
                hasPlayer = true
            end
        end
        if not hasPlayer then
            -- 在列表前面插入一个陌生人
            table.insert(temp, 1, data)
            local count = table.count(temp)
            local friendCountMax = FriendManager:GetConfigById(102)
            -- 超出列表数量上限
            if count > v2n(friendCountMax) then
                table.sort(temp, function(a, b)
                    if a.recentAt and b.recentAt then
                        return v2n(a.recentAt) > v2n(b.recentAt)
                    end
                    return false
                end)
                -- 移除最后一个
                table.remove(temp, count)
            end
        end
        -- 刷新最近联系列表
        self:SetFriendListData(temp, FRIEND_VIEW_TYPE.Recently)
        -- 选中目标
        --self:SelectTargetItem(data.playerId)
        --
        ---- 刷新窗口表现
        ----SetActive(self.ui.m_goFriendTop, false)
        --SetActive(self.ui.m_scrollviewChat, false)
        --SetActive(self.ui.m_goSendMessage, false)
        --SetActive(self.ui.m_goChatExtensionPanel, false)
        --SetActive(self.ui.m_goRecentlyTop, false)
        --SetActive(self.ui.m_goNoRecently, false)
        --
        --if #temp > 0 then
        --    --SetActive(self.ui.m_goFriendTop, true)
        --    SetActive(self.ui.m_scrollviewChat, true)
        --    --SetActive(self.ui.m_goSendMessage, true)
        --    SetActive(self.ui.m_goChatExtensionPanel, false)
        --elseif #temp == 0 then
        --    --SetActive(self.ui.m_goRecentlyTop, true)
        --    SetActive(self.ui.m_goNoRecently, true)
        --end
    end
end

-- 打开私聊面板
function UI_FriendView:OpenPrivateChat(isOpen)
    self.isInPrivateChat = isOpen
    SetActive(self.ui.m_goVerButtons,not self.isInPrivateChat)
    SetActive(self.ui.m_goSwitchChannel,(not self.isInPrivateChat and self.curChatChannel == FRIEND_VIEW_TYPE.WorldChannel
            or self.curChatChannel == FRIEND_VIEW_TYPE.OptionalLanguage))
    SetActive(self.ui.m_goOptions,not self.isInPrivateChat)
    SetActive(self.ui.m_scrollviewChat,(self.isInPrivateChat or self.curChatChannel == FRIEND_VIEW_TYPE.WorldChannel
            or self.curChatChannel == FRIEND_VIEW_TYPE.OptionalLanguage))

    SetActive(self.ui.m_goPrivateChat, self.isInPrivateChat)
    SetActive(self.ui.m_goSendMessage, self.isInPrivateChat or self.curChatChannel == FRIEND_VIEW_TYPE.WorldChannel
            or self.curChatChannel == FRIEND_VIEW_TYPE.OptionalLanguage)

    SetActive(self.ui.m_goFriendBottom, not self.isInPrivateChat)

    if self.isInPrivateChat then
        self.chatRect.sizeDelta = Vector2.New(0, 107)
    else
        self.chatRect.sizeDelta = Vector2.New(0, -107)
    end
end

--endregion -------------------------------------- 好友私聊 -----------------------------------------

--region    ------------------------ 联盟boss相关 ---------------------------------------------------

-------------------- ChateInviteBossOther --------------------------------
function ChateInviteBossOther:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)

    self.hor_layout = GetChild(self.transform, "hor_layout")
    self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.playerHead = GetChild(self.transform, "headbg", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

    self.txtLeagueText = GetChild(self.transform,"bubble/txtLeagueText",UEUI.Text)
    self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)

    self.btnGoto = GetChild(self.transform,"bubble/btnGoto",UEUI.Button)
end

function ChateInviteBossOther:CheckNewState(dataIndex,data)
    local chatType = data.chatType or FRIEND_VIEW_TYPE.Chat
    local _chatType
    local chatChannel
    if chatType == FRIEND_VIEW_TYPE.WorldChannel then
        _chatType = CHAT_TYPE_ENUM.WORLD
        chatChannel = ChatManager:GetWorldChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.OptionalLanguage then
        _chatType = CHAT_TYPE_ENUM.AREA
        chatChannel = ChatManager:GetAreaChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.LeagueChat then
        _chatType = CHAT_TYPE_ENUM.UNION
        chatChannel = CHAT_TYPE_ENUM.UNION
    elseif chatType == FRIEND_VIEW_TYPE.SystemMessages then
        _chatType = CHAT_TYPE_ENUM.SYSTEM
    end

    if CurClickType == FRIEND_VIEW_TYPE.Friend or CurClickType == FRIEND_VIEW_TYPE.Recently then
        _chatType = CHAT_TYPE_ENUM.PRIVATE
    end

    if chatChannel or chatType == FRIEND_VIEW_TYPE.LeagueChat then
        local isNew = ChatManager:GetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
        if isNew then
            ChatManager:SetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
            EventMgr:Dispatch(EventID.CHECK_NEW_MSG,data,true)
            EventMgr:Dispatch(EventID.REFRESH_CHAT_RED,_chatType,true)
        end
    end
end

function ChateInviteBossOther:OnUpdateData(dataIndex,data)
    self:CheckNewState(dataIndex,data)
    if data.player then
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name

        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border,function()
            if playerData and not IsNilOrEmpty(playerData.id) then
                FriendManager:ShowPlayerById(playerData.id)
            end
        end)

        SetChatBorder(self.imgChatBorder,playerData.chatBorder,false)
        local color = GetChatBorderTextColor(playerData.chatBorder,false)
        self.txtLeagueText.color = color
    else
        SetActive(self.hor_layout,false)
    end
    if data.league then
        local leagueData = data.league
        SetImageSprite(self.unionImg,LeagueManager:GetUnionImageById(leagueData.icon),false)
        self.unionName.text = leagueData.name
    end
    local curActiveId
    if data.content then
        local bossData = Json.decode(data.content)
        if bossData and bossData.boss then
            local bossServerData = bossData.boss
            local bossConfigId = v2n(bossServerData.id)
            local langID = v2n(bossServerData.desc)
            curActiveId = v2n(bossServerData.activeId)
            local bossVo = LeagueManager:GetMonsterHeroVo(bossConfigId)
            local bossName = bossVo:GetHeroName()
            self.txtLeagueText.text = LangMgr:GetLangFormat(langID,bossName)
        end
    end
    if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end
    RemoveUIComponentEventCallback(self.btnGoto,UEUI.Button)
    AddUIComponentEventCallback(self.btnGoto,UEUI.Button,function(arg1,arg2)
        LeagueManager:OpenLeagueBoss(curActiveId)
    end)
end

function ChateInviteBossOther:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

-------------------- ChateInviteBossMe --------------------------------
function ChateInviteBossMe:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)

    self.hor_layout = GetChild(self.transform, "hor_layout")
    self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.playerHead = GetChild(self.transform, "headbg", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

    self.txtLeagueText = GetChild(self.transform,"bubble/txtLeagueText",UEUI.Text)
    self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)

    self.btnGoto = GetChild(self.transform,"bubble/btnGoto",UEUI.Button)
end

function ChateInviteBossMe:CheckNewState(dataIndex,data)
    local chatType = data.chatType or FRIEND_VIEW_TYPE.Chat
    local _chatType
    local chatChannel
    if chatType == FRIEND_VIEW_TYPE.WorldChannel then
        _chatType = CHAT_TYPE_ENUM.WORLD
        chatChannel = ChatManager:GetWorldChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.OptionalLanguage then
        _chatType = CHAT_TYPE_ENUM.AREA
        chatChannel = ChatManager:GetAreaChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.LeagueChat then
        _chatType = CHAT_TYPE_ENUM.UNION
        chatChannel = CHAT_TYPE_ENUM.UNION
    elseif chatType == FRIEND_VIEW_TYPE.SystemMessages then
        _chatType = CHAT_TYPE_ENUM.SYSTEM
    end

    if CurClickType == FRIEND_VIEW_TYPE.Friend or CurClickType == FRIEND_VIEW_TYPE.Recently then
        _chatType = CHAT_TYPE_ENUM.PRIVATE
    end

    if chatChannel or chatType == FRIEND_VIEW_TYPE.LeagueChat then
        local isNew = ChatManager:GetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
        if isNew then
            ChatManager:SetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
            EventMgr:Dispatch(EventID.CHECK_NEW_MSG,data,true)
            EventMgr:Dispatch(EventID.REFRESH_CHAT_RED,_chatType,true)
        end
    end
end

function ChateInviteBossMe:OnUpdateData(dataIndex,data)
    self:CheckNewState(dataIndex,data)
    if data.player then
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name

        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border,function()
            if playerData and not IsNilOrEmpty(playerData.id) then
                FriendManager:ShowPlayerById(playerData.id)
            end
        end)

        SetChatBorder(self.imgChatBorder,playerData.chatBorder,false)
        local color = GetChatBorderTextColor(playerData.chatBorder,false)
        self.txtLeagueText.color = color
    else
        SetActive(self.hor_layout,false)
    end
    if data.league then
        local leagueData = data.league
        SetImageSprite(self.unionImg,LeagueManager:GetUnionImageById(leagueData.icon),false)
        self.unionName.text = leagueData.name
    end
    local curActiveId
    if data.content then
        local bossData = Json.decode(data.content)
        if bossData and bossData.boss then
            local bossServerData = bossData.boss
            local bossConfigId = v2n(bossServerData.id)
            local langID = v2n(bossServerData.desc)
            curActiveId = v2n(bossServerData.activeId)
            local bossVo = LeagueManager:GetMonsterHeroVo(bossConfigId)
            local bossName = bossVo:GetHeroName()
            self.txtLeagueText.text = LangMgr:GetLangFormat(langID,bossName)
        end
    end
    if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end
    RemoveUIComponentEventCallback(self.btnGoto,UEUI.Button)
    AddUIComponentEventCallback(self.btnGoto,UEUI.Button,function(arg1,arg2)
        LeagueManager:OpenLeagueBoss(curActiveId)
    end)
end

function ChateInviteBossMe:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

-------------------- ChateBossDamageOther --------------------------------
function ChateBossDamageOther:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)

    self.hor_layout = GetChild(self.transform, "hor_layout")
    self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.playerHead = GetChild(self.transform, "headbg", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

    self.txtTitle = GetChild(self.transform,"bubble/txtTitle",UEUI.Text)
    self.txtDamageText = GetChild(self.transform,"bubble/txtDamageText",UEUI.Text)
    self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)
    self.teamGroup = GetChild(self.transform, "bubble/teamGroup", UE.RectTransform)

    self.btnGoto = GetChild(self.transform,"bubble/btnGoto",UEUI.Button)
    self.heroItemList = {}
end

function ChateBossDamageOther:CheckNewState(dataIndex,data)
    local chatType = data.chatType or FRIEND_VIEW_TYPE.Chat
    local _chatType
    local chatChannel
    if chatType == FRIEND_VIEW_TYPE.WorldChannel then
        _chatType = CHAT_TYPE_ENUM.WORLD
        chatChannel = ChatManager:GetWorldChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.OptionalLanguage then
        _chatType = CHAT_TYPE_ENUM.AREA
        chatChannel = ChatManager:GetAreaChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.LeagueChat then
        _chatType = CHAT_TYPE_ENUM.UNION
        chatChannel = CHAT_TYPE_ENUM.UNION
    elseif chatType == FRIEND_VIEW_TYPE.SystemMessages then
        _chatType = CHAT_TYPE_ENUM.SYSTEM
    end

    if CurClickType == FRIEND_VIEW_TYPE.Friend or CurClickType == FRIEND_VIEW_TYPE.Recently then
        _chatType = CHAT_TYPE_ENUM.PRIVATE
    end

    if chatChannel or chatType == FRIEND_VIEW_TYPE.LeagueChat then
        local isNew = ChatManager:GetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
        if isNew then
            ChatManager:SetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
            EventMgr:Dispatch(EventID.CHECK_NEW_MSG,data,true)
            EventMgr:Dispatch(EventID.REFRESH_CHAT_RED,_chatType,true)
        end
    end
end

function ChateBossDamageOther:OnUpdateData(dataIndex,data)
    self:CheckNewState(dataIndex,data)
    if data.player then
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name

        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border,function()
            if playerData and not IsNilOrEmpty(playerData.id) then
                FriendManager:ShowPlayerById(playerData.id)
            end
        end)

        SetChatBorder(self.imgChatBorder,playerData.chatBorder,false)
        local color = GetChatBorderTextColor(playerData.chatBorder,false)
        self.txtDamageText.color = color
    else
        SetActive(self.hor_layout,false)
    end
    if data.league then
        local leagueData = data.league
        SetImageSprite(self.unionImg,LeagueManager:GetUnionImageById(leagueData.icon),false)
        self.unionName.text = leagueData.name
    end
    local curActiveId
    if data.content then
        local content = data.content
        content = string.gsub(content, '"', "")
        content = string.gsub(content, "'", '"')
        local bossData = Json.decode(content)
        if bossData and bossData.boss then
            local bossServerData = bossData.boss
            local bossConfigId = v2n(bossServerData.id)
            local bossDamage = NumToGameString(bossServerData.damage)
            curActiveId = v2n(bossServerData.activeId)
            local bossName = ""
            local bossVo
            if bossData.boss.isWorldBoss then
                local monsterId = WorldBossManager:GetMonsterId(bossConfigId)
                bossVo = HeroModule.new(monsterId)
            else
                bossVo = LeagueManager:GetMonsterHeroVo(bossConfigId)
            end
            if bossVo then
                bossName = bossVo:GetHeroName()
                self.txtDamageText.text = LangMgr:GetLangFormat(70000366,bossName,bossDamage)
            end
        end
        if bossData and bossData.team then
            for i, v in ipairs(bossData.team) do
                local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_hero,v.code)
                local heroVo = HeroModule.new(v.code, config)
                heroVo:SetHeroValueByKey("level", v.level)
                heroVo:SetHeroValueByKey("starLv", v.star)
                if self.heroItemList[i] == nil then
                    local heroGo = GoSlgHeroItem:Create(self.teamGroup,heroVo)
                    heroGo:SetIsNeedShowSelect(false)
                    heroGo:SetItem()
                    heroGo:SetScale(0.75,0.75)
                    self.heroItemList[i] = heroGo
                else
                    self.heroItemList[i]:ChangHero(heroVo)
                end
            end
        end
        RemoveUIComponentEventCallback(self.btnGoto,UEUI.Button)
        AddUIComponentEventCallback(self.btnGoto,UEUI.Button,function(arg1,arg2)
            if bossData.boss.isWorldBoss then
                WorldBossManager:OpenWorldBossWin();
            else
                LeagueManager:OpenLeagueBoss(curActiveId)
            end
        end)
    end
    self.txtTitle.text = LangMgr:GetLang(70000365)
    if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end

end

function ChateBossDamageOther:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

-------------------- ChateBossDamageMe --------------------------------
function ChateBossDamageMe:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)

    self.hor_layout = GetChild(self.transform, "hor_layout")
    self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.playerHead = GetChild(self.transform, "headbg", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

    self.txtTitle = GetChild(self.transform,"bubble/txtTitle",UEUI.Text)
    self.txtDamageText = GetChild(self.transform,"bubble/txtDamageText",UEUI.Text)
    self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)
    self.teamGroup = GetChild(self.transform, "bubble/teamGroup", UE.RectTransform)

    self.btnGoto = GetChild(self.transform,"bubble/btnGoto",UEUI.Button)
    self.heroItemList = {}
end

function ChateBossDamageMe:CheckNewState(dataIndex,data)
    local chatType = data.chatType or FRIEND_VIEW_TYPE.Chat
    local _chatType
    local chatChannel
    if chatType == FRIEND_VIEW_TYPE.WorldChannel then
        _chatType = CHAT_TYPE_ENUM.WORLD
        chatChannel = ChatManager:GetWorldChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.OptionalLanguage then
        _chatType = CHAT_TYPE_ENUM.AREA
        chatChannel = ChatManager:GetAreaChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.LeagueChat then
        _chatType = CHAT_TYPE_ENUM.UNION
        chatChannel = CHAT_TYPE_ENUM.UNION
    elseif chatType == FRIEND_VIEW_TYPE.SystemMessages then
        _chatType = CHAT_TYPE_ENUM.SYSTEM
    end

    if CurClickType == FRIEND_VIEW_TYPE.Friend or CurClickType == FRIEND_VIEW_TYPE.Recently then
        _chatType = CHAT_TYPE_ENUM.PRIVATE
    end

    if chatChannel or chatType == FRIEND_VIEW_TYPE.LeagueChat then
        local isNew = ChatManager:GetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
        if isNew then
            ChatManager:SetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
            EventMgr:Dispatch(EventID.CHECK_NEW_MSG,data,true)
            EventMgr:Dispatch(EventID.REFRESH_CHAT_RED,_chatType,true)
        end
    end
end

function ChateBossDamageMe:OnUpdateData(dataIndex,data)
    self:CheckNewState(dataIndex,data)
    if data.player then
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name

        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border,function()
            if playerData and not IsNilOrEmpty(playerData.id) then
                FriendManager:ShowPlayerById(playerData.id)
            end
        end)

        SetChatBorder(self.imgChatBorder,playerData.chatBorder,false)
        local color = GetChatBorderTextColor(playerData.chatBorder,false)
        self.txtDamageText.color = color
    else
        SetActive(self.hor_layout,false)
    end
    if data.league then
        local leagueData = data.league
        SetImageSprite(self.unionImg,LeagueManager:GetUnionImageById(leagueData.icon),false)
        self.unionName.text = leagueData.name
    end
    local curActiveId
    if data.content then
        local content = data.content
        content = string.gsub(content, '"', "")
        content = string.gsub(content, "'", '"')
        local bossData = Json.decode(content)
        if bossData and bossData.boss then
            local bossServerData = bossData.boss
            local bossConfigId = v2n(bossServerData.id)
            local bossDamage = NumToGameString(bossServerData.damage)
            curActiveId = v2n(bossServerData.activeId)
            local bossName = ""
            local bossVo
            if bossData.boss.isWorldBoss then
                local monsterId = WorldBossManager:GetMonsterId(bossConfigId)
                bossVo = HeroModule.new(monsterId)
            else
                bossVo = LeagueManager:GetMonsterHeroVo(bossConfigId)
            end
            if bossVo then
                bossName = bossVo:GetHeroName()
                self.txtDamageText.text = LangMgr:GetLangFormat(70000366,bossName,bossDamage)
            end
        end
        if bossData and bossData.team then
            for i, v in ipairs(bossData.team) do
                local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_hero,v.code)
                local heroVo = HeroModule.new(v.code, config)
                heroVo:SetHeroValueByKey("level", v.level)
                heroVo:SetHeroValueByKey("starLv", v.star)
                if self.heroItemList[i] == nil then
                    local heroGo = GoSlgHeroItem:Create(self.teamGroup,heroVo)
                    heroGo:SetIsNeedShowSelect(false)
                    heroGo:SetItem()
                    heroGo:SetScale(0.75,0.75)
                    self.heroItemList[i] = heroGo
                else
                    self.heroItemList[i]:ChangHero(heroVo)
                end
            end
        end
        RemoveUIComponentEventCallback(self.btnGoto,UEUI.Button)
        AddUIComponentEventCallback(self.btnGoto,UEUI.Button,function(arg1,arg2)
            if bossData.boss.isWorldBoss then
                WorldBossManager:OpenWorldBossWin()
            else
                LeagueManager:OpenLeagueBoss(curActiveId)
            end
        end)
    end
    self.txtTitle.text = LangMgr:GetLang(70000365)
    if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end
end

function ChateBossDamageMe:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

-------------------- ChateArenaShareOther --------------------------------
function ChateArenaShareOther:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)

    self.hor_layout = GetChild(self.transform, "hor_layout")
    self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.playerHead = GetChild(self.transform, "headbg", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

    self.txtTitle = GetChild(self.transform,"bubble/txtTitle",UEUI.Text)
    self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)
    self.txtContent = GetChild(self.transform,"bubble/txtContent",UEUI.Text)

    self.imgResult = GetChild(self.transform, "bubble/imgResult", UEUI.Image)
    self.imgResultBg_1 = GetChild(self.transform, "bubble/imgResultBg_1", UEUI.Image)
    self.imgResultBg_2 = GetChild(self.transform, "bubble/imgResultBg_2", UEUI.Image)

    self.attackerHeadPos = GetChild(self.transform, "bubble/attackerHeadPos", UE.RectTransform)
    self.defenderHeadPos = GetChild(self.transform, "bubble/defenderHeadPos", UE.RectTransform)
    self.txtAttackerFail = GetChild(self.transform,"bubble/txtAttackerFail",UEUI.Text)
    self.txtAttackerWin = GetChild(self.transform,"bubble/txtAttackerWin",UEUI.Text)
    self.txtDefenderFail = GetChild(self.transform,"bubble/txtDefenderFail",UEUI.Text)
    self.txtDefenderWin = GetChild(self.transform,"bubble/txtDefenderWin",UEUI.Text)
    self.txtDefenderName = GetChild(self.transform,"bubble/txtDefenderName",UEUI.Text)
    self.txtAttackerName = GetChild(self.transform,"bubble/txtAttackerName",UEUI.Text)

    CreateCommonHead(self.attackerHeadPos,0.5)
    CreateCommonHead(self.defenderHeadPos,0.5)
end

function ChateArenaShareOther:CheckNewState(dataIndex,data)
    local chatType = data.chatType or FRIEND_VIEW_TYPE.Chat
    local _chatType
    local chatChannel
    if chatType == FRIEND_VIEW_TYPE.WorldChannel then
        _chatType = CHAT_TYPE_ENUM.WORLD
        chatChannel = ChatManager:GetWorldChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.OptionalLanguage then
        _chatType = CHAT_TYPE_ENUM.AREA
        chatChannel = ChatManager:GetAreaChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.LeagueChat then
        _chatType = CHAT_TYPE_ENUM.UNION
        chatChannel = CHAT_TYPE_ENUM.UNION
    elseif chatType == FRIEND_VIEW_TYPE.SystemMessages then
        _chatType = CHAT_TYPE_ENUM.SYSTEM
    end

    if CurClickType == FRIEND_VIEW_TYPE.Friend or CurClickType == FRIEND_VIEW_TYPE.Recently then
        _chatType = CHAT_TYPE_ENUM.PRIVATE
    end

    if chatChannel or chatType == FRIEND_VIEW_TYPE.LeagueChat then
        local isNew = ChatManager:GetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
        if isNew then
            ChatManager:SetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
            EventMgr:Dispatch(EventID.CHECK_NEW_MSG,data,true)
            EventMgr:Dispatch(EventID.REFRESH_CHAT_RED,_chatType,true)
        end
    end
end

function ChateArenaShareOther:OnUpdateData(dataIndex,data)
    self:CheckNewState(dataIndex,data)
    local chatPlayerId
    local attackerId
    local reportResult
    local enemyName = ""
    if data.player then
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        chatPlayerId = playerData.id
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name

        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border,function()
            if playerData and not IsNilOrEmpty(playerData.id) then
                FriendManager:ShowPlayerById(playerData.id)
            end
        end)

        SetChatBorder(self.imgChatBorder,playerData.chatBorder,false)
        local color = GetChatBorderTextColor(playerData.chatBorder,false)
        self.txtContent.color = color
    else
        SetActive(self.hor_layout,false)
    end
    if data.league then
        local leagueData = data.league
        SetActive(self.unionImg,leagueData.icon and leagueData.icon ~= "")
        SetImageSprite(self.unionImg,LeagueManager:GetUnionImageById(leagueData.icon),false)
        self.unionName.text = leagueData.name
    else
        SetActive(self.unionImg,false)
    end
    if data.content then
        local content = data.content
        --content = string.gsub(content, '"', "")
        local serverData = Json.decode(content)
        if serverData and serverData.attacker then
            local attackerData = serverData.attacker
            self.txtAttackerName.text = attackerData.name
            attackerId = attackerData.id
            local attackerCustomHeadObj = GetChild(self.transform,"bubble/attackerHeadPos/CustomHead")
            SetHeadAndBorderByGo(attackerCustomHeadObj,attackerData.icon,attackerData.border,function()
                if attackerData and not IsNilOrEmpty(attackerData.id) then
                    --FriendManager:ShowPlayerById(attackerData.id)
                end
            end)
        end
        if serverData and serverData.defender then
            local defenderData = serverData.defender
            self.txtDefenderName.text = defenderData.name
            enemyName = defenderData.name
            local defenderCustomHeadObj = GetChild(self.transform,"bubble/defenderHeadPos/CustomHead")
            SetHeadAndBorderByGo(defenderCustomHeadObj,defenderData.icon,defenderData.border,function()
                if defenderData and not IsNilOrEmpty(defenderData.id) then
                    --FriendManager:ShowPlayerById(defenderData.id)
                end
            end)
        end
        if serverData and serverData.fight then
            local fightData = serverData.fight
            local result = fightData.result
            local reportId = fightData.reportId
            local isWin = result == 1  --1 进攻方胜利 2防御方胜利
            if chatPlayerId and attackerId then
                if v2n(chatPlayerId) == v2n(attackerId) then
                    reportResult = isWin
                else
                    reportResult = not isWin
                end
            end

            self.txtAttackerWin.text = LangMgr:GetLang(70000098)
            self.txtAttackerFail.text = LangMgr:GetLang(70000099)
            self.txtDefenderFail.text = LangMgr:GetLang(70000099)
            self.txtDefenderWin.text = LangMgr:GetLang(70000098)
            SetActive(self.txtAttackerWin,isWin)
            SetActive(self.txtAttackerFail,not isWin)
            SetActive(self.txtDefenderFail,isWin)
            SetActive(self.txtDefenderWin,not isWin)
            local resImg = isWin and ArenaWinImg or ArenaFailImg
            local resBgImg1 = isWin and ArenaWinBgImg or ArenaFailBgImg
            local resBgImg2 = not isWin and ArenaWinBgImg or ArenaFailBgImg
            SetImageSprite(self.imgResult,resImg,false)
            SetImageSprite(self.imgResultBg_1,resBgImg1,false)
            SetImageSprite(self.imgResultBg_2,resBgImg2,false)
        end
    end

    self.txtTitle.text = LangMgr:GetLang(70000653)
    local descLangID = reportResult and 70000655 or 70000654
    self.txtContent.text = LangMgr:GetLangFormat(descLangID,enemyName)

    if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end
end

function ChateArenaShareOther:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

-------------------- ChateArenaShareMe --------------------------------
function ChateArenaShareMe:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)

    self.hor_layout = GetChild(self.transform, "hor_layout")
    self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.playerHead = GetChild(self.transform, "headbg", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

    self.txtTitle = GetChild(self.transform,"bubble/txtTitle",UEUI.Text)
    self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)
    self.txtContent = GetChild(self.transform,"bubble/txtContent",UEUI.Text)

    self.imgResult = GetChild(self.transform, "bubble/imgResult", UEUI.Image)
    self.imgResultBg_1 = GetChild(self.transform, "bubble/imgResultBg_1", UEUI.Image)
    self.imgResultBg_2 = GetChild(self.transform, "bubble/imgResultBg_2", UEUI.Image)

    self.attackerHeadPos = GetChild(self.transform, "bubble/attackerHeadPos", UE.RectTransform)
    self.defenderHeadPos = GetChild(self.transform, "bubble/defenderHeadPos", UE.RectTransform)
    self.txtAttackerFail = GetChild(self.transform,"bubble/txtAttackerFail",UEUI.Text)
    self.txtAttackerWin = GetChild(self.transform,"bubble/txtAttackerWin",UEUI.Text)
    self.txtDefenderFail = GetChild(self.transform,"bubble/txtDefenderFail",UEUI.Text)
    self.txtDefenderWin = GetChild(self.transform,"bubble/txtDefenderWin",UEUI.Text)
    self.txtDefenderName = GetChild(self.transform,"bubble/txtDefenderName",UEUI.Text)
    self.txtAttackerName = GetChild(self.transform,"bubble/txtAttackerName",UEUI.Text)

    CreateCommonHead(self.attackerHeadPos,0.5)
    CreateCommonHead(self.defenderHeadPos,0.5)
end

function ChateArenaShareMe:CheckNewState(dataIndex,data)
    local chatType = data.chatType or FRIEND_VIEW_TYPE.Chat
    local _chatType
    local chatChannel
    if chatType == FRIEND_VIEW_TYPE.WorldChannel then
        _chatType = CHAT_TYPE_ENUM.WORLD
        chatChannel = ChatManager:GetWorldChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.OptionalLanguage then
        _chatType = CHAT_TYPE_ENUM.AREA
        chatChannel = ChatManager:GetAreaChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.LeagueChat then
        _chatType = CHAT_TYPE_ENUM.UNION
        chatChannel = CHAT_TYPE_ENUM.UNION
    elseif chatType == FRIEND_VIEW_TYPE.SystemMessages then
        _chatType = CHAT_TYPE_ENUM.SYSTEM
    end

    if CurClickType == FRIEND_VIEW_TYPE.Friend or CurClickType == FRIEND_VIEW_TYPE.Recently then
        _chatType = CHAT_TYPE_ENUM.PRIVATE
    end

    if chatChannel or chatType == FRIEND_VIEW_TYPE.LeagueChat then
        local isNew = ChatManager:GetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
        if isNew then
            ChatManager:SetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
            EventMgr:Dispatch(EventID.CHECK_NEW_MSG,data,true)
            EventMgr:Dispatch(EventID.REFRESH_CHAT_RED,_chatType,true)
        end
    end
end

function ChateArenaShareMe:OnUpdateData(dataIndex,data)
    self:CheckNewState(dataIndex,data)
    local chatPlayerId
    local attackerId
    local reportResult
    local enemyName = ""
    if data.player then
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        chatPlayerId = playerData.id
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name

        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border,function()
            if playerData and not IsNilOrEmpty(playerData.id) then
                FriendManager:ShowPlayerById(playerData.id)
            end
        end)

        SetChatBorder(self.imgChatBorder,playerData.chatBorder,false)
        local color = GetChatBorderTextColor(playerData.chatBorder,false)
        self.txtContent.color = color
    else
        SetActive(self.hor_layout,false)
    end
    if data.league then
        local leagueData = data.league
        SetActive(self.unionImg,leagueData.icon and leagueData.icon ~= "")
        SetImageSprite(self.unionImg,LeagueManager:GetUnionImageById(leagueData.icon),false)
        self.unionName.text = leagueData.name
    else
        SetActive(self.unionImg,false)
    end
    if data.content then
        local content = data.content
        --content = string.gsub(content, '"', "")
        local serverData = Json.decode(content)
        if serverData and serverData.attacker then
            local attackerData = serverData.attacker
            self.txtAttackerName.text = attackerData.name
            attackerId = attackerData.id
            local attackerCustomHeadObj = GetChild(self.transform,"bubble/attackerHeadPos/CustomHead")
            SetHeadAndBorderByGo(attackerCustomHeadObj,attackerData.icon,attackerData.border,function()
                if attackerData and not IsNilOrEmpty(attackerData.id) then
                    --FriendManager:ShowPlayerById(attackerData.id)
                end
            end)
        end
        if serverData and serverData.defender then
            local defenderData = serverData.defender
            self.txtDefenderName.text = defenderData.name
            enemyName = defenderData.name
            local defenderCustomHeadObj = GetChild(self.transform,"bubble/defenderHeadPos/CustomHead")
            SetHeadAndBorderByGo(defenderCustomHeadObj,defenderData.icon,defenderData.border,function()
                if defenderData and not IsNilOrEmpty(defenderData.id) then
                    --FriendManager:ShowPlayerById(defenderData.id)
                end
            end)
        end
        if serverData and serverData.fight then
            local fightData = serverData.fight
            local result = fightData.result
            local reportId = fightData.reportId
            local isWin = result == 1  --1 进攻方胜利 2防御方胜利
            if chatPlayerId and attackerId then
                if v2n(chatPlayerId) == v2n(attackerId) then
                    reportResult = isWin
                else
                    reportResult = not isWin
                end
            end
        
            self.txtAttackerWin.text = LangMgr:GetLang(70000098)
            self.txtAttackerFail.text = LangMgr:GetLang(70000099)
            self.txtDefenderFail.text = LangMgr:GetLang(70000099)
            self.txtDefenderWin.text = LangMgr:GetLang(70000098)
            SetActive(self.txtAttackerWin,isWin)
            SetActive(self.txtAttackerFail,not isWin)
            SetActive(self.txtDefenderFail,isWin)
            SetActive(self.txtDefenderWin,not isWin)
            local resImg = isWin and ArenaWinImg or ArenaFailImg
            local resBgImg1 = isWin and ArenaWinBgImg or ArenaFailBgImg
            local resBgImg2 = not isWin and ArenaWinBgImg or ArenaFailBgImg
            SetImageSprite(self.imgResult,resImg,false)
            SetImageSprite(self.imgResultBg_1,resBgImg1,false)
            SetImageSprite(self.imgResultBg_2,resBgImg2,false)
        end
    end

    self.txtTitle.text = LangMgr:GetLang(70000653)
    local descLangID = reportResult and 70000655 or 70000654
    self.txtContent.text = LangMgr:GetLangFormat(descLangID,enemyName)

    if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end

end

function ChateArenaShareMe:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

--endregion ----------------------------------- 联盟BOSS相关 -------------------------------------

--region ----------------------------------- 联盟火车相关 -------------------------------------

-------------------- ChateTrainShareOther --------------------------------
function ChateTrainShareOther:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)

    self.hor_layout = GetChild(self.transform, "hor_layout")
    self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.playerHead = GetChild(self.transform, "headbg", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

    self.txtTitle = GetChild(self.transform,"bubble/txtTitle",UEUI.Text)
    self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)
    self.txtContent = GetChild(self.transform,"bubble/txtContent",UEUI.Text)

    self.playerHeadPos = GetChild(self.transform, "bubble/playerHeadPos", UE.RectTransform)
    self.txtPlayerName = GetChild(self.transform,"bubble/txtPlayerName",UEUI.Text)
    self.txtPlayerLevel = GetChild(self.transform,"bubble/txtPlayerLevel",UEUI.Text)
    self.txtPlayerFight = GetChild(self.transform,"bubble/txtPlayerFight",UEUI.Text)
    self.txtSoundnessPercent = GetChild(self.transform,"bubble/txtSoundnessPercent",UEUI.Text)
    self.txtSoundness = GetChild(self.transform,"bubble/txtSoundness",UEUI.Text)
    self.btnGoto = GetChild(self.transform,"bubble/btnGoto",UEUI.Button)

    CreateCommonHead(self.playerHeadPos,0.5)
end

function ChateTrainShareOther:CheckNewState(dataIndex,data)
    local chatType = data.chatType or FRIEND_VIEW_TYPE.Chat
    local _chatType
    local chatChannel
    if chatType == FRIEND_VIEW_TYPE.WorldChannel then
        _chatType = CHAT_TYPE_ENUM.WORLD
        chatChannel = ChatManager:GetWorldChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.OptionalLanguage then
        _chatType = CHAT_TYPE_ENUM.AREA
        chatChannel = ChatManager:GetAreaChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.LeagueChat then
        _chatType = CHAT_TYPE_ENUM.UNION
        chatChannel = CHAT_TYPE_ENUM.UNION
    elseif chatType == FRIEND_VIEW_TYPE.SystemMessages then
        _chatType = CHAT_TYPE_ENUM.SYSTEM
    end

    if CurClickType == FRIEND_VIEW_TYPE.Friend or CurClickType == FRIEND_VIEW_TYPE.Recently then
        _chatType = CHAT_TYPE_ENUM.PRIVATE
    end

    if chatChannel or chatType == FRIEND_VIEW_TYPE.LeagueChat then
        local isNew = ChatManager:GetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
        if isNew then
            ChatManager:SetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
            EventMgr:Dispatch(EventID.CHECK_NEW_MSG,data,true)
            EventMgr:Dispatch(EventID.REFRESH_CHAT_RED,_chatType,true)
        end
    end
end

function ChateTrainShareOther:OnUpdateData(dataIndex,data)
    self:CheckNewState(dataIndex,data)
    local chatPlayerId
    local attackerId
    local playerName = ""
    local trainId
    if data.player then
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        chatPlayerId = playerData.id
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name

        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border,function()
            if playerData and not IsNilOrEmpty(playerData.id) then
                FriendManager:ShowPlayerById(playerData.id)
            end
        end)

        SetChatBorder(self.imgChatBorder,playerData.chatBorder,false)
        local color = GetChatBorderTextColor(playerData.chatBorder,false)
        self.txtContent.color = color
    else
        SetActive(self.hor_layout,false)
    end
    if data.league then
        local leagueData = data.league
        SetActive(self.unionImg,leagueData.icon and leagueData.icon ~= "")
        SetImageSprite(self.unionImg,LeagueManager:GetUnionImageById(leagueData.icon),false)
        self.unionName.text = leagueData.name
    else
        SetActive(self.unionImg,false)
    end
    if data.content then
        local content = data.content
        content = string.gsub(content, '"', "")
        content = string.gsub(content, "'", '"')
        local serverData = Json.decode(content)
        if serverData and serverData.victim then
            local victimData = serverData.victim
            attackerId = victimData.id
            self.txtPlayerName.text = victimData.name
            self.txtPlayerLevel.text = "Lv."..(victimData.level or 0)
            self.txtPlayerFight.text = NumToGameString(victimData.power or 0)
            playerName = victimData.name

            local CustomHeadObj = GetChild(self.transform,"bubble/playerHeadPos/CustomHead")
            SetHeadAndBorderByGo(CustomHeadObj,victimData.icon,victimData.border,function()
                if victimData and not IsNilOrEmpty(victimData.id) then
                    --FriendManager:ShowPlayerById(attackerData.id)
                end
            end)
        end
        if serverData and serverData.victimLeague then
            local victimLeagueData = serverData.victimLeague
            if playerName ~= "" then
                local leagueName = string.format("[%s]",victimLeagueData.name)
                self.txtContent.text = LangMgr:GetLangFormat(70000487,leagueName,playerName)
            else
                self.txtContent.text = ""
            end
        end
        if serverData and serverData.train then
            local trainData = serverData.train
            trainId = trainData.id
            self.txtSoundnessPercent.text = trainData.state .. "%"
        end
    end

    self.txtTitle.text = LangMgr:GetLang(70000486)
    self.txtSoundness.text = LangMgr:GetLang(70000448)
    if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end
    local function reqTrainInfo(trainData)
        if trainData then
            local train_info = trainData.train_info
            if train_info.end_timestamp then
                local remain = train_info.end_timestamp - TimeMgr:GetServerTime()
                local cannotPlunderTime = v2n(TradeWagonsManager:GetTradeSettingConfig(10))
                -- 火车已到站
                if remain <= 0 then
                    UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000489))
                elseif remain > 0 and remain <= cannotPlunderTime then
                    UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(1434))
                else
                    TradeWagonsManager:ClosePanel()
                    UI_SHOW(UIDefine.UI_ActivityRankCenter,8,nil,trainId)
                    UI_CLOSE(UIDefine.UI_FriendView)
                end
            end
        end
    end
    RemoveUIComponentEventCallback(self.btnGoto,UEUI.Button)
    AddUIComponentEventCallback(self.btnGoto,UEUI.Button,function(arg1,arg2)
        if trainId then
            local isOpen = NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.Train)
            if not isOpen then
                local config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_rank,ACTIVITY_RANK_TABINDEX.Train)
                if config then
                    local name = LangMgr:GetLang(config.name)
                    UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(70001120,name))
                end
                return
            end
            TradeWagonsManager:RequestTrainInfo(trainId,reqTrainInfo)
        end
    end)
end

function ChateTrainShareOther:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

-------------------- ChateTrainShareMe --------------------------------
function ChateTrainShareMe:OnInit(transform)
    self.bg = GetChild(self.transform, "bubble", UE.RectTransform)

    self.hor_layout = GetChild(self.transform, "hor_layout")
    self.chatTime = GetChild(self.transform, "chatTime",UEUI.Text)
    self.playerHead = GetChild(self.transform, "headbg", UEUI.Image)
    self.playerName = GetChild(self.transform,"hor_layout/playerName",UEUI.Text)
    self.rankImg = GetChild(self.transform, "hor_layout/rankImg", UEUI.Image)
    self.unionImg = GetChild(self.transform, "hor_layout/unionImg", UEUI.Image)
    self.unionName = GetChild(self.transform,"hor_layout/unionName",UEUI.Text)

    self.txtTitle = GetChild(self.transform,"bubble/txtTitle",UEUI.Text)
    self.imgChatBorder = GetChild(self.transform,"bubble/imgChatBorder",UEUI.Image)
    self.txtContent = GetChild(self.transform,"bubble/txtContent",UEUI.Text)

    self.playerHeadPos = GetChild(self.transform, "bubble/playerHeadPos", UE.RectTransform)
    self.txtPlayerName = GetChild(self.transform,"bubble/txtPlayerName",UEUI.Text)
    self.txtPlayerLevel = GetChild(self.transform,"bubble/txtPlayerLevel",UEUI.Text)
    self.txtPlayerFight = GetChild(self.transform,"bubble/txtPlayerFight",UEUI.Text)
    self.txtSoundnessPercent = GetChild(self.transform,"bubble/txtSoundnessPercent",UEUI.Text)
    self.txtSoundness = GetChild(self.transform,"bubble/txtSoundness",UEUI.Text)
    self.btnGoto = GetChild(self.transform,"bubble/btnGoto",UEUI.Button)

    CreateCommonHead(self.playerHeadPos,0.5)
end

function ChateTrainShareMe:CheckNewState(dataIndex,data)
    local chatType = data.chatType or FRIEND_VIEW_TYPE.Chat
    local _chatType
    local chatChannel
    if chatType == FRIEND_VIEW_TYPE.WorldChannel then
        _chatType = CHAT_TYPE_ENUM.WORLD
        chatChannel = ChatManager:GetWorldChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.OptionalLanguage then
        _chatType = CHAT_TYPE_ENUM.AREA
        chatChannel = ChatManager:GetAreaChatChannel()
    elseif chatType == FRIEND_VIEW_TYPE.LeagueChat then
        _chatType = CHAT_TYPE_ENUM.UNION
        chatChannel = CHAT_TYPE_ENUM.UNION
    elseif chatType == FRIEND_VIEW_TYPE.SystemMessages then
        _chatType = CHAT_TYPE_ENUM.SYSTEM
    end

    if CurClickType == FRIEND_VIEW_TYPE.Friend or CurClickType == FRIEND_VIEW_TYPE.Recently then
        _chatType = CHAT_TYPE_ENUM.PRIVATE
    end

    if chatChannel or chatType == FRIEND_VIEW_TYPE.LeagueChat then
        local isNew = ChatManager:GetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
        if isNew then
            ChatManager:SetChatNewCacheIsRead(_chatType,chatChannel,dataIndex)
            EventMgr:Dispatch(EventID.CHECK_NEW_MSG,data,true)
            EventMgr:Dispatch(EventID.REFRESH_CHAT_RED,_chatType,true)
        end
    end
end

function ChateTrainShareMe:OnUpdateData(dataIndex,data)
    self:CheckNewState(dataIndex,data)
    local chatPlayerId
    local attackerId
    local playerName = ""
    local trainId 
    if data.player then
        local playerData = data.player
        local stage = playerData.stage
        local rankStageConfig = GlobalConfig.RANK_STAGE[stage]
        chatPlayerId = playerData.id
        if rankStageConfig then
            SetActive(self.rankImg,true)
            SetImageSprite(self.rankImg, rankStageConfig[2], false)
        else
            SetActive(self.rankImg,false)
        end
        self.playerName.text = playerData.name

        local customHeadObj = GetChild(self.transform,"headbg/CustomHead")
        SetHeadAndBorderByGo(customHeadObj,playerData.icon,playerData.border,function()
            if playerData and not IsNilOrEmpty(playerData.id) then
                FriendManager:ShowPlayerById(playerData.id)
            end
        end)

        SetChatBorder(self.imgChatBorder,playerData.chatBorder,true)
        local color = GetChatBorderTextColor(playerData.chatBorder,true)
        self.txtContent.color = color
    else
        SetActive(self.hor_layout,false)
    end
    if data.league then
        local leagueData = data.league
        SetActive(self.unionImg,leagueData.icon and leagueData.icon ~= "")
        SetImageSprite(self.unionImg,LeagueManager:GetUnionImageById(leagueData.icon),false)
        self.unionName.text = leagueData.name
    else
        SetActive(self.unionImg,false)
    end
    if data.content then
        local content = data.content
        content = string.gsub(content, '"', "")
        content = string.gsub(content, "'", '"')
        local serverData = Json.decode(content)
        if serverData and serverData.victim then
            local victimData = serverData.victim
            attackerId = victimData.id
            self.txtPlayerName.text = victimData.name
            self.txtPlayerLevel.text = "Lv."..(victimData.level or 0)
            self.txtPlayerFight.text = NumToGameString(victimData.power or 0)
            playerName = victimData.name
            
            local CustomHeadObj = GetChild(self.transform,"bubble/playerHeadPos/CustomHead")
            SetHeadAndBorderByGo(CustomHeadObj,victimData.icon,victimData.border,function()
                if victimData and not IsNilOrEmpty(victimData.id) then
                    --FriendManager:ShowPlayerById(attackerData.id)
                end
            end)
        end
        if serverData and serverData.victimLeague then
            local victimLeagueData = serverData.victimLeague
            if playerName ~= "" then
                local leagueName = string.format("[%s]",victimLeagueData.name)
                self.txtContent.text = LangMgr:GetLangFormat(70000487,leagueName,playerName)
            else
                self.txtContent.text = ""
            end
        end
        if serverData and serverData.train then
            local trainData = serverData.train
            trainId = trainData.id
            self.txtSoundnessPercent.text = trainData.state .. "%"
        end
    end

    self.txtTitle.text = LangMgr:GetLang(70000486)
    self.txtSoundness.text = LangMgr:GetLang(70000448)
    if data.chat then
        SetActive(self.chatTime,data.isShowTime)
        self.chatTime.text = TimeMgr:StampToDateDes(data.chat.timestamp)
    end
    local function reqTrainInfo(trainData)
        if trainData then
            local train_info = trainData.train_info
            if train_info.end_timestamp then
                local remain = train_info.end_timestamp - TimeMgr:GetServerTime()
                local cannotPlunderTime = v2n(TradeWagonsManager:GetTradeSettingConfig(10))
                -- 火车已到站
                if remain <= 0 then
                    UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000489))
                elseif remain > 0 and remain <= cannotPlunderTime then
                    UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(1434))
                else
                    TradeWagonsManager:ClosePanel()
                    UI_SHOW(UIDefine.UI_ActivityRankCenter,8,nil,trainId)
                    UI_CLOSE(UIDefine.UI_FriendView)
                end
            end
        end
    end
    RemoveUIComponentEventCallback(self.btnGoto,UEUI.Button)
    AddUIComponentEventCallback(self.btnGoto,UEUI.Button,function(arg1,arg2)
        if trainId then
            local isOpen = NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.Train)
            if not isOpen then
                local config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_rank,ACTIVITY_RANK_TABINDEX.Train)
                if config then
                    local name = LangMgr:GetLang(config.name)
                    UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(70001120,name))
                end
                return
            end
            TradeWagonsManager:RequestTrainInfo(trainId,reqTrainInfo)
        end
    end)
end

function ChateTrainShareMe:OnUpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

--endregion ----------------------------------- 联盟火车相关 -------------------------------------

------------- BlackListItem -----------------------------
function BlackListItem:OnInit(transform)
    local obj = transform
    self.trans				= transform
    self.txtName			= GetChild(obj, "head/txtName", UEUI.Text)
    self.txtLang			= GetChild(obj, "head/txtLang", UEUI.Text)
    self.imgRank			= GetChild(obj, "imgRank", UEUI.Image)
    self.txtMyID			= GetChild(obj, "txtMyID", UEUI.Text)
    self.txtMyLevel			= GetChild(obj, "txtMyLevel", UEUI.Text)
    self.txtMyRankAll		= GetChild(obj, "txtMyRankAll", UEUI.Text)
    self.btnCancelBlacklist = GetChild(obj, "btnCancelBlacklist", UEUI.Button)
    
    self.customHeadObj = GetChild(self.trans.gameObject,"headNode/CustomHead")
end

function BlackListItem:UpdateData(data,index)
    if not data then return end
    
    self.data = data
    self:RefreshBlacklistPlayer()
    self.btnCancelBlacklist.onClick:AddListener(function ()
        NetFriendData:RemoveBlacklist(tostring(self.data.playerId))
        --SetUIBtnGrayAndEnable(self.ui.m_btnCancelBlacklist, false)
    end)
end

function BlackListItem:RefreshBlacklistPlayer()
    if not self.data then return end 
    local data = self.data
    -- 玩家名称
    self.txtName.text = data.name
    -- 语言
    self.txtLang.text = FriendManager:GetLanguage(data)
    -- 头像
    --local headIconPath = FriendManager:GetHeadIcon(data.headId)
    --if headIconPath then
    --    SetImageSprite(self.ui.m_imgHead, headIconPath, false)
    --end
    SetHeadAndBorderByGo(self.customHeadObj,data.headId,data.border)
    --customHeadObj.transform.localPosition = Vector3.zero

    -- 段位
    local rankIconPath = FriendManager:GetRankStageIcon(data.rankStage)
    if rankIconPath then
        SetImageSprite(self.imgRank, rankIconPath, false)
    end
    -- 玩家 ID
    self.txtMyID.text = data.playerId
    -- 等级
    self.txtMyLevel.text = "Lv." .. data.level
    -- 全服排名
    local rank = NetFriendData:GetBlacklistRank(data.playerId)
    if rank == nil or rank == 0 then
        NetFriendData:QueryUserInfo(data.playerId, function (friend)
            NetFriendData:UpdateBlacklistRank(data.playerId, friend.rank)
            if friend.rank == nil or friend.rank == 0 then
                self.txtMyRankAll.text = LangMgr:GetLang(9056)
            else
                self.txtMyRankAll.text = friend.rank
            end
        end)
    else
        self.txtMyRankAll.text = rank
    end
end

function BlackListItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function BlackListItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function BlackListItem:onDestroy()
    UEGO.Destroy(self.transform.gameObject)
end

return UI_FriendView