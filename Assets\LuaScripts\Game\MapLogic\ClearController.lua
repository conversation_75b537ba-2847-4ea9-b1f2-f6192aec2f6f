local ClearController = Class()
local M = ClearController
local TimerId = "Clear"

function M:ctor()
    self.show = false
	self.isLoading = false
end

function M:Show(type, messItem, callback)
	if  MapControllerVisit:IsVisit() then
		return
	end
    if IntelligentWorkerManager:IsRunning() then
        return
    end
	if self.isLoading then
		return
	end
    self:InitView(function ()
        self.height = 300
        --坐标没变时不重新打开
        if self.show then
            local gridX, gridY = self:GetMessGrid()
            if gridX and gridY and gridX == messItem.m_GridX and gridY == messItem.m_GridY then
                self:SetPos(messItem, true)
                return
            else
                self.last = self.mess
            end
        end
        self:OpenView(type, messItem, callback)
    end)
	
    local function onOpen(params)
        self:OpenView(params[1], params[2], params[3])
    end
    --TimeMgr:CreateTimer("WaitNew", onOpen, 0.07, 1, type, messItem, callback)
end

--初始化界面
function M:InitView( callback)
    if ObjIsNil(self.go) then
		self.isLoading = true
        local prePath = "Prefab/Map/Clear.Prefab"

        ResMgr:LoadAssetAsync(
        prePath,
        AssetDefine.LoadType.Instant,
        function(prefab)
			self.isLoading = false
            local newGo, newTrans = CreateGOAndTrans(prefab)
            self.go = newGo
            MapController:AddUIToWorld(newTrans)
            newTrans:SetLocalScale(0, 0, 0)

            local listUiChild = {}
            local count = newTrans.childCount
            for i = 0, count - 1 do
                table.insert(listUiChild, newTrans:GetChild(i))
            end

            self.child = listUiChild
            self.trans = newTrans
            self:AddButtonCallBack(newGo)

            self.noTimeEnergy = GetChild(newGo, "NotTime/m_btnExpend/m_txtNum", UEUI.Text)
            self.noTimeBlood = GetChild(newGo, "NotTime/Blood")
            self.noTimeImage = GetChild(newGo, "NotTime/m_btnExpend/m_txtNum/m_imgType", UEUI.Image)

            self.timeEnergy = GetChild(newGo, "Time/m_btnExpend/m_txtNum", UEUI.Text)
            self.timeBlood = GetChild(newGo, "Time/Blood")
            self.timeCountDown = GetChild(newGo, "Time/CountDown", UEUI.Text)
            self.timeImage = GetChild(newGo, "Time/m_btnExpend/m_txtNum/m_imgType", UEUI.Image)

            self.buildCountDown = GetChild(newGo, "Build/time/CountDown", UEUI.Text)
            self.buildExpendGo = GetChild(newGo, "Build/m_btnExpend")
            self.buildLimitGo = GetChild(newGo, "Build/m_btnLimitExpend")
            self.buildLimitConsume = GetChild(self.buildLimitGo, "m_txtAuto1023/consume", UEUI.Text)
            self.buildLimitImage = GetChild(self.buildLimitGo, "m_txtAuto1023/image", UEUI.Image)
            self.buildGo = GetChild(newGo, "Build/time")
            
            self.redBuildCountDown = GetChild(newGo, "RedBuild/CountDown", UEUI.Text)
            self.ui_collect_energy = GetChild(newGo, "Collect/m_txtNum", UEUI.Text)	
            self.collect_energyImage = GetChild(newGo, "Collect/m_txtNum/m_imgType", UEUI.Image)
            
            self.castle_collect_energy = GetChild(newGo, "Castle/m_txtNum", UEUI.Text)
            self.castle_energyImage = GetChild(newGo, "Castle/m_txtNum/m_imgType", UEUI.Image)
            self.castle_icon1 = GetChild(newGo, "Castle/m_imgBg1",UE.RectTransform)
            self.castle_icon2 =GetChild(newGo, "Castle/m_imgBg2",UE.RectTransform )

            self.exchangeIcon = GetChild(newGo, "Exchange/m_imgIcon", UEUI.Image)
            self.exchangeNum = GetChild(newGo, "Exchange/m_txtNum", UEUI.Text)

            self.candyTitle = GetChild(newGo, "Candy/m_txtTitle", UEUI.Text)
            self.candyBlood = GetChild(newGo, "Candy/Blood")
            self.candyCountDown = GetChild(newGo, "Candy/CountDown", UEUI.Text)

            self.blueTitle = GetChild(newGo, "BlueBuild/m_txtTitle", UEUI.Text)
            self.blueBlood = GetChild(newGo, "BlueBuild/Blood")
            self.blueCountDown = GetChild(newGo, "BlueBuild/CountDown", UEUI.Text)

            self.m_txtTitleH = GetChild(newGo, "Harvest/m_txtTitleH", UEUI.Text)
            self.m_harvestBlood = GetChild(newGo, "Harvest/Blood")
            self.m_txtCDInfo = GetChild(newGo, "Harvest/CDBG/m_txtCDInfo", UEUI.Text)
            self.m_txtCD = GetChild(newGo, "Harvest/CDBG/m_txtCD", UEUI.Text)

            --TD Add Chirstmasitem in CD, Click Show UI
            self.harvestGradeTitle = GetChild(newGo, "HarvestGrade/m_txtTitle", UEUI.Text)
            self.harvestGradeBlood = GetChild(newGo, "HarvestGrade/Blood")
            self.harvestGradeCountDown = GetChild(newGo, "HarvestGrade/CountDown", UEUI.Text)

            self.m_txtPBCDInfo = GetChild(newGo, "PowerBuild/CDBG/m_txtCDInfo", UEUI.Text)
            self.m_txtPBCD = GetChild(newGo, "PowerBuild/CDBG/m_txtCD", UEUI.Text)
            self.m_RPB = GetChild(newGo, "PowerBuild/CDBG/m_goCDinfo", UE.RectTransform)
            self.activityUI = GetChild(newGo, "PowerBuild/CDBG/activityUI", UE.RectTransform)
            self.m_clock = GetChild(newGo, "PowerBuild/CDBG/clock", UE.RectTransform)

            self.m_txtPRBCDInfo = GetChild(newGo, "PowerRedBuild/CDBG/m_txtCDInfo", UEUI.Text)
            self.m_txtPRBCD = GetChild(newGo, "PowerRedBuild/CDBG/m_txtCD", UEUI.Text)
            self.m_RPRB = GetChild(newGo, "PowerRedBuild/CDBG/m_goCDinfo", UE.RectTransform)

            self.m_txtUseTitle0 = GetChild(newGo, "UseItem/txtTitle0", UEUI.Text)
            self.m_txtUseTitle1 = GetChild(newGo, "UseItem/txtTitle1", UEUI.Text)
            self.m_txtUseTime = GetChild(newGo, "UseItem/txtTitle0/clock/txt", UEUI.Text)
            self.m_imgUsePro = GetChild(newGo, "UseItem/txtTitle0/progress/img", UEUI.Image)
            self.m_imgUseIcon = GetChild(newGo, "UseItem/m_btnDo/icon", UEUI.Image)
            self.m_txtUseNum = GetChild(newGo, "UseItem/m_btnDo/num", UEUI.Text)

            --TD —— Animal Click By State To Show UI
            self.animalZooUITitle = GetChild(newGo, "AnimalZooUI/m_txtTitle", UEUI.Text)
            self.animalZooUIObjFeed = GetChild(newGo, "AnimalZooUI/m_btnFeed/objFeed")
            self.animalZooUIObjFoodIcon = GetChild(newGo, "AnimalZooUI/m_btnFeed/objFeed/m_imgFood")
            self.animalZooUIObjSend = GetChild(newGo, "AnimalZooUI/m_btnFeed/objSend")
            self.animalZooUIObjSendNameTxt = GetChild(newGo, "AnimalZooUI/m_btnFeed/objSend/bg/contentBg/m_txtName", UEUI.Text)
            self.animalZooUIObjSendAtkTxt = GetChild(newGo, "AnimalZooUI/m_btnFeed/objSend/bg/contentBg/attrSp1/m_txtAtk", UEUI.Text)
            self.animalZooUIObjSendLifeTxt = GetChild(newGo, "AnimalZooUI/m_btnFeed/objSend/bg/contentBg/attrSp2/m_txtLife", UEUI.Text)
            self.animalZooUIObjSendDefTxt = GetChild(newGo, "AnimalZooUI/m_btnFeed/objSend/bg/contentBg/attrSp3/m_txtDef", UEUI.Text)

            ---沙漠副本新增弹窗
            self.desertCollectNum = GetChild(newGo, "DesertCollect/m_txtNum", UEUI.Text)
            self.desertCollectImg = GetChild(newGo, "DesertCollect/m_txtNum/m_imgType", UEUI.Image)

            self.desertSearchTitle = GetChild(newGo, "DesertSearch/m_txtTitle", UEUI.Text)

            self.desertGetTitle = GetChild(newGo, "DesertGet/m_txtTitle", UEUI.Text)
            self.desertGetEnergy = GetChild(newGo, "DesertGet/m_txtNum", UEUI.Text)
            self.desertGetEnergyImg = GetChild(newGo, "DesertGet/m_txtNum/m_imgType", UEUI.Image)
            self.desertGetImg1 = GetChild(newGo, "DesertGet/img1", UEUI.Image)
            self.desertGetImg2 = GetChild(newGo, "DesertGet/img2", UEUI.Image)
            self.desertGetImg3 = GetChild(newGo, "DesertGet/img3", UEUI.Image)

            self.desertBoomBlood = GetChild(newGo, "DesertBoom/Blood")
            self.desertBoomImg1 = GetChild(newGo, "DesertBoom/img1")
            self.desertBoomImg2 = GetChild(newGo, "DesertBoom/img2")
            self.desertBoomInfo = GetChild(newGo, "DesertBoom/txtInfo",UEUI.Text)
            
            self.fbDots = GetChild(newGo, "FbNotTime/fbDots")
            self.fbDotsIcon = GetChild(newGo, "FbNotTime/fbDots/fbDotsIcon", UEUI.Image)
            self.fbDotsTxt = GetChild(newGo, "FbNotTime/fbDots/fbDotsIcon/txt", UEUI.Text)
            self.fbNoTimeEnergy = GetChild(newGo, "FbNotTime/m_btnDo/m_btnExpend", UEUI.Text)
            self.fbItem1 = GetChild(newGo, "FbNotTime/itemlist/fbItem1")
            self.fbItem2 = GetChild(newGo, "FbNotTime/itemlist/fbItem2")
            self.fbItem3 = GetChild(newGo, "FbNotTime/itemlist/fbItem3")
            self.fbNoTimeImage = GetChild(newGo, "FbNotTime/m_btnDo/m_btnExpend/m_imgType", UEUI.Image)

            self.doubleOreTipGo = GetChild(newGo, "NotTime/m_btnDoubleOreTip")
            self.doubleOreTipGo2 = GetChild(newGo, "Time/m_btnDoubleOreTip2")

            self.halloweenOreEnergy = GetChild(newGo, "HalloweenOre/m_btnDo/m_txtNum", UEUI.Text)
            self.halloweenOreBlood = GetChild(newGo, "HalloweenOre/Blood")
            self.halloweenOreImage = GetChild(newGo, "HalloweenOre/m_btnDo/m_txtNum/m_imgType", UEUI.Image)
            self.halloweenOreItem1 = GetChild(newGo, "HalloweenOre/itemlist/Item1")
            self.halloweenOreItem2 = GetChild(newGo, "HalloweenOre/itemlist/Item2")
            self.halloweenOreItem3 = GetChild(newGo, "HalloweenOre/itemlist/Item3")
            
            self.NormalShowItemEnergy = GetChild(newGo, "NormalShowItem/m_btnDo/m_txtNum", UEUI.Text)
            self.NormalShowItemBlood = GetChild(newGo, "NormalShowItem/Blood")
            self.NormalShowItemImage = GetChild(newGo, "NormalShowItem/m_btnDo/m_txtNum/m_imgType", UEUI.Image)
            self.NormalShowItemItem1 = GetChild(newGo, "NormalShowItem/itemlist/Item1")
            self.NormalShowItemItem2 = GetChild(newGo, "NormalShowItem/itemlist/Item2")
            self.NormalShowItemItem3 = GetChild(newGo, "NormalShowItem/itemlist/Item3")
            
            self.SkiingMatchItemEnergy = GetChild(newGo, "SkiingMatchItem/m_btnDo/m_txtNum", UEUI.Text)
            self.SkiingMatchItemBlood = GetChild(newGo, "SkiingMatchItem/Blood")
            self.SkiingMatchItemImage = GetChild(newGo, "SkiingMatchItem/m_btnDo/m_txtNum/m_imgType", UEUI.Image)
            self.SkiingMatchItemItem1 = GetChild(newGo, "SkiingMatchItem/itemlist/Item1")
            self.SkiingMatchItemItem2 = GetChild(newGo, "SkiingMatchItem/itemlist/Item2")
            self.SkiingMatchItemItem3 = GetChild(newGo, "SkiingMatchItem/itemlist/Item3")

            self.multipleDropItemEnergy = GetChild(newGo, "MultipleDropItem/m_btnExpend/m_txtNum", UEUI.Text)
            self.multipleDropItemBlood = GetChild(newGo, "MultipleDropItem/Blood")
            self.multipleDropItemImage = GetChild(newGo, "MultipleDropItem/m_btnExpend/m_txtNum/m_imgType", UEUI.Image)
            self.multipleDropNum = GetChild(newGo, "MultipleDropItem/m_btnExpend/m_imgMultipleNum", UEUI.Image)
            self.multipleDropTimeBg = GetChild(newGo, "MultipleDropItem/m_goTimeBg")
            self.multipleDropTimeTxt = GetChild(newGo, "MultipleDropItem/m_goTimeBg/m_txtTime", UEUI.Text)
            self.multipleDropTimeCountDownBg = GetChild(newGo, "MultipleDropItem/CountDownBg1")
            self.multipleDropTimeCountDown = GetChild(newGo, "MultipleDropItem/CountDownBg1/CountDown", UEUI.Text)

            self.multipleDropTimeBuffCountDown = GetChild(newGo, "MultipleDropItem/BuffCountDown")
            self.multipleDropTimeBuffOldTxt = GetChild(newGo, "MultipleDropItem/BuffCountDown/oldCount", UEUI.Text)
            self.multipleDropTimeBuffNewTxt = GetChild(newGo, "MultipleDropItem/BuffCountDown/newCount", UEUI.Text)
            
            InitTextLanguage(newGo)
            if callback then
                callback()
            end
        end)
    else
      if callback then
            callback()
        end
    end
end

function M:SetPos(mess, isRePos)
    local x, y = mess.m_Vt2Pos.x, mess.m_Vt2Pos.y + 1.3
    if mess.GetClearUIPos then
        x, y = mess:GetClearUIPos()
    end
    if not isRePos then
        SetUIPos(self.go, x, y)
    end

    MapController:SetFocusInView(x, y, 250, self.height)
end

function M:IsOpenOtherView()
	local open = false
	if IsNil(self.child) then
		return false
	end
	for k, v in pairs(self.child) do
		if IsNil(v) then
			return false
		end
		if v.gameObject.activeSelf then
			open = true
			break
		end
	end
	return open
end

function M:OpenView(type, mess, callback)
    self.type = type
    self.mess = mess
    self.call = callback

    EventMgr:Dispatch(EventID.MAP_CLEAR_SHOW, self.mess.m_GridX, self.mess.m_GridY)

    --开启界面时隐藏部分UI
    if mess.SetHideUI then
        mess:SetHideUI()
    end

    SetUILastSibling(self.go)

    --先打开UI
    self.show = true
    SetActive(self.child[type], true)
	local rect = GetComponent(self.child[type],UE.RectTransform)
	if rect then
		self.height = rect.sizeDelta.y + 50
	end
	
	self.height = Mathf.Clamp(self.height,150,400)
	self:SetPos(mess)
    local item_id = GlobalConfig:GetNumber(1507)
    SetActive(self.doubleOreTipGo,item_id == self.mess.m_Id)
	SetActive(self.doubleOreTipGo2,item_id == self.mess.m_Id)
    local hp, maxHp
    if mess.GetBloodState then
        hp, maxHp = mess:GetBloodState()
    end
    local useStr = mess.m_Config.id_use4
    local energy
    local consume
    if mess.GetEnergyOutPut then
        energy,consume = mess:GetEnergyOutPut()
    end
	
	local nowenergy
	
	local mapId = NetUpdatePlayerData.playerInfo.curMap
	if IsHomeMap(mapId) then
		nowenergy=NetUpdatePlayerData:GetResourceNumByID(ItemID.ENERGY)
	else
		nowenergy=NetUpdatePlayerData:GetResourceNumByID(ItemID.LimitEnergy)
	end
	local use = 0
	if energy ~= nil and (not consume or consume == ItemID.ENERGY or consume == ItemID.LimitEnergy )  then
		use=tonumber(energy)
	end
	local EnergyColor=Color.New(1,1,1,1)
	if use>nowenergy then
		EnergyColor=Color.New(1,0,0,1)
	else
		EnergyColor=Color.New(1,1,1,1)
	end
	if mapId==MAP_ID_ZOO then
		EnergyColor=Color.New(1,1,1,1)
	end
    local time
    local configTime
    local speed
    if mess.GetCountTime then
        time, speed = mess:GetCountTime()
    end
    if mess.GetConfigTime then
        configTime = mess:GetConfigTime()
    end
    if not configTime then
        configTime = time
    end

    local desertMess
    if mess.GetDesertMss then
        desertMess = mess:GetDesertMss()
    end
    --其他副本获取体力图片
    function GetConsumeImage()
        local energyImage
        if consume then
            energyImage = ItemConfig:GetIcon(consume)
        else
            local mapId = NetUpdatePlayerData.playerInfo.curMap
            --if mapId ~= MAP_ID_MAIN then
                local energyId = BackgroundConfig:GetEnergyByMapId(mapId)
                energyImage = ItemConfig:GetIcon(energyId)
            --end
        end
        return energyImage
    end

    if type == ClearType.NotTime then
	    self.blood = AddBlood(maxHp, self.noTimeBlood, hp)
		
		--local limitEnergy =info[PlayerDefine.LimitEnergy]
		--local str = 
		--if v2n(limitEnergy) < v2n(energy) then
			--str = string.format("<color=#FF0000>%s</color>", str)
		--end
		self.noTimeEnergy.text = energy
		self.noTimeEnergy.color=EnergyColor
		
	    local energyImage = GetConsumeImage()
	    if energyImage then
	    	SetImageSprite(self.noTimeImage,energyImage,false)
	    end
		
	elseif type == ClearType.FbNotTime then
		
		local isOpen,active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.LimitRank)
		if isOpen then
			local curmap = NetUpdatePlayerData.playerInfo.curMap
			if active.info.mapId == curmap then
				local spr = "Sprite/ui_activity/icon_fb_phbjf_1.png"
				SetImageSprite(self.fbDotsIcon,spr,false)		
			end	
		end
		SetActive(self.fbDots,isOpen)
		
		local energyImage = GetConsumeImage()
		if energyImage then
			SetImageSprite(self.fbNoTimeImage,energyImage,false)
		end
		local tools = DropController.new()
		if next(mess.drops) ~= nil then
			
			local drop_list = tools:GetBoxUIList(mess.drops[1])
			local count = #drop_list
			for i = 1,3 do
				if i <= count then
					local image = GetChild(self["fbItem"..i], "icon", UEUI.Image)
					
					local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,v2n(drop_list[i]))
					SetImageSprite(image,config.icon_b,false)
				end
			local show = count >= i
			SetActive(self["fbItem"..i],show)
			end		
		end
		local info = NetUpdatePlayerData:GetPlayerInfo()
		local limitEnergy =info[PlayerDefine.LimitEnergy] 
		local str = energy
		if v2n(limitEnergy) < v2n(energy) then
			str = string.format("<color=#FF0000>%s</color>", str)
		end
		self.fbNoTimeEnergy.text = str
		self.fbDotsTxt.text = "+ "..energy
		
    elseif type == ClearType.Time then
        self.blood = AddBlood(maxHp, self.timeBlood, hp)
        self.timeEnergy.text = energy
		self.timeEnergy.color=EnergyColor
        self:SetBuffTimeView(type,configTime,time)
        
        local energyImage = GetConsumeImage()
        if energyImage then
            SetImageSprite(self.timeImage, energyImage,false)
        end
    elseif type == ClearType.Build then
		SetActive(self.buildGo,time >0)
        self.buildCountDown.text = TimeMgr:GetHMS(time)

        --Chanage Pic
        if not useStr then
            SetActive(self.buildExpendGo, true)
            SetActive(self.buildLimitGo, false)
			
			--第一次建造引导
			--if NetGlobalData:IsFirstBuild() then
				--NetGlobalData:SetFirstBuild(true)
				--MapController:SetTouchLockState(1)
				--UI_SHOW(UIDefine.UI_MapBlockWall,mess,1)
				--local func = self.call
				--self.call = function(go)
					
					--if func then func(go) end
					
					--UI_SHOW(UIDefine.UI_BuildIntro)
					--UI_CLOSE(UIDefine.UI_MapBlockWall)
				--end
			--end
        else
            SetActive(self.buildExpendGo, false)
            SetActive(self.buildLimitGo, true)
            local arrSp = string.split(useStr, "|")
            SetImageSprite(self.buildLimitImage, ItemConfig:GetIcon(tonumber(arrSp[1])))
            self.buildLimitConsume.text = tostring(arrSp[2])
        end
    elseif type == ClearType.RedBuild then
        self.redBuildCountDown.text = TimeMgr:GetHMS(time)
    elseif type == ClearType.Collect then
        self.ui_collect_energy.text = energy
		self.ui_collect_energy.color=EnergyColor
        local energyImage = GetConsumeImage()
        if energyImage then
            SetImageSprite(self.collect_energyImage, energyImage,false)
        end
		
	elseif type == ClearType.Castle then
		self.castle_collect_energy.text = energy
		self.castle_collect_energy.color=EnergyColor
		local energyImage = GetConsumeImage()
		if energyImage then
			SetImageSprite(self.castle_energyImage, energyImage,false)
		end
		local objCastle = self.mess
		local isLock9Lv = (NetUpdatePlayerData:GetLevel() < GlobalConfig.LEVEL_OPEN_CASTLE_GIFT)
		local function setUIIconB(strItemInf, trans, isLock)
			if not trans then
				return
			end
			local arrInf = string.split(strItemInf, ',')
			local imgIcon = GET_UI_CHILD(trans, 0, "Image")
			local txt = GET_UI_CHILD(trans, 1, "Text")

			local config = ItemConfig:GetDataByID(tonumber(arrInf[1]))
			if config then
				SetUIImage(imgIcon, config["icon_b"], false)
			end
			local imgque = GetChild(trans,  "Image",UEUI.Image)

			local rarity = ItemConfig:GetZooTypeByItemID(tonumber(arrInf[1]), "rarity")
			local zooRarityConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.zoo_rarity, rarity)
			SetUIImage(imgque, zooRarityConfig["rarity_icon"], false)


			if isLock then
				SetUIImageGray(imgIcon, true)
				SetUIImageGray(trans, true)
				imgIcon.color = Color.New(1, 1, 1, 0.5)
				SetActive(txt, false)
			else
				SetUIImageGray(imgIcon, false)
				SetUIImageGray(trans, false)

				if arrInf[2] == "0" then
					SetActive(GetChild(trans, "btnLockStar"), true)
					SetActive(txt, false)
					imgIcon.color = Color.New(1, 1, 1, 0.5)
				else
					SetActive(GetChild(trans, "btnLockStar"), false)
					imgIcon.color = Color.New(1, 1, 1, 1)
					txt.text = "x" .. arrInf[2]
					SetActive(txt, true)
				end
			end
		end
		
		setUIIconB(objCastle.m_CurInf.show_icon_inf[1],self.castle_icon1,isLock9Lv)
		setUIIconB(objCastle.m_CurInf.show_icon_inf[2],self.castle_icon2,isLock9Lv)
		--for i = 1, 2 do
			
			----self:setUIIconB(objCastle.m_CurInf.show_icon_inf[i], self.["castle_icon" .. i],isLock9Lv)
			--Log.Error(objCastle.m_CurInf.show_icon_inf[i])
		--end
		
		
		
    elseif type == ClearType.Exchange then
        if mess.GetClearInfo then
            local inf = mess:GetClearInfo()
            if inf then
                local config = ItemConfig:GetDataByID(inf.id)
                if config then
                    SetUIImage(self.exchangeIcon, config["icon_ss"] or config["icon_b"])
                end
                self.exchangeNum.text = inf.num
            end
        end
    elseif type == ClearType.Candy then
        self.blood = AddBlood(maxHp, self.candyBlood, hp)
        self.candyTitle.text = mess:GetFieldName()
        TimeMgr:CreateTimer(TimerId, function()
            local nowTime = mess:CheckFieldTime()
            if nowTime <= 0 then
                self:CheckClose()
            else
                self.candyCountDown.text = TimeMgr:GetHMS(nowTime)
            end

        end, 0.1)
    elseif type == ClearType.BlueBuild then
        self.blood = AddBlood(maxHp, self.blueBlood, hp)
        self.blueTitle.text = mess:GetFieldName()
        TimeMgr:CreateTimer(TimerId, function()
            local nowTime = mess:CheckFieldTime()
            if nowTime <= 0 then
                self:CheckClose()
            else
                self.blueCountDown.text = TimeMgr:GetHMS(nowTime)
            end
        end, 0.1)
    elseif type == ClearType.Harvest then
        self.blood = AddBlood(maxHp, self.m_harvestBlood, hp)
        self.m_txtTitleH.text = mess:GetFieldName()
        local speed = mess:GetSpeed()
        if speed then
            self.m_txtCDInfo.text = string.format(LangMgr:GetLang(7063), speed)
        end
        TimeMgr:CreateTimer(TimerId, function()
            local nowTime = mess:CheckFieldTime()
            if nowTime <= 0 then
                self:CheckClose()
            else
                self.m_txtCD.text = TimeMgr:GetHMS(nowTime)
            end
        end, 1)
    elseif type == ClearType.PowerBuild then
        if NetSeasonActivity:GetIsPay() and NetSeasonActivity:GetItemIsAddSpeed(mess.m_Id) then
            SetActive(self.activityUI ,true) 
            SetActive(self.m_clock,false)
        else 
            SetActive(self.activityUI ,false) 
            SetActive(self.m_clock,true)
        end
        
        if speed then
            --self.m_txtPBCDInfo.text = string.format(LangMgr:GetLang(7063), speed)
			self.m_txtPBCDInfo.text = TimeMgr:GetHMS(time*100/(100-speed))
		end
        self.m_txtPBCD.text = TimeMgr:GetHMS(time)

    elseif type == ClearType.PowerRedBuild then
        if speed then
            --self.m_txtPRBCDInfo.text = string.format(LangMgr:GetLang(7063), speed)
			self.m_txtPRBCDInfo.text = TimeMgr:GetHMS(time*100/(100-speed))
        end
        self.m_txtPRBCD.text = TimeMgr:GetHMS(time)

    elseif type == ClearType.UseItem then
        if mess.GetItemUseInf then
            local inf = mess:GetItemUseInf()
            if inf then
                local ts = inf.timeEnd
                if ts then
                    SetActive(self.m_txtUseTitle0, true)
                    SetActive(self.m_txtUseTitle1, false)

                    self.m_txtUseTitle0.text = LangMgr:GetLang(inf.lang)

                    local function onRefreshTime()
                        local lt = ts - TimeMgr:GetServerTime()
                        if lt <= 0 then
                            self:CheckClose()
                        else
                            self.m_txtUseTime.text = TimeMgr:GetHMS(lt)
                            self.m_imgUsePro.fillAmount = (lt / inf.timeMax)
                        end
                    end
                    TimeMgr:CreateTimer(TimerId, onRefreshTime, 1)
                else
                    SetActive(self.m_txtUseTitle0, false)
                    SetActive(self.m_txtUseTitle1, true)

                    self.m_txtUseTitle1.text = LangMgr:GetLang(inf.lang)
                end

                local config = ItemConfig:GetDataByID(inf.id)
                if config then
                    SetUIImage(self.m_imgUseIcon, config["icon_ss"] or config["icon_b"])
                end
                self.m_txtUseNum.text = inf.num
            end
        end
    elseif type == ClearType.HarvestGrade then--TD Add Chirstmasitem in CD, Click Show UI
        local function AddSimpleProgress(blood, parent, nowBlood)
            --初始化物体
            local pre
            pre = ResMgr:LoadAssetSync("Prefab/Map/SimpleProg.prefab", AssetDefine.LoadType.Instant)

            local item
            if parent then
                item = CreateGameObjectWithParent(pre, parent.transform)
            else
                item = CreateGameObject(pre)
                MapController:AddUIToWorld(item.transform)
            end
            --设置血量
            local text = GetChild(item, "max/Text", UEUI.Text)
            text.text = LangMgr:GetLangFormat(7090, nowBlood, blood)

            return item
        end
        self.blood = AddSimpleProgress(maxHp, self.harvestGradeBlood, hp)
        self.harvestGradeTitle.text = mess:GetFieldName()
        TimeMgr:CreateTimer(TimerId, function()
            local nowTime = mess:CheckFieldTime()
            if nowTime <= 0 then
                self:CheckClose()
            else
                self.harvestGradeCountDown.text = TimeMgr:GetHMS(nowTime)
            end

        end, 0.1)
    elseif type == ClearType.AnimalZooUI then--TD Add Animal Hungry, Click Show UI
        if mess:IsHungry() then
            self.animalZooUITitle.text = LangMgr:GetLang(7004)
            SetActive(self.animalZooUIObjFeed, true)
            local foodConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, mess.m_iId_use3)
            SetUIImage(self.animalZooUIObjFoodIcon, foodConfig.img, false)
            SetActive(self.animalZooUIObjSend, false)
        else
            self.animalZooUITitle.text = LangMgr:GetLang(7005)
            local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_hero_animal, mess.m_Id);
            if config then
                self.animalZooUIObjSendNameTxt.text = ItemConfig:GetLangByID(mess.m_Id);
                self.animalZooUIObjSendAtkTxt.text = config.atk;
                self.animalZooUIObjSendLifeTxt.text = config.life;
                self.animalZooUIObjSendDefTxt.text = config.defense;
            end
            SetActive(self.animalZooUIObjFeed, false)
            SetActive(self.animalZooUIObjSend, true)
        end
    elseif type == ClearType.DesertSearch then
        self.desertSearchTitle.text = LangMgr:GetLang(desertMess[1])
    elseif type == ClearType.DesertGet then
        self.desertGetTitle.text = LangMgr:GetLang(desertMess[1])
        self.desertGetEnergy.text = energy
		self.desertGetEnergy.color=EnergyColor
        local energyImage = GetConsumeImage()
        if energyImage then
            SetImageSprite(self.desertGetEnergyImg,energyImage,false)
        end
        local img1 = ItemConfig:GetIcon(desertMess[2])
        local img2 = ItemConfig:GetIcon(desertMess[3])
        local img3 = ItemConfig:GetIcon(desertMess[4])
        if img1 then
            SetImageSprite(self.desertGetImg1, img1,false)
        end
        if img2 then
            SetImageSprite(self.desertGetImg2,img2,false)
        end
        if img3 then
            SetImageSprite(self.desertGetImg3,img3,false)
        end
    elseif type == ClearType.DesertBoom then
        self.blood = AddBlood(maxHp, self.desertBoomBlood, hp)
        local strList = string.split(LangMgr:GetLang(7006),"|")
        self.desertBoomInfo.text = strList[1]
		local pos
		if strList[2]~=nil then
        	pos = string.split(strList[2],",",nil,nil,1)
        	SetUIPos(self.desertBoomImg1,pos[1],pos[2])
		end
		if strList[3]~=nil then
        	pos = string.split(strList[3],",",nil,nil,1)
        	SetUIPos(self.desertBoomImg2,pos[1],pos[2])
		end
    elseif type == ClearType.DesertCollect then
        self.desertCollectNum.text = mess.m_Config.id_use2
        SetImageSprite(self.desertCollectImg,ItemConfig:GetIcon(mess.m_Config.id_use),false)
    elseif type == ClearType.HalloweenOre then
        self.blood = AddBlood(maxHp, self.halloweenOreBlood, hp)
        self.halloweenOreEnergy.text = energy
        self.halloweenOreEnergy.color = EnergyColor
        local energyImage = GetConsumeImage()
        if energyImage then
            SetImageSprite(self.halloweenOreImage,energyImage,false)
        end
        
        local tools = DropController.new()
        if next(mess.drops) ~= nil then
            local drop_list = tools:GetBoxUIList(mess.drops[1])
            local count = #drop_list
            for i = 1,3 do
                if i <= count then
                    local image = GetChild(self["halloweenOreItem"..i], "icon", UEUI.Image)
                    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,v2n(drop_list[i]))
                    SetImageSprite(image,config.icon_b,false)
                end
                local show = count >= i
                SetActive(self["halloweenOreItem"..i],show)
            end
        end    
    elseif type == ClearType.NormalShowItem then
        self.blood = AddBlood(maxHp, self.NormalShowItemBlood, hp)
        self.NormalShowItemEnergy.text = energy
        self.NormalShowItemEnergy.color = EnergyColor
        local energyImage = GetConsumeImage()
        if energyImage then
            SetImageSprite(self.NormalShowItemImage,energyImage,false)
        end
        
        local tools = DropController.new()
        if next(mess.drops) ~= nil then
            local drop_list = tools:GetBoxUIList(mess.drops[1])
            local count = #drop_list
            for i = 1,3 do
                if i <= count then
                    local image = GetChild(self["NormalShowItemItem"..i], "icon", UEUI.Image)
                    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,v2n(drop_list[i]))
                    SetImageSprite(image,config.icon_b,false)
                end
                local show = count >= i
                SetActive(self["NormalShowItemItem"..i],show)
            end
        end
	elseif type == ClearType.SkiingMatchItem then
		self.blood = AddBlood(maxHp, self.SkiingMatchItemBlood, hp)
		self.SkiingMatchItemEnergy.text = energy
		self.SkiingMatchItemEnergy.color = EnergyColor
		local energyImage = GetConsumeImage()
		if energyImage then
			SetImageSprite(self.SkiingMatchItemImage,energyImage,false)
		end
		
		local tools = DropController.new()
		if next(mess.drops) ~= nil then
			local drop_list = tools:GetBoxUIList(mess.drops[1])
			local count = #drop_list
			for i = 1,3 do
				if i <= count then
					local image = GetChild(self["SkiingMatchItemItem"..i], "icon", UEUI.Image)
					local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,v2n(drop_list[i]))
					SetImageSprite(image,config.icon_b,false)
				end
				local show = count >= i
				SetActive(self["SkiingMatchItemItem"..i],show)
			end
		end
    elseif type == ClearType.MultipleDropItem then
        self.blood = AddBlood(maxHp, self.multipleDropItemBlood, hp)
        self.multipleDropItemEnergy.text = energy
        self.multipleDropItemEnergy.color = EnergyColor
        
        local needShowTime = (hp == 1 and time ~= nil)
        SetActive(self.multipleDropTimeCountDownBg,needShowTime)
        local buffTimeGo = GetChild(self.go, "MultipleDropItem/BuffCountDown")
        SetActive(buffTimeGo,false)
        if needShowTime then
            self:SetBuffTimeView(type,configTime,time)
        end
        
        local energyImage = GetConsumeImage()
        if energyImage then
            SetImageSprite(self.multipleDropItemImage,energyImage,false)
        end
        local curSelectMultiple = NetMulitpleDropData:GetDataByKey("curSelectMultiple") or 1
        local assetPath
        local cur_time = 0
        if curSelectMultiple == MultipleNum.ONE then
        elseif curSelectMultiple == MultipleNum.THREE then
            cur_time = NetMulitpleDropData:GetDataByKey("multipleItemTime3") or 0
            assetPath = "Assets/ResPackage/Sprite/ui_duobeicaiji/duobei_mapicon_x3.png"
        elseif curSelectMultiple == MultipleNum.NINE then
            cur_time = NetMulitpleDropData:GetDataByKey("multipleItemTime9") or 0
            assetPath = "Assets/ResPackage/Sprite/ui_duobeicaiji/duobei_mapicon_x9.png"
        elseif curSelectMultiple == MultipleNum.TWENTY_SEVEN then
            cur_time = NetMulitpleDropData:GetDataByKey("multipleItemTime27") or 0
            assetPath = "Assets/ResPackage/Sprite/ui_duobeicaiji/duobei_mapicon_x27.png"
        end
        if assetPath then
            SetActive(self.multipleDropNum,true)
            SetImageSprite(self.multipleDropNum,assetPath,false)
        else
            SetActive(self.multipleDropNum,false)
        end
        --TimeMgr:CreateTimer(TimerId, function()
        --    local timeNow = TimeMgr:GetServerTime()
        --    local leftTime = cur_time - timeNow
        --    if leftTime <= 0 then
        --        if curSelectMultiple == MultipleNum.THREE or curSelectMultiple == MultipleNum.NINE or curSelectMultiple == MultipleNum.TWENTY_SEVEN then
        --            self:CheckClose()
        --        end
        --        SetActive(self.multipleDropTimeBg,false)
        --    else
        --        SetActive(self.multipleDropTimeBg,true)
        --        self.multipleDropTimeTxt.text = TimeMgr:GetHMS(leftTime)
        --    end
        --end, 0.1)
    end
	
    DOScale(self.trans, 0.008, 0.008, nil, Ease.OutBack)
end

--处理移除建筑buff加成显示
function M:SetBuffTimeView(clearType,configTime,buffTime)
    local newGo = self.go
    local hasBuff = configTime and buffTime < configTime
    if clearType == ClearType.MultipleDropItem then
        local buffTimeGo = GetChild(newGo, "MultipleDropItem/BuffCountDown")
        local oldTxt = GetChild(newGo, "MultipleDropItem/BuffCountDown/oldCount", UEUI.Text)
        local newTxt = GetChild(newGo, "MultipleDropItem/BuffCountDown/newCount", UEUI.Text)
        
        SetActive(buffTimeGo,hasBuff)
        SetActive(self.multipleDropTimeCountDownBg,not hasBuff)
        if hasBuff then
            oldTxt.text = TimeMgr:GetHMS(configTime)
            newTxt.text = TimeMgr:GetHMS(buffTime)
        else
            self.multipleDropTimeCountDown.text = TimeMgr:GetHMS(buffTime)
        end
    elseif clearType == ClearType.Time then
        local buffTimeGo = GetChild(newGo, "Time/BuffCountDown")
        local oldTxt = GetChild(newGo, "Time/BuffCountDown/oldCount", UEUI.Text)
        local newTxt = GetChild(newGo, "Time/BuffCountDown/newCount", UEUI.Text)
        SetActive(buffTimeGo,false)
        SetActive(buffTimeGo,hasBuff)
        SetActive(self.timeCountDown,not hasBuff)
        if hasBuff then
            oldTxt.text = TimeMgr:GetHMS(configTime)
            newTxt.text = TimeMgr:GetHMS(buffTime)
        else
            self.timeCountDown.text = TimeMgr:GetHMS(buffTime)
        end
    end
    
end

--关闭界面，type不为空时，自动打开新的界面
function M:CheckClose()
    if self.show then
        self.show = false
        self.call = nil
        if self.blood then
            UEGO.Destroy(self.blood)
            self.blood = nil
        end
        TimeMgr:DestroyTimer(TimerId)
       -- local function onScaleClose()
            --关闭是显示隐藏UI
        if self.last then
            if self.last["SetShowUI"] then
                self.last:SetShowUI()
                self.last = nil
            end
		end
		if self.mess then
			if self.mess["SetShowUI"] then
				self.mess:SetShowUI()
			end
			if self.mess["CloseClear"] then
				self.mess:CloseClear()
			end
        end
        SetActive(self.child[self.type], false)
		if self.type == 15 then
			UI_UPDATE(UIDefine.UI_ZooMainFace,18)
		end
        self.mess = nil
        self.type = nil
        --end
       -- DOScale(self.trans, 0, 0.05, onScaleClose, Ease.InBack)

        if self.mess and self.mess.onDisappearByCC then
            self.mess:onDisappearByCC()
        end
    end
end

function M:onEventClick(arg1, arg2)
    if arg1.name == "m_btnNormalShowItemTip" then
        UI_SHOW(UIDefine.UI_ActToyTip,NetActToyData.data.activityId)
        return
    end
	if arg1.name == "m_btnSkiingMatchItemTip" then
		UI_SHOW(UIDefine.UI_SkiingMatchHelp)
		return
	end
    if arg1.name == "m_btnDoubleOreTip" or arg1.name == "m_btnDoubleOreTip2" then
        local open, active = LimitActivityController:GetActiveIsOpen(ActivityTotal.DoubleOre, ActivitySubtype.DoubleOre)
        if open and active then
            UI_SHOW(UIDefine.UI_DoubleOreTips,active.info.activeId)
            return
        end
    end
    if arg1.name == "m_btnMultipleTip" then
        UI_SHOW(UIDefine.UI_MultipleDropView)
        return
    end
    if self.call then
        self.call(arg1)
        self:CheckClose()
    else
        Log.Info("not have click")
    end
end

function M:AddButtonCallBack(btn)
    local function onClickTemp(arg1, arg2)
        self:onEventClick(arg1, arg2)
    end
    AddUIComponentEventCallback(btn, UEUI.Button, onClickTemp)
end

function M:GetMessGrid()
    if self.mess then
        return self.mess.m_GridX, self.mess.m_GridY
    end
    return nil
end

function M:Clear()
    if self.go then
		TimeMgr:DestroyTimer(TimerId)
        UEGO.Destroy(self.go)
        self.go = nil
    end
    self.show = false
	self.child = nil
end

return M