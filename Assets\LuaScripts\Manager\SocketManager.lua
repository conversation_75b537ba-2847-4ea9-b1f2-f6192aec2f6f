local SocketManager = Class()

local DEF_HEART_TICK_COUNT = 30
local MAX_SOCKET_RECONNECT_TIME = 3
local MAX_SOCKET_RECONNECT_COUNT = 6 --长连接重连次数限s制
local SOCKET_TIMER = "SOCKET_TIMER"

local SocketEventEnum = {
    NetworkEvent_None = 0,

    NetworkEvent_StartConnect = 1, --发起连接
    NetworkEvent_Disconnect = 2, --断开连接
    NetworkEvent_SendMessage = 3, --发送消息

    NetworkEvent_ConnectOK = 4, --发起连接成功
    NetworkEvent_ConnectFail = 5, --发起连接失败
    NetworkEvent_ConnectionLost = 6, --连接断开
    NetworkEvent_ConnectionError = 7, --连接发生错误
    NetworkEvent_ReceivedMessage = 8, --收到一条消息
    NetworkEvent_SocketClosed = 9, --socket已经关闭
}

function SocketManager:ctor()
    self.m_gameConnID = 0
    self.m_socketTimer = 0
    self.m_isSocketLogin = false
    self.m_isReconnecting = false
    self.m_isKicked = false
    self.m_TickHeartBeat = 0
    self.m_socketReConnectCount = 0
	self.m_isKickedSave = false
	
	self.m_AllSocketReConnectCount = 0 -- 计算玩家最大断了多少次。
end

function SocketManager:IsOnLine()
    return (self.m_isSocketLogin and not self.m_isKicked)
end

function SocketManager:IsKickedSave()
	return self.m_isKickedSave
end

function SocketManager:SetKickedSave(value)
	 self.m_isKickedSave = value
end

function SocketManager:Tick(deltaTime)
    if not self.m_IsReady then
        return
    end
    if self.m_isSocketLogin then
        self.m_TickHeartBeat = self.m_TickHeartBeat + deltaTime
        if self.m_TickHeartBeat >= DEF_HEART_TICK_COUNT then
            self:C2S_HeartBeat()
            self.m_TickHeartBeat = 0
        end
    else
        self.m_TickHeartBeat = self.m_TickHeartBeat + deltaTime
        if self.m_TickHeartBeat >= DEF_HEART_TICK_COUNT then
            Log.Info("****** wait reconnect", self.m_socketReConnectCount)
            self.m_TickHeartBeat = 0
        end
    end
end

function SocketManager:C2S_Login()
    local params = {}
    params.token = HttpClient.STR_TOKEN
    params.userId = NetUpdatePlayerData:GetPlayerInfo().id

    local json = Json.encode(params)
    self:SendData(self.m_gameConnID, 1, json)
end

function SocketManager:C2S_HeartBeat ()
    self:SendData(self.m_gameConnID, 2, "{}")
end

function SocketManager:C2S_ACK (msgId, sig)
    local params = {}
    params.id = msgId
    params.sig = sig
    local json = Json.encode(params)
    self:SendData(self.m_gameConnID, 100, json)
end

function SocketManager:IsConnect(connID)
    return NetEventMgr.IsConnected(connID)
end

function SocketManager:Disconnect(connID)
    Log.Info("------ push close: " .. connID)
    NetEventMgr.Disconnect(connID)
end

function SocketManager:GetConnectDetail(connID)
    local data = self.m_connectDict[connID]
    if data == nil then
        Log.Error("###### GetConnectDetail:", connID)
        return "", 0
    end
    return data[2], data[3]
end

function SocketManager:SendData(connID, protoID, binarydata)
    if binarydata == nil then
        Log.Error("###### push send nil")
        return false
    end
    Log.Info("------push send: " .. connID .. " pid: " .. protoID .. " msg: " .. binarydata)
    NetEventMgr.SendMessage(connID, protoID, binarydata)
    return true
end

function SocketManager:OnNetworkEvent(eventType, curConnID, protoID, msg)
    if eventType == SocketEventEnum.NetworkEvent_ReceivedMessage then
        self:HandleMessage(curConnID, protoID, msg)
    else
        Log.Info("****** push state:", eventType)
        if eventType == SocketEventEnum.NetworkEvent_ConnectOK then
            self:OnConnectSuccess(curConnID)

        elseif eventType == SocketEventEnum.NetworkEvent_ConnectFail then
            self:OnConnectFail(curConnID)

        elseif eventType == SocketEventEnum.NetworkEvent_ConnectionError then
            self:OnConnectFail(curConnID)

        elseif eventType == SocketEventEnum.NetworkEvent_ConnectionLost then
            self:OnConnectLost(curConnID)

        elseif eventType == SocketEventEnum.NetworkEvent_SocketClosed then
            self:OnConnectClosed(curConnID)

        end
    end
end

function SocketManager:OnConnectSuccess()
    self:C2S_Login()
    self.m_isReconnecting = false
	--重连以后请求 聊天频道信息
	ChatManager:RequestPlayerChannelInfo()
end

function SocketManager:OnConnectFail(connID)
    self.m_isSocketLogin = false
    self.m_isReconnecting = false
    self:ResetSocket()
    self:TriggerReconnectTimer()

    Log.Warning("###### push fail")
end

function SocketManager:OnConnectLost(connID)
    self.m_isSocketLogin = false
    self.m_isReconnecting = false
    self:ResetSocket()

    Log.Warning("###### push lost")

    if self.m_isKicked then
        Log.Info("###### push kick down")
    else
        self:TriggerReconnectTimer()
    end
end

function SocketManager:OnConnectClosed (connID)
    self.m_isSocketLogin = false
end

function SocketManager:TriggerReconnectTimer()
    local function onReConnect()
        self:ReConnectSocket()
    end
    TimeMgr:CreateTimerSingleOne(self, onReConnect, 5, 1)
end

function SocketManager:ReConnectSocket()
    Log.Info("****** retry push")
    self.m_socketReConnectCount = self.m_socketReConnectCount + 1
    if self.m_socketReConnectCount >= MAX_SOCKET_RECONNECT_COUNT then
        self:ReStartGame()
		self.m_AllSocketReConnectCount = self.m_AllSocketReConnectCount + 1
		local thinkTable = {}
		thinkTable = {
			["b_count"] = self.m_AllSocketReConnectCount
		}
		SdkHelper:ThinkingTrackEvent("Socket_ReConnect", thinkTable)
        return
    end
    self.m_isReconnecting = true
    self:ResetSocket()
    self:ConnectSocket(1)
end

function SocketManager:ConnectSocket(state)
    if not Game.IsUseTCPNet then
        return
    end

    local function _func(eventType, curConnID, protoID, msg)
		local msgStr = ""
		if msg then
			msgStr = GameUtil.BytesToString(msg)
		end
        self:OnNetworkEvent(eventType, curConnID, protoID, msgStr)
        if state == 0 then
            self.m_IsReady = true
        end
    end
    local ip, port = HttpClient:GetSocketInf()
    self.m_gameConnID = NetEventMgr.StartConnect(ip, port, _func)
end

function SocketManager:DisconnectGame()
    if self.m_gameConnID > 0 then
        self:Disconnect(self.m_gameConnID)
        self.m_gameConnID = 0
    end
end

function SocketManager:ResetSocket(reTryTimes)
    self.m_isSocketLogin = false
    self.m_isKicked = false
    self:DisconnectGame()
    if reTryTimes then
        self.m_socketReConnectCount = reTryTimes
        if reTryTimes == 0 then
            self.m_IsReady = false
        end
    end
end

function SocketManager:HandleMessage (curConnID, protoID, strMsg)
    Log.Info("------push msg: ", protoID, v2s(strMsg))
    local objJson = Json.decode(strMsg)
    if objJson == nil then
        return
    end
    local ret = self:OnResultRet(objJson)
    if not ret then
        return
    end
    HttpClient:UpdateNetTime(objJson)
    local result = tonumber(objJson["info"]["result"])
    if result == 0 then
        --正常情况
        if protoID == 100 then
            self:OnPushRet(objJson)
        elseif protoID == 1 then
            self.m_isSocketLogin = true

        elseif protoID == 2 then
        end
    elseif result == 1 then
        --token 校验失败 (可能号被顶了 -,-)
        self.m_isKicked = true
		self.m_isKickedSave = true
        self:ReLoginNeeded()
    end

end

function SocketManager:OnResultRet (objJson)
    local info = objJson["info"]
    if info == nil then
        return false
    end
    local result = v2n(info["result"])
    if result == nil then
        return false
    end

    --if result == 1 then
        ----self:ReLogin()
        --return false
    --else
    --end
    return true
end

function SocketManager:OnPushRet(objJson)

    local data = objJson["data"]
    local info = objJson["info"]
    if data == nil or info == nil then
        --do
        return
    end

    local result = v2n(info["result"])
    if result == nil or result ~= 0 then
        --do
        return
    end

    if result == 0 then
        local packet_uid = data["id"]
        local packet_type = data["type"]
        local packet_addTime = data["addTime"]
        local packet_encTime = data["encTime"]
        local packet_sig = data["sig"]
        local packet_msg = data["msg"]

        if packet_encTime ~= 0 then
            local key = "38^36f" .. tostring(packet_encTime)
            local md5Key = GameUtil.GetMD5(key)
            packet_msg = GameUtil.Base64ToString(packet_msg)
            packet_msg = GameUtil.GetXOR(md5Key, packet_msg)
        end
		
		if packet_type == 10 then -- 封号 提前告知服务器
			self:C2S_ACK(packet_uid, packet_sig)
		end
		
        local msgJson = Json.decode(packet_msg)
        if msgJson then
            self:ExecutePushMsg(packet_type, msgJson)
        end

        if packet_type >= 1 and packet_type <= 999 and packet_type ~= 10 then
            self:C2S_ACK(packet_uid, packet_sig)
        end
    end
end

function SocketManager:ExecutePushMsg (packet_type, objJson)
    local data = objJson["data"]
    if data == nil then
        --TODO		
        return
    end

    if packet_type == 1 then

    elseif packet_type == 2 then
        --pay push
		
        if data.productId then
			Log.Info("ExecutePushMsg.data.productId" .. data.productId)
            PaymentConfig:DoPayPushEnd(data.productId,data)
        else
            UI_SHOW(UIDefine.UI_WidgetTip, "payment push err")
			UI_UPDATE(UIDefine.UI_Payment, 1027)
        end

    elseif packet_type == 3 then
        NetContactData:PushMsgChat()
        NetContactData:ReadNetData(data)
        --EventMgr:Dispatch(EventID.UPDATE_CONTACTUS_EVENT)
		UI_UPDATE(UIDefine.UI_ContactUSUI, 1, nil)
        --EventMgr:Dispatch(EventID.UPDATE_SETTING_REDPOINT, nil)
		UI_UPDATE(UIDefine.UI_Setting, 3)

    elseif packet_type == 998 then
        local ctrlCode = data["cheat_code"]
        if ctrlCode then
            local tb = string.split(ctrlCode, '~')
            for k, v in pairs(tb) do
                local tbSub = string.split(v, ' ')
                DebugConsole(tbSub[1], tbSub[2], tbSub[3])
            end
        end

    elseif packet_type == 1001 then
        --email red point netdata
        NetMailData:SetEmailModelPoint(data["mailstat"], false)
        --Panel Update Event
        --EventMgr:Dispatch(EventID.EMAIL_PANEL_UPDATE, nil)
		UI_UPDATE(UIDefine.UI_MailPanel, 4, nil)
		
	elseif packet_type == 10 then
		--被封号了
		self.m_isKicked = true
		self:ReLoginNeeded()
	elseif packet_type == 4 then
		NetFriendData:SetAppleRed(true)
	elseif packet_type == 5 then --联盟聊天 - 帮助可领取
		NetLeagueData:SetReceiveChatRed(true);
        UI_UPDATE(UIDefine.UI_MainFace, 50);
        UI_UPDATE(UIDefine.UI_Union, 1, 4);
	elseif packet_type == 6 then
		NetFriendData:SetReceiveGiftRed(true)
    elseif packet_type == 7 then -- 联盟被踢
        UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(9258))
        LeagueManager:SetMyLeagueId(GetNumberValue(data["leagueId"], -1),GetNumberValue(data["leagueQuitTime"], -1))
    elseif packet_type == 8 then -- 加入联盟
        local function joinBack(_data)
            UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(9226))
            LeagueManager:SetMyLeagueId(GetNumberValue(_data.id, -1),GetNumberValue(data["leagueQuitTime"], -1))
        end
        LeagueManager:SendGetLeagueDetails(nil,joinBack)
    elseif packet_type == 9 then -- 联盟解散通知(不通知会长)
        UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(9261))
        LeagueManager:SetMyLeagueId(GetNumberValue(data["leagueId"], -1),GetNumberValue(data["leagueQuitTime"], -1))
	elseif packet_type == 11 then
		NetShushuGiftData:PushShushuMsg(data)
    elseif packet_type == 1002 then
        if data["red_diamond"] then
            local value = v2n(data["red_diamond"]) or 0
            RedDiamondManager:OnRecharge(value)
        end
    elseif packet_type == 10001 then
        AnnounceManager:PushAnnounceMsg(data)
	elseif packet_type == 1006 then
		--社交分享奖励推送
		--type: 1 拉新，2 拉活
		local type = v2n(data["type"]) or 0
		--已奖励次数 目前暂定 30 次，大于 30 次之后就不再推送
		local num = v2n(data["num"]) or 0
		
		if type == 1 then
			NetInviteFriendData:OnInviteNewFriendSuccess(num)
		elseif type == 2 then
			NetInviteFriendData:OnInviteOldFriendSuccess(num)
		else
			Log.Error("InviteFriend push type exception",type)
		end
    elseif packet_type == 1008 then -- 联盟被邀请通知
        NetLeagueData:SetInviteRed(true)
    elseif packet_type == 10002 then -- 聊天推送
        ChatManager:PushChatCacheList(data,true)
    elseif packet_type == 10006 then -- 联盟科技变化通知
        LeagueManager:OnScienceLevelUp(data.tech_id);
        LeagueManager:SetScienceDic(v2n(data.type), data);

        if UIMgr:ViewIsShow(UIDefine.UI_UnionScience) then
            UI_UPDATE(UIDefine.UI_UnionScience, 1);
            UI_UPDATE(UIDefine.UI_UnionScienceUpgrade, 1);
        end
    elseif packet_type == 10008 then -- 联盟添加公告通知
        ChatManager:UpdateAnnounceMsg(data)
    elseif packet_type == 10021 then -- 联盟公告点赞更新
        ChatManager:UpdateAttitudeMsg(data)
    elseif packet_type == 10009 then -- 联盟申请列表变化通知
        LeagueManager:RefreshApplyRedDot();
    elseif packet_type == 10010 then --  联盟内有礼包或者奖励帮助
        LeagueChatManager:OnRefreshRed();
    elseif packet_type == 10011 then  -- 目标会收到推送，有玩家申请添加对方为好友，用于红点展示
        NetFriendData:RequestFriendList(FRIEND_REQUEST_TYPE.OtherRequest, function ()
            NetFriendData:RefreshRedPoint()
        end)
    elseif packet_type == 10012 then  -- 被赠送的目标会收到通知，用于红点展示
        NetFriendData:RequestFriendList(FRIEND_REQUEST_TYPE.Friend, function ()
            UI_UPDATE(UIDefine.UI_FriendView, 1)
            NetFriendData:RefreshRedPoint()
        end)
    elseif packet_type == 10003 then  -- 联盟中有新的建筑帮助请求
        UI_UPDATE(UIDefine.UI_MainFace,"LeagueHelp")
		NetLeagueData:UpdateBuildHelpList()
    elseif packet_type == 10005 then  -- 有其他玩家帮助自己的建筑帮助请求
        NetLeagueData:ReduceBuildTime(data)
    elseif packet_type == 10013 then -- 联盟信息发生了变化
        LeagueManager:SendGetLeagueDetails();
    elseif packet_type == 10014 then -- 联盟自己邀请列表有变化
        LeagueManager:RemoveSelfInvite(data);
    elseif packet_type == 10004 then -- 联盟有玩家移除了帮助申请（提前建造完成）
        UI_UPDATE(UIDefine.UI_MainFace,201,v2n(data))
    elseif packet_type == 10015 then -- 联盟科技推荐变化
        LeagueManager:OnRefreshScience();
    elseif packet_type == 10017 then -- 分配频道
        local worldChannelId = v2n(data["worldChannelId"]) or 0
        local areaChannelId = v2n(data["areaChannelId"]) or 0
        ChatManager:PushChatChannel(worldChannelId,areaChannelId)
    elseif packet_type == 10018 then -- 删除公告推送
        ChatManager:PushDeleteLeagueAnnouncement(data)
    elseif packet_type == 10019 then -- 删除好友
        NetFriendData:FriendDeleteMe(data)
    elseif packet_type == 10020 then -- 玩家同意加好友
        NetFriendData:FriendAgreeMe(data)
    end
end

function SocketManager:ClearUp()
    self.m_gameConnID = nil
    self.m_socketTimer = nil
    self.m_isSocketLogin = nil
    self.m_isKicked = nil
    self.m_TickHeartBeat = nil
    self.m_socketReConnectCount = 0
end

function SocketManager:ReLoginNeeded()
    Log.Info("****** push relogin")
    HttpClient:ShowUIReConnect()
end

function SocketManager:ReStartGame()
	local function onRetry()
		SceneMgr:SwitchScene(UISceneDefine.LoginScene)
	end
	UI_SHOW(UIDefine.UI_TipsTop, 7062, onRetry, nil, 2)
	SocketMgr:ResetSocket(0)
	Socket2Mgr:ResetSocket(0)
	SdkHelper:ThinkingTrackEvent(ThinkingKey.login_restart,nil)
end

return SocketManager