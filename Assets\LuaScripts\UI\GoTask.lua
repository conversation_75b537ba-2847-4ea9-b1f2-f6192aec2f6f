local GoTask = {}
local M = GoTask

local prePath = "Assets/ResPackage/Prefab/UI/GoTask.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
 
    item.loadCallBack = loadCallBack
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)
end

function M:Init()
    self.icon = GetChild(self.go,"bg/icon",UEUI.Image)
    self.scheduleText = GetChild(self.go,"bg/num",UEUI.Text)
    self.count = GetChild(self.go,"bg/CountDown/countTxt",UEUI.Text)
    self.arrowGo = GetChild(self.go,"arrow")
    self.point = GetChild(self.go,"bg/goPoint")
    self.goFlag = GetChild(self.go,"bg/goFlag")
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
end

function M:SetItem(param,taskType)
    self.taskType = taskType
    self.id = param.id
    if taskType == 1 then
        self.totalType = param.totalType or param.total_type
        self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
        self.info = self.activeInfo.info
        local mapId = self.activeInfo.form.map
        local activeTaskCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_task,  self.id)
		
		if activeTaskCfg then
			local rewardItem = self.activeInfo.info.skinid
			
			local  config = ConfigMgr:GetDataByID(ConfigDefine.ID.skin_collection,rewardItem)
			if config then
				local iconPath = config.activity_icon
				SetImageSprite(self.icon,iconPath,false)
			end
	        --local maxActive = v2n(activeTaskCfg.progress)
	        --local nowTask = NetTaskData:GetFinishTaskCount(mapId)
			local nowCount,maxCount = CollectionItems:GetCollectionProgress(activeTaskCfg.collection_id)
			if self.lastNowCount == nil then
				self.lastNowCount = nowCount
			end
			if param.task_ui == 3 or param.showUI == 3 then
				local num = string.format("%.2f",nowCount/maxCount)
				self.count.text =  (num*100).."%"
			else		
	        	self.count.text = nowCount .. "/" .. maxCount
			end
			self:ChangeProgress()
		end
    elseif taskType == 2 then
        local conf = ConfigMgr:GetDataByID( ConfigDefine.ID.npc_order_condition,param.id)
        SetImageSprite(self.icon,conf.icon,true)
        self:ChangeValue(param)
    end

    if self.goFlag then
        SetActive(self.goFlag,param.showFlag == true)
    end
end


function M:ChangeValue(param)
    if self.taskType == 2 then
        self.count.text = param.enough .. "/" .. param.needCount
        if param.needCount > param.enough then
            SetActive(self.point,false)
            SetActive(self.arrowGo,false)
        else
            SetActive(self.point,true)
            SetActive(self.arrowGo,true)
        end
    end
end

function M:ChangeProgress()
	local activeTaskCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_task,  self.id)
	local nowCount,maxCount = CollectionItems:GetCollectionProgress(activeTaskCfg.collection_id)
	local num = 0
	local isAdd = false
	if self.lastNowCount == nil then
		self.lastNowCount = nowCount
	else
		if v2n(nowCount) > v2n(self.lastNowCount) then
			isAdd = true
		end
	end
	
	if nowCount and maxCount then
		num = string.format("%.2f",nowCount/maxCount)
		self.count.text =  (num*100).."%"
		if nowCount >= maxCount and self.totalType == 4 then
			SetActive(self.point,true)
			--UI_SHOW
			NetPushViewData:PushView(PushDefine.DressTask)
			NetPushViewData:PushView(PushDefine.MaxReward)
		else
			SetActive(self.point,false)
		end
	end
	--进度有变化
	if isAdd then
		local mapId = NetUpdatePlayerData.playerInfo.curMap
		local skin_time = num*100
		local thinkTable = {["map_id"] = mapId,["skin_time"] = skin_time,["skin_id"] = self.activeInfo.info.skinid}
		SdkHelper:ThinkingTrackEvent(ThinkingKey.skin_plan,thinkTable)
		self.lastNowCount = nowCount
	end
end

function M:ClickItem(arg1)
    if self.taskType == 1 then
        UI_SHOW(UIDefine.UI_DressTask)
    else
        UI_SHOW(UIDefine.UI_NpcOrder,self.id)
    end
end

function M:Close()
    UEGO.Destroy(self.go)
end

return M