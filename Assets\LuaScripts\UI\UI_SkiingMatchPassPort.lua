local UI_SkiingMatchPassPort = Class(BaseView)
local PerProgress = 0.1
function UI_SkiingMatchPassPort:OnInit()

end

function UI_SkiingMatchPassPort:OnCreate(param)
	self.rewardIndex = -1
	self.activityItem = LimitActivityController:GetActive(ActivityTotal.NewSkiing)
	self.activeId = self.activityItem.info.activeId
	self.nowScore = v2n(self.activityItem.info.integral)
	self.nowPer = self:GetNowPer()
	self.itemList = {}
	self.itemGoTable = {[1] = {},[2] = {}}
	self.isShowRewardAnim = false  -- 当前是否显示了奖励动画界面
	self.isPlayingRewardAnim = false  -- 当前是否正在播放云飞到右下角的动画
	self.showRewardList = {}
	local needConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.skating_setting_new,1)
	local maxLv = "Lv"..table.count(self.activityItem:GetForm())
	local str = LangMgr:GetLangFormat(90200122,maxLv,tostring(needConfig.value))
	self.ui.m_txtTitle.text = str
	self:SetIsUpdateTick(true)
	self:InitUI()
	self:TurnPageScroll()
	self:RefreshChestInfo()
end

function UI_SkiingMatchPassPort:OnRefresh(type)
	if type == 1 then
		self:InitItemList()
		--解锁VIP
		EffectConfig:CreateEffect(141, 0, 0, 0, self.ui.m_goEffectBg.transform, function(data, tGo, go)
				self:SortOrderAllCom(true)
			end)
	elseif type == 2 then
		self:InitPanelUI()
	end
end

function UI_SkiingMatchPassPort:GetNowPer()
	local progress          = 0
	local energySch         = self:GetNowEnergySch()
	local nowMaxScore       = v2n(self.activityItem.form.exps[energySch])
	local per = self.nowScore * 1.0 / nowMaxScore * 0.1
	progress = progress + (energySch - 1)  * PerProgress + per
	return progress
end

function UI_SkiingMatchPassPort:GetScheduleList()
	local oIndex = self.activityItem:GetSchedule()
	local vIndex = self.activityItem:GetVIPSchedule()
	--local v3Index = self.activityItem:GetVIP3Schedule()
	return {oIndex,vIndex}
end


function UI_SkiingMatchPassPort:IsHaveReward( progress,index )
	--if true then--待删 将所有奖励标记为未领取过
	--return true
	--end
	local indexList = self:GetScheduleList()
	if CompareNum( progress,self.nowPer ) then
		if self.activityItem:IsBuyVip() then
			if   indexList[2]  < index  then
				return true
			end
		end
		--if self.activityItem:IsBuyVip3() then
		--if   indexList[3]  < index  then
		--return true
		--end
		--end
		if  indexList[1]  < index  then
			return true
		end
	end
	return false
end

function UI_SkiingMatchPassPort:RewardAll()
	local rewardStr = ""
	if self.rewardIndex <= 0  then
		return
	end
	local oIndex = self.activityItem:GetSchedule()--普通奖励的领取情况
	local vIndex = self.activityItem:GetVIPSchedule()--vip奖励的领取情况
	--local v3Index = self.activityItem:GetVIP3Schedule()
	--self.rewardIndex=3--待删
	--oIndex=0--待删
	local indexList = {oIndex,vIndex}

	local pass_id = {}
	local vip_pass_id = {}
	--local vip3_pass_id = {}
	--local curMapID = NetUpdatePlayerData.playerInfo.curMap
	for i = 1, #indexList do
		if (i == 2 and self.activityItem:IsBuyVip()) or i == 1 then
			for index, v in ipairs(self.itemList) do
				if index > indexList[i] and index <= self.rewardIndex then
					local cId = v2n(NetSeasonActivity:GetChangeItemId(v[i][1]))
					if cId < ItemID._RESOURCE_MAX then
						NetUpdatePlayerData:AddResource(PlayerDefine[v[i][1]], v[i][2],nil,nil,"UI_SkiingMatchPassPort")
						MapController:AddResourceBoomAnim(0,0,v[i][1],v[i][2])
					else
						local cdata=ItemConfig:GetDataByID(cId)

						local count = v[i][2]
						local itemStr  = v2s(cId) .. "|" .. v2s(count) .. ";"
						if i==1 then
							table.insert(pass_id,v2n(index))
						elseif i==2 then
							table.insert(vip_pass_id,v2n(index))
						elseif i == 3 then
							--table.insert(vip3_pass_id,v2n(index))
						end
						rewardStr = rewardStr .. itemStr
					end
				end
			end
		end
	end

	rewardStr = string.sub(rewardStr,1,string.len(rewardStr) - 1)


	self.activityItem:SetSchedule(self.rewardIndex)
	if self.activityItem:IsBuyVip() then
		self.activityItem:SetVipSchedule(self.rewardIndex)
	end

	if self.activityItem:IsBuyVip3() then
		self.activityItem:SetVip3Schedule(self.rewardIndex)
	end

	if not IsNilOrEmpty(rewardStr) then
		MapController:SendRewardToMap(rewardStr,nil,nil,nil,"UI_SkiingMatchPassPort",nil,true)
		self:PlayRewardAnim(rewardStr)
		-- 刷新遗迹主界面通行证红点
		UI_UPDATE(UIDefine.UI_RelicView)
	end

    -- 通行证埋点
    if next(pass_id) ~= nil then
        for _, v in pairs(pass_id) do
            local thinkTable = {["racing_passfreelv"] = v}
            SdkHelper:ThinkingTrackEvent(ThinkingKey.frostyracing_pass, thinkTable)
        end
    end
    if next(vip_pass_id) ~= nil then
        for _, v in pairs(vip_pass_id) do
            local thinkTable = {["racing_passiaplv"] = v}
            SdkHelper:ThinkingTrackEvent(ThinkingKey.frostyracing_pass, thinkTable)
        end
    end
end

function UI_SkiingMatchPassPort:InitUI()
	self:InitItemList()
	self:InitPanelUI()
	--self:InitChainUI()
end

function UI_SkiingMatchPassPort:GetActiveTime()
	local condition = self.activityItem.info.state
	local time = 0
	if condition == 1 then
		time = self.activityItem:GetRemainingTime()
	elseif condition == 3 then
		time = self.activityItem:GetStartRemainingTime()
	elseif condition == 4 then
		time = self.activityItem:GetWaitTime()
	end
	return time
end

function UI_SkiingMatchPassPort:TickUI(deltaTime)
	local time = self:GetActiveTime()
	if time and time > 0 then
		self.ui.m_txtTime.text = TimeMgr:BaseTime(time,3,2)
	else
		self.ui.m_txtTime.text = LangMgr:GetLang(7077)
	end
end

--function UI_SkiingMatchPassPort:InitChainUI()

	--local coll_id = RelicManager:GetCollectionLian()
	--local collectionList = CollectionItems:GetMaterialById(tonumber(coll_id))
	--local itemPrefab = self.ui.m_transChainParent:GetChild(0)
	--for index, itemId in ipairs(collectionList) do
		--local itemBg = UEGO.Instantiate(itemPrefab,itemPrefab.parent)
		--itemBg.gameObject:SetActive(true)
		--local itemImage = itemBg:GetChild(0):GetComponent(typeof(UEUI.Image))
		--local itemText = itemBg:GetChild(1):GetComponent(typeof(UEUI.Text))
		--SetImageSprite(itemImage,ItemConfig:GetIcon(itemId),false)
		--itemText.text = "lv." .. index
		---- 图腾奖励列表的最后一个
		--if index == #collectionList then
			---- 显示说明按钮
			--local itemMessage = itemBg:GetChild(2):GetComponent(typeof(UEUI.Button))
			--itemMessage.gameObject:SetActive(true)
			--itemMessage.onClick:AddListener(function ()
					--if self.isPushing then return end
					---- 显示图腾说明界面
					--UI_SHOW(UIDefine.UI_RelicItemMessage, RelicManager:GetSouvenirReward(itemId), self.highestItemId)
				--end)
			---- 最高级奖励的 ID
			--self.highestItemId = itemId
		--end

		--if NetNewSkiingData:GetCollectionData(itemId) == ITEM_STATE.UNLOCK then
			---- SetUIImageGray(itemImage,true)
			--SetUIColor(itemImage,1,1,1,0.5)
		--end
	--end
--end

function UI_SkiingMatchPassPort:GetNowEnergySch()
	local maxCount = self.activityItem.form.max
	local schedule = self.activityItem.info.energySch + 1
	schedule = math.min(schedule,maxCount)
	return schedule
end

function UI_SkiingMatchPassPort:InitPanelUI()
	local energySch  = self:GetNowEnergySch()
	local nextScore = self.activityItem.form.exps[energySch]
	if v2n(self.nowScore) > v2n(nextScore) then
		self.nowScore = nextScore
	end
	if self.activityItem:GetIsMaxLimit() then
		self.ui.m_txtScore.text = LangMgr:GetLang(5003)
	else
		self.ui.m_txtScore.text = string.format("%d/%d",self.nowScore,nextScore)
	end	
	--self.ui.m_txtScore.text = string.format("%d/%d",self.nowScore,nextScore)
	self.ui.m_txtLevel.text = energySch
	self.ui.m_slider3.value = self.nowScore/nextScore

	local isBuyVip = self.activityItem:IsBuyVip()
	self.ui.m_btnVip.gameObject:SetActive(not isBuyVip)

	local type,value = self.activityItem:GetPayTypeAndValue()
	local imageDiamond = GetChild(self.uiGameObject,"m_panelUI/bg/bgContent/imgMap/btn_vip/centerObj/Image")
	if type == 1 then
		SetActive(imageDiamond,true)
		self.ui.m_txtRePrice.text = value
	elseif type == 2 then
		SetActive(imageDiamond,false)
		local payConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.payment,value)
		self.ui.m_txtRePrice.text = LangMgr:GetLang(payConfig["price_langid"])
	end
	--SetActive(self.ui.m_goCollectionRed,NetNewSkiingData:IsShowCollectionRed())
	--SetUIImage(self.ui.m_imgPassIcon,self.activityItem.form.pass_icon2,false)
	--SetImageSprite(self.ui.m_imgPassIcon,self.activityItem.form.pass_icon2)
end

function UI_SkiingMatchPassPort:InitItemList()
	local parseConfig = function (itemDataString)
		local itemTable = {}
		for _, i in ipairs(itemDataString:split(";")) do
			local j = i:split("|")
			table.insert(itemTable, {itemId = tonumber(j[1]),itemNum = tonumber(j[2])})
		end
		return itemTable
	end


	local initItemUI = function (item,itemData,index,type)
		local itemIcon = GetChild(item,"itemIcon",UEUI.Image)
		local itemId = tonumber(NetSeasonActivity:GetChangeItemId(itemData[1]))
		SetImageSync(itemIcon,ItemConfig:GetIcon(itemId),false)
		GetChild(item,"txtNum",UEUI.Text).text = "x" .. itemData[2]
		local button = GetChild(item,"btnReward",UEUI.Button)
		local effectGo = GetChild(item,"ImgEffect")
		local rewardTag = GetChild(item,"rewardTag")
		local canRewardTag = GetChild(item,"kelinqu")
		local lockGo = GetChild(item,"ImgLock")
		
		

		local state
		--if not isRare then
		--state = NetRollDiceData:GetNormalTreasureState(index)
		--else
		--state = NetRollDiceData:GetRareTreasureState(index)
		--end
		state = NetNewSkiingData:GetStateByIndex(index,type)


		local isBuyVip = self.activityItem:IsBuyVip()
		SetActive(self.ui.m_btnVip,not isBuyVip)
		if lockGo and isBuyVip then
			lockGo:SetActive(false)

		end
		if not lockGo or lockGo and isBuyVip then
			if state == ITEM_STATE.UNLOCK then
			elseif state == ITEM_STATE.CAN_REWARD then
				effectGo:SetActive(true)
				canRewardTag:SetActive(true)
				local itemIconAnim = GetChild(item, "itemIcon", UE.Animation)
				PlayAnimStatus(itemIconAnim, "activity_itemicon")
			elseif state == ITEM_STATE.REWARED then
				rewardTag:SetActive(true)
			end
		end

		-- 购买 VIP 之后会重复绑定点击事件
		button.onClick:RemoveAllListeners()
		button.onClick:AddListener(function ()
				if self.isPushing then return end

				local state
				--if not isRare then
				--state = NetRollDiceData:GetNormalTreasureState(index)
				--else
				--state = NetRollDiceData:GetRareTreasureState(index)
				--end
				state = NetNewSkiingData:GetStateByIndex(index,type)

				if state == ITEM_STATE.UNLOCK then
					UI_SHOW(UIDefine.UI_ItemTips,itemId)
				elseif state == ITEM_STATE.CAN_REWARD then
					self:RewardAll()
				elseif state == ITEM_STATE.REWARED then
					UI_SHOW(UIDefine.UI_ItemTips,itemId)
				end
			end)
	end
	local formTable =  self.activityItem:GetForm()
	local itemCount  = GetTableLength(formTable.ordinary)
	local oData = formTable.ordinary
	local vData = formTable.vipReward

	for i = 1, itemCount do
		local list = {}
		if vData  and  oData then--and v3Data
			list = {{v2n(oData[i][1]),v2n(oData[i][2])},{v2n(vData[i][1]),v2n(vData[i][2])}}
		else
			list = {{0,0},{0,0}}
		end
		local nowPer = i * PerProgress
		if self:IsHaveReward(nowPer,i) then
			self.rewardIndex = i
		end
		table.insert(self.itemList,list)
	end
	--for i = 1, self.ui.m_transItemParent.childCount - 1, 1 do
	--UEGO.Destroy(self.ui.m_transItemParent:GetChild(i).gameObject)
	--end
	for i = 1, itemCount, 1 do
		local item1
		local item2
		if nil == self.itemGoTable[1][i] then
			local itemInstance = UEGO.Instantiate(self.ui.m_goItem,self.ui.m_transItemParent)
			itemInstance:SetActive(true)
			item1 = itemInstance.transform:GetChild(0)
			table.insert(self.itemGoTable[1], item1)
			local level_bg = itemInstance.transform:GetChild(1):GetComponentInChildren(typeof(UEUI.Text))
			level_bg.text = i
            --local level_image = GetChild(itemInstance, "level_bg/level_image")
            --SetActive(level_image, i <= self:GetNowEnergySch())
			item2 = itemInstance.transform:GetChild(2)
			table.insert(self.itemGoTable[2], item2)			
		else
			item1 = self.itemGoTable[1][i]
			item2 = self.itemGoTable[2][i]
		end
		initItemUI(item1,oData[i],i,1)
		initItemUI(item2,vData[i],i,2)

		local tempState = NetNewSkiingData:GetStateByIndex(i,1)
		local isNodeOk = tempState == ITEM_STATE.CAN_REWARD or tempState == ITEM_STATE.REWARED
		item1.transform.parent:Find("level_bg/levelBg1").gameObject:SetActive(isNodeOk)
		item1.transform.parent:Find("level_bg/levelBg2").gameObject:SetActive(not isNodeOk)
		

		--local nowPer = i * PerProgress
		--if self:IsHaveReward(nowPer,i) then
		--self.rewardIndex = i
		--end
	end
end

function UI_SkiingMatchPassPort:BuyVip()
	local imagePath = "Sprite/ui_activity_tongxingzheng/txz_jiangli3.png"
	UI_SHOW(UIDefine.UI_BuyPassPort, self.activeId, nil, {["imagePath"] = imagePath})
end

function UI_SkiingMatchPassPort:onDestroy()
	self.itemList = nil
	self.itemGoTable = nil
	self.activityItem = nil
	self.robotData = nil
end

function UI_SkiingMatchPassPort:onUIEventClick(go,param)
	local name = go.name
	if name == "m_btnClose" then
		self:Close()
	elseif name == "m_btnVipTips" then
		local imagePath = "Sprite/ui_activity_tongxingzheng/txz_jiangli3.png"
		UI_SHOW(UIDefine.UI_BuyPassPortTip, {["imagePath"] = imagePath})
	elseif name == "m_btnVip" then
		self:BuyVip()
	elseif name == "m_btnCollection" then
		--UI_SHOW(UIDefine.UI_RelicCollection,self.activeId)
	elseif name == "m_btnHelp" then
		UI_SHOW(UIDefine.UI_SkiingScoreHelp)
	elseif name == "m_btnChest" then
		local function clickChestTips()
			self:RefreshGoBox()
		end
		local function openChest()
			local curSorce = 0
			local maxValue = v2n(self:GetSettingByIndex(1).value or 1000)
			curSorce = self.activityItem.info.integralAfterMax or 0
			local count = Mathf.Floor(curSorce/maxValue)
			if count > 0 then
				local chestConfig = self:GetSettingByIndex(2).value
				local rewardArr = Split1(chestConfig,";")
				local rewardStr = ""
				for i, v in ipairs(rewardArr) do
					local rewardT = Split1(v,"|")
					local itemId = v2n(rewardT[1])
					local itemCount = v2n(rewardT[2]) * count
					local cId = v2n(NetSeasonActivity:GetChangeItemId(itemId))
					if cId < ItemID._RESOURCE_MAX then
						NetUpdatePlayerData:AddResource(PlayerDefine[itemId], itemCount,nil,nil,"UI_SkiingMatchPassPort")
						MapController:AddResourceBoomAnim(0,0,itemId,itemCount)
					else
						local cdata = ItemConfig:GetDataByID(cId)
						local itemStr  = v2s(cId) .. "|" .. v2s(itemCount) .. ";"
						rewardStr = rewardStr .. itemStr
					end
				end
				rewardStr = string.sub(rewardStr,1,string.len(rewardStr) - 1)
				if not IsNilOrEmpty(rewardStr) then
					MapController:SendRewardToMap(rewardStr,nil,nil,nil,"UI_SkiingMatchPassPort",nil,true)
					self:PlayRewardAnim(rewardStr)
					-- 刷新猫咪主界面通行证红点
				end

				local after = self.activityItem.info.integralAfterMax - (count * maxValue)
				-- 领完奖后要扣积分
				self.activityItem:SetIntegralAfterMax(after)
				--UI_UPDATE(UIDefine.UI_KeepCatView,5)
			else
				clickChestTips()
			end
		end

		if self.activityItem:GetIsMaxLimit() then
			openChest()
		else
			clickChestTips()
		end
	end
end

function UI_SkiingMatchPassPort:AutoClose()
	-- 当前正在显示奖励动画界面，播放云飞到右下角的动画
	if self.isShowRewardAnim then
		-- 防止连点
		if self.isPlayingRewardAnim then return end
		self:DoCloseAnim()
	else
		self:Close()
	end
end

--- 播放奖励动画
--- @param rewardStr string 奖励配置
function UI_SkiingMatchPassPort:PlayRewardAnim(rewardStr)
	self.isShowRewardAnim = true
	-- 隐藏界面根节点
	SetActive(self.ui.m_panelUI, false)
	-- 显示奖励节点
	SetActive(self.ui.m_goTarget2, true)
	SetActive(self.ui.m_goGetTarget, true)

	local itemDatas = rewardStr:split(";")
	local posIndex = 1
	local panel = GetChild(self.ui.m_goTarget2, "panel")
	local posMax = panel.transform.childCount
	for _, itemData in ipairs(itemDatas) do
		local data = itemData:split("|")
		local itemID = data[1]
		local count = v2n(data[2])
		for i = 1, count, 1 do
			self:AddToGoTarget(posIndex, posMax, itemID)
			posIndex = posIndex + 1
		end
	end
end

--- 显示云里的奖励图标
--- @param index number 索引
--- @param itemID string 物品 ID
function UI_SkiingMatchPassPort:AddToGoTarget(index, posMax, itemID)
	-- 奖励图标不够用，则只设置第一个图标
	if index > posMax then index = 1 end
	local rewardGo = GetChild(self.ui.m_goTarget2, "panel/pos" .. index, UEUI.Image)
	if not rewardGo then return end
	if IsNilOrEmpty(itemID) then return end
	SetActive(rewardGo,true)
	local icon = ItemConfig:GetIcon(v2n(itemID))
	SetImageSprite(rewardGo, icon, false)
end

--- 播放云飞到右下角的动画
function UI_SkiingMatchPassPort:DoCloseAnim()
	self.isPlayingRewardAnim = true
	-- 飞行动画的目标点
	local targetPoint = MapController:GetFlyPos(FlyId.Air)
	targetPoint.x = targetPoint[1]
	targetPoint.y = targetPoint[2]

	TimeMgr:CreateTimer(UIDefine.UI_SkiingMatchPassPort,
		function()
			-- 在目标点创建特效
			EffectConfig:CreateEffect(136, targetPoint.x, targetPoint.y, 0, UIMgr.layers[UILayerType.Top])
			local targetRT2 = GetComponent(self.ui.m_goTarget2, UE.RectTransform)
			if not targetRT2 then return end
			-- 多段位移和缩放动画
			DOLocalMoveY(targetRT2.transform, targetRT2.localPosition.y + 200, 0.2,
				function()
					DOLocalMoveY(targetRT2.transform, targetPoint.y, 0.5, function() end, Ease.InQuad)
				end, Ease.OutQuad)

			DOLocalMoveX(targetRT2.transform, targetRT2.localPosition.x - 150, 0.1,
				function()
					DOLocalMoveX(targetRT2.transform, targetPoint.x, 0.6, function() end, Ease.InQuad)
				end,Ease.OutQuad)

			DOScale(targetRT2.transform, 0.8, 0.3,
				function()
					DOScale(targetRT2.transform, 0, 0.5,
						function()
							self:Close()
						end,Ease.InQuad)
				end,Ease.InQuad)
		end, 0.3, 1)
end

--- 滑动到可领奖位置
function UI_SkiingMatchPassPort:TurnPageScroll()
	-- 当前领奖进度
	local currentSchedule
	local normalSchedule = self.activityItem:GetSchedule()
	local isBuyVip = self.activityItem:IsBuyVip()
	-- 已购买 VIP 奖励
	if isBuyVip then
		local vipSchedule = self.activityItem:GetVIPSchedule()
		currentSchedule = math.min(normalSchedule, vipSchedule)
	else
		currentSchedule = normalSchedule
	end
	-- 获取 item 的宽度
	local itemRT = GetComponent(self.ui.m_goItem, UE.RectTransform)
	if not itemRT then return end
	local itemHeight = itemRT.rect.height
	-- 计算 content 的位置
	local moveY = currentSchedule * itemHeight
	local moveX = self.ui.m_scrollview.content.anchoredPosition.x
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_scrollview.content)
	-- 滑动最大距离限制
	local viewportHeight = self.ui.m_scrollview.viewport.rect.height
	local contentHeight = self.ui.m_scrollview.content.rect.height
	local limit = math.abs(contentHeight - viewportHeight)
	if moveY > limit then moveY = limit end
	-- 滚动视图自动滑动的动画
	self.ui.m_scrollview.enabled = false
	self.ui.m_scrollview.content:DOAnchorPos(Vector2.New(moveX, moveY), 1)
	:SetId(UIDefine.UI_SkiingMatchPassPort)
	:OnComplete(function()
			self.ui.m_scrollview.enabled = true
		end):SetDelay(0.6)
end

function UI_SkiingMatchPassPort:GetSettingByIndex(index)
	local data = ConfigMgr:GetDataByID(ConfigDefine.ID.skating_setting_new,index)
	if data then
		return data
	end
	return {}
end

function UI_SkiingMatchPassPort:RefreshChestInfo()
	local maxCount = self.activityItem.form.max
	local curSorce = 0
	local maxValue = v2n(self:GetSettingByIndex(1).value or 1000)
	if self.activityItem:GetIsMaxLimit() then
		SetActive(self.ui.m_imgChestLock,false)
		SetActive(self.ui.m_goChestNumBg,true)
		SetActive(self.ui.m_goSliderChestBg,true)
		curSorce = self.activityItem.info.integralAfterMax or 0
		self.ui.m_txtChestProgress.text = string.format("%s/%s",curSorce,maxValue)
		self.ui.m_txtChestNum.text = Mathf.Floor(curSorce/maxValue)
		self.ui.m_sliderChest.value = curSorce/maxValue
	else
		SetActive(self.ui.m_imgChestLock,true)
		SetActive(self.ui.m_goChestNumBg,false)
		SetActive(self.ui.m_goSliderChestBg,false)
	end

end

function UI_SkiingMatchPassPort:RefreshGoBox()
	local chestConfig = self:GetSettingByIndex(2).value
	local rewardArr = Split1(chestConfig,";")
	local rewardItem = self.ui.m_goBox.transform:Find("rewardItem"):GetComponent(typeof(UE.RectTransform))
	if self.boxOpen then
		SetActive(self.ui.m_goBox, false)
		self.boxOpen = false
	else
		SetActive(self.ui.m_goBox, true)
		local m_Anim = GetComponent(self.ui.m_goBox, UE.Animation)
		if m_Anim then
			m_Anim:Play("keepCatBox")
		end
		self.boxOpen = true
		local arrList = rewardArr
		local count = table.count(arrList)

		for i = 1, count do
			local obj

			if self.showRewardList[i] == nil then
				obj = UEGO.Instantiate(rewardItem.gameObject, self.ui.m_goBox.transform)
				self.showRewardList[i] = obj
			else
				obj = self.showRewardList[i]
			end
			if obj == nil then
				return
			end

			obj.transform.localScale = Vector3.New(1, 1, 1)
			local itemIcon = GetChild(obj, "itemIcon", UEUI.Image)
			local itemText = GetChild(obj, "itemText", UEUI.Text)
			local arr = string.split(arrList[i], "|")
			local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
			local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, tonumber(itemId))
			SetUIImage(itemIcon, itemConfig.icon_b, false)
			itemText.text = "x" .. arr[2]
			local button = GetChild(obj,"btnReward",UEUI.Button)
			button.onClick:RemoveAllListeners()
			button.onClick:AddListener(function ()
					UI_SHOW(UIDefine.UI_ItemTips,itemId)
				end)
		end
		for i, v in ipairs(self.showRewardList) do
			SetActive(v,count >= i)
		end
	end
end


return UI_SkiingMatchPassPort