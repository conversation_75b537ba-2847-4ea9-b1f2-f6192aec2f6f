local UI_MainFace = Class(BaseView)
local taskItem = require("UI.TaskItem")
local taskFlyItem = require("UI.TaskFlyItem")
local GoActivity = require("UI.GoActivity")
local GoLimit = require("UI.GoLimit")
local GoLimit2 = require("UI.GoLimit2")
local GoLimit3 = require("UI.GoLimit3")
local GoLimit4 = require("UI.GoLimit4")
local GoLimit5 = require("UI.GoLimit5")
local GoLimit10 = require("UI.GoLimit10")
local GoLimit12 = require("UI.GoLimit12")
local GoLimit13 = require("UI.GoLimit13")
local GoLimit14 = require("UI.GoLimit14")
local GoLimit17 = require("UI.GoLimit17")
local GoLimit18 = require("UI.GoLimit18")
local GoLimit19 = require("UI.GoLimit19")
local GoLimit20 = require("UI.GoLimit20")
local GoLimit21 = require("UI.GoLimit21")
local GoLimit22 = require("UI.GoLimit22")
local GoLimit23 = require("UI.GoLimit23")
local GoLimit24 = require("UI.GoLimit24")
local GoLimit25 = require("UI.GoLimit25")
local GoLimit26 = require("UI.GoLimit26")
local GoLimit27 = require("UI.GoLimit27")
local GoLimit28 = require("UI.GoLimit28")
local GoLimit30 = require("UI.GoLimit30")
local GoLimit31 = require("UI.GoLimit31")
local GoTask = require("UI.GoTask")

local prePath = "Assets/ResPackage/Prefab/UI/FlyTask.prefab"
local pre
local maxEnergy
local workOpenLevel = tonumber(GlobalConfig:GetNumber(1039))
local WorkMaxTime = 3 * OneDaySeconds --工人工作的上限
local WorkerMax = 3 --工人的上限
local taskFlyDuration = 0.7
local tabNormalImg = "Sprite/ui_mainface/mainface2_weixuanzhong.png"
local tabSelectImg = "Sprite/ui_mainface/mainface2_xuanzhong.png"
local tabLoopTimes = 0
local tabChangeCD = 60

-- 刷新类型
local RefreshType = {
    ResourcePosWorker = 46,  -- 工人资源位置
    RefreshAthleticTalent = 47,  -- 刷新竞技达人
    RefreshRank = 48,  -- 刷新排行榜
    SwitchTab = 49,    -- 切换活动竞赛页签
    UpdateUnionRed = 50, -- 刷新联盟红点
    FriendRedPoint = 51, -- 刷新好友红点
}

-- 右下角的折叠工具栏
local FolderItemType = {
    Shovel = 1,    -- 铲子
    AiWorker = 2,  -- 智能工人
    Magnet = 3,    -- 磁铁
	Lottery = 4,   -- slg抽奖
    SLGBag = 5,    -- slg背包
}

local ActivityRankCenterType = {
    AthleticTalent = 2,  -- 竞技达人
}

local cpCustomHead = require "UI.CustomHead"

--function UI_MainFace:GetPathName()
--	local is_show = DailyTargetManager:IsOpenDailyTargetSwitch()
--	local name = "UI_MainFace"
--	if is_show then
--		name = "UI_MainFace_2"
--	end
--	
--	return name
--end

function UI_MainFace:OnInit()
	DungeonManager:OnRequestDungeonLoad()
	PurchaseManager:Initialize()
end

function UI_MainFace:OnCreate(param)
	--空的次数
    EnergyModule:EnergyRecoveryTime()
    self.setAni = GetChild(self.uiGameObject, "RightTop/Setlists", UE.Animation)
    self.aAni = GetComponent(self.uiGameObject, UE.Animation)
   -- self.rotateTrans = self.ui.m_goRotate.transform
	-- 每日目标动画队列
	self.targetSequence = {}
	self.dailyTargetUnLockAni = GetComponent(self.ui.m_goDailyTargetRoot,  UE.Animation)
	self.dailyTargetAni = GetChild(self.ui.m_goDailyTarget, "targetBg2/m_btnDailyTargetIcon", UE.Animation)
	self.imgDailyTarget = GetChild(self.ui.m_goDailyTarget,"targetBg2/m_btnDailyTargetIcon",UEUI.Image)
	self.DailyTargetEff = GetChild(self.ui.m_goDailyTarget,"targetBg2/eff")
	self.imgTatgetLock = GetChild(self.ui.m_goDailyTargetRoot,"lock")
	self.dailyTargetUpAni = GetChild(self.ui.m_goDailyTarget, "targetBg1/m_goCurDailyIcon", UE.Animation)
	self.dailyTargetIcon = GetChild(self.ui.m_goDailyTarget, "targetBg1/m_goCurDailyIcon", UEUI.Image)
	self.goExpTarget = self.ui.m_txtLev.transform.parent.transform
	self.isTween = false
	-- 活动竞赛倒计时文本
	self.oneToOneTimeBg = GetChild(self.ui.m_goActivityTabGroup1, "time", UEUI.Image);
	self.oneToOneTimeTxt = GetChild(self.ui.m_goActivityTabGroup1, "time/timeTxt", UEUI.Text);
	self.athleticTalentTimeTxt = GetChild(self.ui.m_goActivityTabGroup2, "time/timeTxt", UEUI.Text);
	self.bowlingTimeTxt = GetChild(self.ui.m_goActivityTabGroup3, "time/timeTxt", UEUI.Text);
	self.rankTimeTxt = GetChild(self.ui.m_goActivityTabGroup4, "time/timeTxt", UEUI.Text);
	self.levelTimeTxt = GetChild(self.ui.m_goActivityTabGroup6, "time/timeTxt", UEUI.Text);
	self.towerTimeBg = GetChild(self.ui.m_goActivityTabGroup7, "time", UEUI.Image);
	self.towerTimeTxt = GetChild(self.ui.m_goActivityTabGroup7, "time/timeTxt", UEUI.Text);
	self.jjcTimeTxt = GetChild(self.ui.m_goActivityTabGroup9, "time/timeTxt", UEUI.Text);
	self.worldBossTimeTxt = GetChild(self.ui.m_goActivityTabGroup10, "timeBg/timeTxt", UEUI.Text);
	self.topFightTimeTxt = GetChild(self.ui.m_goActivityTabGroup11, "timeBg/timeTxt", UEUI.Text);
	
    self.needHide ={}
    self.energyState = 0
	self.energyArrow = 0
	self.airArrow = 0
    self.PayWorkerTipsTimerId = 0
    --滑动
	self.taskView = GetChild(self.ui.m_goTask,"taskList",UEUI.ScrollRect)
	--self.taskViewimg = GetChild(self.ui.m_goUpList,"taskList",UEUI.Image)
	self.viewViewportimg = GetChild(self.ui.m_goUpList,"taskList/Viewport",UEUI.Image)
	self.taskRect = GetChild(self.ui.m_goUpList,"taskList",UE.RectTransform)
	self.goUpListRect = GetComponent(self.ui.m_goUpList, UE.RectTransform)

	self.taskScrollRect = GetChild(self.ui.m_goUpList,"taskList",UEUI.ScrollRect)
	
	--self.taskViewimg.enabled = false
	--self.viewViewportimg.enabled = false
    self.taskCont = self.taskView.content
    self.defaultTaskPos = self.taskCont.anchoredPosition
    self.workState = {false,false,false}

    self.setIsOpen = false
    self.gifts = {}
    self.limits = {}
    self.limitsGo = {}
    self.tipGo = {}
	self.limitsSpeGo = {}
    LimitActivityController:SetActivePos(self.limitsGo)
    self.timerTime = {}
    self.grayBtn = {}
    self.tempShow = {}
    self.pushMsgList = {}
    self.pushMsgInterval = 3.6
    self.pushMsgTime = 0
	
	-- 竞技达人
    self.athleticTalent = {}
    self.isRefreshAthleticTalentRank = false
    local posScreen = UIMgr:GetUIPosByWorld(self.ui.m_scrollviewActivityEnter.transform.position)
    LimitActivityController:SetAthleticTalentPos(posScreen)
	
	--主界面的特效容器
	self.mUIEffects = {}
	--主界面完成任务队列
	self.finishTaskQuence = {}
	--主界面完成任务队列数量
	self.finishTaskQuenceCount = 0
	self.isCanPopingFingTask = true
	self.isPlayNewTaskEff = false
	--主界面新任务队列
	self.newTaskQuence = {}
	self.sliderTarget = self.ui.m_sliderDailyTarget
	--self.mUIEffects[MainFaceDefine.Collect] = GetChild(self.uiGameObject,"RightBottom/m_goCollect/effect_UI_xuanzhuang")
	self.mUIEffects[MainFaceDefine.DailyTask] = GetChild(self.uiGameObject,"LeftBottom/m_goDailyTask/effect_UI_xuanzhuang")
	--self.mUIEffects[MainFaceDefine.Market] = GetChild(self.uiGameObject,"RightBottom/m_goMarket/effect_UI_xuanzhuang")
    self.mUIEffects[MainFaceDefine.Work1] = GetChild(self.ui.m_goWork1,"gongrentubiao/base",UE.ParticleSystem)
    self.mUIEffects[MainFaceDefine.Work2] = GetChild(self.ui.m_goWork2,"gongrentubiao/base",UE.ParticleSystem)
    self.taskPos = {}
    for i = 3, 1,-1 do
        local child = GetChildTrans(self.ui.m_goTaskPos,"Pos"..i)
        self.taskPos[i] = Vector3.New(child.position.x,child.position.y,0)
    end
    self.canvas = {}
    self.canvas[PlayerDefine.Energy] = {canvas = GetComponent(self.ui.m_goEnergy,UE.Canvas),count = 0}
    self.canvas[PlayerDefine.Coin] =  {canvas = GetComponent(self.ui.m_goCoin,UE.Canvas),count = 0}
    --self.canvas[PlayerDefine.Diamond] =  {canvas = GetComponent(self.ui.m_goDiamond,UE.Canvas),count = 0}
    self.canvas[MAIN_UI_ICON_ENUM.Collect] =  {canvas = GetComponent(self.ui.m_goCollect,UE.Canvas),count = 0}
	
    -- 初始化右下角的折叠工具栏
    self.rightBottomArowIsPut = NetGlobalData:GetDataByKey("rightBottomArowIsPut")
    self.rightBottomList = {}
	local rbTrans = GetChild(self.ui.m_goRightBottomList,"Layout")
	self.rbLayoutTrans = rbTrans.transform
	--折叠节点数
    self.rightBottomCount = 3
    for i = 1, self.rightBottomCount do
        local child = GetChildTrans(rbTrans, "sortGo" .. i)
        self.rightBottomList[i] = { go = child, isOpen = false }
    end
	self.rbBgRect = GetComponent(self.ui.m_goRightBottomList,UE.RectTransform)
	self.rbBgImg = GetComponent(self.ui.m_goRightBottomList,UEUI.Image)

	--关卡系统
	self.levelBoxStatus = -1
	self:InitLevelTimer()
	
	-- 任务飞行动画helper
	self.taskFlyHelper = require "Base.BezierHelper".new()

    self.shipAdState = false
	-- 是否开启多倍采集倒计时
	self.isStartMultipleTime = false
	--self.privilegeCardTime = 0
	-- 主界面礼包集合倒计时
	self.triggerGiftTime = nil
	-- 主界面比拼活动集合入口轮播
	self.activityRankTabList = {}
    self.activityRankTabIndexList = {}
	self.curActivityRankTabPage = 0
	self.maxActivityRankTabPage = 0
	self.tabDeltaTime = 0
	self.rankSubType = nil;
    --等级显示
    self:SetLevel()
    --资源设置
    self:ChangeResource()

	--
	self:CloudEvent()

    --倒计时功能
    self:CreateScheduleFun(function() self:Timer() end,1)
    --任务
    self.taskShowArrow = false
    self.taskItems = {}
    self.taskFlyItems = {}
    if pre == nil then
        pre = ResMgr:LoadAssetSync(prePath, AssetDefine.LoadType.Instant)
    end
    EventMgr:Add(EventID.USE_WORKER, self.SetPayWorker,self)--self.ChangeWorker, self)
	EventMgr:Add(EventID.MAP_CLOUD_UNLOCKED,self.CloudEvent,self)--云层解锁
    EventMgr:Add(EventID.LEVEL_UP, self.SetLevel, self)
	EventMgr:Add(EventID.ENERGY_BUFF_MINUS, self.onEnergyBuffMinus, self)
	EventMgr:Add(EventID.USING_RESOURCE,self.ResourseChange,self)
	EventMgr:Add(EventID.NEW_ACTIVITY,self.OpenSeason,self)
	EventMgr:Add(EventID.END_ACTIVITY,self.OpenSeason,self)
	EventMgr:Add(EventID.ON_FLY_RESOURCE_FINISH,self.OnFlyResourceFinish,self)
	if Game.Channel_IOS_Notch then
		self:IosInit()
	end
	--UPDATE_MAIN_ENERGY_UI
	--EventMgr:Add(EventID.UPDATE_MAIN_ENERGY_UI, self.OnEnergyPoint, self)

	--if IsRuGoogle() then
	--	SetUIPos(self.ui.m_goRedDiamond,-968.18,13.9)
	--	SetActive(self.ui.m_goRedDiamond,RedDiamondManager:IsOpenRedDiamond())
	--else
		SetUIPos(self.ui.m_goRedDiamond,-75.4,13.9)
		SetActive(self.ui.m_goRedDiamond,false)
	--end

	self.m_CustomHead = cpCustomHead.new()
	self.m_CustomHead:CreateHead(self.ui.m_imgHead.transform)
	self.m_CustomHead:SetClickCall(
		function()
			if UIMgr:GetUIOpen(UIDefine.UI_MapDressPreview) then
				return
			end
			UI_SHOW(UIDefine.UI_Setting)
		end)
	
    self:InitResourcePos()
    self:SetHead()
    --初始化任务列表
    NetTaskData:InitTask()
    --初始化触发礼包列表
    NetGiftBox:PushOpenBag()
    --初始化活动
    LimitActivityController:PushOpenLimit()
    --待推送礼包推送
    NetGiftBox:PushAllWaitGift()
	--活动中心推送
	--月卡推送
	--NetMonthCardData:CheckNeedPush()
    --初始化工人显示
    self:WorkerInit()    
	--初始化每日目标显示
    self:DailyTargetInit()	
	--vip弹窗推送
    self:CheckVIPContactPush()
	--红钻充值弹窗检测
    self:CheckRedDiamondRechargePush()
	
    --注册事件
    EventMgr:Add(EventID.CHANGE_RESOURCE,  self.ChangeResource, self)
    EventMgr:Add(EventID.MAP_ITEM_COMBINE, self.NewHeroUnlock, self)
	--EventMgr:Add(EventID.HANDLE_TEMPSAVEREWARDS, self.delayHandleTempSaveRewards, self)
	EventMgr:Add(EventID.MAP_ITEM_HEADVIP, self.HeadVipAdd, self)
	EventMgr:Add(EventID.MAP_ITEM_NEW,self.RefreshTgArrow,self)
	EventMgr:Add(EventID.ADD_DAILYTARGET_SCORE,self.DoDailyTargetTween,self)
	EventMgr:Add(EventID.PLAY_DAILYTARGET_SCORE_TWEEN,self.PlayDailyTargetTween,self)
	EventMgr:Add(EventID.RESET_DAILYTARGET_CACHE,self.RefreshTargetCache,self)	
	EventMgr:Add(EventID.ALL_LOAD_END, self.OnAllLoadEnd, self)
	EventMgr:Add(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
	EventMgr:Add(EventID.UPDATE_HERO_TOTAL_FIGHT, self.ChangeFight, self)
	EventMgr:Add(EventID.UPDATE_HERO_INFO, self.OnHeroChange, self)
	EventMgr:Add(EventID.DUNGEON_UPDATE, self.OnDungeonUpdate, self)
    EventMgr:Add(EventID.BAG_CHANGE, self.BagChange, self)
	EventMgr:Add(EventID.ROLE_GIFT_CHANGE, self.RoleGiftChange, self)
	EventMgr:Add(EventID.BATTLE_PASS_CHANGE, self.BattlePassChange, self)
	EventMgr:Add(EventID.BATTLE_MANUAL_SAVE,self.BATTLE_MANUAL_SAVE,self)
    self:SetIsUpdateTick(true)
    self:BindingUIToVar()
	
	self:InitTinyGameData()
	
	--初始化界面红点
	self:InitRedPoint()

	self.freeEnergyTip = GET_UI(self.uiGameObject, "energyTip", TP(UEUI.Image))
	self.energyRow = GET_UI(self.uiGameObject, "rowEnergy", TP(UEUI.Image))
	self.energyRowPos = self.energyRow.transform:GetPosition()

    self.extraWorkRow = GET_UI(self.uiGameObject, "rowExtraWork", TP(UEUI.Image))
    SetActive(self.extraWorkRow, false)
	
	self.rowTinyArrow = GET_UI(self.uiGameObject, "rowTinyArrow", TP(UEUI.Image))
	SetActive(self.rowTinyArrow, false)

	--
	self:OnEnergyPoint(nil)
	
	MarketModule:refreshTime()
	--市场的提示
	if NetUpdatePlayerData.playerInfo.curMap == MAP_ID_MAIN then
		local mapType, level = FunctionOpenConfig:GetopenConditionById(9)
		local isShow = false
		if mapType == MapType.Main and NetUpdatePlayerData:GetLevel() >= level then
			isShow = true
		end
		local showRow = NetMarketData:get_IsFirst() and isShow
		SetActive(self.ui.m_goArrow, showRow)
	end
	
	CollectionItems:ReflushMainPoint()
	
	--MarketConfig:redPoint()
	self:InitEnergyLogic()
	self:GiveReward()
	self:Timer()
	GiftPackModule:refreshTime()
	local isSetTime = NetGiftPackData:get_IsFirst()
	if not isSetTime then
		NetGiftPackData:SetSevenDay()
	end
	self:CheckGiftPackShow()
	self:CheckActToyWaste()
	--self:CheckActHalloweenWaste()
	self:CheckSkiingWaste()
	self:CheckNewSkiingWaste()
	self:CheckRollDiceWaste()
	self:CheckLuckOreWaste()
	--self:CheckRelicWaste()
	self:CheckIsOpenGrowthFund()
	local isTinyGame = (Game.IsTinyGame or Game.IsNativeTinyGame) and (TinyGameMgr:GetTinyGameID() > 0)
 	SetActive(self.ui.m_goTinyGame, isTinyGame)
	SetActive(self.ui.m_goGameDevChange,false)
	SetActive(self.ui.m_goFriend, FriendManager:IsOpen())
	local open,_= LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Season)
	SetActive(self.ui.m_goSeason,open)
	SetActive(self.ui.m_goUnion, LeagueManager:IsOpenUnion())

	self:MagnetInit()
	self:FingerInit()--手指弱引导
	self:UpdateMainTask()	-- 主界面主线任务初始化
	
	self:RefreshTgArrow()
	self:RefreshMultipleDropInfo()
	self:ResetTaskViewimg()
	self:updateActivityGroup()
	self:InitActivityRankTab()
	--self:AutoFitTaskList()
	self:UpdateWenjuan(true)

    self:FoldRightBottom()
    self:CheckChatChannelConnet()

	self:FoldTaskTip(NetGlobalData:GetDataByKey("taskArrowIsPut"))
	self:ChangeFight(nil,true)
    self.leagueAnnounceDesc = GetComponent(self.ui.m_goMessageContent, CS.TMPro.TextMeshProUGUI)
    self.myHeadNode = CreateCommonHead(GetChild(self.ui.m_goMessageHead, "headNode",UE.Transform),0.55)
	
	-- 英雄
	self:OnHeroChange(nil, true);
	self:RoleGiftChange()
	self:BattlePassChange()
	self:CheckLotteryEntry()
	--JJC
	self:BATTLE_MANUAL_SAVE()
end

function UI_MainFace:BATTLE_MANUAL_SAVE()
	local data = HeroManager:GetBattleTeamByType(BATTLE_TEAM_TYPE.ARENA_DEFENDER)
	local totalPower = JJcManager:GetPower() or
	HeroManager:GetBattleListTotalPower(BATTLE_TEAM_TYPE.ARENA_DEFENDER, data)
	self.ui.m_txtJJcPower.text = NumToGameString(totalPower)
	local rank = JJcManager:GetJJcPlayerRank() or 0
	if rank > 0 then
		local color = "fffb74"
		self.ui.m_txtJJcRank.text = GetStrRichColor("No."..rank,color) 
		SetOutline(self.ui.m_txtJJcRank,"6a217f")
		SetUIShadow(self.ui.m_txtJJcRank,"6a217f")
	else
		local color = "ffffff"
		self.ui.m_txtJJcRank.text = GetStrRichColor(LangMgr:GetLang(9056),color) 
		SetOutline(self.ui.m_txtJJcRank,"444444")
		SetUIShadow(self.ui.m_txtJJcRank,"444444")
	end	
end

function UI_MainFace:ChangeFight(data,noFly)
	--if not noFly then 
		--local toPos = UIMgr:GetObjectScreenPos(self.ui.m_txtFightAdd.gameObject.transform)
		--MapController:FlyUIAnimByImg(0,0,"Sprite/ui_public/Resource_wanju_zhanli.png", 1,toPos.x, toPos.y,nil,nil,nil,nil,FlyResourceType.FlyNone)
	--end
	SetActive(self.ui.m_goPlayerPower,NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.LevelEnter))
	self.ui.m_txtFightAdd.text = HeroManager:GetHeroTotalFight()
	SetUIForceRebuildLayout(self.ui.m_goPlayerPower)
end

function UI_MainFace:CheckChatChannelConnet()
	local worldChannel = ChatManager:GetWorldChatChannel()
	local areaChannelId = ChatManager:GetAreaChatChannel()
	if worldChannel == 0 or areaChannelId == 0 then
		ChatManager:RequestPlayerChannelInfo()
	end
end

function UI_MainFace:OnFlyResourceFinish(id,curCnt,totalCnt)
	if id == ItemID.EXP and self.goExpTarget and not Tween.IsTweening(self.goExpTarget) then
		self.goExpTarget:DOPunchScale(Vector3(0.2, 0.2, 1), 0.8,1)
	end
end

function UI_MainFace:OpenSeason(_totalType)
	if _totalType ~= ActivityTotal.Season then
		return 
	end
	local open,_= LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Season)
	SetActive(self.ui.m_goSeason,open)
end

function UI_MainFace:ResourseChange(param,param2)
	local num = param
	local type = param2
	if num < 0 then
		NetGiftBox:SetTodayResourceUse(type,num)
	end
end

function UI_MainFace:OnAllLoadEnd()
	self.loadEnd = true
	self:CheckActCenterBigImage()
	self:CheckRedDiamondShopBigImage()
end

function UI_MainFace:IsOnLoaded()
	return self.loadEnd or false
end

function UI_MainFace:UpdateMainTask(taskId)
	local curTaskId
	local curProgress
	if taskId then
		curTaskId = taskId
	else
		local sort = NetTaskData:GetCurTaskSort()
		local firstTask = sort[1]
		if firstTask == SpecialId.SeasonsTaskId then
			firstTask = sort[2]
		end
		curTaskId = firstTask
	end
	if nil == curTaskId then
		SetActive(self.ui.m_goMainTaskBg,false)
		return
	end
	local info = NetTaskData:GetTaskItem(curTaskId)
	if info == nil then
		return
	end
	
	local isFold = NetGlobalData:GetDataByKey("taskArrowIsPut")
	if not isFold then
		self:FoldTaskTip(isFold)
	end
	
	local desc = LangMgr:GetLang(info.conditions_lid)
	local target = NetTaskData:GetTarget(curTaskId)
	local progress = NetTaskData:GetProgressById(curTaskId)
	curProgress = progress
	if progress == TaskProgress.Finish then
		curProgress = target
	end

	local progress_str = ""
	if target then
		if curProgress < target then
			progress_str = string.format(" (<color=#FF0000>%s</color><color=#55FE0A>/%s</color>)", curProgress , target)
		else
			progress_str = string.format(" (<color=#55FE0A>%s/%s</color>)",curProgress , target)
		end
	end
	local task_desc = desc .. progress_str
	self.ui.m_txtMainTaskDesc.text = task_desc
	
	--SetUIForceRebuildLayout(self.ui.m_txtMainTaskDesc)
	--
	--local bg_width = self.ui.m_goMainTaskBg.transform.rect.width - 20
	--local descRect = GetComponent(self.ui.m_txtMainTaskDesc,UE.RectTransform)
	--local desc_width = descRect.sizeDelta.x
	--if desc_width > bg_width then
	--	SetUIPivot(self.ui.m_txtMainTaskDesc,0,0.5)
	--	SetUIAnchors(self.ui.m_txtMainTaskDesc,0,0.5,0,0.5)
	--	DOKill(self.ui.m_txtMainTaskDesc.transform)
	--	self.ui.m_txtMainTaskDesc.rectTransform.anchoredPosition = Vector2.New(20,0)
	--	local oldValue = self.ui.m_txtMainTaskDesc.transform.localPosition.x
	--	local endValue = oldValue - (desc_width-bg_width) - 20
	--	--self.ui.m_txtMainTaskDesc.transform:DOLocalMoveX(-(desc_width-bg_width),5):SetEase(Ease.Unset):SetLoops(-1,LoopType.Restart)
	--	DOLocalMoveXLoop(self.ui.m_txtMainTaskDesc.transform,endValue,5,LoopType.Restart)
	--else
	--	SetUIPivot(self.ui.m_txtMainTaskDesc,0.5,0.5)
	--	SetUIAnchors(self.ui.m_txtMainTaskDesc,0.5,0.5,0.5,0.5)
	--	DOKill(self.ui.m_txtMainTaskDesc.transform)
	--	self.ui.m_txtMainTaskDesc.rectTransform.anchoredPosition = Vector2.New(0,0)
	--end
	
	self:AutoFitTaskPanel()
end

function UI_MainFace:UpdateSeasonInfo()
	local activityOpen, activity = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Season)
	if activityOpen then
		local config = ConfigMgr:GetDataByID(ConfigDefine.ID.limit_activities, activity.form.activeMess.id)
		if config then
			SetImageSprite(self.ui.m_imgSeasonIcon, config.icon, true)
		else
			Log.Error("ActivityTotal.Season  UpdateSeasonInfo" ,activity.form.activeMess.id)
		end
		self.ui.m_txtSeasonNum.text = NumToGameString(NetUpdatePlayerData.playerInfo.seasonEnergy)
	end
	--SetActive(self.ui.m_goSeason,activityOpen)
end

function UI_MainFace:UpdateSeasonRed()
	SetActive(self.ui.m_goSeasonPoint, NetSeasonActivity:GetShowRed() 
			or NetLimitActGift:IsShowRed(ActivityTotal.Season) 
			or CollectionItems:CheckSeasonRedDot());
end

function UI_MainFace:MagnetInit()
	self.magnetPress = self.ui.m_btnMagnet.transform:GetComponent(typeof(CS.ButtonPressed))	
	self.magnetPress.buttonPressed = function()
		if NetPrivilegeCardData:IsHaveFreeTime() then
		MapController:SetIsMagnetValue(true)
			CombinePopMgr:InitMagnet(NetPrivilegeCardData:IsHaveFreeTime())
			return
		end
		if NetPrivilegeCardData.data.magnetState == PRIVILEGE_CARD.LOCK then
			UI_SHOW(UIDefine.UI_PrivilegeCard)
			return
		end
		MapController:SetIsMagnetValue(true)
		CombinePopMgr:InitMagnet()
		
	end
		
	self.magnetPress.buttonPressedCallBack = function()
		if CombinePopMgr:IsMagneting()then
			CombinePopMgr:EndMagnetState()
		else
			UI_SHOW(UIDefine.UI_PrivilegeCard)
		end
		MapController:SetIsMagnetValue(false)
		
	end
	self:RefreshMagnetState()
end

function UI_MainFace:FingerInit()
	local info = NetUpdatePlayerData:GetPlayerInfo()
	if v2n(info.levelid) <= GlobalConfig.FINGER_GUIDE_MAX then
		CombinePopMgr:InitFinger()
		self.fingerWaitT = 0
		EventMgr:Add(EventID.TOUCH_INTERRUPT,self.RestFingerTime,self)		
	end
end

function UI_MainFace:RestFingerTime()
	self.fingerWaitT = 0
	CombinePopMgr:SetFingerActive(false)
end

function UI_MainFace:IsShowFinger()
	if CombinePopMgr:IsCanShowFinger() then
		CombinePopMgr:ShowFingerGuide()
	end	
end
	
function UI_MainFace:RefreshMagnetState()
	local img = GetChild(self.ui.m_goPrivilege, "m_btnMagnet", UEUI.Image)
	if NetPrivilegeCardData.data.magnetState == PRIVILEGE_CARD.LOCK then
		if NetPrivilegeCardData:IsHaveFreeTime() then
			--SetUIImage(img, "Sprite/ui_mainface/icon_tq_citieshenqi.png", false)
			--
			--SetUIImage(self.ui.m_imgMagnet, "Sprite/ui_mainface/iconbox6.png", false)
			--SetUIImage(self.ui.m_imgPrivilegeTime,"Sprite/ui_mainface/iconbox6_time.png",false)
			SetUIImageGray(img,false)
			SetUIImageGray(self.ui.m_imgMagnet,false)
			SetUIImageGray(self.ui.m_imgPrivilegeTime,false)
		else
			--SetUIImage(img, "Sprite/ui_mainface/icon_tq_citieshenqi_1.png", false)
			--
			--SetUIImage(self.ui.m_imgMagnet, "Sprite/ui_mainface/iconbox6_1.png", false)
			--SetUIImage(self.ui.m_imgPrivilegeTime,"Sprite/ui_mainface/iconbox6_time_1.png",false)
			SetUIImageGray(img,true)
			SetUIImageGray(self.ui.m_imgMagnet,true)
			SetUIImageGray(self.ui.m_imgPrivilegeTime,true)
		end
	else
		--SetUIImage(img, "Sprite/ui_mainface/icon_tq_citieshenqi.png", false)
		--
		--SetUIImage(self.ui.m_imgMagnet, "Sprite/ui_mainface/iconbox6.png", false)
		--SetUIImage(self.ui.m_imgPrivilegeTime,"Sprite/ui_mainface/iconbox6_time.png",false)
		SetUIImageGray(img,false)
		SetUIImageGray(self.ui.m_imgMagnet,false)
		SetUIImageGray(self.ui.m_imgPrivilegeTime,false)
	end
end

--ios适配
function UI_MainFace:IosInit()
	local trans = GetComponent(self.uiGameObject.transform , UE.RectTransform)
	local off = (70/(trans.rect.size.x))
	trans.anchorMin = Vector2.New( off, 0)
	trans.anchorMax = Vector2.New( 1-off, 1)
end

function UI_MainFace:onEnergyBuffMinus()
	UIMgr:Refresh(UIDefine.UI_MainFace, 9, { id = NetEnergyBuff:GetActiveId(), state = 2 })
end

function UI_MainFace:CheckActToyWaste()
	local open, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Toy)
	if not open then
		if NetActToyData.data.autoWaste or not self.checkToy then
			self.checkToy = NetActToyData:ChangeToWasteId(nil,true)
		end
		
	end
end

function UI_MainFace:CheckActHalloweenWaste()
	if not self:IsOnLoaded() then return end
	if IsOnlyHomeMap(MapController.m_MapId) then
		local open, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.HalloweenOre)
		if not open then
			if NetHalloweenOreData.data.autoWaste or not self.checkHalloween then
				self.checkHalloween = HalloweenOreManager:SetAutoWaste()
			end
		end
	end
end

function UI_MainFace:CheckSkiingWaste()
	if NetSkiingMatchData.data.autoWaste then
		SkiingMatchManager:ActivateEndWaste()
	end
	--if clear then
		--local open, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.SkiingMatch)
		--if not open then
			--SkiingMatchManager:ActivateEndWaste()
		--end
	--end
end

function UI_MainFace:CheckNewSkiingWaste()
	if NetNewSkiingData.data.autoWaste then
		NewSkiingManager:ActivateEndWaste()
	end
	local open, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.NewSkiing)
	if not open then
		local tree = MapController:GetItemByType(ItemUseType.SkiingTree)
		if nil ~= next(tree) then
			for i, item in pairs(tree) do
				MapController:DeleteAndNew(item, item.m_GridX, item.m_GridY, ItemConfig:GetIDWaste(item.m_Id))
			end
		end
	end
end

function UI_MainFace:CheckRollDiceWaste()
	--local open, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.RollDice)
	if NetRollDiceData.data.autoWaste then
		RollDiceManager:ActivateEndWaste()
	end
end

function UI_MainFace:CheckRelicWaste()
	if not self:IsOnLoaded() then return end
	if NetRelicData.data.autoWaste then
		NetRelicData:ActivateEndWaste()
	end
end

function UI_MainFace:CheckLuckOreWaste()
	if NetLuckOreData.data.autoWaste then
		NetLuckOreData:ChangeToWasteId()
	end
end

function UI_MainFace:CheckKeepCatWaste()
	if not self:IsOnLoaded() then return end
	local open, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.KeepCat)
	if not open then
		if NetKeepCatData.data.autoWaste or not self.checkCat then
			self.checkCat = NetKeepCatData:ChangeToWasteId()
		end
	end
end

function UI_MainFace:CheckGiftPackShow()
	local level = LevelConfig:GetLevel()

	local isGetAllReward = NetFirstGiftData:CheckAllReward(true)
	if isGetAllReward then
		SetActive(self.ui.m_goFirstPack,false)
	else
		SetActive(self.ui.m_goFirstPack,level >= GlobalConfig.OPEN_FIRSTPAY_LEVEL)
	end
	
	local isNewGetAllReward,isUnlock = NetNewFirstGiftData:CheckAllReward()
	if isUnlock then		
		SetActive(self.ui.m_goNewFirstPack,not isNewGetAllReward)
	else
		local time = NetNewFirstGiftData:GetNewFirstEndTime()
		if time > 0 then
			SetActive(self.ui.m_goNewFirstPack,true)
		else
			local newFirstConifg = ConfigMgr:GetDataByID(ConfigDefine.ID.new_first_gift,1)
			local needLevel = newFirstConifg.level or 99
			if level == needLevel and  NetNewFirstGiftData.data.endTime == 0 then
				SetActive(self.ui.m_goNewFirstPack,true)
				NetNewFirstGiftData:SetNewFirstEndTime()
				NetNewFirstGiftData:CheckNeedPush()
			else
				SetActive(self.ui.m_goNewFirstPack,false)
			end
		end	
	end
		
	SetActive(self.ui.m_goCarousel,level >= GlobalConfig.OPEN_GIFTPACK_LEVEL)
	self.ui.m_txtGiftPack.text =  TimeMgr:CheckHMS(GiftPackModule:getLastTime())
	--SetActive(self.ui.m_goGiftDaily,level >= GlobalConfig.OPEN_GIFT_DAILY)
	SetActive(self.ui.m_goDailyGiftRed,not NetGiftDaily.data.getFreeDay)
	if level >= GlobalConfig.OPEN_FIRSTPAY_LEVEL and not isGetAllReward then
		NetFirstGiftData:CheckNeedPush()
	end
	-- NetMonthCardData:CheckNeedPush()
	NetEndlessData:CheckNeedPush()
	NetEnd2lessData:CheckNeedPush()
	--local isOpenMonth = NetMonthCardData:IsOpenMonthCard()
	--SetActive(self.ui.m_goMonthCard,isOpenMonth)

	local firstGiftConfig = ConfigMgr:GetData(ConfigDefine.ID.first_gift)
	self.ui.m_txtFirstPack.text = LangMgr:GetLang(firstGiftConfig[1].title)
	
	local newFirstConifg = ConfigMgr:GetDataByID(ConfigDefine.ID.new_first_gift,1)
	self.ui.m_txtNewFirstPack.text = LangMgr:GetLang(newFirstConifg.title)
	
	local isShow = NetFirstGiftData:GetFirstGifyRed()
	SetActive(self.ui.m_imgFirstGiftRed,isShow)
	
	local isShow = NetGrowthFund:CheckShowRed()
	SetActive(self.ui.m_imgGrowthFundRed,isShow)
	local isRed = NetGrowthFundNew:GetIsShowRed()
	SetActive(self.ui.m_imgGrowthFundNewRed,isRed)
end

function UI_MainFace:CheckSevenDayShow()
	if NetSevenDayData:IsOn7Activity() then
		--活动开启中
		SetActive(self.ui.m_goSevenDay,true)
		-- 红点
		local red = GetChild(self.ui.m_goSevenDay,"bg (1)/m_goSevenRed")
		SetActive(red,SevenDayManager:HasRedPointTotal())
		self:UpdateSevenDayProgress()
	else
		--活动关闭中
		SetActive(self.ui.m_goSevenDay,false)
	end
end

function UI_MainFace:UpdateSevenDayProgress()
	local config = ConfigMgr:GetData(ConfigDefine.ID.rookietarget_reward)
	local score = NetSevenDayData:GetTaskScore()
	local finishCnt = 0
	local pct = 0
	local maxScore = config[#config].rank
	local progress = score / maxScore
	if progress > 1 then
		progress = 1
	end
	self.ui.m_sliderSevenDay.value = progress
	self.ui.m_txtSevenDayProgress.text = score .. "/" .. maxScore
end

function UI_MainFace:CheckIsOpenGrowthFund()
	if NetGrowthFund:IsOpenActivity() then
		NetGrowthFund:SetOpenFlag(true)
	end
end

function UI_MainFace:SetHead()
    --SetImageSprite(self.ui.m_imgHead,NetUpdatePlayerData:GetPlayerHead(),false)
    --SetImageSprite(self.ui.m_imgNotCompetitionHeadLeft,NetUpdatePlayerData:GetPlayerHead(),false)

	SetActive(self.ui.m_imgNotCompetitionHeadLeft,false)
	SetActive(self.ui.m_imgHeadLeft,false)
	SetActive(self.ui.m_imgHeadRight,false)
	
	SetActive(self.ui.m_imgNotCompetitionHeadLeft,false)
	self.my1v1Head1 = CreateCommonHead(GetChild(self.ui.m_goNotCompetitionHead,"Left").transform,0.35)
	self.my1v1Head2 = CreateCommonHead(GetChild(self.ui.m_goHead,"Left").transform,0.35)

	SetMyHeadAndBorderByGo(self.my1v1Head1)
	SetMyHeadAndBorderByGo(self.my1v1Head2)
	
	local rect = GetComponent(self.my1v1Head1,UE.RectTransform)
	rect.anchoredPosition = Vector2.New(0,5)
	rect = GetComponent(self.my1v1Head2,UE.RectTransform)
	rect.anchoredPosition = Vector2.New(0,5)
	
	--self.other1v1Head = CreateCommonHead( GetChild(self.ui.m_goNotCompetitionHead,"Left"),0.45)
	--
	
	self.m_CustomHead:SetHeadByID(NetUpdatePlayerData:GetPlayerInfo().head)
	self.m_CustomHead:SetHeadBorderByID(NetUpdatePlayerData:GetPlayerInfo().headBorder)
end
--初始化体力部分
function UI_MainFace:InitEnergyLogic()
	local span = EnergyModule:get_PowerRecoveryTimeLeft()
	if span > 0 then
		self:AddTimerCom(PlayerDefine.Energy, 1)
	else
		self:RemoveTimerCom(PlayerDefine.Energy)
	end
end

function UI_MainFace:GiveReward()
	local reward = NetInfoData:GetReward()
	local rewardType = type(reward)
	if reward == nil then
		return
	end
	if rewardType == "string" then
		if IsNilOrEmpty(reward) then
			return
		end
	elseif rewardType == "table" then
		if GetTableLength(reward) <= 0 then
			return
		end
	end

	MapController:SendRewardToMap(reward,nil,nil,nil,"UI_MainFace")
	NetInfoData:SetRewardEmpty()
end

function UI_MainFace:NewHeroUnlock(mapId, id,highCount)
	--if self.twoGuid then
		--self:TwoLevelGuide(true)
	--end
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, id)
    if not config then
        return
    end

    local itemType = config["type_use"]
	local id_use = config["id_use2"]
    if itemType == ItemUseType.HeroFly then
        NetInfoData:SetHeadRedStateByHeroUnlock(id_use,id)
        self:SettingRedPoint()
		self:updateActivityGroup()
    end
end

function UI_MainFace:HeadVipAdd(id)
	local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, id)
	if not config then
		return
	end

	local itemType = config["type_use"]
	local id_use = config["id_use"]
	if itemType == ItemUseType.HeadVip then
		NetInfoData:SetHeadRedStateByHeroUnlock(id_use)
		self:SetListRedPoint()
	end
end


function UI_MainFace:SettingRedPoint()
    local havePush = NetContactData:IsHavePushMsg()
    local headRed = NetInfoData:IsShowHeadRed() or NetHeadSeasonVip:IsShowRedPoint()
    if havePush or headRed then
        self:SetRedPoint(MainFaceDefine.Setting, true)
    else
        self:SetRedPoint(MainFaceDefine.Setting, false)
    end
end

function UI_MainFace:SetListRedPoint()
    local havePush = NetContactData:IsHavePushMsg()
    local haveChatRed = NetGlobalData:GetIsAIChatRed()
    local headRed = NetInfoData:IsShowHeadRed() or NetHeadSeasonVip:IsShowRedPoint()
    local mailRed = RedPointMgr:IsRed(RedID.MailRoot)
    local rankRed = NetFriendData:GetNewDailyRewardRed()
	local inviteRed = NetInviteFriendData:CheckRedPoint()
    if headRed or mailRed or rankRed or inviteRed or haveChatRed then
        if not self.setIsOpen then
            self:SetRedPoint(MainFaceDefine.SetList, true)
        end
    else
        self:SetRedPoint(MainFaceDefine.SetList, false)
    end
	SetActive(self.ui.m_goAIChatRed,haveChatRed)
end

function UI_MainFace:SetTinyGameRedPoint()
	--小游戏提示红点
	if TinyGameMgr:GetTinyGameID() <= 0 then
		return
	end
	
	TinyGameMgr:RefreshPassStageID()
	
	--self:SetRedPoint(MainFaceDefine.TinyGamePoint, TinyGameMgr:IsRedPointTips())
	--local num , IsEnd  = TinyGameMgr:GetPassStageIDShow() 
	--self.ui.m_txtShowGameNum.text = num
	--SetActive(self.ui.m_imgTinyGameEnd,not IsEnd)
end

function UI_MainFace:InitTinyGameData()
	if TinyGameMgr:GetTinyGameID() <= 0  then
		return
	end
	local tgIcon = GetChild(self.ui.m_btnTinyGame, "btnBg/Image", UEUI.Image)
	local tgCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.tinygame, TinyGameMgr:GetTinyGameID())
	if tgCfg then
		SetImageSprite(tgIcon, tgCfg.icon, false)
	end
end

--处理和解锁地块相关的 UI 功能
function UI_MainFace:CloudEvent(mapId, id)
	--local isOpen = NetMapNoteData:GetNoteCount(MAP_ID_MAIN,NetMapNoteData.ID.cloud_unlocked,2)
	--if isOpen > 0 then
	--	self:SetButtonGray(AddBtnDefine.diamond, false)
	--else
	--	self:SetButtonGray(AddBtnDefine.diamond, true)
	--end
	--SetActive(self.ui.m_goGrowthFund,NetGrowthFund:IsOpenActivity())
end

function UI_MainFace:WorkerInit(isClickId)
    local isShow = NetUpdatePlayerData:GetPlayerInfoByKey("workInit")
    if isClickId then
        self:CreateScheduleFun(function()
            if isClickId == ItemID.Worker1 then
                SetActive(self.ui.m_goWork1,true)
                self.mUIEffects[MainFaceDefine.Work1]:Play()
            elseif isClickId == ItemID.Worker2 then
                SetActive(self.ui.m_goWork2,true)
                self.mUIEffects[MainFaceDefine.Work2]:Play()
            end
        end,1.1,1)
    else
        SetActive(self.ui.m_goWork1,isShow)
        SetActive(self.ui.m_goWork2,isShow)
    end
    if isShow then
        self:SetPayWorker(true)
    end
    --error test : self.ui.m_goWork1.fillAmount = 1
end

function UI_MainFace:UpdateAiWorker()
	if IntelligentWorkerManager:HasOpen() then
		SetActive(self.ui.m_goAiWork,true)
        if self.rightBottomList[FolderItemType.AiWorker].isOpen == false then
            self.rightBottomList[FolderItemType.AiWorker].isOpen = true
            self:UpdateRightBottomArow()
            SetActive(self.rightBottomList[FolderItemType.AiWorker].go, true)
        end
        self:FoldRightBottom()
		SetActive(self.ui.m_imgAiWorkRed,IntelligentWorkerManager:HasRedPoint())

		local time = IntelligentWorkerManager:GetRemainTime()
		if time >= OneDaySeconds then
			self.ui.m_txtAiWorkerTime.text = math.floor(time / OneDaySeconds).. LangMgr:GetLang(2040)
		elseif time < OneDaySeconds and time >= 3600 then
			self.ui.m_txtAiWorkerTime.text = math.floor(time / 3600) ..LangMgr:GetLang(2041)
		else
			self.ui.m_txtAiWorkerTime.text = TimeMgr:TimeToMS(math.floor(time))
		end
	else
        SetActive(self.ui.m_goAiWork, false)
        self.rightBottomList[FolderItemType.AiWorker].isOpen = false
        SetActive(self.rightBottomList[FolderItemType.AiWorker].go, false)
	end
end

--更新界面体力相关的数据
function UI_MainFace:OnEnergyPoint(param)
	
	--如果是界面打开的状态不更新任何的状态
	local bViewDisplay = NetEnergyLotteryData:get_ViewDisplay()
	if bViewDisplay then
		
		self:SetButtonGray(AddBtnDefine.energy, true)
		SetActive(self.energyRow, false)
		SetActive(self.freeEnergyTip, false)
		UIMgr:Refresh(UIDefine.UI_MainFace, 6, {MainFaceDefine.Energy, false})
		
	else
		--按钮部分
		local isStart = NetEnergyLotteryData:get_IsStart()
		self:SetButtonGray(AddBtnDefine.energy, not isStart)

		--箭头
		if isStart then
			local isFreeHigh =  NetEnergyLotteryData:get_IsHighFreeOver()
			SetActive(self.energyRow, not isFreeHigh)
			if not isFreeHigh then
                self.energyRow.transform.anchoredPosition = Vector2.New(-14, -128)
				DOKill(self.energyRow.transform)
				DOLocalMoveY(self.energyRow.transform , -90 , 0.5 , nil ,Ease.InOutSine):SetLoops(-1 , LoopType.Yoyo)
			end
		else
			SetActive(self.energyRow, false)
		end

		--需要先处理刮刮卡的数据是否解锁
		local isOpenCard = NetEnergyLotteryData:Is_UnLockLogicData(1)
		if isOpenCard then
			isOpenCard = NetEnergyLotteryData:ui_IsShowCondition_1(1)
		end
		if isOpenCard then
			--免费刮刮卡
			local isFreeCard = EnergyLotteryModule:get_CurState()
			SetActive(self.freeEnergyTip, isFreeCard == CARD_STATE.FREE)
			--local AdState , subTime = ADMovieModule:State(ADMovieModule.EM_AD_ID.ENERGY_MAIN_0)
			--红点儿
			UIMgr:Refresh(UIDefine.UI_MainFace, 6, {MainFaceDefine.Energy, isFreeCard == CARD_STATE.CANREWARD})

		else
			--广告体力
			--local AdState , subTime = ADMovieModule:State(ADMovieModule.EM_AD_ID.ENERGY_MAIN_0)
			
			SetActive(self.freeEnergyTip, false)
			UIMgr:Refresh(UIDefine.UI_MainFace, 6, {MainFaceDefine.Energy, false})
		end
	end
end

function UI_MainFace:TickUI(deltaTime)
    for _, v in pairs(self.taskItems) do
        v:TickUI(deltaTime)
    end    
	for _, v in pairs(self.taskFlyItems) do
        v:TickUI(deltaTime)
    end

    self.pushMsgTime = self.pushMsgTime - deltaTime
    if self.pushMsgTime < 0 then
        self.pushMsgTime = self.pushMsgInterval
        if IsTableNotEmpty(self.pushMsgList) then
            local data = self.pushMsgList[1]
            if data.type == 300 then
                self:PushLeagueAnnounceMsg(data.msg)
                table.remove(self.pushMsgList, 1)
            elseif data.type == 302 then
                self:PushFriendMsg(data.msg)
                table.remove(self.pushMsgList, 1)
            end
        end
    end
    --HeroController:TickHeroArrowIcon(deltaTime, self.ui.m_rtransHeroArrow, self.ui.m_rtransHeroArrowTemp)
end

function UI_MainFace:RemoveGift(param)
	local item = self.gifts[param]
	if item == nil then
		return
	end
	self.gifts[param] = nil
	item:Close()
	SetActive(self.ui.m_scrollviewActivity,false)
	SetActive(self.ui.m_scrollviewActivity,true)
	if table.count(self.gifts) <= 0 then
		local level = LevelConfig:GetLevel()
		local isGetAllReward = NetFirstGiftData:CheckAllReward(true)
		if not isGetAllReward and level >= GlobalConfig.OPEN_FIRSTPAY_LEVEL then
			
		else
			SetActive(self.ui.m_scrollviewActivity,false)
		end
	end
	
	--if not isAdd then
	--local sortGifts = NetGiftBox:GetBoxSort()
	--local firstId = sortGifts[1]
	--if firstId then
		--self:OnRefresh(2,firstId)
	--else
		--SetActive(self.ui.m_scrollviewActivity,false)
	--end
	--end

	--for i, v in ipairs(self.gifts) do
	    --if v.id == id then
	        --v.item:Close()
	        --table.remove(self.gifts,i)
	        --break
	    --end
	--end
	--if #self.gifts >= 2 then
	    --return
	--end
	--local sortGifts = NetGiftBox:GetBoxSort()
	--if #sortGifts > 1 then
	    --for _,giftId in ipairs(sortGifts) do
	        --if self.gifts[1] == nil or giftId ~= self.gifts[1].id then
	            --self:OnRefresh(2,giftId)
	        --end
	    --end
	--end
end

function UI_MainFace:InitResourcePos()
	local ResourceItemsGo = GetChild(self.uiGameObject, "RightTop/ResourceItems")
	SetUIForceRebuildLayout(ResourceItemsGo)
	SetUIForceRebuildLayout(self.ui.m_goBottomRoot)
    local pos
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doOrder.transform.position)
    MapController:SetUIResourcePos(0, pos.x, pos.y)
    --pos = UIMgr:GetUIPosByWorld(self.ui.m_doCollect.transform.position)
    --MapController:SetUIResourcePos(0, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doEnergy.transform.position)
    MapController:SetUIResourcePos(ItemID.ENERGY, pos.x, pos.y)
    MapController:SetUIResourcePos(ItemID.LimitEnergy, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doDiamond.transform.position)
    MapController:SetUIResourcePos(ItemID.DIAMOND, pos.x, pos.y)
	pos = UIMgr:GetUIPosByWorld(self.ui.m_doRedDiamond.transform.position)
	MapController:SetUIResourcePos(ItemID.RED_DIAMOND, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doCoin.transform.position)
    MapController:SetUIResourcePos(ItemID.COIN, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doMagic.transform.position)
    MapController:SetUIResourcePos(ItemID.MAGIC, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doExp.transform.position)
    MapController:SetUIResourcePos(ItemID.EXP, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doDiamond.transform.position)
    MapController:SetUIResourcePos(ItemID.TUANDUIGONGXIAN, pos.x, pos.y);

    -- 设置工人的资源位置
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doWorker.transform.position)
    MapController:SetUIResourcePos(ItemID.Worker1, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doWorker.transform.position)
    MapController:SetUIResourcePos(ItemID.Worker2, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doWorker.transform.position)
    MapController:SetUIResourcePos(ItemID.Worker3, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_imgAiWorkRed.transform.position)
    MapController:SetUIResourcePos(ItemID.Worker4, pos.x, pos.y)

    pos = UIMgr:GetUIPosByWorld(self.ui.m_goAir.transform.position)
    MapController:SetUIResourcePos(FlyId.Air, pos.x, pos.y)
	
	pos = UIMgr:GetUIPosByWorld(self.ui.m_goCollect.transform.position)
	MapController:SetUIResourcePos(ItemID.COLLECTION, pos.x, pos.y)
	
	pos = UIMgr:GetUIPosByWorld(self.ui.m_imgHead.transform.position)
	MapController:SetUIResourcePos(ItemID.Head_UIMainFace, pos.x, pos.y)
	
	pos = UIMgr:GetUIPosByWorld(self.ui.m_imgHead.transform.position)
	MapController:SetUIResourcePos(ItemID.SkinBtn, pos.x, pos.y)	
	-- 表情包解锁飞往聊天界面
	pos = UIMgr:GetUIPosByWorld(self.ui.m_goFriend.transform.position)
	MapController:SetUIResourcePos(ItemID.EmojiUnLockItem, pos.x, pos.y)
	self:ResetDOLLARPos()
	self:InitDailyTargetResourcePos()
	if #self.taskPos >= 1 then
		pos = UIMgr:GetUIPosByWorld(self.ui.m_goSeason.transform.position)	--self.taskPos[1]
		MapController:SetUIResourcePos(ItemID.SeasonEnergy, pos.x, pos.y)
	end

    -- 收藏册位置
    local collectionPos = UIMgr:GetUIPosByWorld(self.ui.m_goCollect.transform.position)
    LimitActivityController:SetCollectionPos({ collectionPos.x, collectionPos.y })

	

	local m_goMsBuffPos = UIMgr:GetUIPosByWorld(self.ui.m_goMsBuff.transform.position)
	NetGlobalData:SetMsBuffPos(m_goMsBuffPos)
	local PrivilegeLayoutGo = GetChild(self.uiGameObject, "RightBottom/m_goRightBottomList/Layout")
	SetUIForceRebuildLayout(PrivilegeLayoutGo)
	local privilegePos = UIMgr:GetUIPosByWorld(self.ui.m_goPrivilege.transform.position)
	NetGlobalData:SetPrivilegePos(privilegePos)
end

function UI_MainFace:InitDailyTargetResourcePos()
	local pos = UIMgr:GetUIPosByWorld(self.ui.m_goCurDailyIcon.transform.position)
	for i = 1, 7 do
		local key = string.format("DailyTargetItem%s",i)
		local ItemId = ItemID[key]
		MapController:SetUIResourcePos(ItemId, pos.x, pos.y)
	end
end

function UI_MainFace:InitRedPoint()
	--市场的红点儿
	self:UpdateMarketRedDot();
	
	--收集的红点儿
	local isShow = CollectionItems:MainRedPoint()
	self:SetRedPoint(MainFaceDefine.Collect, isShow)
	
	local count = NetMailData:GetPointCount()

	self:SetRedPoint(MainFaceDefine.Mail, count > 0)
    self:SettingRedPoint()
    self:SetListRedPoint()
	self:SetTinyGameRedPoint()
	--self:SetRedPoint(MainFaceDefine.SetList, havePush)
	
	local collectionEffect = GET_UI(self.uiGameObject, "CollEffect", "RectTransform")
	SetActive(collectionEffect, isShow)
	
	EffectConfig:CreateEffect(17, 0, 0, 0, collectionEffect.transform, function(data, tGo, go)
		self:SortOrderAllCom(true)
	end)

	EffectConfig:CreateEffect(28, 0, 0, 0, collectionEffect.transform, function(data, tGo, go)
		self:SortOrderAllCom(true)
	end)

    if self.ui.m_goEnergyTaskPoint then
        local isHaveEnergyTaskReward = NetEnergyTaskData:IsHaveReward()
        SetActive(self.ui.m_goEnergyTaskPoint,isHaveEnergyTaskReward)
    end
	--月卡红点
	--self:ChangeMonthCardRed()
	--首充红点
	local isShow = NetFirstGiftData:GetFirstGifyRed()
	SetActive(self.ui.m_imgFirstGiftRed,isShow) 

	--特权月卡
	self:ChangePrivilegeCardRed()
	--活动中心
	self:ChangeActCenterRed()
	--右下角折叠按钮红点
	--self:CheckRBFoldBtnRed()
	-- 联盟红点
	NetLeagueData:ResetRedDotState();
	SetActive(self.ui.m_goUnionRed, NetLeagueData:GetAllRedPoint());
	SetActive(self.ui.m_goLeagueHelp,NetLeagueData:CanHelpOther())
    -- 好友红点
    self:SetRedPoint(MainFaceDefine.FriendRed, NetFriendData:GetAllRedPoint())
    local hasFriendRed, hasStrangerRed = NetFriendData:HasFriendMessageRed()
    if hasFriendRed or hasStrangerRed then
        SetActive(self.ui.m_goChatTip, true)
    end
	--多倍采集入口红点
	self:SetMultipleRed()
	-- 英雄红点
	SetActive(self.ui.m_goHeroRed, RedPointMgr:IsRed(RedID.HeroRedEntry));
	SetActive(self.ui.m_goBagRed, RedPointMgr:IsRed(RedID.BagRedEntry));
	SetActive(self.ui.m_goLotteryRed, RedPointMgr:IsRed(RedID.LotteryEntry));
    SetActive(self.ui.m_goTradeWagonsRedPoint, RedPointMgr:IsRed(RedID.TradeWagons))
	SetActive(self.ui.m_goArenaRedPoint, RedPointMgr:IsRed(RedID.ArenaEntry))
	SetActive(self.ui.m_goFullBoxDot, RedPointMgr:IsRed(RedID.DungeonEntry))
	SetActive(self.ui.m_goWorldBossRed, RedPointMgr:IsRed(RedID.WorldBoss))
	SetActive(self.ui.m_goBattleRed, RedPointMgr:IsRed(RedID.BattlePass))
	SetActive(self.ui.m_goTopFightRed, RedPointMgr:IsRed(RedID.TopFight) or NetLimitActGift:IsShowRed(TopFightManager.GIFT_TYPE))
end

function UI_MainFace:UpdateMarketRedDot()
	local curMapID = NetUpdatePlayerData.playerInfo.curMap
	local isReceived2 = MarketConfig:isHaveRedPoint(curMapID, 2)
    local isReceived4 = MarketConfig:isHaveRedPoint(curMapID, 4)--MarketConfig:redPoint()
    local isReceived = isReceived2 or isReceived4
    self:SetRedPoint(MainFaceDefine.Market, isReceived)
end

function UI_MainFace:ChangePrivilegeCardRed()
	local isShow = NetPrivilegeCardData:CheckEnergyRed()
	SetActive(self.ui.m_goPrivilegeRed,isShow)
	
end

function UI_MainFace:ChangeMonthCardRed()
	local isShow = NetMonthCardData:CheckMonthRedPoint()
	SetActive(self.ui.m_goMonthCardRed,isShow)
end

function UI_MainFace:ChangeActCenterRed()
	local actCenter,num = self:CheckActCenterRed()
	SetActive(self.ui.m_goActPoint,actCenter)
	--SetActive(self.ui.m_txtActPointNum.gameObject,num > 1)
	--self.ui.m_txtActPointNum.text = num
	local actView = UIMgr:GetUIItem(UIDefine.UI_ActCenter)
	if nil ~= actView and actView.isShow then
		actView:UpdateRedPoint()
	end
end

function UI_MainFace:SetPayWorker(isInit)
    local timeValue = WorkerController:GetHaveWorkTime()
    local isPayWork = timeValue > 0
    --SetActive(self.ui.m_goWorkBuy,not isPayWork)
    --SetActive(self.ui.m_goWorkNotBuy,isPayWork)    
    SetActive(self.ui.m_goPayWorkerTimeBg,isPayWork)
    --if isPayWork then
    --    self.ui.m_imgWorkSlider.fillAmount = timeValue / WorkMaxTime
    --end
    --if isInit or isPayWork then
    --    self:ChangeWorker()
    --end

	local len = WORKER_CNT_NORMAL + WORKER_CNT_PAY + WORKER_CNT_EXPAND;
	local unLock;
	local workNum = 0;
	local activeNum = 0;
	for i = 1, len do
		if i <= WORKER_CNT_NORMAL then
			unLock = true;
		elseif i <= WORKER_CNT_NORMAL + WORKER_CNT_PAY then
			unLock = isPayWork;
		elseif i <= len then
			local expandId = i - WORKER_CNT_NORMAL - WORKER_CNT_PAY;
			unLock = WorkerController:IsExpandWorkerUnlock(expandId);
		end

		if unLock then
			if not WorkerController:GetWorkingById(i) then
				workNum = workNum + 1;
			end
			activeNum = activeNum + 1;
		end
	end
	self.ui.m_txtWorker.text = workNum .. "/" .. activeNum
end

function UI_MainFace:ChangeWorker(id)
    Log.Info("==============ChangeWorker==============",id)
    for i = 1,3 do
        local isWork = WorkerController:GetWorkingById(i)
        if isWork then
            if not self.workState[i] then
                self.workState[i] = true
                SetActive(self.ui["m_doWorkCount"..i],true)
                SetActive(self.ui["m_doWork"..i],false)
                self.ui["m_doWorkCount"..i]:DORestart()
            end
        else
            if self.workState[i] then
                self.workState[i] = false
                SetActive(self.ui["m_doWorkCount"..i],false)
                SetActive(self.ui["m_doWork"..i],true)
                self.ui["m_doWork"..i]:DORestart()
            end
        end
    end

    --self:CheckTipsPayWorker()
end

--有元素变化的时候会走到这里
function UI_MainFace:ChangeResource(type, num, changeValue,isTop)
	local coinBtn = GET_UI(self.uiGameObject, "m_imgCoinBuy", TP(UEUI.Button))
	local guideId = GlobalConfig:GetNumber(1436,2)
	local isEnd = GuideController:MaxId() >= guideId --  GuideController:IsGuideDone(guideId)
	local curMap = NetUpdatePlayerData.playerInfo.curMap
	if curMap == MAP_ID_SECOND then
		isEnd = true
	end
	if not isEnd then
		--置灰
		self:SetButtonGray(AddBtnDefine.coin, true)
	else
		self:SetButtonGray(AddBtnDefine.coin, false)
	end

    local info = NetUpdatePlayerData:GetPlayerInfo()
    --初始化的时候type为空
    if type == nil then
        self.ui.m_txtDiamond.text = math.floor(info["diamond"])
        self.ui.m_txtCoin.text = math.floor(info["coin"])
        self.ui.m_txtMagic.text = info["magic"]
		self.ui.m_txtDollar.text = info["dollar"]
		local redNum = RedDiamondManager:GetRedDiamondNum()
		self.ui.m_txtRedDiamond.text = NumToGameString(redNum)
        self:SetEnergy(info["energy"])
        --初始化设置状态
        self:ChangeEnergyState(1)
    end
	--时间恢复的逻辑
	if type == PlayerDefine.Energy then
		local span = EnergyModule:get_PowerRecoveryTimeLeft()
		if span <= 0 then
			if info.energy < maxEnergy then
				self:AddTimerCom(PlayerDefine.Energy, 1)
			else
				self:RemoveTimerCom(PlayerDefine.Energy)
			end
		end
		local span2 = EnergyModule:get_PowerTimeLeft(GIFT_SCOPE.MAIN_CAMP)
		if span2 <= 0 then
			if info.energy < maxEnergy then
				EnergyModule:set_StartReciveTime(nil,GIFT_SCOPE.MAIN_CAMP)
			end
		end
		
		self:SetMultipleRed()
	end
    function FinishOrder(type)
		
    end

    if type == PlayerDefine.Exp then
        self:SetLevel(nil, changeValue)
    elseif type == PlayerDefine.Magic then
        local magic = info["magic"]
        self.ui.m_doMagic:DORestart()
        AddDOTweenNumberDelay(magic - changeValue, magic, 0.3, 0.8, function(value)
           
            self.ui.m_txtMagic.text = math.floor(value)
        end)
		if changeValue >0 then
			AudioMgr:Play(43)
		end
    elseif type == PlayerDefine.Diamond then
        local diamond = info["diamond"]
        self.ui.m_doDiamond:DORestart()
        AddDOTweenNumberDelay(diamond - changeValue, diamond, 0.3, 0.8, function(value)
        
            self.ui.m_txtDiamond.text = math.floor(value)
        end,function() FinishOrder(PlayerDefine.Diamond) end)
		if changeValue >0 then
			AudioMgr:Play(12)
		end
    elseif type == PlayerDefine.Coin then
        local coin = info["coin"]
        self.ui.m_doCoin:DORestart()
        AddDOTweenNumberDelay(coin - changeValue, coin, 0.3, 0.8, function(value)
           
            self.ui.m_txtCoin.text = math.floor(value)
        end,function() FinishOrder(PlayerDefine.Coin) end)
		if changeValue > 0 then
			AudioMgr:Play(10)
		end
    elseif type == PlayerDefine.Energy then
        local energy = info["energy"]
        self.ui.m_doEnergy:DORestart()
        AddDOTweenNumberDelay(energy - changeValue, energy, 0.3, 0.8, function(value)
			--local vvv = FinishOrder(PlayerDefine.Energy)
			--Log.Error("vvv",energy,changeValue,vvv)
            self:SetEnergy(math.floor(value))
				
        end,function() FinishOrder(PlayerDefine.Energy) end)
		if changeValue >0 then
			AudioMgr:Play(11)
		end
		
		if LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.EnergyBuff) then
			if changeValue < 0 then
				NetEnergyBuff:OnEnergyChange(changeValue)
			end

			UIMgr:Refresh(UIDefine.UI_MainFace, 9, { id = NetEnergyBuff:GetActiveId(), state = 2 })
		end
	elseif type == PlayerDefine.Dollar then
		local dollar = info["dollar"]
		self.ui.m_doDollar:DORestart()
		AddDOTweenNumberDelay(dollar - changeValue, dollar, 0.3, 0.8, function(value)
				self.ui.m_txtDollar.text = math.floor(value)
			end)
		if changeValue >0 then
			AudioMgr:Play(43)
		end
		self:RefreshTgArrow()
    end
end

function UI_MainFace:SetEnergy(energy)
	if energy == 0 then
		self.ui.m_txtEnergy.text = GetStrRichColor(0, "ff5e5e") .. "/" .. maxEnergy
	elseif energy > maxEnergy then
		self.ui.m_txtEnergy.text = GetStrRichColor(tostring(math.floor(energy)), "24ff52") .. "/" .. maxEnergy
	else
		self.ui.m_txtEnergy.text = tostring(math.floor(energy)) .. "/" .. maxEnergy
	end
	self.ui.m_imgEnergySlider.fillAmount = tostring(math.floor(energy)) / maxEnergy
end

function UI_MainFace:RefreshTgArrow()
	local info = NetUpdatePlayerData:GetPlayerInfo()
	if v2n(info["dollar"]) >= 100 and v2n(info.levelid) <= 5 and v2n(info.levelid) > 1 then
		SetActive(self.ui.m_goTgArrow,true)
	else
		SetActive(self.ui.m_goTgArrow,false)
	end
	SetActive(self.ui.m_goTinyGamePoint,TinyGameMgr:IsNextStageUnlock())
end                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

function UI_MainFace:ChangeTask(id, progress, isInit)
    if id then
        if progress == TaskProgress.Finish or (self.taskItems[id] and progress == nil) then
            self:RemoveTaskItem(id)
			self:PushFinishTaskQuence(id)
        elseif isInit then
            self:AddTaskItem(id, progress)
        else
            self:ChangeTaskValue(id, progress)
			self:UpdateMainTask()
        end
    end
end

--控制计时器
function UI_MainFace:AddTimerCom(type, time)
    if type == PlayerDefine.Energy then
        self:ChangeEnergyState(2)
        self.timerTime[type] = time
    end
end

function UI_MainFace:RemoveTimerCom(type)
    if type == PlayerDefine.Energy then
        self:ChangeEnergyState(1)
        SetActive(self.ui.m_goEnergyTime, false)
        self.timerTime[type] = nil
    end
end

function UI_MainFace:SetEnergyReceive(isReceive)
    SetActive(self.ui.m_goEnergyRec, isReceive)
end

--设置主界面红点是否显示
function UI_MainFace:SetRedPoint(faceType,isShow)
    local ui = self.ui[faceType]
    if ui == nil then
        Log.Error("red point name is wrong",faceType)
        return
    end
    if ui.activeSelf ~= isShow then
		if faceType == MainFaceDefine.Market or faceType == MainFaceDefine.AirPoint then
			local conditionId = faceType == MainFaceDefine.Market and 9 or 19
			local mapType, level = FunctionOpenConfig:GetopenConditionById(conditionId)
			local isOpen = false;
			if mapType == MapType.Main and NetUpdatePlayerData:GetLevel() >= level then
				isShow = true
			end
			isShow = isShow and isOpen
		end
        SetActive(ui,isShow)
    end
	
	local uiEffect = self.mUIEffects[faceType]
	if nil ~= uiEffect then
		if uiEffect.activeSelf ~= isShow then
			SetActive(uiEffect,isShow)
		end
	end
end

--处理任务item
function UI_MainFace:AddTaskItem(id, count)
    local item = taskItem:Create(self.ui.m_goTasks)
    item:SetItem(id, function(arg1, arg2)
        self:onUIEventClick(arg1, arg2)
    end, count)
    self.taskItems[id] = item
    return item
end

function UI_MainFace:RemoveTaskItem(id)
    local item = self.taskItems[id]
    if item == nil then
        return
    end
    item:Close()
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.FINISH_MAIN_TASK,1)
    self.taskItems[id] = nil
end

function UI_MainFace:ChangeTaskValue(id, count)
    local item = self.taskItems[id]
    if item == nil then
        Log.Warning(id, ":task already not have")
        return
    end
    item:ChangeItem(count)
end

--添加item的点击事件
function UI_MainFace:AddItemBtn(btn, param)
    AddUIComponentEventCallback(btn, UEUI.Button, function(arg1, arg2)
        self:onUIEventClick(arg1, arg2)
    end, param)
end

--倒计时的都写在这里
function UI_MainFace:Timer(type)
    
    for k, v in pairs(self.timerTime) do
        if v then
            if k == PlayerDefine.Energy then
				local span = EnergyModule:get_PowerRecoveryTimeLeft() --拿到当前的剩余时间
				self.ui.m_txtEnergyTime.text = TimeMgr:GetSpecialMS(span)
				--if DailyTargetManager:IsOpenDailyTargetSwitch() then
				--	local allSpan = EnergyModule:get_PowerRecoveryAllTimeLeft()
				--	if self.ui.m_txtEnergyFullTime then
				--		self.ui.m_txtEnergyFullTime.text = TimeMgr:GetSpecialMS(allSpan)
				--	end
				--end

				if span <= 0 then
					self:RemoveTimerCom(k)
				end
            end
        end
    end
    for _, v in pairs(self.gifts) do
        v:ChangeItem()
    end    

    for _, v in pairs(self.limits) do
        v:ChangeItem()
    end

	local lastTime = MarketModule:getLastTime()
	if lastTime <= 0 then
		UIMgr:Refresh(UIDefine.UI_MainFace, 6, {MainFaceDefine.Market, true})
	end
    if self.energyState == 3 then
        local infinity = NetUpdatePlayerData:GetInfinityLastTime(PlayerDefine.Energy)
        self.ui.m_txtEnergyInTime.text = TimeMgr:GetSpecialMS(infinity)--TimeMgr:ConverSecondToString(infinity)
        if infinity <= 0 then
            local span = EnergyModule:get_PowerRecoveryTimeLeft()
            self:ChangeEnergyState(span > 0 and 2 or 1)
        end
    end
    self:SetAirPoint()
	
	self:UpdateArrow()
	self:UpdateWorker()
	self:UpdateAiWorker()
	self:CheckActToyWaste()
	self:CheckSkiingWaste()
	self:CheckNewSkiingWaste()
	self:CheckRollDiceWaste()
	self:CheckLuckOreWaste()
	self:CheckRelicWaste()
	self:CheckActHalloweenWaste()
	self:CheckKeepCatWaste()
	if self.rightBottomArowIsPut then
		self:CheckRBFoldBtnRed()
	end
	self:UpdateShushuGift()
	self:UpdateDailyTargetTime()
	--self:RefreshMultipleDropTime()
	self:RefreshTriggerGiftTime()
	self:FinishTaskQuenceTimer()
	self:CheckPlayNewTaskEff()
	self:ActivityRankTabTimer()
	self:UpdateDownTimer()
	self:GiftDailyFreeTimer()
	self:UpdateRoleGift()
	self.ui.m_txtGiftPack.text =  TimeMgr:CheckHMS(GiftPackModule:getLastTime())
	
	--if MapController:GetMagnetState() then
		--CombinePopMgr:IsShowMagnet()
	--end
	
	local isNewGetAllReward = NetNewFirstGiftData:CheckAllReward()
	if isNewGetAllReward then
		SetActive(self.ui.m_goNewFirstPack,false)
	else
		local time = NetNewFirstGiftData:GetNewFirstEndTime()
		if time > 0 then
			self.ui.m_txtNewFirstTime.text = TimeMgr:CheckHMS(time)
		else
			SetActive(self.ui.m_goNewFirstPack,false)
		end
	end
	
	if NetPrivilegeCardData:ClockReached() then
		self:RefreshMagnetState()
	end
	--if self.privilegeCardTime < 30 then
		--self.privilegeCardTime = self.privilegeCardTime + 1
	--else
		--self.privilegeCardTime = 0
		--if NetPrivilegeCardData:IsCanMagnetGuide() then
			--self:PlayPrivilegeHandAnim()
		--end
	--end
	
	if self.fingerWaitT then
		if self.fingerWaitT < 30 then
			self.fingerWaitT = self.fingerWaitT + 1
		else
			self.fingerWaitT = 0
			self:IsShowFinger()
		end
	end
	 
	--if not NetPrivilegeCardData:IsHaveFreeTime() and NetPrivilegeCardData.data.magnetState == PRIVILEGE_CARD.LOCK then
	--	local time = NetPrivilegeCardData.data.magnetFreeTime - TimeMgr:GetServerTimestamp()
	--	--self.ui.m_txtPrivilegeTime.text =  TimeMgr:CheckHMS(time)
	--	self.ui.m_txtPrivilegeTime.text =  TimeMgr:GetHMS(time)
	--else
	--	self.ui.m_txtPrivilegeTime.text = "00:00"--LangMgr:GetLang(8319)
	--end
	self.ui.m_txtPrivilegeTime.text = NetPrivilegeCardData:GetMagnetTitleStr()
	
	if NetGlobalData.data.powerBuidTime and NetGlobalData.data.powerBuidTime - TimeMgr:GetServerTimestamp() > 0 then
		local time = NetGlobalData.data.powerBuidTime - TimeMgr:GetServerTimestamp()
		self.ui.m_txtMsBuff.text =  TimeMgr:GetHMS(time)
		if not self.ui.m_goMsBuff.activeSelf then
			SetActive(self.ui.m_goMsBuff,true)
		end
	else
		if self.ui.m_goMsBuff.activeSelf then
			SetActive(self.ui.m_goMsBuff,false)
		end
	end

	local endTime,isCanFree,isForever = NetGlobalData:GetADFreeTime()
	if isCanFree then
		if isForever then
			self.ui.m_txtADBuff.text =  LangMgr:GetLang(412)
		else
			self.ui.m_txtADBuff.text =  TimeMgr:GetHMS(endTime)
		end
		
		if not self.ui.m_goADBuff.activeSelf then
			SetActive(self.ui.m_goADBuff,true)
		end
	else
		if self.ui.m_goADBuff.activeSelf then
			SetActive(self.ui.m_goADBuff,false)
		end	
	end

	--7天
	if self.ui.m_goSevenDay.activeSelf then
		local remainTime = SevenDayManager:GetRemainTime()
		if remainTime > 0 then
			self.ui.m_txtDevenDay.text = TimeMgr:CheckHMSNotEmpty(remainTime)
		else
			self.ui.m_txtDevenDay.text = ""
			self:CheckSevenDayShow()
		end
	end
end

function UI_MainFace:UpdateShushuGift()
	SetActive(self.ui.m_goShushu,NetShushuGiftData:IsShowShushuIcon())
end

--function UI_MainFace:UpdateMonthCard()
--	NetMonthCardData:MonthZeroClock()
--	local is_lockWeek,timeWeek = NetMonthCardData:IsUnLockWeekCard()
--	local is_lockMonth,timeMonth = NetMonthCardData:IsUnLockMonthCard()
--	if is_lockWeek and is_lockMonth then
--		if timeWeek < timeMonth then
--			self.ui.m_txtMonthTime.text = TimeMgr:CheckHMSNotEmpty(timeWeek)
--		else
--			self.ui.m_txtMonthTime.text = TimeMgr:CheckHMSNotEmpty(timeMonth)
--		end
--	elseif is_lockWeek or is_lockMonth then
--		if nil ~= timeWeek then
--			self.ui.m_txtMonthTime.text = TimeMgr:CheckHMSNotEmpty(timeWeek)
--		end
--		if nil ~= timeMonth then
--			self.ui.m_txtMonthTime.text = TimeMgr:CheckHMSNotEmpty(timeMonth)
--		end
--	else
--		self.ui.m_txtMonthTime.text = LangMgr:GetLang(93)
--	end
--end

function UI_MainFace:CheckActCenterRed()
	local redNum = 0
	local monthRed = NetMonthCardData:CheckMonthRedPoint()
	if monthRed then
		redNum = redNum + 1
		--return true
	end
	
	--local firstGiftRed = NetFirstGiftData:GetFirstGifyRed()
	--if firstGiftRed then
		--redNum = redNum + 1
		----return true
	--end
	if not NetGiftDaily.data.getFreeDay then
		redNum = redNum + 1
	end
	
	if NetGrowthFund:IsOpenActivity() then
		local GrowthFundRed = NetGrowthFund:CheckShowRed()
		if GrowthFundRed then
			redNum = redNum + 1
			--return true
		end
	else
		local GrowthFundRedNew = NetGrowthFundNew:GetIsShowRed()
		if GrowthFundRedNew then
			redNum = redNum + 1
			--return true
		end		
	end
	
	-- 红钻充值
	local vipPurchaseInfo = NetGlobalData:GetVipPurchaseInfo()
	if vipPurchaseInfo and vipPurchaseInfo.tips and vipPurchaseInfo.tips == 1 then
		if vipPurchaseInfo.first_reward and v2n(vipPurchaseInfo.first_reward) == 2 then
			redNum = redNum + 1
		end
	end
	
	-- 每日必买 免费礼包
	if BagManager.freeGiftRed then
		redNum = redNum + 1;
	end
	
	--累充礼包
	if PurchaseManager:CheckRedDot() then
		redNum = redNum + 1
	end
	
	return redNum > 0,redNum
end

function UI_MainFace:UpdateWorker()
	local time = WorkerController:GetHaveWorkTime()
	if time < 0 then
		return
	end
	self.ui.m_txtWorkerTime.text = TimeMgr:CheckHMSNotEmpty(time)
	--if time >= OneDaySeconds then
	--	self.ui.m_txtTime.text = math.floor(time / OneDaySeconds).. LangMgr:GetLang(2040)
	--elseif time < OneDaySeconds and time >= 3600 then
	--	self.ui.m_txtTime.text = math.floor(time / 3600) ..LangMgr:GetLang(2041)
	--else
	--	self.ui.m_txtTime.text = math.floor(time / 60)..LangMgr:GetLang(2042)
	--end
	
	--self.ui.m_txtTime.text = TimeMgr:CutBuyWorkTime(time)
	--if time <= 0 then	
		--self:SetPayWorker(true)
	--end
end

function UI_MainFace:UpdateArrow()
	if self.energyArrow ~= nil then
		self.energyArrow = self.energyArrow - 1
		if self.energyArrow <= 0 then
			self.energyArrow = nil
			SetActive(self.ui.m_energyArrow,false)
		end
	end
	if self.airArrow ~= nil then
		self.airArrow = self.airArrow - 1
		if self.airArrow <= 0 then
			self.airArrow = nil
			SetActive(self.ui.m_goArrArrow,false)
		end
	end
end

function UI_MainFace:SetAirPoint()
    local data = BackgroundConfig:GetData()
    local level = LevelConfig:GetLevel()
    local isRed = false
    local limitIsOpen,active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.LimitIns)
	
	local curActivieMapId = 0
	if limitIsOpen and active and active.form then
		curActivieMapId = active.form.map
	end
    for mapId, v in pairs(data) do
		--if (v.map_type ~= MapType.Limit or limitIsOpen)  and mapId ~= NetUpdatePlayerData.playerInfo.curMap then
		if (v.map_type ~= MapType.Limit and mapId ~= NetUpdatePlayerData.playerInfo.curMap)
				or (limitIsOpen and mapId == curActivieMapId and mapId ~= NetUpdatePlayerData.playerInfo.curMap) then
            if not v.unlock_lv or v.unlock_lv <= level then
                if MarketConfig:isHaveRedPoint(mapId) then
                    isRed = true
                    break
                end
                local reward = NetMarketData:GetPopReward(mapId)
                if reward then -- gift point
                    isRed = true
                    break
                end
                if v.map_type ~= MapType.Zoo then -- energy point
                    if EnergyModule:get_IsEnergyMax(mapId) then
                        isRed = true
                        break
                    end
                end
            end
        end
    end
    self:SetRedPoint(MainFaceDefine.AirPoint,isRed)
end

--设置等级经验，只改经验level可以传nil
function UI_MainFace:SetLevel(id, add)
    function SetLevelSlider(value,max)
        self.ui.m_txtExp.text = NumToGameString(value)  .. "/" .. NumToGameString(max)
        local fill = value / max
        --self.ui.m_imgSlider.fillAmount = fill
        --self.rotateTrans.localRotation = Quaternion.Euler(0, 0, (0.5 - fill) * 360)
        --self.ui.m_imgSliderMask.fillAmount = fill+0.2
		self.ui.m_sliderExp.value = fill
    end
    local level, exp, maxExp = NetUpdatePlayerData:GetLevel()
    maxEnergy = LevelConfig:GetLimitEnergy()
    self:SetButtonGray(AddBtnDefine.worker,level<workOpenLevel)
    self.ui.m_txtLev.text = level
    if add then
        AddDOTweenNumberDelay(exp - add, exp, 0.3, 0.8, function(value)
            value = math.floor(value)
            SetLevelSlider(value ,maxExp)
        end)
        AudioMgr:Play(2)
        self.ui.m_doExp:DORestart()
    else
        SetLevelSlider( exp, maxExp)
    end
	self:CheckGiftPackShow()
	self:CheckSevenDayShow()
	self:SetTinyGameRedPoint()
	self:ResetTaskViewimg()
	
	SetActive(self.ui.m_goGrowthFund,NetGrowthFund:IsOpenActivity())
	SetActive(self.ui.m_goGrowthFundNew,NetGrowthFundNew:IsOpenActivity())
	--SetActive(self.ui.m_btnSkin.gameObject,level >= GlobalConfig.OPEN_SKINCONTROLL_LEVEL)
	SetActive(self.ui.m_goFriend, FriendManager:IsOpen())
	SetActive(self.ui.m_goPrivilege.gameObject,level >= GlobalConfig.OPEN_PRIVILEGECARD_LEVEL)
	self:ChangePrivilegeCardRed()
    self.rightBottomList[FolderItemType.Magnet].isOpen = level >= GlobalConfig.OPEN_PRIVILEGECARD_LEVEL
    SetActive(self.rightBottomList[FolderItemType.Magnet].go, level >= GlobalConfig.OPEN_PRIVILEGECARD_LEVEL)
	SetActive(self.ui.m_btnMultipleDrop,NetMulitpleDropData:IsOpenActivity())
	SetActive(self.ui.m_goActCenter,level >= GlobalConfig.OPEN_ACTCENTER_LEVEL)
	SetActive(self.ui.m_goUnion, LeagueManager:IsOpenUnion())
	--SetActive(self.ui.m_btnAIChat.gameObject,level >= 6)
	
	if id then
		local openLevel = DailyTargetManager:GetShowDailyTargetLevel()
		if level == openLevel then
			self.dailyTargetUnLockAni:Play("anim_dailytargetunlcok")
			self:DailyTargetInit()
		end

		self:SetListRedPoint();
		self:UpdateMarketRedDot();
	end

	if NetMulitpleDropData:IsOpenActivity() then
		local curMultiple = NetMulitpleDropData:GetDataByKey("curSelectMultiple")
		-- 第一次开启自动设为3倍
		if curMultiple == MultipleNum.NONE then
			NetMulitpleDropData:ChangeSelectMultiple(MultipleNum.ONE)
			self:SetMultipleRed()
		end
	end
	self:UpdateRightBottomArow()
	self:UpdateSeasonInfo()	-- 季节活动按钮信息初始化
	self:UpdateWenjuan()
end

function UI_MainFace:AddLimits(param)
    local item
	local parent = self.ui.m_goLimitList
	local function loadCallBack(obj)
		item = obj

		if not item then
			return
		end
		self:ResetTaskViewimg()
		item:SetItem(param)
		self.limits[param.id] = item
		SetUIForceRebuildLayout(self.ui.m_goUp)
		self.limitsGo[param.id] = item.go
		--if param.showUI == 3 and LimitActivityController:IconIsCanCreate(param.id) then
		--item = GoTask:Create(self.ui.m_goUp)
		--self:ResetTaskViewimg()
		--self.limitsSpeGo[param.id] = item
		--item:SetItem(param,1)
		--end
		self:SortGoDownRootUIIndex()
		--SetActive(self.ui.m_goGrowthFund,NetGrowthFund:IsOpenActivity())
		--SetActive(self.ui.m_goGrowthFundNew,NetGrowthFundNew:IsOpenActivity())

		if param.totalType == ActivityTotal.LimitRank then -- or param.totalType == ActivityTotal.Rank
			self:CheckRankAutoOpen(item, param)
		end
		--self:SortOrderAllCom(true)

		--self:AutoFitTaskList()
	end
	
    if param.condition == 3 then
    	if param.totalType ~= ActivityTotal.BowlingBattle then
    		GoLimit3:Create(self.ui.m_goActivity,loadCallBack);
    	end
    elseif param.totalType == ActivityTotal.Extra or
		param.totalType == ActivityTotal.BuildBuff or
		param.totalType == ActivityTotal.GoldBuff then
        GoLimit2:Create(self.ui.m_goUp,loadCallBack) --m_goDown
    elseif param.totalType == ActivityTotal.LimitRank then -- or param.totalType == ActivityTotal.Rank
    	GoLimit4:Create(self.ui.m_goUp,loadCallBack) --m_goDown
   	elseif param.totalType == ActivityTotal.Rank then
		self.limitsGo[param.id] = self.ui.m_scrollviewActivityEnter.gameObject;
   		self:CheckRankAutoOpen(nil, param)
	elseif param.totalType == ActivityTotal.saving_bank then
		if NetSavingBank:GoLimitCanShow() then
			GoLimit5:Create(self.ui.m_goActivity,loadCallBack)
		else
			return nil
		end
		--local pos = UIMgr:GetUIPosByWorld(item.transform.position)
		--MapController:SetUIResourcePos(ItemID.xxxx, pos.x, pos.y)
	elseif param.totalType == ActivityTotal.Endless then
		GoLimit10:Create(self.ui.m_goActivity,loadCallBack)
	elseif param.totalType == ActivityTotal.FlashBuy then
        GoLimit12:Create(self.ui.m_goActivity,loadCallBack)
	elseif param.totalType == ActivityTotal.Toy then
		GoLimit13:Create(self.ui.m_goUp,loadCallBack)		
	elseif param.totalType == ActivityTotal.EnergyBuff then
		--item = GoLimit14:Create(self.ui.m_goDown)
		GoLimit14:Create(self.ui.m_goUp,loadCallBack)
	elseif param.totalType == ActivityTotal.EasterEgg then
		GoLimit2:Create(self.ui.m_goUp,loadCallBack)
	elseif param.totalType == ActivityTotal.DiamondBuff then
		GoLimit2:Create(self.ui.m_goUp,loadCallBack)
	elseif param.totalType == ActivityTotal.DiyGift then
		GoLimit5:Create(self.ui.m_goActivity,loadCallBack)
	elseif param.totalType == ActivityTotal.FlowerCompetition then
		GoLimit17:Create(self.ui.m_goUp,loadCallBack)
	elseif param.totalType == ActivityTotal.BowlingBattle then
		--item = GoLimit18:Create(self.ui.m_goUp)
		--检查参加活动的情况
    	BowlingBattleManager:CheckJoinState(
    		function()
    			BowlingBattleManager:RefreshRedPoint()
    		end
    		, false);
	elseif param.totalType == ActivityTotal.DoubleOre then
		if NetUpdatePlayerData.playerInfo.curMap == MAP_ID_MAIN then
			GoLimit19:Create(self.ui.m_goUp,loadCallBack)
		else
			return
		end
	elseif param.totalType == ActivityTotal.HalloweenOre then
		if NetHalloweenOreData:IsGetCompleteReward() and HalloweenOreManager:IsGetCompleteCollection() then
			return
		end	
		GoLimit20:Create(self.ui.m_goUp,loadCallBack)
	elseif param.totalType == ActivityTotal.MonthlySeason then
		GoLimit21:Create(self.ui.m_goUp,loadCallBack)
	elseif param.totalType == ActivityTotal.SkiingMatch then
		GoLimit22:Create(self.ui.m_goUp,loadCallBack)
	elseif param.totalType == ActivityTotal.End2less then
		GoLimit23:Create(self.ui.m_goActivity,loadCallBack)
	elseif param.totalType == ActivityTotal.RollDice then
		GoLimit24:Create(self.ui.m_goUp,loadCallBack)
	elseif param.totalType == ActivityTotal.Relic then
		GoLimit27:Create(self.ui.m_goUp,loadCallBack)
    elseif param.totalType == ActivityTotal.OneToOne then
        --item = GoLimit28:Create(self.ui.m_goDown)
		NetOneToOneData:InitActiveData(param.id)
    elseif param.totalType == ActivityTotal.AthleticTalent then
        NetAthleticTalentData:InitActiveData(param.id)
	elseif param.totalType == ActivityTotal.ContinuousRecharge then
		if ContinuousRechargeManager:HasCanReward() then
			GoLimit25:Create(self.ui.m_goActivity,loadCallBack)
		else
			return
		end
	elseif param.totalType == ActivityTotal.LuckOre then
		if NetLuckOreData:HasCanRewardAndUnlockReward() then
			GoLimit26:Create(self.ui.m_goUp,loadCallBack)
		else
			NetLuckOreData:SetAutoWaste(true)
			return 
		end
	elseif param.totalType == ActivityTotal.KeepCat then
		GoLimit30:Create(self.ui.m_goUp,loadCallBack)
	elseif param.totalType == ActivityTotal.NewSkiing then
		GoLimit31:Create(self.ui.m_goUp,loadCallBack)
    elseif param.totalType == ActivityTotal.CollectCard then
        NetCollectCardData:InitActiveData(param.id)
	else
		GoLimit:Create(self.ui.m_goUp,loadCallBack)
    end
end

function UI_MainFace:SortGoDownRootUIIndex()
	local sortList = {}
	local sortTirggerGiftList = {}

	for _, v in pairs(self.limits) do
		local config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_sorting,v.totalType)
		v.sortIndex = config and config.sort or 1
		table.insert(sortList,v)
	end
	local config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_sorting,18)
	--if config then
		--local growthfund = {}
		--growthfund.go = self.ui.m_goGrowthFund
		--growthfund.sortIndex = config.sort
		--table.insert(sortList,growthfund)
	--end
	--新成长基金
	--config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_sorting,999)
	--if config then
		--local new_growthfund = {}
		--new_growthfund.go = self.ui.m_goGrowthFundNew
		--new_growthfund.sortIndex = config.sort
		--table.insert(sortList,new_growthfund)
	--end
	--7日
	config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_sorting,998)
	if config then
		local day7 = {}
		day7.go = self.ui.m_goSevenDay
		day7.sortIndex = config.sort
		table.insert(sortList,day7)
	end

	if table.length(sortList) > 1 then
		table.sort(sortList,function(a,b)
			return a.sortIndex < b.sortIndex
		end)

		for _, v in ipairs(sortTirggerGiftList) do
			SetUIFirstSibling(v.go)
		end
		for _, v in ipairs(sortList) do
			SetUIFirstSibling(v.go)
		end
		for _, v in pairs(self.limitsSpeGo) do
			SetUILastSibling(v.go)
		end
	end
end


function UI_MainFace:ResetTaskViewimg()
	local count = self.ui.m_goUp.transform.childCount
	local countActive = 0
	for i = 0, count - 1 do
		local transSub = GET_UI_CHILD(self.ui.m_goUp.transform, i, "RectTransform")
		local isActive = transSub.gameObject.activeSelf
		if isActive then
			countActive = countActive + 1
		end
	end
	SetActive(self.ui.m_goUpList,countActive > 0)
	local size = self.goUpListRect.rect.size
	--if countActive > 0 and countActive < 4 then
	--	--SetUISize(self.ui.m_imgGoUpBg, 167 * countActive + 58, 145 );
	--	SetUISize(self.ui.m_imgGoUpBg, 147, 167 * countActive + 58 );
	--else
	--	SetUISize(self.ui.m_imgGoUpBg, 147, 690);
	--end
	local compareValue = size.y < 731 and 4 or 5 
	if countActive >= compareValue then
		SetActive(self.ui.m_imgGoUpBg,true)
		self.taskScrollRect.enabled = true
	else
		SetActive(self.ui.m_imgGoUpBg,false)
		self.taskScrollRect.enabled = false
	end
end

function UI_MainFace:CheckRankAutoOpen(item, param)
	local active = LimitActivityController:GetActiveMessage(param.id);
	if active.info.isNew then
		active:SetIsNewFalse()
		if item then
			item:ClickItemPush()
		end
		return
	end
	
	--local active = LimitActivityController:GetActiveMessage(item.id)
	--if active:GetRemainingTime() <= 0 then
		--item:ClickItemPush()
	--end
end

function UI_MainFace:CloseLimits(id)
	if id == nil then
		return
	end
    local item = self.limits[id]
    self.limitsGo[id] = nil
	if item == nil then
		return nil
	end
    item:Close()
    self.limits[id] = nil
	self:CloseSpeLimit(id)
end

function UI_MainFace:CheckTaskIsNewInMainUI(id)
	local sort = NetTaskData:GetCurTaskSort()
	local firstTask = sort[1]
	local cur_data = NetTaskData:GetTaskItem(firstTask)
	local data = NetTaskData:GetTaskItem(id)
	local a_recommend = cur_data and cur_data.recommend_list or 999
	local b_recommend = data and data.recommend_list or 999
	return a_recommend <= b_recommend
end

function UI_MainFace:OnRefresh(type, param)
    --刷新新任务item
    if type == 1 then
        local count = #param
        self.taskView.content.anchoredPosition = self.defaultTaskPos
		local newTaskSortT = {}
        for i = count, 1,-1 do
            local v = param[i]
            local item = self:AddTaskItem(v.id, NetTaskData:GetProgressById(v.id))
			local taskConfig = NetTaskData:GetTaskItem(v.id)
			if NetTaskData:IsCanShowMainUI(taskConfig) then
				table.insert(newTaskSortT,v.id)
			end
            SetUIFirstSibling(item.go)
            item:ShowActive(false)
			--local go = CreateGameObjectWithParent(pre, self.uiGameObject)
            local flyItem = taskFlyItem:Create(self.uiGameObject)--CreateGameObjectWithParent(pre, self.uiGameObject)
            local img = flyItem.taskImg--GetChild(go, "Image", UEUI.Image)
			self.taskFlyItems[v.id] = flyItem
			
			if param[i].id == SpecialId.SeasonsTaskId then
				local activityopen, activity = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Season)
				if activityopen then
					local config = ConfigMgr:GetDataByID(ConfigDefine.ID.limit_activities,activity.form.activeMess.id)
					SetImageSprite(img, config.icon, false,function() SetActive(img,true)  end)
				end
			else
            	SetImageSprite(img, NetTaskData:GetTaskItem(v.id).icon, false,function() SetActive(img,true)  end)
			end
			local index = i
            EffectConfig:CreateEffect(27, 0, 0, 0, GetChildTrans(flyItem.go, "Effect"), function()
				local index2 = index
                local function LastCall()
					if index2 == 1 then
                        WorkerController:SetWorkAni()
					end
                    --if index2 == count then
                    --    local isInit = NetInfoData:GetDataMess("FreeBoxInit")
                    --    if not isInit then
                    --        for id, item in pairs(self.taskItems) do
                    --            if id == SpecialId.InitFreeBoxTaskId then
                    --                SetUIFirstSibling(item.go)
                    --                break
					--			end
                    --        end
                    --    end
                    --end
                end
				local taskIconPos = Vector3.New(self.ui.m_imgDailyIcon.transform.position.x,self.ui.m_imgDailyIcon.transform.position.y,0)
                --item:PlayAddTask(go, v.pos,taskIconPos, LastCall)	--self.taskPos[index]
				flyItem:PlayAddTask(v.pos,taskIconPos, LastCall)
                self:SortOrderAllCom(true)
            end)
        end
		table.sort(newTaskSortT,function(a,b)
			local a_data = self:GetTaskItem(a)
			local b_data = self:GetTaskItem(b)
			local a_recommend = a_data.recommend_list or 999
			local b_recommend = b_data.recommend_list or 999
			return a_recommend < b_recommend
		end)
		local is_new = self:CheckTaskIsNewInMainUI(newTaskSortT[1])
		if is_new then
			table.insert(self.newTaskQuence,newTaskSortT[1])
			self.isPlayNewTaskEff = true
		end
    elseif type == 2 then
        ----礼包增加
        if param == nil or self.gifts[param] then
            return
        end
        --self:RemoveGift()
		local cfg_data = TriggerGiftConfig:GetDataByID(param)
		if cfg_data == nil then
			return
		end
		if cfg_data.aging == "one" or cfg_data.aging == "onceday" then
			return
		end
		if GetBoolValue(cfg_data.show_alone) then
			local function GoActivityBack(item)
				item:SetItem(param)
				self.gifts[param] = item
				self:SortGoDownRootUIIndex()
			end	
			GoActivity:Create(self.ui.m_goActivity,GoActivityBack)		
		end
		--
        --local item = GoActivity:Create(self.ui.m_goActivity)
        --item:SetItem(param)
		--self.gifts[param] = item
		--
		--local sortList = {}
		--for k, v in pairs(self.gifts) do
		--	table.insert(sortList,v)
		--end
		--table.sort(sortList,function(a,b)
		--		return a.show_priority < b.show_priority
		--	end)
		--if table.length(sortList) > 1 then	
		--	for _, v in ipairs(sortList) do
		--		if v.go then
		--			SetUIFirstSibling(v.go)
		--		end
		--	end
		--end
		--
		--if self.ui.m_scrollviewActivity then
		--	TimeMgr:CreateTimer(self, function ()
		--			SetActive(self.ui.m_scrollviewActivity,false)
		--			SetActive(self.ui.m_scrollviewActivity,true)
		--			SetUIFirstSibling(self.ui.m_goFirstPack)
		--			SetUIFirstSibling(self.ui.m_goNewFirstPack)
		--	end, 0.1, 1)
		--end
		
    elseif type == 3 then
        --每日任务刷新
        if param == nil then
            return
        end
        if param.now and param.target then
            local isEnough = param.now >= param.target
            if isEnough then
                self.ui.m_txtDailyTask.text = param.target .. "/" .. param.target
                --SetRichColorText(self.ui.m_txtDailyTask, "00FF00", param.target .. "/" .. param.target)
            else
                self.ui.m_txtDailyTask.text = param.now .. "/" .. param.target
                --SetRichColorText(self.ui.m_txtDailyTask, "FF0000", param.now .. "/" .. param.target)
            end
            --self.ui.m_txtDailyTask.text = param.now .. "/" .. param.target
            --self.ui.m_imgDailySlider.fillAmount = param.now / param.target
        end
        if param.show ~= nil then
            SetActive(self.ui.m_goDailyFinish, param.show)
			--local isOpenTarget = DailyTargetManager:IsOpenDailyTargetSwitch()
			--if isOpenTarget then
			--	SetActive(self.ui.m_goNewDailyFinish, param.show)
			--end
            --特效部分
            local uiEffect = self.mUIEffects[MainFaceDefine.DailyTask]
            if nil ~= uiEffect then
                if uiEffect.activeSelf ~= param.show then
                    SetActive(uiEffect,param.show)
                end
            end
        end
    elseif type == 4 then
        --tips 选中状态
        self:SetTips(param)
    elseif type == 5 then
        self.ui.m_doOrder:DORestart()
    elseif type == 6 then
		if param then
			if param[1] == MainFaceDefine.Setting then
				self:SettingRedPoint()
			else
				self:SetRedPoint(param[1],param[2])
			end
		end
        self:SetListRedPoint()
    elseif type == 7 then
        if param == true then
            SetActive(self.ui.m_goArrow, true)
        else
            if NetUpdatePlayerData.playerInfo.curMap == MAP_ID_MAIN then
                local showRow = NetMarketData:get_IsFirst()
                if not showRow then
                    SetActive(self.ui.m_goArrow, false)
                end
            end
        end
    elseif type == 8 then
        self:SetGuideShow(param)
    elseif type == 9 then
        if param.state == 1 then
            self:AddLimits(param)
			if param.totalType == ActivityTotal.BowlingBattle or param.totalType == ActivityTotal.OneToOne
					or param.totalType == ActivityTotal.AthleticTalent or param.totalType == ActivityTotal.Rank then
				self:updateActivityGroup()
				UI_UPDATE(UIDefine.UI_ActivityRankCenter, 1);
			end
        elseif param.state == 2 then
			if self.limits[param.id] then
				self.limits[param.id]:ChangeValue()
			end

			local totalType = LimitActivityController:GetTotalTypeById(param.id)
			if totalType and totalType == ActivityTotal.BowlingBattle or totalType == ActivityTotal.OneToOne
					or totalType == ActivityTotal.AthleticTalent or totalType == ActivityTotal.Rank then
				self:updateActivityGroup();
				UI_UPDATE(UIDefine.UI_ActivityRankCenter, 2);
			end
        elseif param.state == 3 then
        	self:CloseLimits(param.id)

			local totalType = LimitActivityController:GetTotalTypeById(param.id)
			if totalType and totalType == ActivityTotal.BowlingBattle or totalType == ActivityTotal.OneToOne 
					or totalType == ActivityTotal.AthleticTalent or totalType == ActivityTotal.Rank then
				self:updateActivityGroup()
				local config = ConfigMgr:GetData(ConfigDefine.ID.activity_rank)
				local showIndex
				for k, v in pairs(config) do
					local openArr = string.split(v.open, "|");
					local openType = v2n(openArr[1]);
					local openParm = v2n(openArr[2]);
					if openType == 1 and openParm == totalType then
						showIndex = tonumber(k)
						break
					end
				end
        		UI_UPDATE(UIDefine.UI_ActivityRankCenter, 1, showIndex)
        	end
        end
    elseif type == 10 then
        if param then
            Log.Info("+++++++++++PlayClearScreen++++++++++++++++")
			--local is_show = DailyTargetManager:IsOpenDailyTargetSwitch()
			local ani_str = "main_clearscreen"
			--if is_show then
			--	ani_str = "main_clearscreen2"
			--end
            self.aAni:Play(ani_str)
        else
            Log.Info("+++++++++++PlayBackScreen++++++++++++++++")
			--if Game.Channel_IOS_Notch then
				--self.aAni:Play("main_back_ios")
			--else
				--self.aAni:Play("main_back")
			--end
			--local is_show = DailyTargetManager:IsOpenDailyTargetSwitch()
			local ani_str = "main_back"
			--if is_show then
			--	ani_str = "main_back_2"
			--end
			self.aAni:Play(ani_str)
			self:ResetDOLLARPos()
        end
    elseif type == 11 then
        SetActive(self.ui.m_goOrderFinish,param == OrderShowDefine.Finish)
        SetActive(self.ui.m_doOrderTimer,param == OrderShowDefine.IsCooking)
        SetActive(self.ui.m_goOrderPoint,param == OrderShowDefine.HasOrder)

        if(param == OrderShowDefine.HasOrder) then
            AudioMgr:Play(60)
        end

        local isShowEffect = param == OrderShowDefine.Finish and true or false
        SetActive(self.ui.m_goOrderEffect,isShowEffect)
    elseif type == 12 then
        --local isShow = param.isShow
        --for _, id in ipairs(param.list) do
        --local mess = self.canvas[id]
        --if isShow then
        --if not mess.order then
        --mess.order = mess.canvas.sortingOrder
        --mess.canvas.sortingOrder = 199
        --self:SetButtonGrayState(AddBtnDefine[id], 1)
        --end
        --mess.count = mess.count + 1
        --else
        --if mess.order then
        --mess.count = mess.count - 1
        --if mess.count <= 0 then
        --mess.count = 0
        --mess.canvas.sortingOrder = mess.order
        --mess.order = nil
        --self:SetButtonGrayState(AddBtnDefine[id], -1)
        --end
        --end
        --end
        --end
    elseif type == 13 then
        self:OnEnergyPoint()
    elseif type == 14 then
        self:ChangeTask(param[1],param[2],param[3])
		--self:UpdateMainTask()
    elseif type == 15 then
        self:SetPayWorker()
    elseif type == 16 then
        self:WorkerInit(param)
    elseif type == 17 then
        self:RemoveGift(param)
		self.triggerGiftTime = nil
    elseif type == 18 then
        self:ChangeEnergyState(param)
    elseif type == 19 then
        --self:ChangeWorker(param)
		self:SetPayWorker()
    elseif type == 20 then
        if self.shipAdState ~= param then
            self.shipAdState = param
            SetActive(self.ui.m_goOrderAd,param)
        end
	elseif type == 21 then
		
		--if param == nil then
			--return
		--end
		--local isShow = param[1]
		--local time = param[2]
		--if isShow == true then
			--self.energyArrow = time --3秒
		--else
			--self.energyArrow = nil
		--end
		--SetActive(self.ui.m_energyArrow,isShow)
	elseif type == 22 then
		if param == nil then
			return
		end
		local isShow = param[1]
		local time = param[2]
		if isShow == true then
			self.airArrow = time
		else
			self.airArrow = nil
		end
		SetActive(self.ui.m_goArrArrow,isShow)
    elseif type == 102 then
        self:SetHead()
	elseif type == 23 then
		self:CloseSpeLimit(param)
    elseif type == 24 then
        --刷新 第3个工人的购买提示
        self:CheckTipsPayWorker(param)
	elseif type == 25 then
		self:CheckGiftPackShow()
	
	elseif type == 126 then
		 if self.limits[param[1]] then
		 	self.limits[param[1]]:SetRedShow(param[2])
		 end

		-- 更新保龄球红点
		self:updateActivityGroup()
	elseif type == 27 then
		self:ChangeActCenterRed()
		SetActive(self.ui.m_goGrowthFundNew,NetGrowthFundNew:IsOpenActivity())
		--self:ChangeMonthCardRed()
		maxEnergy = LevelConfig:GetLimitEnergy()

		local info = NetUpdatePlayerData:GetPlayerInfo();
		self:SetEnergy(info["energy"]);
	elseif type == 277 then
		--刷新体力
		self:ChangeResource(PlayerDefine.Energy,nil,0)
	elseif type == 127 then
		self:ChangePrivilegeCardRed()
	elseif type == 28 then
		--SetActive(self.ui.m_goTinyArrow,true)
		--SetActive(self.rowTinyArrow, true)
		
		self:CheckTipsTinyGame()
	elseif type == 29 then
		if self.limits[param] then
			self.limits[param]:ChangeValue()
		end
	elseif type == 30 then
		NetActToyData:ChangeToWasteId(param)
	elseif type == 31 then
		if self.taskItems[param] ~= nil then
			self.taskItems[param].go.transform:SetAsFirstSibling(0)
			self.taskItems[param]:PlayHuxi()
			self.taskItems[param]:ChangePro()
			local v3=self.ui.m_goTasks.transform.localPosition
			v3.y=0
			self.ui.m_goTasks.transform.localPosition=v3
		end
		EventMgr:Dispatch(EventID.CHANGE_SEASON_ENERGY)
		self:UpdateSeasonInfo()
	elseif type == 32 then
		if self.taskItems[param] ~= nil then
			self.taskItems[param]:SetShowRed()
		end
		self:UpdateSeasonRed()
	elseif type == 33 then
		self:SetRedPoint(MainFaceDefine.FriendRed, NetFriendData:GetAllRedPoint())
	elseif type == 34 then
		self:RefreshMagnetState()
	elseif type == 35 then
		self:PlayPrivilegeHandAnim()	
	elseif type == 36 then
		self:ShowScaleCameraAnimPic(param)
	elseif type == 1001 then --刷新小游戏红点
		self:SetTinyGameRedPoint()
	elseif type == 1002 then --差异化主分支的mainface动画（小游戏专用）
		if param then
			Log.Info("+++++++++++ PlayClearScreen tg ++++++++++++++++")
			--local is_show = DailyTargetManager:IsOpenDailyTargetSwitch()
			local ani_str = "main_clearscreen_tg"
			--if is_show then
			--	ani_str = "main_clearscreen_tg2"
			--end
			self.aAni:Play(ani_str)
			
			--if self.twoGuid then
				--self:TwoLevelGuide(true)
			--end
		else
			Log.Info("+++++++++++ PlayBackScreen tg ++++++++++++++++")
			--local is_show = DailyTargetManager:IsOpenDailyTargetSwitch()
			local ani_str = "main_back_tg"
			--if is_show then
			--	ani_str = "main_back_tg2"
			--end
			self.aAni:Play(ani_str)
			self:ResetDOLLARPos()
			
			--local level = LevelConfig:GetLevel()
			--if level < 3 then
				--self:TwoLevelGuide()
			--end
		end
	elseif type == 1003 then --新关卡提示“New”
		self:RefreshTgArrow()
	elseif type == 1004 then --红钻变化
		if param == nil then
			return
		end
		local from = v2n(param[1]) or 0
		local to = v2n(param[2]) or 0
		self:ShowRedDiamond(from,to)
	elseif type == 37 then --每日目标
		SetActive(self.ui.m_btnDailyTargetIcon,false)	
		SetActive(self.ui.m_txtTarget,false)	
		SetActive(self.DailyTargetEff,false)	
	elseif type == 38 then --每日目标
		SetActive(self.ui.m_btnDailyTargetIcon,true)
		SetActive(self.ui.m_txtTarget,true)
		SetActive(self.DailyTargetEff,true)
		-- 在动画期间继续消耗还有问题
		
		self:UpdateDailyTarget(param)
		--if not IsTableEmpty(self.targetSequence) then
		--	self.isTween = true
		--	self:StartDailyTargetTween()
		--else
		--	self.isTween = false
		--end
	elseif type == 39 then --每日目标新手入口
		self:RefreshBtnDailyRookie()
	elseif type == 40 then --主界面ui置顶显示
		if self.canvas[param[1]] then
			local canvas = self.canvas[param[1]].canvas
			local childButton = {}
			GetChildsComponent(canvas.transform,childButton,typeof(UEUI.Button))
			for index, value in ipairs(childButton) do
				value.interactable = not param[2]
			end
			canvas.sortingLayerName = param[2] and "UI_MIDDLE" or "Default"
		end
	elseif type == 41 then --主界面成长基金红点
		local isRed = NetGrowthFundNew:GetIsShowRed()
		SetActive(self.ui.m_imgGrowthFundNewRed,isRed)
	elseif type == 42 then
		--SetActive(self.ui.m_goGrowthFundNew,NetGrowthFundNew:IsOpenActivity())
    elseif type == 283701 then  -- 1v1 比拼条显示隐藏
        SetActive(self.ui.m_goCompetition, param)
        SetActive(self.ui.m_goNotCompetition, not param)
        if self.oneToOneTimeBg then
        	SetActive(self.oneToOneTimeBg, not param)
        end
        self:RefreshCompetition()
    elseif type == 283702 then  -- 1v1 比拼条刷新
        self:RefreshCompetition(param)
    elseif type == 283703 then  -- 1v1 比拼条剩余时间
        self:RefreshCompetitionTime(param)
	elseif type == 43 then  -- 多倍采集
		self:RefreshMultipleDropInfo()
		--self:SetStartMultipleCountDownTime(param[1])
    elseif type == 44 then --7天
		self:CheckSevenDayShow()
		--self:AutoFitTaskList()
	elseif type == 45 then --触发礼包弹窗结束播飞行特效
		self:PlayTriggerGiftFlyEff(param)
	elseif type == 145 then
		self:PlayShushuGiftFlyEff()
	elseif type == 146 then
		self:UpdateWenjuan()
    elseif type == RefreshType.ResourcePosWorker then
        -- 刷新蓝工人的资源位置
        local pos = UIMgr:GetUIPosByWorld(self.ui.m_doWorker.transform.position)
        MapController:SetUIResourcePos(ItemID.Worker3, pos.x, pos.y)

        -- 刷新黄工人的资源位置
        -- 工具栏折叠状态
        if self.rightBottomArowIsPut then
            pos = UIMgr:GetUIPosByWorld(self.ui.m_btnRightBottomRow.transform.position)
        -- 工具栏展开状态
        else
            pos = UIMgr:GetUIPosByWorld(self.ui.m_imgAiWorkRed.transform.position)
        end
        MapController:SetUIResourcePos(ItemID.Worker4, pos.x, pos.y)
    elseif type == RefreshType.RefreshAthleticTalent then
        self:UpdateAthleticTalent(param)
    elseif type == RefreshType.RefreshRank then
    	self:UpdateRankRed();
    elseif type == RefreshType.SwitchTab then
        self:MoveTargetTab(param)
    elseif type == RefreshType.UpdateUnionRed then
    	SetActive(self.ui.m_goUnionRed, NetLeagueData:GetAllRedPoint());
    elseif type == RefreshType.FriendRedPoint then
        SetActive(self.ui.m_goFriendRed, NetFriendData:GetAllRedPoint())
	elseif type == "LeagueHelp" then
		--联盟成员发送申请建筑帮助
		SetActive(self.ui.m_goLeagueHelp,true)
	elseif type == 200 then
		--联盟数据发生变化
		local isActive = NetLeagueData:CanHelpOther()
		SetActive(self.ui.m_goLeagueHelp,isActive)
	elseif type == 201 then
		if not param then
			return
		end
		if v2n(param) == 0 then
			--0：表示联盟内已无需要帮助的记录
			SetActive(self.ui.m_goLeagueHelp,false)
		else
			--非0：可帮助条数，整型,重新请求更新列表
			NetLeagueData:UpdateBuildHelpList()
		end
	elseif type == 300 then		-- 联盟公告推送
        table.insert(self.pushMsgList, {
            msg = param,
            type = 300
        })
	elseif type == 301 then		-- 关闭联盟公告推送
		self:CloseLeagueAnnouncePush()
    elseif type == 302 then		-- 好友消息推送
        local isBlack = NetFriendData:IsBlacklist(param.player.id)
        local playerId = NetUpdatePlayerData:GetPlayerID()
        if not isBlack and v2n(playerId) ~= v2n(param.player.id) then
            table.insert(self.pushMsgList, {
                msg = param,
                type = 302
            })
        end
    elseif type == 303 then		-- 关闭好友消息推送
        self:CloseFriendPush()
	elseif type == 304 then --刷新头像
		if self.m_CustomHead then
			self.m_CustomHead:RefreshHead()
		end
	elseif type == 305 then --刷新关卡
		self:InitLevelTimer()
	elseif type == 306 then --刷新关卡
		--self.rightBottomArowIsPut  true 折叠 false 展开
		--param  0:折叠  1:展开
		--if param == 0 then
		--	if not self.rightBottomArowIsPut then
		--		self:ChangeRightBottomArow()
		--	end
		--elseif param == 1 then
		--	if self.rightBottomArowIsPut then
		--		self:ChangeRightBottomArow()
		--	end
		--end
	end
end

function UI_MainFace:ShowScaleCameraAnimPic(is_show)
	local isInGame = TinyGameMgr:GetIsInTinyGame()
	if isInGame then
		return
	end
	SetActive(self.ui.m_imgScaleCamera,is_show)
end

function UI_MainFace:PlayPrivilegeHandAnim()
	SetActive(self.ui.m_imgPrivilegeHand,true)
	local Ani = GetComponent(self.ui.m_imgPrivilegeHand, UE.Animation)
	if Ani then
		local function PlayAniEnd()
			SetActive(self.ui.m_imgPrivilegeHand,false)
		end
		PlayAnimStatus(Ani,"privilege_hand",PlayAniEnd)
	end
	--self.ui.m_imgPrivilegeHand:GetComponent("CanvasGroup"):DOFade(1, 0.1)
	--local function PlayAniEnd()
	--	SetActive(self.ui.m_imgPrivilegeHand, false)
	--end
	--local tween, tId = TweenMgr:CreateSequence(UIDefine.UI_MainFace, false, PlayAniEnd)
	--local pFrom = Vector2.New(0,0)
	--local x = UIWidth*0.5
	--local y = (UIHeight+self.ui.m_goDownList.transform.localPosition.y)*0.5
	--local pTo = Vector2.New(-x,-y)
	--local itemRect = GetComponent(self.ui.m_imgPrivilegeHand,UE.RectTransform)
	--itemRect.position = pFrom
	----tween:Append(self.ui.m_imgPrivilegeHand:GetComponent("CanvasGroup"):DOFade(1, 0.5)):SetDelay(0.1)
	--tween:Append(itemRect:DOAnchorPos(pFrom, 0.6))
	--tween:Append(itemRect:DOAnchorPos(pTo, 0.6):SetDelay(0.1))
	--tween:AppendInterval(0.2)
	--
	--tween:SetLoops(2, LoopType.Restart)
end

function UI_MainFace:GetLimits(ActivityID)
	return self.limits[ActivityID]
end

---刷新第3个工人的购买提示   isNeedWorker是否需要用工人
function UI_MainFace:CheckTipsPayWorker(isNeedWorker)
    isNeedWorker = isNeedWorker or false

    if WorkerController:JudgeTipsPayWorker() and isNeedWorker then
        SetActive(self.extraWorkRow, true)

        self:onTimerDestroyPayWorkerTips()
        local function iCallBack()
            SetActive(self.extraWorkRow, false)
            self:onTimerDestroyPayWorkerTips()
        end

        self.PayWorkerTipsTimerId = TimeMgr:CreateTimer(self, iCallBack, 2, 2)
    end
end
function UI_MainFace:onTimerDestroyPayWorkerTips()
    if self.PayWorkerTipsTimerId > 0 then
        TimeMgr:DestroyTimer(self, self.PayWorkerTipsTimerId)
        self.PayWorkerTipsTimerId = 0
    end
end

--- 出现指向小游戏图标的箭头(订单图标未开启前，钥匙不足时)
function UI_MainFace:CheckTipsTinyGame()

	SetActive(self.rowTinyArrow, true)
	SetActive(self.ui.m_goTinyArrow, true)
	SetActive(self.ui.m_goTgDialogTips, true)

	self:onTimerDestroyTinyGameTips()
	local function iCallBack()
		SetActive(self.rowTinyArrow, false)
		SetActive(self.ui.m_goTinyArrow, false)
		SetActive(self.ui.m_goTgDialogTips, false)
		self:onTimerDestroyTinyGameTips()
	end

	self.TinyGameTipsTimerId = TimeMgr:CreateTimer(self, iCallBack, 2, 2)

end
function UI_MainFace:onTimerDestroyTinyGameTips()
	if self.TinyGameTipsTimerId and self.TinyGameTipsTimerId > 0 then
		TimeMgr:DestroyTimer(self, self.TinyGameTipsTimerId)
		self.TinyGameTipsTimerId = 0
	end
end


function UI_MainFace:CloseSpeLimit(id)
	local item = self.limitsSpeGo[id]
	if item then
		item:Close()
		self.limitsSpeGo[id] = nil
	end
end

function UI_MainFace:ChangeEnergyState(state)
    if state == 1 or state == 2 then
        if NetUpdatePlayerData:GetInfinityLastTime(PlayerDefine.Energy) > 0 then
            if self.energyState ~= 3 then
                state = 3
                self.energyState = 3
            else
                return
            end
        end
    end
    if state >= 0 then
        self.energyState = state
    end
    --local isOpen = LimitActivityController:GetActiveIsOpen(ActivityTotal.Extra, ActivitySubtype.EnergyMax)
    --if isOpen then
    --SetUIImage(self.ui.m_imgEnergyTime, "Sprite/ui_main/activity_energy_countdown_bg1.png", false)
    --SetUIImage(self.ui.m_imgEnergyInfinite, "Sprite/ui_main/activity_energy_countdown_bg1.png", false)
    --else
    --SetUIImage(self.ui.m_imgEnergyTime, "Sprite/ui_main/main_resources_tipsbg.png", false)
    --SetUIImage(self.ui.m_imgEnergyInfinite, "Sprite/ui_main/main_resources_tipsbg.png", false)
    --end
    if state == 1 then
        SetActive(self.ui.m_imgEnergyInfinite,false)
        SetActive(self.ui.m_imgEnergyTime,false)
        SetActive(self.ui.m_goEnergyNormal,true)
    elseif state == 2 then
        SetActive(self.ui.m_imgEnergyInfinite,false)
        SetActive(self.ui.m_imgEnergyTime,true)
        SetActive(self.ui.m_goEnergyNormal,true)
    elseif state == 3 then
        SetActive(self.ui.m_imgEnergyInfinite,true)
        SetActive(self.ui.m_imgEnergyTime,false)
        SetActive(self.ui.m_goEnergyNormal,false)
    end

end

function UI_MainFace:BindingUIToVar()
    local list = {}
    --list[1] = self.ui.m_goDiamond--宝石
    list[2] = self.ui.m_goCoin--金币
    list[3] = self.ui.m_goLevel--等级
    list[4] = self.ui.m_goMagic--魔棒
    list[5] = self.ui.m_goEnergy--体力
    list[6] = self.ui.m_goWorkerGroup--self.ui.m_goWorker--工人
    list[7] = self.ui.m_goDailyTask--每日
    list[8] = self.ui.m_goCollect--藏品
    list[9] = self.ui.m_goMarket--市场
    list[10] = self.ui.m_btnDelete--删除
    list[11] = self.ui.m_goOrder--港口
    --list[12] = self.ui.m_goTip --提示
    --list[13] = self.ui.m_goTask --任务
    list[14] = self.ui.m_goSetlist --信息列表
    list[15] = self.ui.m_btnEnergyTask -- 体力任务
    list[19] = self.ui.m_goAir -- 地图切换
    --list[15] = self.ui.m_goDeleted --删除

	list[20] = self.ui.m_goTinyGame--小游戏
    self.m_ArrUI = list
end

function UI_MainFace:SetGuideShow(param)
    param = param or {}
    local list = GlobalConfig:GetBtnMess("0")
    for i, v in pairs(self.m_ArrUI) do
        local strIndex = tostring(i)
        local isShow = param[strIndex] ~= nil
        for _, k in ipairs(list) do
            if k[1] == strIndex then
				--if i ~= 7 then --新增新手任务在同一个按钮，取消m_goDailyTask隐藏条件，在dailytask.lua里修改状态
                	--isShow = GuideController:IsGuideDone(k[2])
				--end
				isShow = GuideController:IsGuideDone(k[2])
            end
        end
        --if i == 10 then
            --self.needHide[i] = not isShow
        --else
		--每日任务图标还是新手任务图标
		if i == 7 then
			--local dailyOpen = DailyTaskController:IsCanShowDailyTask()
			--local imagePath
			--if dailyOpen then
			--	imagePath = "Assets/ResPackage/Sprite/ui_mainface/icon-renwu.png"
			--	SetImageSprite(self.ui.m_imgDailyIcon,imagePath,false)				
			--else
			--	imagePath = "Assets/ResPackage/Sprite/ui_mainface/icon-renwu_1.png"
			--	SetImageSprite(self.ui.m_imgDailyIcon,imagePath,false)
			--end
			--SetActive(self.ui.m_goDailyPro,dailyOpen)
			--Log.Error("dailyOpen",dailyOpen)
			isShow = true
			--if DailyTargetManager:IsOpenDailyTargetSwitch() then
			--	isShow = false
			--end
		end
        if i == 14 and not isShow and self.setIsOpen then
            self.setIsOpen = not self.setIsOpen
           -- self.setAni:Play("setlists_close")
        elseif i == 15 then
            isShow = NetEnergyTaskData:IsOpen()
        end
		--订单图标
		if i == 11 then
			isShow = MapController:GetHeroNum() >= 1 --= (NetUpdatePlayerData:GetLevel() >= GlobalConfig.OPEN_ORDER_LEVEL)
		end
		--小游戏图标
		if i == 20 then
			if NetTinyGameData:IsShowEntry() then
				 isShow = TinyGameMgr:GetTinyGameID() > 0
			else
				local iPassStage = TinyGameMgr:GetPassStageID()
				local iShowListStage  = v2n(ConfigMgr:GetDataByID(ConfigDefine.ID.global_setting, 1096).value)
				isShow = (iPassStage >= iShowListStage)
			end
			if TinyGameMgr:GetTinyGameID() <= 0 then
				isShow = false
			elseif NetUpdatePlayerData:GetLevel() >= 4 then
				isShow = true
			end

			isShow = TinyGameMgr:GetTinyGameID() > 0 and isShow

		end
		
		
		--if (i == 19 or i == 9 )and NetUpdatePlayerData:GetLevel() >= 7 then
			--isShow = true
		--end
		if i == 8 or i == 19 or i == 9 then
			if isShow == false or v.activeSelf == false then
				local mapType, level = FunctionOpenConfig:GetopenConditionById(i)
				if mapType == MapType.Main and NetUpdatePlayerData:GetLevel() >= level then
					isShow = true
				end
			end
		end

        if i == 10 then
            self.rightBottomList[FolderItemType.Shovel].isOpen = isShow
            SetActive(self.rightBottomList[FolderItemType.Shovel].go, isShow)
            self:FoldRightBottom()
        end
		if i == 19 or i == 9 or i == 8 then
			local lockObj = GET_UI(v, "imgLock")
			SetActive(lockObj,not isShow)
			--SetUIObjGray(v, not isShow)
		else
			SetActive(v, isShow)
		end
        --end
    end
	self:UpdateCollectArow()
	self:UpdateRightBottomArow()
	self:ResetDOLLARPos()
end

--处理藏品的按钮箭头的逻辑
function UI_MainFace:UpdateCollectArow()
	if self.m_ArrUI[8].activeSelf == true then
		
		local m_collectRow = GET_UI(self.m_ArrUI[8], "row", TP(UEUI.Image))
		local bVisible = NetCollection:get_VisibleState()
		if not bVisible then
            self:CreateScheduleFun(function()
                DOKill(m_collectRow.transform)
                SetActive(m_collectRow, false)
            end,3)
			DOLocalMoveYLoop(m_collectRow.transform, 187, 0.7, LoopType.Yoyo, Ease.InOutSine)
			SetActive(m_collectRow, true)
			NetCollection:set_VisibleState(true)
		end
		
	end
end

function UI_MainFace:onDestroy()
    self.timerTime = {}
    self.tipItem = nil
    for _, v in pairs(self.limits) do
        v:Close()
    end
    self.limits = {}
    self.limitsGo = {}
	self.limitsSpeGo = {}
    self.tipGo = {}
    self.gifts = {}
    self.taskItems = {}
    self.taskFlyItems = {}
	self.targetSequence = nil
	self.uiDrag = nil
	self.activityRankTabList = {}
	if self.ActivityTabTween then
		self.ActivityTabTween:Kill()
		self.ActivityTabTween = nil
	end
    -- 清理竞技达人
    self.athleticTalent = nil
    self.isRefreshAthleticTalentRank = nil
	self.dailyTargetUpAni = nil
    LimitActivityController:SetActivePos(nil)
	DailyTargetManager:SetIsPlayingTween(false)
    self:onTimerDestroyPayWorkerTips()
	self:onTimerDestroyTinyGameTips()

	-- 清理活动竞赛倒计时文本
	self.oneToOneTimeBg = nil;
	self.oneToOneTimeTxt = nil;
	self.athleticTalentTimeTxt = nil;
	self.bowlingTimeTxt = nil;
	self.rankTimeTxt = nil;
	self.levelTimeTxt = nil;
	self.towerTimeTxt = nil
	self.jjcTimeTxt = nil
	self.worldBossTimeTxt = nil;
	self.topFightTimeTxt = nil
	if self.m_CustomHead then
		self.m_CustomHead:onDestroy()
		self.m_CustomHead = nil
	end
    self.myHeadNode = nil
	self.my1v1Head1 = nil
	self.my1v1Head2 = nil
	self.other1v1Head = nil
	
    EventMgr:Remove(EventID.MAP_ITEM_COMBINE, self.NewHeroUnlock, self)
	EventMgr:Remove(EventID.MAP_ITEM_HEADVIP, self.HeadVipAdd, self)
    EventMgr:Remove(EventID.CHANGE_RESOURCE, self.ChangeResource, self)
    EventMgr:Remove(EventID.USE_WORKER, self.SetPayWorker, self)--ChangeWorker
    EventMgr:Remove(EventID.MAP_CLOUD_UNLOCKED,self.CloudEvent,self)--云层解锁
    EventMgr:Remove(EventID.LEVEL_UP, self.SetLevel, self)
	EventMgr:Remove(EventID.ENERGY_BUFF_MINUS, self.onEnergyBuffMinus, self)
	--EventMgr:Remove(EventID.HANDLE_TEMPSAVEREWARDS, self.delayHandleTempSaveRewards, self)
	
	EventMgr:Remove(EventID.USING_RESOURCE,self.ResourseChange, self)
	--EventMgr:Remove(EventID.UPDATE_MAIN_ENERGY_UI, self.OnEnergyPoint)
	EventMgr:Remove(EventID.MAP_ITEM_NEW,self.RefreshTgArrow,self)
	EventMgr:Remove(EventID.TOUCH_INTERRUPT,self.RestFingerTime,self)
	EventMgr:Remove(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
	EventMgr:Remove(EventID.UPDATE_HERO_TOTAL_FIGHT, self.ChangeFight, self)
	EventMgr:Remove(EventID.UPDATE_HERO_INFO, self.OnHeroChange, self)
	EventMgr:Remove(EventID.DUNGEON_UPDATE, self.OnDungeonUpdate, self)
	EventMgr:Remove(EventID.ROLE_GIFT_CHANGE, self.RoleGiftChange, self)
	EventMgr:Remove(EventID.BATTLE_PASS_CHANGE, self.BattlePassChange, self)		
    EventMgr:Remove(EventID.BAG_CHANGE, self.BagChange, self)
	EventMgr:Remove(EventID.BATTLE_MANUAL_SAVE,self.BATTLE_MANUAL_SAVE,self)
end

function UI_MainFace:SetTips(id, isOnlyShow)
    if not isOnlyShow then
        self.selectId = id
    end
    if not self.selectIsClose then
        if self.selectId == nil then
            SetActive(self.ui.m_goSelect, false)
            SetActive(self.ui.m_goNoSelect, true)
        else
            SetActive(self.ui.m_goSelect, true)
            SetActive(self.ui.m_goNoSelect, false)
            self.ui.m_txtTipTitle.text = ItemConfig:GetRareID(self.selectId, true)
            local data = ItemConfig:GetDataByID(self.selectId)
            self.ui.m_txtTipContent.text = LangMgr:GetLang(data.explain)
            if data.is_can_delete == 1 and not self.needHide[15] then
                SetActive(self.ui.m_goDeleted, true)
            else
                SetActive(self.ui.m_goDeleted, false)
            end
            if data.type_use == ItemUseType.HeroFly then
                SetActive(self.ui.m_goHeroBtn, true)
            else
                SetActive(self.ui.m_goHeroBtn, false)
            end
            self.selectId = id

            local isType = CollectionItems:GetCollectId(self.selectId)
            if isType == nil then
                SetActive(self.ui.m_goDetails, false)
            else
                SetActive(self.ui.m_goDetails, true)
            end
        end
    end
end

function UI_MainFace:PlayGrayBtn()
    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(1056))
end

function UI_MainFace:SetButtonGrayState(btnType,showState)
    self.tempShow[btnType] = (self.tempShow[btnType] or 0) + showState
    if self.tempShow[btnType] > 0 then
        SetActive(self.ui[btnType],false)
    else
        SetActive(self.ui[btnType],true)
    end
end

function UI_MainFace:SetButtonGray(btnType,isGray)
    --新手引导关闭的话，不置灰按键
    if not Enable_Guide then return end
    local ui = self.ui[btnType]
    if ui == nil then
        Log.Error("red point name is wrong")
        return
    end
    if self.grayBtn[btnType] ~=nil and self.grayBtn[btnType] == isGray then return end
    self.grayBtn[btnType] = isGray
    if btnType == AddBtnDefine.worker then
        SetActive(ui,not isGray)
        return
    else
        SetUIImage(ui, isGray and "Sprite/ui_mainface/button-add2.png" or "Sprite/ui_mainface/mainface2_button-add.png", false)
    end
end

function UI_MainFace:onUIEventClick(go, param)
    local name = go.name
    if name == "m_btnSetting" then
        UI_SHOW(UIDefine.UI_Setting)
	elseif name == "m_goMsBuff" then
		UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(51033001))	
	elseif name == "m_goADBuff" then
		UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(402))
    elseif name == "m_btnEmail" then
		local funBack = function(objJson)
            UIMgr:ShowWaiting(false)
			if objJson == nil then
				UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(5012))
				return
			else
				NetMailData:ReadNetData(objJson)
				UI_SHOW(UIDefine.UI_MailPanel)
			end
		end
		local params = {}
		params["cate"] = 1
		HttpClient:SendToGS("/sns/maillist", params, funBack)
        UIMgr:ShowWaiting(true)
    elseif name == "m_goOrder" then
        UI_SHOW(UIDefine.UI_PageOrder)
    elseif name == "m_goCollect" then
		local mapType, level = FunctionOpenConfig:GetopenConditionById(8)
		local isShow = false
		if mapType == MapType.Main and NetUpdatePlayerData:GetLevel() >= level then
			isShow = true
		end
		if isShow then
			UI_SHOW(UIDefine.UI_CollectionCenter)
		else
			UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(1448))
		end
	elseif name == "m_btnTinyGame" then
		--UI_SHOW(TinyGameUIDefine.UI_ChapterStage2)
		self:OnClickTinyGameIcon()
    elseif name == "m_goAir" then
		local mapType, level = FunctionOpenConfig:GetopenConditionById(19)
		local isShow = false
		if mapType == MapType.Main and NetUpdatePlayerData:GetLevel() >= level then
			isShow = true
		end
		if isShow then
			UI_SHOW(UIDefine.UI_Travel, nil)
		else
			UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(1448))
		end
	elseif name == "taskInfo" then
        UI_SHOW(UIDefine.UI_Task, param)
    elseif name == "m_btnShop" then
        --self:OnRefresh(7, false)
		local lockObj = GET_UI(self.ui.m_btnShop, "imgLock")
		local isShow = false
		if not lockObj.activeSelf then
			isShow = true
		end
		if isShow then
			UI_SHOW(UIDefine.UI_Market, 0)
		else
			UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(1448))
		end
		--UI_SHOW(UIDefine.UI_SeasonDesc)
    -- 好友入口
    elseif name == "goFriend" then
        UI_SHOW(UIDefine.UI_FriendView)
        UIMgr:RefreshAllMainFace(303)
    elseif name == "m_imgDiamBuy" or name == "m_goDiamond" then
        if self.tempShow[AddBtnDefine.diamond] and self.tempShow[AddBtnDefine.diamond] > 0 then
        elseif self.grayBtn[AddBtnDefine.diamond] then
            self:PlayGrayBtn(AddBtnDefine.diamond)
        else
            UI_SHOW(UIDefine.UI_Shop,nil,true)
        end
    elseif name == "m_imgCoinBuy" or name == "m_goCoin" then
        if self.tempShow[AddBtnDefine.coin] and self.tempShow[AddBtnDefine.coin] > 0 then
        elseif self.grayBtn[AddBtnDefine.coin] then
            self:PlayGrayBtn(AddBtnDefine.coin)
        else
            UI_SHOW(UIDefine.UI_Market,"Money")
        end
    elseif name == "m_imgEnergyBuy" or name == "m_goEnergyRec" or name == "m_goEnergy" then
        if self.tempShow[AddBtnDefine.energy] and self.tempShow[AddBtnDefine.energy] > 0 then
        elseif self.grayBtn[AddBtnDefine.energy] then
			self:PlayGrayBtn(AddBtnDefine.energy)
		else
            --触发流程
            UIMgr:Show(UIDefine.UI_Energy , OPEN_ENERGY_TYPE.NORMAL)
		end
    elseif name == "m_goWork1" or name == "m_goWork2" or name == "m_goExtraWork" or name == "workAdd" then
        if not self.grayBtn[AddBtnDefine.worker] then
            UI_SHOW(UIDefine.UI_WorkerList)
        end
	elseif name == "m_goAiWork" then
		if UIMgr:GetUIOpen(UIDefine.UI_MapDressPreview) then
			return
		end
		UI_SHOW(UIDefine.UI_IntelligentWorker)
    elseif name == "DailyBG" then
        DailyTaskController:ShowUI()
    --elseif name == "m_goRecover" then
    --    SetActive(self.ui.m_goRecover, false)
    --    SetActive(self.ui.m_goExpand, true)
    --    SetActive(self.ui.m_goTips, false)
    --    self.selectIsClose = true
    --elseif name == "m_goExpand" then
    --    SetActive(self.ui.m_goRecover, true)
    --    SetActive(self.ui.m_goExpand, false)
    --    SetActive(self.ui.m_goTips, true)
    --    self.selectIsClose = false
        --self:SetTips(self.selectId, true)
    elseif name == "m_goDeleted" then
        UI_SHOW(UIDefine.UI_Delete, self.selectId)
    elseif name == "m_goDetails" then
        UI_SHOW(UIDefine.UI_Collection, self.selectId)
    elseif name == "m_goHeroBtn" then
        UI_SHOW(UIDefine.UI_HeroHelp, self.selectId)
    elseif name == "m_btnDelete" then
        UI_SHOW(UIDefine.UI_DeleteMain)
	elseif name == "m_goDress" then
		UI_SHOW(UIDefine.UI_DressChange)
    elseif name == "m_goSetlist" then
        if self.setAni.isPlaying then
            return
        end

        local isActive = self.ui[MainFaceDefine.Mail].activeSelf
		local isSettingActive = self.ui[MainFaceDefine.Setting].activeSelf
        if self.setIsOpen then
            if isActive then
                self:SetRedPoint(MainFaceDefine.SetList,true)
            end

			if isSettingActive then
				self:SetRedPoint(MainFaceDefine.SetList,true)
			end
            self.setAni:Play("setlists_close")
        else
			if isActive then
				self:SetRedPoint(MainFaceDefine.SetList,false)
			end

			if isSettingActive then
				self:SetRedPoint(MainFaceDefine.SetList,false)
			end
			self:SetRedPoint(MainFaceDefine.SetList,false)

            self.setAni:Play("setlists_open")
        end
        self.setIsOpen = not self.setIsOpen
	elseif name == "m_btnExpbg" then
		if UIMgr:GetUIOpen(UIDefine.UI_MapDressPreview) then
			return
		end
		UI_SHOW(UIDefine.UI_Setting)
    elseif name == "m_btnEnergyTask" then
        --UI_SHOW(UIDefine.UI_EnergyTask)
	elseif name == "m_goCarousel" then
		UI_SHOW(UIDefine.UI_GiftPack)
	elseif name == "m_goGiftDaily" then
		UI_SHOW(UIDefine.UI_GiftDaily)
	elseif name == "m_goSevenDay" then
		UI_SHOW(UIDefine.UI_SevenDayView)
	elseif name == "m_goFirstPack" then		
		UI_SHOW(UIDefine.UI_FirstGift)	
	elseif name == "m_goNewFirstPack" then
		UI_SHOW(UIDefine.UI_NewFirstGift)
	elseif name == "m_btnMonthCard" then
		UI_SHOW(UIDefine.UI_MonthCard)
	elseif name == "m_btnGrowthFund" then
		UI_SHOW(UIDefine.UI_GrowthFund)	
	elseif name == "m_btnGrowthFundNew" then
		UI_SHOW(UIDefine.UI_GrowthFundNew)
	elseif name == "m_btnActCenter" then
		UI_SHOW(UIDefine.UI_ActCenter)
	elseif name == "m_goMagic" then
		local level = LevelConfig:GetLevel()
		if level >= 4 then
			UI_SHOW(UIDefine.UI_KeyHelp)
			local data = NetGiftBox:GetGiftById(400)
			if data ~= nil and (data.state == 1) then
				UI_SHOW(UIDefine.UI_BuyKeys,400)
			end
		else
			UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(1056))
		end
	elseif name == "m_goGameDevChange" then
		UI_SHOW(UIDefine.UI_GameDevChange)
	elseif name == "m_btnSkin" then
		UI_SHOW(UIDefine.UI_SkinCollection)
		
	elseif name == "m_btnMagnet" or name == "m_btnEnergy" then
		if not UIMgr:GetUIOpen(UIDefine.UI_PrivilegeCard) then
			UI_SHOW(UIDefine.UI_PrivilegeCard)
		end
	elseif name == "m_btnDailyTargetRoot" or name == "m_btnDailyTargetIcon" then
		local isShow = DailyTargetManager:IsCanShowDailyTarget()
		if isShow then
			UI_SHOW(UIDefine.UI_DailyTarget)
		else
			UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(20018))
		end
	elseif name == "m_btnNewDailyTask" then
		DailyTaskController:ShowUI()
	elseif name == "m_goRedDiamond" then
		UI_SHOW(UIDefine.UI_ItemTips,ItemID.RED_DIAMOND)
	elseif name == "m_btnMultipleDrop" then
		UI_SHOW(UIDefine.UI_MultipleDropView)
    elseif name == "m_btnCompetition" then
		local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.OneToOne)
		if activityItem:IsActivityEnd() then
			if activityItem.info.activeId then
				NetOneToOneData:CheckEndPush(activityItem.info.activeId)
			end
		else
			UI_SHOW(UIDefine.UI_ActivityRankCenter, 1)
		end
	elseif name == "m_btnBowlingBattle" then
        UI_SHOW(UIDefine.UI_ActivityRankCenter, 3)
	elseif name == "m_goWorkerGroup" then
		UI_SHOW(UIDefine.UI_WorkerList)
	elseif name == "m_goSeason" then
		UI_SHOW(UIDefine.UI_SeasonActivityCheck)
	elseif name == "m_goTriggerGift" then
		--UI_SHOW(UIDefine.UI_Shop)
		UI_SHOW(UIDefine.UI_ActCenter, 7);
	elseif name == "m_goShushu" then
		--UI_SHOW(UIDefine.UI_Shop)
		if NetShushuGiftData:IsShowShushuIcon() then
			UI_SHOW(UIDefine.UI_ActCenter, 9)
		end
	elseif name == "m_btnActivityEnter" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter)
	elseif name == "m_btnRightBottomRow" then
		self:ChangeRightBottomArow()
	elseif name == "rankBtn" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 4);
	elseif name == "m_goGrowthFund" then
		UI_SHOW(UIDefine.UI_ActCenter, 3);	
	elseif name == "m_goGrowthFundNew" then
		UI_SHOW(UIDefine.UI_ActCenter, 4);
	elseif name == "m_btnLBLeft" then
		self:ClickCheckLB(true)
	elseif name == "m_btnLBRight" then
		self:ClickCheckLB(false)
	elseif name == "m_btnWenjuan" then
		UI_SHOW(UIDefine.UI_WenJuanDiaocha)
	elseif name == "m_btnTaskFold" then
		self:FoldTaskTip(true)
	elseif name == "m_btnActiveFollow" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 5);
	elseif name == "goUnion" then
		if LeagueManager:IsJoinUnion() then
			LeagueManager:SendGetLeagueDetails(nil, function()
				UI_SHOW(UIDefine.UI_Union);
			end);
		else
			UI_SHOW(UIDefine.UI_Union);
		end
	elseif name == "leagueHelpAll" then
		NetLeagueData:HelpAllBuildApply()
		SetActive(self.ui.m_goLeagueHelp,false)
	elseif name == "m_btnLottery" then
		UI_SHOW(UIDefine.UI_LotteryCenter)
	elseif name == "m_btnSlg" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 6)
	elseif name == "goHero" then
		local openHeroState = HeroManager:IsHeroActive(GlobalConfig.OPEN_SLG_HERO);
		if not openHeroState then
			UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(1448))
			return
		end
		UI_SHOW(UIDefine.UI_HeroWindow);
    elseif name == "m_btnEquipment" then
        UI_SHOW(UIDefine.UI_EquipmentDevelopView)
	elseif name == "m_goBag" then
		UI_SHOW(UIDefine.UI_BagView)
	elseif name == "m_btnTowerClimbing" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 7)
	elseif name == "m_goRoleGift" then
		UI_SHOW(UIDefine.UI_RoleGiftList,self.roleGiftId)
    elseif name == "m_btnTradeWagons" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 8)
	elseif name == "m_btnArena" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 9)
	elseif name == "worldBossBtn" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 10)
	elseif name == "m_goBattle" then
		BattlePassManager:OnReqBattlepassLoad(true)
	elseif name == "topFightBtn" then
		TopFightManager:OpenTopFightView()
	elseif name == "m_btnAIChat" then
		--self:OpenAIChatService()
		SdkHelper:AIChatHelpAndShow()
		NetGlobalData:SetDataByKey("ai_chat_red",false)
		self:SetListRedPoint()
	end
end


function UI_MainFace:test()
	--local fight = require("Proto.Handler.NetRequest.Fight")
	--fight:OnReqFightTestRead(function (res,data)
	--	local report = ProtocManager:Decode("marge.topia.battle.Report", data.report)
	--	--UI_SHOW(UIDefine.UI_SlgBattleMainView,{["report"]=report})
	--	
	--end)
	BattleSceneManager:OpenScene(BATTLE_SCENE_TYPE.BATTLE_DEBUG)
end

--function UI_MainFace:delayHandleTempSaveRewards()
	
	--self.timer_HandleTempSaveRewards = TimeMgr:CreateTimer(self,
		--function()
			--TinyGameMgr:HandleTempSaveRewards()
			--Game.Save(1)
		--end, 0.3, 1)
	
--end

function UI_MainFace:ResetDOLLARPos()
	TimeMgr:CreateTimer(self, function()
			local pos = UIMgr:GetUIPosByWorld(self.ui.m_doDollar.transform.position)
			MapController:SetUIResourcePos(ItemID.DOLLAR, pos.x, pos.y)
		end, 1, 1)
end

function UI_MainFace:OnClickTinyGameIcon()
	local iStage = GlobalConfig:GetNumber(10304,3)
	local iPassStage = TinyGameMgr:GetPassStageID()
	local tgCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.tinygame, TinyGameMgr:GetTinyGameID())
	
	if iStage and iPassStage < v2n(iStage) and not tgCfg.is_tgcenter then
		TinyGameMgr:SetStageID(iPassStage+1)
		TinyGameMgr:EnterTgStageInMainScene()
	else
		if tgCfg.is_tgcenter then
			UI_SHOW(TinyGameUIDefine.UI_TinyGameCenter)
		else
			UI_SHOW(TinyGameUIDefine.UI_ChapterStage2)
		end

	end

end

function UI_MainFace:OnRedPointDirty(dirtyIdSet)
	if dirtyIdSet[RedID.HeroRedEntry] then
		SetActive(self.ui.m_goHeroRed, RedPointMgr:IsRed(RedID.HeroRedEntry));
	end
	if dirtyIdSet[RedID.BagRedEntry] then
		SetActive(self.ui.m_goBagRed, RedPointMgr:IsRed(RedID.BagRedEntry))
	end
	if dirtyIdSet[RedID.LotteryEntry] then
		SetActive(self.ui.m_goLotteryRed, RedPointMgr:IsRed(RedID.LotteryEntry))
	end
	if dirtyIdSet[RedID.MailRoot] then
		self:SetListRedPoint()
	end
    if dirtyIdSet[RedID.TradeWagons] then
        SetActive(self.ui.m_goTradeWagonsRedPoint, RedPointMgr:IsRed(RedID.TradeWagons))
    end
	if dirtyIdSet[RedID.ArenaEntry] then
		SetActive(self.ui.m_goArenaRedPoint, RedPointMgr:IsRed(RedID.ArenaEntry))
	end
	if dirtyIdSet[RedID.DungeonEntry] then
		SetActive(self.ui.m_goFullBoxDot, RedPointMgr:IsRed(RedID.DungeonEntry))
	end
	if dirtyIdSet[RedID.WorldBoss] then
		SetActive(self.ui.m_goWorldBossRed, RedPointMgr:IsRed(RedID.WorldBoss))
	end
	if dirtyIdSet[RedID.BattlePass] then
		SetActive(self.ui.m_goBattleRed, RedPointMgr:IsRed(RedID.BattlePass))
	end
	if dirtyIdSet[RedID.TopFight] then
		SetActive(self.ui.m_goTopFightRed, RedPointMgr:IsRed(RedID.TopFight) or NetLimitActGift:IsShowRed(TopFightManager.GIFT_TYPE))
	end
end

function UI_MainFace:OnDungeonUpdate()
	self:updateActivityGroup()
end

function UI_MainFace:RoleGiftChange(_roleGiftList)
	local roleGiftList = _roleGiftList or BagManager:GetRoleGiftList()
	if roleGiftList then
		SetActive(self.ui.m_goRoleGift,table.count(roleGiftList)>0)
		self.roleGiftId = BagManager:GetShowRoleGiftId()
		local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_role_gift,self.roleGiftId)
		if config then
			SetUIImage(self.ui.m_imgRoleGift,config.gift_role,false)
			local giftData = BagManager:GetRoleGiftByGiftId(self.roleGiftId)
			SetActive(self.ui.m_goGiftClock,giftData.open_timestamp ~= 0)
			local pos = self.ui.m_txtRoleGift.transform.localPosition
			if giftData.open_timestamp == 0 then
				self.ui.m_txtRoleGift.transform.localPosition = Vector3(0,pos.y,pos.z)
			else
				self.ui.m_txtRoleGift.transform.localPosition = Vector3(12,pos.y,pos.z)
			end
			--self.ui.txtRoleGift.gameObject.transform.rect.
		end
	else
		SetActive(self.ui.m_goRoleGift,false)
	end
end

function UI_MainFace:BattlePassChange()
	local isShow = BattlePassManager:IsShowEnter()
	SetActive(self.ui.m_goBattle,isShow)
end

function UI_MainFace:UpdateRoleGift()
	if self.roleGiftId then
		local giftData = BagManager:GetRoleGiftByGiftId(self.roleGiftId)
		if giftData then			
			local time = giftData.open_timestamp == 0 and LangMgr:GetLang(70000274) or TimeMgr:CheckHMSNotEmpty(giftData.open_timestamp - TimeMgr:GetServerTimestamp())
			self.ui.m_txtRoleGift.text = time
		end
	end
end

function UI_MainFace:OnHeroChange(changeIdList, isAddHero)
	if isAddHero then
		local openBagState = HeroManager:IsHeroActive(GlobalConfig.OPEN_SLG_BAG);
		--self.rightBottomList[FolderItemType.SLGBag].isOpen = openBagState
		--SetActive(self.rightBottomList[FolderItemType.SLGBag].go, openBagState)

		local openHeroState = HeroManager:IsHeroActive(GlobalConfig.OPEN_SLG_HERO);
		self.openHeroState = openHeroState
		local lockObj = GET_UI(self.ui.m_goHero, "imgLock")
		SetActive(lockObj,not openHeroState)
		--SetUIObjGray(self.ui.m_goHero, not openHeroState)
		--SetActive(self.ui.m_goHero, openHeroState)

        SetActive(self.ui.m_goPlayerPower, NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.LevelEnter))
		--self:FoldRightBottom()
	end
end

---------------------- 每日目标 ---------------------------------

function UI_MainFace:DailyTargetInit()
	local isShow = DailyTargetManager:IsCanShowDailyTarget()
	if not isShow then
		SetActive(self.ui.m_goDailyTargetRoot,false)
		return
	end
	local todayItemID = DailyTargetManager:GetTodayObjItemId()
	SetImageSprite(self.dailyTargetIcon,ItemConfig:GetIcon(todayItemID),false)
	self:CheckTargetLockState()
	local Pos = DailyTargetManager:GetDailyTargetIconPos()
	if Pos == nil then
		DailyTargetManager:SetDailyTargetIconPos(self.ui.m_goCurDailyIcon.transform.position)
	end
	self.ui.m_txtUnLockDesc.text = LangMgr:GetLang(20018)
	self:UpdateDailyTarget()
	self:UpdateDailyTargetTime()
	SetActive(self.ui.m_goDailyTargetRoot,true)
	--self:RefreshBtnDailyRookie()
end

-- 新手入口
function UI_MainFace:RefreshBtnDailyRookie()
	-- 新手任务入口
	--判断是否完成全部新手任务
	--if DailyTargetManager:IsOpenDailyTargetSwitch() then
	--	local isGetRookie = NetDailyTaskData:IsGetRookieBoxReward()
	--	SetActive(self.ui.m_btnNewDailyTask,not isGetRookie)
	--else
	--end
	SetActive(self.ui.m_btnNewDailyTask,false)
end

function UI_MainFace:CheckTargetLockState()
	local isShow = DailyTargetManager:IsCanShowDailyTarget()
	if isShow then
		SetActive(self.imgTatgetLock,false)
		SetActive(self.ui.m_goDailyTarget,true)
	else
		SetActive(self.imgTatgetLock,true)
		SetActive(self.ui.m_goDailyTarget,false)
	end
end

function UI_MainFace:UpdateDailyTargetTime()
	local endTime = NetDailyTargetData:GetResetTime()--TimeZoneMgr:GetServerClockStampByNextDay()
	local nowTime = TimeMgr:GetServerTime()
	local time = endTime - nowTime
	if time < 0 then
		if IntelligentWorkerManager:IsRunning() then
			return
		end
		NetDailyTargetData:ResetNetData()
		local todayItemID = DailyTargetManager:GetTodayObjItemId()
		SetImageSprite(self.dailyTargetIcon,ItemConfig:GetIcon(todayItemID),false)
		self:UpdateDailyTarget()
	else
		self.ui.m_txtDailyTargetTime.text = TimeMgr:CheckHMSNotEmpty(time)
	end
end

function UI_MainFace:UpdateDailyTarget(stage,rewardCount)
	local curStage = NetDailyTargetData:GetDataByKey("curStage")
	if nil ~= stage then
		curStage = stage
	end
	local curScore = NetDailyTargetData:GetDataByKey("targetScore")
	curScore = math.floor(curScore)
	local nextStage = curStage + 1
	local isMaxStage = DailyTargetManager:GetIsMaxStage(nextStage)
	local curStageMaxScore = DailyTargetManager:GetStageTargetNeedScore(curStage)
	if curStageMaxScore then
		--if not isChangeStage then
		--	self.ui.m_txtTargetProgress.text = curScore .. "/" .. curStageMaxScore
		--	self.sliderTarget.value = curScore / curStageMaxScore
		--else
		--	self.sliderTarget.value = 0
		--	self.ui.m_txtTargetProgress.text = 0 .. "/" .. curStageMaxScore
		--end
		self.ui.m_txtTargetProgress.text = curScore .. "/" .. curStageMaxScore
		self.sliderTarget.value = curScore / curStageMaxScore
	end

	local stageRewardId,num = DailyTargetManager:GetStageTargetReward(curStage)
	local lastRewardItem = DailyTargetManager:GetLastRewardCacheItem()
	local realStage = NetDailyTargetData:GetDataByKey("curStage")
	if IsTableNotEmpty(lastRewardItem) and curStage == realStage then
		stageRewardId = v2n(NetSeasonActivity:GetChangeItemId(lastRewardItem.itemId))
		num = lastRewardItem.curnum 
	end
	if stageRewardId then
		SetImageSprite(self.imgDailyTarget,ItemConfig:GetIcon(stageRewardId),false)
		self.ui.m_txtTarget.text = "x" .. NumToGameString(num)
	end

	if isMaxStage and curScore > curStageMaxScore then
		self.sliderTarget.value = 1
		self.ui.m_txtTargetProgress.text = LangMgr:GetLang(5003)
	end
	self:RefreshTargetCache(rewardCount)
end

function UI_MainFace:RefreshTargetCache(rewardCount)
	local rewardList = NetDailyTargetData:GetDataByKey("rewardCache")
	local count = GetTableLength(rewardList)
	if rewardCount then
		count = rewardCount
	end
	SetActive(self.ui.m_goTargetCache,count > 0)
	self.ui.m_txtTargetCacheNum.text = count
	if count > 0 then
		PlayAnimStatus(self.dailyTargetAni,"item_yundong")
	else
		ResetAnimation(self.dailyTargetAni,"item_yundong")
	end
end

function UI_MainFace:DoDailyTargetTween(changeParam)
	local oldScore = changeParam.oldScore
	local newScore = changeParam.newScore
	local curStage = changeParam.oldStage
	local rewardCount = changeParam.rewardCount
	local needScore = DailyTargetManager:GetStageTargetNeedScore(curStage)
	
	local last = oldScore / needScore
	local endcount = newScore / needScore
	local nowScore = newScore

	table.insert(self.targetSequence,{startValue = last,endValue = endcount,
									  oldScore = oldScore,nowScore = nowScore,needScore = needScore,
									  curStage = curStage, rewardCount = rewardCount})
	if not self.isTween then
		self.isTween = true
		self:StartDailyTargetTween()
	end
end

function UI_MainFace:StartDailyTargetTween()
	--table.sort(self.targetSequence,function(a, b)
	--	if a.curStage == b.curStage then
	--		return a.oldScore < b.oldScore
	--	end
	--	return a.curStage < b.curStage
	--end)
	local count = table.count(self.targetSequence)
	local tweenItem = table.remove(self.targetSequence,1)
	if not tweenItem then
		return
	end
	DailyTargetManager:SetIsPlayingTween(true)
	local startValue = tweenItem.startValue
	local endValue = tweenItem.endValue
	local txtStar = tweenItem.oldScore
	local txtEnd = tweenItem.nowScore
	local txtNeed = tweenItem.needScore
	local curStage = tweenItem.curStage
	local rewardCount = tweenItem.rewardCount

	self.ui.m_txtTargetProgress.text = math.floor(txtStar) .. "/" .. math.floor(txtNeed)
	self.sliderTarget.value = startValue
	local function onFinishCallBack()
		if endValue >= 1 then
			local function movingAction()
				self:UpdateDailyTarget(curStage,rewardCount)
				if not IsTableEmpty(self.targetSequence) then
					self.isTween = true
					self:StartDailyTargetTween()
				else
					self.isTween = false
				end
			end
			local tween = DOScale(self.imgDailyTarget.transform, Vector3.New(1.3, 1.3, 1), 0.1, movingAction)
			tween:SetDelay(0.1)
			tween:SetLoops(2, LoopType.Yoyo)

			if not DailyTargetManager:GetIsPushView() then
				DailyTargetManager:SetIsPushView(true)
				local pos = self.imgDailyTarget.transform.position
				local posScreen = UIMgr:GetUIPosByWorld(pos)
				NetPushViewData:PushView(PushDefine.UI_DailyTargetReward, {pos = {x = posScreen.x,y = posScreen.y, z = posScreen.z}})
			end
		else
			self:UpdateDailyTarget(curStage,rewardCount)
			if not IsTableEmpty(self.targetSequence) then
				self.isTween = true
				self:StartDailyTargetTween()
			else
				self.isTween = false
			end
		end

		if IsTableEmpty(self.targetSequence) then
			DailyTargetManager:SetIsPlayingTween(false)
		end
	end

	local tweenTime = 0.3
	if count > 2 then
		tweenTime = 0.1
	end
	AddDOTweenNumberDelay(txtStar,txtEnd,tweenTime,0.1,
			function (value)
				self.ui.m_txtTargetProgress.text = math.floor(value) .. "/" .. txtNeed
			end):SetEase(Ease.Linear):SetId("m_txtTargetProgress")
	
	AddDOTweenNumberDelay(startValue,endValue,tweenTime,0.1,
			function (value)
				self.sliderTarget.value = value
			end,onFinishCallBack):SetEase(Ease.Linear):SetId("m_sliderDailyTarget")
	

end

function UI_MainFace:PlayDailyTargetTween()
	TimeMgr:CreateTimer(self, function()
		if self.dailyTargetUpAni then
			self.dailyTargetUpAni:Stop()
			self.dailyTargetUpAni:Play("DailyTargetUpAnim")
		end
	end, 0.8, 1)
end

function UI_MainFace:ShowDailyTargetReward(curStage,itemId,num,rewardCfg)
	local pos = self.imgDailyTarget.transform.position
	local posScreen = UIMgr:GetUIPosByWorld(pos)
	local t = {}
	t.curStage = curStage
	t.itemId = itemId
	t.curnum = num
	t.rewardCfg = rewardCfg
	NetDailyTargetData:AddRewardCache(t)
	local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,itemId)
	if config.type_name == -1 then
		NetUpdatePlayerData:AddResourceNumByID(v2n(itemId), v2n(num),nil,"", true)
	else
		MapController:SendRewardToMap(rewardCfg,nil,nil,nil,"")
	end
	--UI_SHOW(UIDefine.UI_DailyTargetReward,param)
	NetDailyTargetData:SetRewardState(curStage,DailyTargetRewardState.CAN_REWARD)
	--NetPushViewData:PushView(PushDefine.UI_DailyTargetReward, param)
end

function UI_MainFace:ShowRedDiamond(from,to)
	if not IsRuGoogle() then
		SetActive(self.ui.m_goDiamond,false)
		SetActive(self.ui.m_goRedDiamond,true)
	end
	
	self.ui.m_doRedDiamond:DORestart()
	self.ui.m_txtRedDiamond.text = NumToGameString(from)
	AddDOTweenNumberDelay(from, to, 0.3, 0.8, function(value)
		self.ui.m_txtRedDiamond.text = NumToGameString(math.floor(value))
	end,function()

		AddDOTweenNumberDelay(from, to, 0.3, 1.3,function ()
			
		end,function ()
			if not IsRuGoogle() then
				SetActive(self.ui.m_goDiamond,true)
				SetActive(self.ui.m_goRedDiamond,false)
			end
		end)
		
	end)
	AudioMgr:Play(12)
end

function UI_MainFace:GetADBuffPos()
	return self.ui.m_goADBuff.transform.position
end

function UI_MainFace:GetMagnetBuffPos()
	return self.ui.m_goPrivilege.transform.position
end

function UI_MainFace:CheckVIPContactPush()
	local ServerSwitch = Game.GetServerSwitch()
	if ServerSwitch and ServerSwitch.redemption_show == 0 then
		NetGlobalData:SetPlayerVipData(nil)
		return
	end
	local playerVipData = NetGlobalData:GetPlayerVipData()
	if playerVipData then
		NetPushViewData:PushView(PushDefine.UI_VIPContact,playerVipData)
		NetGlobalData:SetPlayerVipData(nil)
	end
	local vipSign = NetGlobalData:GetVipSign()
	if vipSign == false then
		NetGlobalData:SetVipSign(true)
	end
end

function UI_MainFace:RefreshCompetition(param)
    -- 刷新玩家头像
    --local playerHead = NetUpdatePlayerData:GetPlayerInfo().head
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerHead)
	--if headConfig then
    --	SetUIImage(self.ui.m_imgHeadLeft, headConfig["icon"], false)
	--end
	SetMyHeadAndBorderByGo(self.my1v1Head1)
	SetMyHeadAndBorderByGo(self.my1v1Head2)
	
    -- 刷新玩家名字
    if NetUpdatePlayerData:GetPlayerInfo().name then
        self.ui.m_txtNameLeft.text = NetUpdatePlayerData:GetPlayerInfo().name
    else
        self.ui.m_txtNameLeft.text = "player-" .. NetUpdatePlayerData:GetPlayerInfo().id
    end
    ---- 设置匹配对手头像
    --local currentRobotHead = NetOneToOneData:GetDataByKey("currentRobotHead")
    --if not IsNilOrEmpty(currentRobotHead) then
    --    SetUIImage(self.ui.m_imgHeadRight, currentRobotHead, false)
	--
	--	if self.other1v1Head == nil then
	--		self.other1v1Head = CreateCommonHead(GetChild(self.ui.m_goHead,"Right").transform,0.45)
	--	end
	--	SetHeadAndBorderByGo(self.other1v1Head,currentRobotHead,121)
    --end

	-- 设置匹配对手头像
	local currentRobotHead = NetOneToOneData:GetDataByKey("currentRobotHead")
	local currentRobotBorder = NetOneToOneData:GetDataByKey("currentRobotBorder")
	if self.other1v1Head == nil then
		self.other1v1Head = CreateCommonHead(GetChild(self.ui.m_goHead,"Right").transform,0.35)
		local rect = GetComponent(self.other1v1Head,UE.RectTransform)
		rect.anchoredPosition = Vector2.New(0,5)
	end
	
	if not IsNilOrEmpty(currentRobotHead)  and not IsNilOrEmpty(currentRobotBorder)then
		SetHeadAndBorderByGo(self.other1v1Head,currentRobotHead,currentRobotBorder)
	end
	
    local currentRobotName = NetOneToOneData:GetDataByKey("currentRobotName")
    self.ui.m_txtNameRight.text = currentRobotName
    -- 显示当前比拼分数
    self:RefreshScore()
	local winNum = NetOneToOneData:GetDataByKey("winningStreak")
	local config = OneToOneManager:GetVictoryConfig(winNum)
	if config then
		self.ui.m_txtCompetition.text = config.victory_points
	end
    -- 飞积分
    -- if param and param.isFly then
    --     local view = UIMgr:GetUIItem(UIDefine.UI_OneToOneView)
    --     local isShow = view and view.isShow
    --     if isShow then return end
    --     local oldPos =  UIMgr:GetObjectScreenPos(self.ui.m_transFlyPos)
    --     local endPos
    --     -- 玩家
    --     if param.endPos == 0 then
    --         endPos = self.ui.m_imgHeadLeft
    --         if param.startPos then
    --             oldPos = param.startPos
    --             OneToOneManager.startPos = nil
    --         end
    --     -- 机器人
    --     elseif param.endPos == 1 then
    --         endPos = self.ui.m_imgHeadRight
    --         oldPos =  UIMgr:GetObjectScreenPos(self.ui.m_transFlyPosRobot)
    --     end
    --     local posScreen = UIMgr:GetUIPosByWorld(endPos.transform.position)
    --     local flyId = 55002
    --     local score = 5
    --     MapController:FlyUIAnim(oldPos.x, oldPos.y, flyId, score, posScreen.x, posScreen.y,
    --     nil, nil, nil, 0.7, nil,
    --     function ()
            
    --     end)
    -- end
end

function UI_MainFace:RefreshCompetitionTime(time)
    self.ui.m_txtCompetitionCountDown.text = TimeMgr:ConverSecondToString(time)
end

function UI_MainFace:RefreshScore()
    local curScore = NetOneToOneData:GetDataByKey("curScore")
    local robotScore = NetOneToOneData:GetDataByKey("robotScore")
    if NetOneToOneData.playerChangeScore > 0 then
        if self.txtScoreBlueTween then
            self.txtScoreBlueTween:Kill()
            self.txtScoreBlueTween = nil
        end
        self.txtScoreBlueTween = Tween.To(function(value)
            self.ui.m_txtScoreBlue.text = tostring(math.floor(value))
        end, curScore - NetOneToOneData.playerChangeScore, curScore, 1)

        local item = UEGO.Instantiate(self.ui.m_goScoreBlue, self.ui.m_goScore.transform)
        SetActive(item, true)
        local text = GetChild(item.transform, "m_txtScoreBlueAdd", UEUI.Text)
        text.text = "+" .. NetOneToOneData.playerChangeScore
        item.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOScale(item.transform, Vector3.New(1.0, 1.0, 1.0), 0.8, nil, Ease.OutBack)
        local rect = GetComponent(item.transform, UE.RectTransform)
        rect.anchoredPosition3D = Vector3.New(-188, -70, 0)
        DOLocalMove(item.transform, Vector3.New(-168, -64, 0), 0.8, nil, Ease.OutBack):OnComplete(function ()
            DOScale(item.transform, Vector3.New(0, 0, 0), 1, nil, Ease.OutBack):SetDelay(1)
            DOLocalMove(item.transform, Vector3.New(-109, -2, 0), 1, nil, Ease.OutBack):OnComplete(function ()
                UEGO.Destroy(item)
            end):SetDelay(1)
        end)

        NetOneToOneData.playerChangeScore = 0
    else
        self.ui.m_txtScoreBlue.text = tostring(curScore)
    end

    if NetOneToOneData.robotChangeScore > 0 then
        if self.txtScoreRedTween then
            self.txtScoreRedTween:Kill()
            self.txtScoreRedTween = nil
        end
        self.txtScoreRedTween = Tween.To(function(value)
            self.ui.m_txtScoreRed.text = tostring(math.floor(value))
        end, robotScore - NetOneToOneData.robotChangeScore, robotScore, 1)

        local item = UEGO.Instantiate(self.ui.m_goScoreRed, self.ui.m_goScore.transform)
        SetActive(item, true)
        local text = GetChild(item.transform, "m_txtScoreRedAdd", UEUI.Text)
        text.text = "+" .. NetOneToOneData.robotChangeScore
        item.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOScale(item.transform, Vector3.New(1.0, 1.0, 1.0), 0.8, nil, Ease.OutBack)
        local rect = GetComponent(item.transform, UE.RectTransform)
        rect.anchoredPosition3D = Vector3.New(82, -70, 0)
        DOLocalMove(item.transform, Vector3.New(62, -64, 0), 0.8, nil, Ease.OutBack):OnComplete(function ()
            DOScale(item.transform, Vector3.New(0, 0, 0), 1, nil, Ease.OutBack):SetDelay(1)
            DOLocalMove(item.transform, Vector3.New(3, -2, 0), 1, nil, Ease.OutBack):OnComplete(function ()
                UEGO.Destroy(item)
            end):SetDelay(1)
        end)

        NetOneToOneData.robotChangeScore = 0
    else
        self.ui.m_txtScoreRed.text = tostring(robotScore)
    end

    if curScore > 0 or robotScore > 0 then
        local percent = curScore / (curScore + robotScore)
        local diff = math.abs(percent - 0.5)
        local target = diff * 0.33
        if curScore > robotScore then
            target = 0.5 + target
        else
            target = 0.5 - target
        end
        self.ui.m_sliderVS:DOValue(target, 1)
    else
        self.ui.m_sliderVS.value = 0.5
    end
end

-- 刷新多倍掉落
function UI_MainFace:RefreshMultipleDropInfo()
	local isOpen = NetMulitpleDropData:IsOpenActivity()
	if not isOpen then
		return
	end
	local curSelectMultiple = NetMulitpleDropData:GetDataByKey("curSelectMultiple")

	local btn1 = "Assets/ResPackage/Sprite/ui_mainface/mainface2_menu_duobeishu1.png"
	local btn3 = "Assets/ResPackage/Sprite/ui_mainface/mainface2_menu_duobeishu2.png"
	local btn9 = "Assets/ResPackage/Sprite/ui_mainface/mainface2_menu_duobeishu3.png"
	local btn27 = "Assets/ResPackage/Sprite/ui_mainface/mainface2_menu_duobeishu4.png"

	local assetPath
	if curSelectMultiple == MultipleNum.ONE then
		assetPath = btn1
	elseif curSelectMultiple == MultipleNum.THREE then
		assetPath = btn3
	elseif curSelectMultiple == MultipleNum.NINE then
		assetPath = btn9
	elseif curSelectMultiple == MultipleNum.TWENTY_SEVEN then
		assetPath = btn27
	end
	if assetPath then
		SetImageSprite(self.ui.m_imgMultipleIcon,assetPath,false)
	end
	
	self:SetMultipleRed()
end

function UI_MainFace:SetStartMultipleCountDownTime(value)
	self.isStartMultipleTime = value
end

function UI_MainFace:RefreshMultipleDropTime()
	local isOpen = NetMulitpleDropData:IsOpenActivity()
	if not isOpen then
		return
	end

	local curSelectMultiple = NetMulitpleDropData:GetDataByKey("curSelectMultiple")
	local endTime3 = NetMulitpleDropData:GetDataByKey("multipleItemTime3") or 0
	local endTime9 = NetMulitpleDropData:GetDataByKey("multipleItemTime9") or 0
	local endTime27 = NetMulitpleDropData:GetDataByKey("multipleItemTime27") or 0
	local timeNow = TimeMgr:GetServerTime()
	local time3 = endTime3 - timeNow
	local time9 = endTime9 - timeNow
	local time27 = endTime27 - timeNow
	local isShowTime = false
	local showTime = 0

	if curSelectMultiple == MultipleNum.ONE then
		isShowTime = false
	elseif curSelectMultiple == MultipleNum.THREE then
		isShowTime = time3 > 0
		showTime = time3
	elseif curSelectMultiple == MultipleNum.NINE then
		isShowTime = time9 > 0
		showTime = time9
	elseif curSelectMultiple == MultipleNum.TWENTY_SEVEN then
		isShowTime = time27 > 0
		showTime = time27
	end
	SetActive(self.ui.m_goMultipleTimeBg,isShowTime)

	if showTime > 0 then
		self.ui.m_txtMultipleTime.text = TimeMgr:GetHMS(showTime)
	else
		if curSelectMultiple == MultipleNum.THREE or curSelectMultiple == MultipleNum.NINE or curSelectMultiple == MultipleNum.TWENTY_SEVEN then
			-- 时间到自动切回3倍
			NetMulitpleDropData:ChangeSelectMultiple(MultipleNum.ONE)
			self:RefreshMultipleDropInfo()
		end

		self.ui.m_txtMultipleTime.text = ""
	end
end

function UI_MainFace:RefreshTriggerGiftTime()
	if self.triggerGiftTime == nil then
		local gift = NetGiftBox:GetGiftLessTime()
		if nil == gift or gift.time == nil then
			SetActive(self.ui.m_goTriggerGift,false)
			return
		end
		SetActive(self.ui.m_goTriggerGift,true)
		local data = TriggerGiftConfig:GetDataByID(gift.id)
		if data.main_icon then
			SetUIImage(self.ui.m_imgTriggerGiftIcon,data.main_icon,false)
		end
		self.triggerGiftTime = gift.time or 0
	end
	local time = self.triggerGiftTime - TimeMgr:GetServerTime()
	if time > 0 then
		self.ui.m_txtTriggerGift.text = TimeMgr:CheckHMS(time)
	else
		self.ui.m_txtTriggerGift.text = ""
		self.triggerGiftTime = nil
	end
end

function UI_MainFace:PlayTriggerGiftFlyEff(id)
	if nil ~= id then
		local data = TriggerGiftConfig:GetDataByID(v2n(id))
		if data and data.main_icon then
			local toPos = UIMgr:GetObjectScreenPos(self.ui.m_goTriggerGift.transform)
			EffectConfig:CreateEffect(136, toPos.x, toPos.y, 0,UIMgr.layers[UILayerType.Top])
			MapController:FlyUIAnimByImg(0,0,data.main_icon, 1,toPos.x, toPos.y,nil,nil,nil,nil,FlyResourceType.FlyNone,function ()
				
			end)
		end
	end
end
function UI_MainFace:PlayShushuGiftFlyEff()
	local toPos = UIMgr:GetObjectScreenPos(self.ui.m_goActCenter.transform)
	EffectConfig:CreateEffect(136, toPos.x-60, toPos.y, 0,UIMgr.layers[UILayerType.Top])
	MapController:FlyUIAnimByImg(0,0,"Sprite/ui_mainface/icon_hd_zhencanglibao.png", 1,toPos.x-60, toPos.y,nil,nil,nil,nil,FlyResourceType.FlyNone)

end
----------------------- 新版主界面任务栏效果 --------------------------------
function UI_MainFace:PushFinishTaskQuence(taskId)
	table.insert(self.finishTaskQuence,taskId)
	self:UpdateFinishTaskQuenceCount()
end

function UI_MainFace:UpdateFinishTaskQuenceCount()
	self.finishTaskQuenceCount = table.count(self.finishTaskQuence)
end

function UI_MainFace:FinishTaskQuenceTimer()
	if self.finishTaskQuenceCount > 0 and self.isCanPopingFingTask then
		self:PopFinishTaskQuence()
	end
end

function UI_MainFace:PopFinishTaskQuence()
	self.isCanPopingFingTask = false
	SetActive(self.ui.m_imgFinishTask,true)
	local curTaskId = table.remove(self.finishTaskQuence,1)
	self:UpdateMainTask(curTaskId)
	self:PlayFinishTaskEff()
end

function UI_MainFace:PlayFinishTaskEff()
	EffectConfig:CreateEffect(22, 0, 0, 0, self.ui.m_imgDailyIcon.transform, function(data, tGo, go)
	end)
	TimeMgr:CreateTimer("PlayFinishTaskEff", function ()
		self.isCanPopingFingTask = true
		SetActive(self.ui.m_imgFinishTask,false)
		self:UpdateFinishTaskQuenceCount()
		if self.finishTaskQuenceCount <= 0 then
			self:UpdateMainTask()
		end
	end, 2, 1)
end

function UI_MainFace:PlayNewTaskEff()
	EffectConfig:CreateEffect(175, 0, 0, 0, self.ui.m_goMainTaskBg.transform, function(data, tGo, go)
	end)
	SetActive(self.ui.m_imgNewTaskFlag,true)
	TimeMgr:CreateTimer(UIDefine.UI_MainFace, function ()
		SetActive(self.ui.m_imgNewTaskFlag,false)
		local newTaskCount = table.count(self.newTaskQuence)
		if newTaskCount <= 0 then
			self.isPlayNewTaskEff = false
			self:UpdateMainTask()
		else
			self.isPlayNewTaskEff = true
		end
	end, 3, 1)
end

function UI_MainFace:CheckPlayNewTaskEff()
	local newTaskCount = table.count(self.newTaskQuence)
	if self.finishTaskQuenceCount <= 0 and newTaskCount > 0 and self.isPlayNewTaskEff then
		local newTask = table.remove(self.newTaskQuence,1)
		self:UpdateMainTask(newTask)
		self:PlayNewTaskEff()
	end
end

--处理右下角工具栏箭头
function UI_MainFace:UpdateRightBottomArow()
	local count = 0
	for i = 1, self.rightBottomCount do
		local isOpen = self.rightBottomList[i].isOpen
		if isOpen then
			count = count + 1
		end
	end
	SetActive(self.ui.m_goRightBottomList,count > 0)
	SetActive(self.ui.m_btnRightBottomRow,count > 1)
end

function UI_MainFace:ChangeRightBottomArow()
	self.rightBottomArowIsPut = not self.rightBottomArowIsPut
    NetGlobalData:SetDataByKey("rightBottomArowIsPut", self.rightBottomArowIsPut)
    self:FoldRightBottom()
	self:CheckRBFoldBtnRed()
	self:ShowRBFoldPop()
end

--- 折叠右下角的工具栏
function UI_MainFace:FoldRightBottom()
    -- 折叠
    if self.rightBottomArowIsPut then
		self.ui.m_btnRightBottomRow.transform:SetLocalScale(-1, 1, 1)
    -- 展开
    else
		self.ui.m_btnRightBottomRow.transform:SetLocalScale(1, 1, 1)
		local childCount = self.rbLayoutTrans.childCount
		local activeCount = 0
		for i = 0,childCount - 1 do
			local child = self.rbLayoutTrans:GetChild(i)
			if child.gameObject.activeSelf then
				activeCount = activeCount + 1
			end
		end
		self.rbBgRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Horizontal, activeCount*94+46);
    end

	local openCount = 0
	for i = 1, self.rightBottomCount do
		local isOpen = self.rightBottomList[i].isOpen
		if isOpen then
			openCount = openCount + 1
		end
	end
	--region	旧收起逻辑
	--local hideCount = 1
	--if openCount > 2 then
	--	hideCount = 2
	--end
    ---- 折叠时隐藏前两个
    --for i = 1, hideCount do
    --    local go = self.rightBottomList[i].go
    --    if not IsNil(go) then
    --        SetActive(go, not self.rightBottomArowIsPut)
    --    end
    --end
	--
	----(扩展逻辑)隐藏新增的按钮
    --local extendList = {4,5}
	--for k,v in ipairs(extendList) do
	--	local go = self.rightBottomList[v].go
	--	if not IsNil(go) then
	--		--true 折叠 false 展开
	--		if self.rightBottomArowIsPut then
	--			SetActive(go, false)
	--		else
	--			SetActive(go, self.rightBottomList[v].isOpen)
	--		end
	--	end
	--end
	--endregion
	local showIndex = FolderItemType.Magnet
	--if self.rightBottomList[FolderItemType.SLGBag].isOpen then
	--	showIndex = FolderItemType.SLGBag
	--else
	--	showIndex = FolderItemType.Magnet
	--end
	for i = 1, self.rightBottomCount do
		local isOpen = self.rightBottomList[i].isOpen
		if isOpen then
			local go = self.rightBottomList[i].go
			if not IsNil(go) and i ~= showIndex then
				--true 折叠 false 展开
				if self.rightBottomArowIsPut then
					SetActive(go, false)
				else
					SetActive(go, self.rightBottomList[i].isOpen)
				end
			end
		end
	end
	
	--当工具栏只有1个入口时就不用显示黑色地图和箭头图标(新增需求)
	local onlyOne = (openCount == 1)
	self.rbBgImg.enabled = not onlyOne
	SetActive(self.ui.m_btnRightBottomRow, not onlyOne)

	local goBagPos = UIMgr:GetUIPosByWorld(self.ui.m_goBag.transform.position)
	EquipmentManager:SetFlyEndPos(goBagPos)
end

function UI_MainFace:GetActivityGroupSortList()
	local sortList = {}
	local config = ConfigMgr:GetData(ConfigDefine.ID.activity_rank)
	for i, v in ipairs(config) do
		table.insert(sortList,v)
	end
	table.sort(sortList, function(a, b)
		return a.sort < b.sort;
	end)
	return sortList
end

function UI_MainFace:updateActivityGroup()
	local config = self:GetActivityGroupSortList()
	local count = 0
	local nameStr;
	local redImg;
	local level = NetUpdatePlayerData:GetLevel()
	for i, v in ipairs(config) do
		nameStr = "m_goActivityTabGroup"..v.id;
		local openArr = string.split(v.open,"|")
		local openType = v2n(openArr[1])
		local openParm = v2n(openArr[2])
		local activityOpen ,activity = NetGlobalData:GetIsOpenActivityRank(v)
		if activityOpen then
			count = count + 1
			SetActive(self.ui[nameStr],true)
		else
			if activity and activity.info.state == 3 and openParm == ActivityTotal.BowlingBattle then
				activityOpen = activity and activity.info.state == 3;
				count = count + 1
				SetActive(self.ui[nameStr],true)
			else
				SetActive(self.ui[nameStr],false)
			end
		end
		self.activityRankTabList[v.id] = activityOpen
        self.activityRankTabIndexList[v.id] = count
		
		if activityOpen and activity then
			local redImg = GetChild(self.ui[nameStr], "redImg");
			if openParm == ActivityTotal.BowlingBattle then
				SetActive(redImg, BowlingBattleManager:CheckRedPoint())
			elseif openParm == ActivityTotal.OneToOne then
				SetActive(redImg, NetOneToOneData:IsShowRedPoint())
				self.oneToOneTime = activity:GetRemainingTime();
			elseif openParm == ActivityTotal.AthleticTalent then
				SetActive(redImg, NetAthleticTalentData:IsShowRedPoint())
				self.athleticTalentTime = activity:GetRemainingTime();
			elseif openParm == ActivityTotal.Rank then
				SetActive(redImg, NetRankData:IsShowRedPoint(ActivityTotal.Rank) or NetUpdatePlayerData:GetPlayerRankOneRewardRed());
				self.rankTime = activity:GetRemainingTime();
			else
				SetActive(redImg, false)
			end
		end
	end
	self.maxActivityRankTabPage = count - 1
	local tabNameStr;
	for i = 1, #config do
		tabNameStr = "m_imgActivityTab"..i;
		if self.ui[tabNameStr] then
			SetActive(self.ui[tabNameStr], i <= count)
		end
	end
	for i, v in ipairs(config) do
		nameStr = "m_goActivityTabGroup"..v.id;
		SetUILastSibling(self.ui[nameStr])
	end
	SetActive(self.ui.m_goActivityGrop,count > 0)
	SetActive(self.ui.m_goActivityTabRoot,count > 0)

    if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.AthleticTalent] then
        self:UpdateAthleticTalent(true)
    end
	if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.BowlingBattle] then
		self:updateBowlingShow()
	end
	if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.Rank] then
		self:UpdateRankShow();
	end	
	if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.TowerClimbing] then
		self:UpdateTowerShow()
	end
    SetActive(self.ui.m_goPlayerPower, NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.LevelEnter))
end

function UI_MainFace:updateBowlingShow()
	local isOpen, activity = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.BowlingBattle);
	local isPreview = activity and activity.info.state == 3;
	if isPreview then
		local helperNum = 0
		local detail = LeagueManager:GetMyLeagueDetails()
		if detail and detail.GetHelpNumber then
			helperNum = detail:GetHelpNumber()
		end

		SetActive(self.ui.m_goBowlingConditionOK, helperNum > 0)
		SetActive(self.ui.m_goBowlingConditionNO, helperNum <= 0)

		local replaceText;
		if helperNum > 0 then
			replaceText = "(" .. helperNum .. "/1)"
		else
			replaceText = string.format("(<color=#%s>0</color>/1)", "ff0000")
		end
		self.ui.m_txtBowlingCondition.text = LangMgr:GetLangFormat(9312, replaceText);

		self.bowlingDownTime = activity:GetStartRemainingTime();
	elseif isOpen then
		local rank = NetBowlingBattleData:GetRankMyLeague();
		if rank > 0 then
			self.ui.m_txtRank.text = rank;
		end
		self.bowlingDownTime = activity:GetRemainingTime();

		isOpen = rank > 0;
	end

	SetActive(self.ui.m_goOpen, isPreview);
	SetActive(self.ui.m_goRank, isOpen);
end

function UI_MainFace:UpdateDownTimer()
	if self.oneToOneTimeTxt then
		if self.oneToOneTime and self.oneToOneTime > 0 then
			self.oneToOneTime = self.oneToOneTime - 1;
			self.oneToOneTimeTxt.text = TimeMgr:ConverSecondToString(self.oneToOneTime);
		else
			self.oneToOneTimeTxt.text = TimeMgr:ConverSecondToString(0);
		end
	end

	if self.athleticTalentTimeTxt then
		if self.athleticTalentTime and self.athleticTalentTime > 0 then
			self.athleticTalentTime = self.athleticTalentTime - 1;
			self.athleticTalentTimeTxt.text = TimeMgr:ConverSecondToString(self.athleticTalentTime);
		else
			self.athleticTalentTimeTxt.text = TimeMgr:ConverSecondToString(0);
		end
	end

	if self.bowlingTimeTxt then
		if self.bowlingDownTime and self.bowlingDownTime > 0 then
			self.bowlingDownTime = self.bowlingDownTime - 1;
			self.bowlingTimeTxt.text = TimeMgr:ConverSecondToString(self.bowlingDownTime);
		else
			self.bowlingTimeTxt.text = TimeMgr:ConverSecondToString(0);
		end
	end

	if self.rankTimeTxt then
		if self.rankTime and self.rankTime > 0 then
			self.rankTime = self.rankTime - 1;
			self.rankTimeTxt.text = TimeMgr:ConverSecondToString(self.rankTime);
		else
			self.rankTimeTxt.text = TimeMgr:ConverSecondToString(0);
		end
	end	
	
	if self.towerTimeTxt then
		if not self.towerTime then
			local nowTime = TimeMgr:GetServerTimestamp()
			--明天天零点
			local tomorrowTimeStamp = TimeZoneMgr:GetServerClockStampByNDay(1,nowTime)
			self.towerTime = tomorrowTimeStamp - nowTime
		end
		
		if self.towerTime and self.towerTime > 0 then
			self.towerTime = self.towerTime - 1
			self.towerTimeTxt.text = TimeMgr:ConverSecondToString(self.towerTime);
		else
			self.towerTime = nil
			self.towerTimeTxt.text = TimeMgr:ConverSecondToString(0);
		end
	end
	if self.jjcTimeTxt then
		local time = JJcManager:GetJJcRemainingTime()
		if time and time > 0 then
			self.jjcTimeTxt.text = TimeMgr:ConverSecondToString(time)
		else
			self.jjcTimeTxt.text = TimeMgr:ConverSecondToString(0)
		end
	end
	if self.worldBossTimeTxt then
		local time = WorldBossManager:GetRemainTime();
		if time > 0 then
			self.worldBossTimeTxt.text = TimeMgr:ConverSecondToString(time);
		else
			self.worldBossTimeTxt.text = LangMgr:GetLang(70000697);
		end
	end
	
	if self.topFightTimeTxt then
		local time = TopFightManager:GetTopFightAt(FightStage.End)
		if time and time > 0 then
			self.topFightTimeTxt.text = TimeMgr:ConverSecondToString(time)
		else
			self.topFightTimeTxt.text = LangMgr:GetLang(70000697)
		end
	end
	self:CheckLevelTimer()
end

--- 刷新竞技达人
--- @param refreshRank boolean 是否刷新排名
function UI_MainFace:UpdateAthleticTalent(refreshRank)
    local type = ACTIVITY_RANK_TABINDEX.AthleticTalent--ActivityRankCenterType.AthleticTalent
    local nameStr = "m_goActivityTabGroup" .. type
    local root = self.ui[nameStr]
    local activity = self.activityRankTabList[type]
    -- 初始获取组件
    if IsTableEmpty(self.athleticTalent) then
        local icon = GetChild(root, "icon", UEUI.Image)
        local title = GetChild(root, "title", UEUI.Text)
        local button = GetChild(root, "button", UEUI.Button)
        if button then
            button.onClick:AddListener(function ()
                local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.AthleticTalent)
                if activityItem:IsActivityEnd() then
                    if activityItem.info.activeId then
                        NetAthleticTalentData:CheckEndPush(activityItem.info.activeId)
                    end
                else
                    UI_SHOW(UIDefine.UI_ActivityRankCenter, type)
                end
            end)
        end
        local score = GetChild(root, "score/text", UEUI.Text)
        local rank = GetChild(root, "rank/text", UEUI.Text)
        local rankParent = GetChild(root, "rank")
        self.athleticTalent.icon = icon
        self.athleticTalent.title = title
        self.athleticTalent.button = button
        self.athleticTalent.score = score
        self.athleticTalent.rank = rank
        self.athleticTalent.rankParent = rankParent
    end
    -- 刷新图片
    if self.athleticTalent.icon then
        local iconPath = "Sprite/ui_mainface/mainface2_huodong_jingjidaren.png"
        SetUIImage(self.athleticTalent.icon, iconPath, false)
    end
    -- 刷新标题
    if self.athleticTalent.title then
        local langID = AthleticTalentManager:GetActivityNameLangID() or 2203001
        self.athleticTalent.title.text = LangMgr:GetLang(langID)
    end
    -- 刷新分数
    if self.athleticTalent.score then
        local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
        if 1 <= currentDay and currentDay <= 6 then
            local score = NetAthleticTalentData:GetDailyScore()
            self.athleticTalent.score.text = tostring(score)
        elseif currentDay >= 7 then
            local score = NetAthleticTalentData:GetTotalScore()
            self.athleticTalent.score.text = tostring(score)
        end
    end
    -- 刷新排名
    if self.athleticTalent.rank and refreshRank then
        if not self.isRefreshAthleticTalentRank then
            self:RefreshAthleticTalentRank()
            self.isRefreshAthleticTalentRank = true
            TimeMgr:CreateTimer("RefreshAthleticTalentRank", function()
                self.isRefreshAthleticTalentRank = false
            end, 1, 1)
        end
    end
    -- 刷新红点
    local redImg = GetChild(self.ui.m_goActivityTabGroup2, "redImg")
    SetActive(redImg, NetAthleticTalentData:IsShowRedPoint())
end

--- 刷新竞技达人排名
function UI_MainFace:RefreshAthleticTalentRank()
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    local function dayRankCallBack(data)
        -- 已上榜
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        if data.player.rank and isEnterRank then
            if self.athleticTalent then
                self.athleticTalent.rank.text = tostring(data.player.rank)
                SetActive(self.athleticTalent.rankParent, true)
            end
        -- 未上榜
        else
            if self.athleticTalent then
                self.athleticTalent.rank.text = ""
                SetActive(self.athleticTalent.rankParent, false)
            end
        end
    end
    if 1 <= currentDay and currentDay <= 6 then
        NetAthleticTalentData:RequestRankData(dayRankCallBack, currentDay, true)
    end

    local function totalRankCallBack(data)
        -- 已上榜
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        if data.player.rank and isEnterRank then
            if self.athleticTalent then
                self.athleticTalent.rank.text = tostring(data.player.rank)
                SetActive(self.athleticTalent.rankParent, true)
            end
        -- 未上榜
        else
            if self.athleticTalent then
                self.athleticTalent.rank.text = ""
                SetActive(self.athleticTalent.rankParent, false)
            end
        end
    end
    if currentDay >= 7 then
        NetAthleticTalentData:RequestRankData(totalRankCallBack, nil, true)
    end
end

function UI_MainFace:UpdateRankShow()
	local _, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Rank);
	if not active then return end
	
	if self.rankSubType ~= active.info.subtype then
		self.rankSubType = active.info.subtype;
		local bg = GetChild(self.ui.m_goActivityTabGroup4, "bg", UEUI.Image);
		local score = GetChild(self.ui.m_goActivityTabGroup4, "score", UEUI.Image);
		local title = GetChild(self.ui.m_goActivityTabGroup4, "title", UEUI.Text);
		local titleOutline = GetChild(self.ui.m_goActivityTabGroup4, "title", UEUI.Outline);
		local titleUIShadow = GetChild(self.ui.m_goActivityTabGroup4, "title", CS.Coffee.UIEffects.UIShadow);
		
		if self.rankSubType == ActivitySubtype.MergeRank then -- 合成竞赛
			SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_hechengjinsai.png", false);
			SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_hechengjinsai_2.png", false);
			title.color = Color.New(1, 253/255, 204/255, 1); -- fffdcc
			titleOutline.effectColor = Color.New(200/255, 24/255, 129/255, 1); -- c81881
			titleUIShadow.effectColor = Color.New(200/255, 24/255, 129/255, 1); -- c81881
		elseif self.rankSubType == ActivitySubtype.BuildRank then -- 建造竞赛
			SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_jianzaojinsai.png", false);
			SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_jianzaojinsai_2.png", false);
			title.color = Color.New(242/255, 1, 252/255, 1); -- f2fffc
			titleOutline.effectColor = Color.New(47/255, 90/255, 192/255, 1); -- 2f5ac0
			titleUIShadow.effectColor = Color.New(47/255, 90/255, 192/255, 1); -- 2f5ac0
		elseif self.rankSubType == ActivitySubtype.CollectRank then -- 采集竞赛
			SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_caijijinsai.png", false);
			SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_caijijinsai_2.png", false);
			title.color = Color.New(1, 1, 229/255, 1); -- ffffe5
			titleOutline.effectColor = Color.New(179/255, 76/255, 0, 1); -- b34c00
			titleUIShadow.effectColor = Color.New(179/255, 76/255, 0, 1); -- b34c00
		elseif self.rankSubType == ActivitySubtype.OrderRank then -- 订单排行榜
			SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_dingdanjinsai.png", false);
			SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_dingdanjinsai_2.png", false);
			title.color = Color.New(1, 254/255, 238/255, 1); -- fffeee
			titleOutline.effectColor = Color.New(0, 120/255, 71/255, 1); -- 007847
			titleUIShadow.effectColor = Color.New(0, 120/255, 71/255, 1); -- 007847
		end
		title.text = LangMgr:GetLang(active.form.title)
	end

	local actInfo = active.info
	local rank = actInfo.rank;
	if rank > 0 then
		local text = GetChild(self.ui.m_goActivityTabGroup4, "rank/text", UEUI.Text);
		text.text = rank;
	end
	local rankObj = GetChild(self.ui.m_goActivityTabGroup4, "rank");
	SetActive(rankObj, rank > 0);
	
	local numTxt = GetChild(self.ui.m_goActivityTabGroup4, "score/numTxt", UEUI.Text);
	numTxt.text = actInfo.integral or 0;
end

function UI_MainFace:UpdateRankRed()
	local redImg = GetChild(self.ui.m_goActivityTabGroup4, "redImg");
    SetActive(redImg, NetRankData:IsShowRedPoint(ActivityTotal.Rank) or NetUpdatePlayerData:GetPlayerRankOneRewardRed());
end

function UI_MainFace:UpdateTowerShow()
	local _,imgPath = TowerManager:GetTodayOpenTowerType()
	SetImageSprite(self.ui.m_imgSlgTower,imgPath)
end

---------------------- 轮播 ------------------------------------------------
function UI_MainFace:ActivityRankTabTimer(deltaTime)
	if self.tabDeltaTime then
		if self.tabDeltaTime < tabChangeCD then
			self.tabDeltaTime = self.tabDeltaTime + 1
		else
			self.tabDeltaTime = 0
			self.curActivityRankTabPage = self.curActivityRankTabPage + 1
			if UIMgr.uiLockActivityRankTabPage and UIMgr.uiLockActivityRankTabPage > 0 then
				self.curActivityRankTabPage = Mathf.Clamp(self.activityRankTabIndexList[UIMgr.uiLockActivityRankTabPage] - 1, 0, self.maxActivityRankTabPage)
			else
				local isStartCompetition = NetOneToOneData:GetDataByKey("isStartCompetition")
				if isStartCompetition then
					self.curActivityRankTabPage = Mathf.Clamp(self.activityRankTabIndexList[ACTIVITY_RANK_TABINDEX.One_to_one] - 1, 0, self.maxActivityRankTabPage)
				end
			end
            if self.curActivityRankTabPage > self.maxActivityRankTabPage then
				self.curActivityRankTabPage = 0
			end
			self:SwitchActivityTabPage(self.curActivityRankTabPage)
		end
	end
end

function UI_MainFace:InitActivityRankTab()
	self.uiDrag = self.ui.m_scrollviewActivityEnter:GetComponent(typeof(CS.UIDrag))
	self.curActivityRankTabPage = 0
	local startPosition = 0
	self:SwitchActivityTabPage(self.curActivityRankTabPage)
	self.uiDrag.m_BeginDrag = function ()
		if self.ActivityTabTween then
			self.ActivityTabTween:Kill()
		end
		startPosition = self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition
	end

	self.uiDrag.m_EndDrag = function ()
		local endPosition = self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition
		if endPosition > startPosition then
			self:MoveNextTab()
		else
			self:MovePreTab()
		end
	end
end

function UI_MainFace:SwitchActivityTabPage(pageIndex)
	local moveParam = pageIndex
	if self.maxActivityRankTabPage > 1 then
		moveParam = pageIndex / self.maxActivityRankTabPage--* 0.5
	end
	self.ActivityTabTween = Tween.To(function(value)
		self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition = value
		self.ui.m_scrollviewActivityEnter.velocity.x = 0
	end,self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition,moveParam,0.3)
	for i = 1, self.maxActivityRankTabPage + 1 do
		local tabNameStr = "m_imgActivityTab"..i;
		if self.ui[tabNameStr] then
			local tabImag = tabNormalImg
			local selectIndex = pageIndex + 1
			if i == selectIndex then
				tabImag = tabSelectImg
			end
			SetImageSprite(self.ui[tabNameStr],tabImag,true)
		end
	end

end

function UI_MainFace:MovePreTab()
	if self.maxActivityRankTabPage <= 0 then
		return
	end

	self.curActivityRankTabPage = self.curActivityRankTabPage - 1
	if self.curActivityRankTabPage < 0 then
		local step = 1/self.maxActivityRankTabPage
		self.curActivityRankTabPage = self.maxActivityRankTabPage
		self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition = (1+step)+self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition
	end

	if self.ActivityTabTween then
		self.ActivityTabTween:Kill()
	end
	self:SwitchActivityTabPage(self.curActivityRankTabPage)
end

function UI_MainFace:MoveNextTab()
	
	if self.maxActivityRankTabPage <= 0 then
		return
	end

	self.curActivityRankTabPage = self.curActivityRankTabPage + 1
	if self.curActivityRankTabPage > self.maxActivityRankTabPage then
		local step = 1/self.maxActivityRankTabPage
		self.curActivityRankTabPage = 0
		self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition = -(1+step)+(self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition)
	end

	if self.ActivityTabTween then
		self.ActivityTabTween:Kill()
	end
	self:SwitchActivityTabPage(self.curActivityRankTabPage)
end

function UI_MainFace:MoveTargetTab(type)
    local isStartCompetition = NetOneToOneData:GetDataByKey("isStartCompetition")
    if isStartCompetition then return end
    local index = self.activityRankTabIndexList[type] - 1
    local lastPage = self.curActivityRankTabPage
    self.curActivityRankTabPage = Mathf.Clamp(index, 0, self.maxActivityRankTabPage)
    if lastPage == self.curActivityRankTabPage then
        return
    end
    if self.ActivityTabTween then
        self.ActivityTabTween:Kill()
    end
    self:SwitchActivityTabPage(self.curActivityRankTabPage)
end
------------------------------------------------------------------------------

--右下角折叠按钮红点检测
function UI_MainFace:CheckRBFoldBtnRed()
	local isShow
	local openCount = 0
	for i = 1, self.rightBottomCount do
		local isOpen = self.rightBottomList[i].isOpen
		if isOpen then
			openCount = openCount + 1
		end
	end
	if self.rightBottomArowIsPut then
		if openCount > 2 then
			isShow = IntelligentWorkerManager:HasRedPoint()
		else
			isShow = false
		end
	else 
		--在展开时折叠按钮不显示红点
		isShow = false
	end
	SetActive(self.ui.m_goRightBottomRed,isShow)
end

--右下角折叠区域弹出动效
function UI_MainFace:ShowRBFoldPop()
	local width = 0
	if self.rightBottomArowIsPut then
		width = 167
	else
		local childCount = self.rbLayoutTrans.childCount
		local activeCount = 0
		for i = 0,childCount - 1 do
			local child = self.rbLayoutTrans:GetChild(i)
			if child.gameObject.activeInHierarchy then
				activeCount = activeCount + 1
			end
		end
		width = activeCount*94+46
	end
	local y = self.rbBgRect.sizeDelta.y
	self.rbBgRect:DOSizeDelta(Vector2.New(width,y),0.3,true):SetEase(Ease.OutBack)
end

--问卷调查
function UI_MainFace:UpdateWenjuan(sendRequest)
	if sendRequest then
		WenJuanDiaochaMgr:SendCheckRequest(function ()
			if self.ui then
				SetActive(self.ui.m_goWenjuan,WenJuanDiaochaMgr:HasList())
			end
		end)
	end
	SetActive(self.ui.m_goWenjuan,WenJuanDiaochaMgr:HasList())
end

--根据活动个数对任务列表背景适配
function UI_MainFace:AutoFitTaskList()
	local taskCount = 0
	local trans = self.ui.m_goUp.transform
	local childCount = trans.childCount
	for i = 0,childCount-1 do
		local child = trans:GetChild(i)
		if child.gameObject.activeInHierarchy then
			taskCount = taskCount + 1
		end
	end
	local count = taskCount
	local width = 0
	
	local max = 630
	if count <= 4 then
		width = 170*count
		width = width > max and max or width
	else
		width = max
	end
	self.taskRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical, width);
end

--------------------------------- 每日礼包免费礼包领取 ---------------------------------------------
function UI_MainFace:GiftDailyFreeTimer()
	local freeDayRestTime = NetGiftDaily:GetFreeDayRestTime()
	local nowTime = TimeMgr:GetServerTimestamp()
	if freeDayRestTime - nowTime < 0 then
		NetGiftDaily:ResetFreeDayState()
	end
end
------------------------------------------------------------------------------------------------

----------------------------------- 检查红钻充值弹窗 ----------------------------------------------

function UI_MainFace:CheckRedDiamondRechargePush()
	local vipSign = NetGlobalData:GetRedDiamondRechargeLoginSign()
	if vipSign == false then
		NetGlobalData:SetRedDiamondRechargeLoginSign(true)
		local playerVipData = NetGlobalData:GetVipPurchaseInfo()
		if playerVipData and playerVipData.tips and playerVipData.tips == 1 then
			NetPushViewData:PushView(PushDefine.ActCenter,8)
		end
	end
end

------------------------------------------------------------------------------------------------
---------------------------------任务---------------------------------
--根据任务内容自动适配背景
function UI_MainFace:AutoFitTaskPanel()
    --设置最小和最大背景宽度
	--文本长度小于最大背景宽度时，背景根据文本内容进行适配
	--文本长度大于最大背景宽度时，文本播放滚动动画
	local minWidth = 310
	local maxWidth = 480
	--是否显示对勾图标
	local txt = self.ui.m_txtMainTaskDesc.text
	local toolTxt = GetChild(self.ui.m_goMainTaskBg,"toolTxt",UEUI.Text)
	local maskTxt = GetChild(self.ui.m_goMainTaskBg,"taskMask/txt",UEUI.Text)
	toolTxt.text = txt
	local descRect = GetComponent(toolTxt,UE.RectTransform)
	local taskMask = GetChild(self.ui.m_goMainTaskBg,"taskMask")
	
	maskTxt.text = txt
	self.ui.m_txtMainTaskDesc.text = txt
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(descRect)
	local desc_width = descRect.rect.width
	local isFinish = self.ui.m_imgFinishTask.gameObject.activeInHierarchy
	local offset = isFinish and 45+98 or 45+70
	local sum = desc_width+offset
	
	local showTxt1 = false
	if sum <= minWidth then
		sum = minWidth
		SetUIPivot(self.ui.m_txtMainTaskDesc,0.5,0.5)
		SetUIAnchors(self.ui.m_txtMainTaskDesc,0.5,0.5,0.5,0.5)
		self.ui.m_txtMainTaskDesc.rectTransform.anchoredPosition = Vector2.New(0,0)
		showTxt1 = true
	elseif sum > minWidth and sum <= maxWidth then
		SetUIPivot(self.ui.m_txtMainTaskDesc,0,0.5)
		SetUIAnchors(self.ui.m_txtMainTaskDesc,0,0.5,0,0.5)
		self.ui.m_txtMainTaskDesc.rectTransform.anchoredPosition = Vector2.New(45,0)
		showTxt1 = true
	else
		DOKill(maskTxt.transform)
		local maskTxtRect = GetComponent(maskTxt,UE.RectTransform)
		maskTxtRect.anchoredPosition = Vector2.New(0,0)
		sum = maxWidth
		local maskWidth = 330
		local endValue = - (desc_width-maskWidth)-20
		DOLocalMoveXLoop(maskTxt.transform,endValue,5,LoopType.Restart)
		showTxt1 = false
	end
	SetActive(self.ui.m_txtMainTaskDesc,showTxt1)
	SetActive(taskMask,not showTxt1)
	
	local bgRect = GetComponent(self.ui.m_goMainTaskBg,UE.RectTransform)
	bgRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Horizontal, sum);
end

--折叠任务提示栏
function UI_MainFace:FoldTaskTip(isFold)
	local taskAnimBgRect = GetComponent(self.ui.m_goTaskAnimBg,UE.RectTransform)
	local btnObj = GetChild(self.ui.m_goTaskAnimBg,"btn")

	RemoveUIComponentEventCallback(btnObj, UEUI.Button)
	AddUIComponentEventCallback(btnObj, UEUI.Button, function()
		self:FoldTaskTip(not isFold)
	end)
	
	local width = 0
	if isFold then
		--折叠任务提示栏
		SetActive(self.ui.m_goMainTaskBg,false)
		SetActive(self.ui.m_goTaskAnimBg,true)
		width = 60
	else
		----展开任务提示栏
		local bgRect = GetComponent(self.ui.m_goMainTaskBg,UE.RectTransform)
		width = bgRect.rect.width
	end
	
	btnObj.transform.localScale = isFold and Vector3.New(1,1,1) or Vector3.New(-1,1,1)
	local y = taskAnimBgRect.sizeDelta.y
	taskAnimBgRect:DOSizeDelta(Vector2.New(width,y),0.3,true):SetEase(Ease.OutBack):OnComplete(function()
		if not isFold then
			SetActive(self.ui.m_goMainTaskBg,true)
			SetActive(self.ui.m_goTaskAnimBg,false)
		end
	end)
	NetGlobalData:SetDataByKey("taskArrowIsPut", isFold)
end
------------------------------------------------------------------------------------------------
--左右点击查看(左下角任务列表)
function UI_MainFace:ClickCheckLB(isLeft)
	local content = self.taskScrollRect.content
	local childCount = content.transform.childCount;
	if childCount <= 4 then
		return
	end

	SetUIForceRebuildLayout(content)
	local contentHeight = content.rect.height;
	local posY = content.anchoredPosition.y
	local moveValue = math.abs(posY)+167*2.5
	if isLeft then
		moveValue = (math.abs(posY) - 167*2.5) <= 0 and 0 or -(math.abs(posY) - 167*2.5)
	else
		local compareValue  = contentHeight - 630
		moveValue = moveValue>=(compareValue - 5) and compareValue or moveValue
	end
	content:DOLocalMoveY(moveValue, 0.5);
end

------------------------------------------------------------------------------------------------
--多倍采集红点
function UI_MainFace:SetMultipleRed()
	local isRed = NetMulitpleDropData:CheckMultipleRedpoint()
	SetActive(self.ui.m_goMultiplePoint,isRed)
end
------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------
--第三方活动 添加背景缓存
function UI_MainFace:CheckActCenterBigImage()
	local imgAsset = NetGlobalData:GetActCenterBigImageCache()
	local netActivityInfo = NetGlobalData:GetVipActivityInfo()
	if netActivityInfo and imgAsset == nil then
		local webUrl = netActivityInfo.background_url 
		local function loadImageCallBack(spriteAsset)
			NetGlobalData:SetActCenterBigImageCache(spriteAsset)
		end
		SetImageSpriteByUrl(nil,webUrl,false,loadImageCallBack)
	end
end

--红钻积分商城添加背景缓存
function UI_MainFace:CheckRedDiamondShopBigImage()
	local imgAsset = NetGlobalData:GetRedDiamondShopBigImageCache()
	local netActivityInfo = NetGlobalData:GetRedDiamondShopInfo()
	if netActivityInfo and imgAsset == nil then
		local webUrl = netActivityInfo.background_url 
		local function loadImageCallBack(spriteAsset)
			NetGlobalData:SetRedDiamondShopBigImageCache(spriteAsset)
		end
		SetImageSpriteByUrl(nil,webUrl,false,loadImageCallBack)
	end
end
------------------------------------------------------------------------------------------------
------------------------------------ 联盟公告推送 ------------------------------------------------

function UI_MainFace:PushLeagueAnnounceMsg(msg)
    self:FoldMessageTip(false)

    SetActive(self.ui.m_imgMessageIcon, true)
    SetActive(self.ui.m_goMessageHead, false)

	local lederName ,leagueName = LeagueManager:GetMyLeagueLeaderDetail()
	local imgPath = LeagueManager:GetUnionImageById()
	if imgPath ~= nil then
        SetImageSprite(self.ui.m_imgMessageIcon, imgPath, false)
        SetImageSprite(self.ui.m_imgLeagueTip, imgPath, false)
	end
    self.ui.m_txtMessageTitle.text = leagueName
	self.leagueAnnounceDesc.text = msg.content
	TimeMgr:CreateTimer(UIDefine.UI_MainFace, function ()
        if not self.ui then return end
        self:FoldMessageTip(true)
        SetActive(self.ui.m_goLeagueTip, true)
    end, 3, 1)
end

function UI_MainFace:CloseLeagueAnnouncePush()
	SetActive(self.ui.m_goLeagueTip, false)
end

function UI_MainFace:PushFriendMsg(msg)
    self:FoldMessageTip(false)

    SetActive(self.ui.m_imgMessageIcon, false)
    SetActive(self.ui.m_goMessageHead, true)

    self.ui.m_txtMessageTitle.text = msg.player.name
    self.leagueAnnounceDesc.text = msg.chat.content
    SetHeadAndBorderByGo(self.myHeadNode, msg.player.icon, msg.player.border)

    TimeMgr:CreateTimer(UIDefine.UI_MainFace, function ()
        if not self.ui then return end
        self:FoldMessageTip(true)
        SetActive(self.ui.m_goChatTip, true)
    end, 3, 1)
end

function UI_MainFace:CloseFriendPush()
    SetActive(self.ui.m_goChatTip, false)
end

function UI_MainFace:FoldMessageTip(isFold)
    local m_goMessageTipRect = GetComponent(self.ui.m_goMessageTip, UE.RectTransform)

    local width = 0
    if isFold then
        width = 60
    else
        width = 480
        SetUISize(self.ui.m_goMessageTip, 60, 170)
        SetActive(self.ui.m_goMessageTip, true)
    end

    SetActive(self.ui.m_imgMessageIcon, not isFold)
    SetActive(self.ui.m_goMessageHead, not isFold)
    SetActive(self.ui.m_txtMessageTitle, not isFold)
    SetActive(self.ui.m_goMessageContent, not isFold)
    SetActive(self.ui.m_goMessageTipArrow, true)

    local y = m_goMessageTipRect.sizeDelta.y
    m_goMessageTipRect:DOSizeDelta(Vector2.New(width,y),0.3,true):SetEase(Ease.OutBack):OnComplete(function()
        if isFold then
            SetActive(self.ui.m_goMessageTipArrow, false)
            SetActive(self.ui.m_goMessageTip, false)
        end
    end)
end

------------------------------------------------------------------------------------------------

-------------------------------------------关卡系统----------------------------------------------
--初始化关卡系统
function UI_MainFace:InitLevelTimer()
	local level = DungeonManager:GetLevelId()
	self.MaxHangTime = DungeonManager:GetMaxHangTime()
	self.hangUpTime1,self.hangUpTime2 = DungeonManager:GetHangConfigTime()
	--if level == 1 then
	--	self.canTimer = false
	--	self.levelTimeTxt.text = "00:00:00"
	--	SetActive(self.ui.m_goFullBoxDot,false)
	--	SetUIImage(self.ui.m_imgBoxIcon,self:GetBoxSprite(1),false)
	--else
	--	self.canTimer = true
	--	self.levelHangTime = TimeMgr:GetServerTime() - DungeonManager:GetTimestamp()
	--	self.str1 = LangMgr:GetLang(70000086)
	--	self.levelTimeTxt.text = self.levelHangTime >= self.MaxHangTime and self.str1 or TimeMgr:ConverSecondToString(self.levelHangTime)
	--end

	self:CheckIsCanLevelTimer()
    self:CheckLotteryEntry()
end

function UI_MainFace:CheckIsCanLevelTimer()
	local isCanGet = DungeonManager:GetIsCanGetFreeTicket()
	if not isCanGet then
		SetActive(self.levelTimeTxt,true)
		self.canTimer = true
		local nowTime = TimeMgr:GetServerTimestamp()
		--下一个领取时间戳
		local nextTimeStamp = DungeonManager:GetNextGetTimeStamp()
		self.levelHangTime = nextTimeStamp - nowTime
		self.levelTimeTxt.text = TimeMgr:ConverSecondToString(self.levelHangTime)
	else
		self.canTimer = false
		SetActive(self.levelTimeTxt,false)
	end
end

--关卡系统倒计时逻辑
function UI_MainFace:CheckLevelTimer()
	if self.canTimer then
		self.levelHangTime = self.levelHangTime - 1
		self.levelTimeTxt.text = TimeMgr:ConverSecondToString(self.levelHangTime)
		--self:CheckLevelEnterIcon(self.levelHangTime)
	end
end

---判断关卡系统入口状态贴图
---在0-1小时之间，显示空宝箱；
---在1-4小时之间，显示开启一半的宝箱
---在4-挂机时间上限，显示满载宝箱
function UI_MainFace:CheckLevelEnterIcon(time)
	local status = self.levelBoxStatus
	if time < self.hangUpTime1 then
		status = 1
	elseif time >= self.hangUpTime1 and time < self.hangUpTime2 then
		status = 2
	else
		status = 3
	end

	SetActive(self.ui.m_goFullBoxDot,DungeonManager:CheckLevelEnterRedDot())
	if status == self.levelBoxStatus then
		return
	end
	self.levelBoxStatus = status
	SetUIImage(self.ui.m_imgBoxIcon,self:GetBoxSprite(self.levelBoxStatus),false)
	--SetActive(self.ui.m_goFullBoxDot,time >= self.hangUpTime1)
end

--获取关卡入口宝箱贴图
function UI_MainFace:GetBoxSprite(status)
	return "Sprite/ui_mainface/mainface2_huodong_PVE_jl"..status..".png"
end

--判断抽卡入口是否解锁
function UI_MainFace:CheckLotteryEntry()
	if self.rightBottomList and self.rightBottomList[FolderItemType.Lottery] then
		local openLotteryState = LotteryManager:CheckLotteryEntryUnlock()
		--self.rightBottomList[FolderItemType.Lottery].isOpen = openLotteryState
		--SetActive(self.rightBottomList[FolderItemType.Lottery].go, openLotteryState)
		--self:FoldRightBottom()
	end
end
------------------------------------------------------------------------------------------------

function UI_MainFace:BagChange(data)
    self.ui.m_txtDiamond.text = NetUpdatePlayerData:GetResourceNumByID(ItemID.DIAMOND)
end

function UI_MainFace:OpenAIChatService()
	if not (_G.CS.StartGame.Instance and _G.CS.StartGame.Instance.GetSDKToken) then
		return
	end
	local sdkToken = _G.CS.StartGame.Instance:GetSDKToken()
	local HOST = "https://kefu-ea.q1.com"
	local path = "/aichat/index.html?"
	local SECRET = "Uz7NbStqvBx6WthB"

	local loginType = 0
	local platFormType = 1
	local mainType = 0
	local gameId = GAME_APP_ID
	local uld = _G.CS.StartGame.Instance:GetUserId()
	local actorId = v2s(NetUpdatePlayerData:GetPlayerID())
	local sign = GameUtil.MD5String(loginType..platFormType..mainType..gameId..actorId..SECRET)

	local str = string.format("gameId=%s&loginType=%s&platFormType=%s&mainType=%s&sign=%s&actorId=%s&sdkToken=%s",gameId,loginType,platFormType,mainType,sign,actorId,sdkToken)
	local url = HOST..path..str
	--CS.UnityEngine.Application.OpenURL(url)
	if CS.LuaSdkHelper.Instance.OpenH5Url then
		CS.LuaSdkHelper.Instance:OpenH5Url(url,1)
	end
end

return UI_MainFace
