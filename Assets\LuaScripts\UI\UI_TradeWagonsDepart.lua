local UI_TradeWagonsDepart = Class(BaseView)
local GoSlgHeroItem = require("UI.GoSlgHeroItem")

local QualityStr = {
    [1] = "N",
    [2] = "R",
    [3] = "SR",
    [4] = "SSR",
    [5] = "UR"
}

local DiceIconPath = "Sprite/ui_huodongjingsai_maoyihuoyun/touzi%s.png"

function UI_TradeWagonsDepart:OnInit()
    
end

function UI_TradeWagonsDepart:OnCreate(index, data, isGuide, callback)
    self.callback = callback
    self.index = index

    local transform = self.ui.m_goBg
    self.battle_shaoguang_n = GetChild(transform, "bg2/middle/left/battle_shaoguang_n")
    self.battle_shaoguang_r = GetChild(transform, "bg2/middle/left/battle_shaoguang_r")
    self.battle_shaoguang_sr = GetChild(transform, "bg2/middle/left/battle_shaoguang_sr")
    self.battle_shaoguang_ssr = GetChild(transform, "bg2/middle/left/battle_shaoguang_ssr")
    self.battle_shaoguang_ur = GetChild(transform, "bg2/middle/left/battle_shaoguang_ur")

    self.diceAnim = GetComponent(self.ui.m_goDiceAnim, typeof(UE.Animation))
    self.truckAnim = GetChild(self.ui.m_goBg, "bg2/middle", typeof(UE.Animation))

    self.heroItemList = {}
    self.heroItemParentList = {}
    self.battleTeamType = BATTLE_TEAM_TYPE.TRADE_CAR_1

    if self.index == 1 then
        self.battleTeamType = BATTLE_TEAM_TYPE.TRADE_CAR_1
    elseif self.index == 2 then
        self.battleTeamType = BATTLE_TEAM_TYPE.TRADE_CAR_2
    elseif self.index == 3 then
        self.battleTeamType = BATTLE_TEAM_TYPE.TRADE_CAR_3
    elseif self.index == 4 then
        self.battleTeamType = BATTLE_TEAM_TYPE.TRADE_CAR_4
    end

    self:RefreshInfo(data.info)
    self:RefreshReward(data.info.rewards)

    -- 防止创建两次
    if self.ui.m_transHero.childCount == 0 then
        for i = 1, 5, 1 do
            local item = CreateGameObjectWithParent(self.ui.m_goHeroItem, self.ui.m_transHero)
            local btn = GetChild(item, "bg", UEUI.Button)
            if btn then
                btn.onClick:AddListener(function ()
                    BattleSceneManager:GotoChooseTradeCarTeam(self.battleTeamType)
                end)
            end
            SetActive(item, true)
            table.insert(self.heroItemParentList, item)
        end
    end

    local team = HeroManager:GetTradeCarBattleListByType(self.battleTeamType)
    self:RefreshHero(team)

    self:RefreshResource()

    EventMgr:Add(EventID.BAG_CHANGE, self.BagChange, self)
    EventMgr:Add(EventID.BATTLE_TRADEWAGON_SAVE, self.BattleTradeWagonSave, self)

    if isGuide then
        self:Guide()
    end
end

function UI_TradeWagonsDepart:OnRefresh(type)

end

function UI_TradeWagonsDepart:onDestroy()
    EventMgr:Remove(EventID.BAG_CHANGE, self.BagChange, self)
    EventMgr:Remove(EventID.BATTLE_TRADEWAGON_SAVE, self.BattleTradeWagonSave, self)

    if self.callback then
        self.callback()
    end
end

function UI_TradeWagonsDepart:onUIEventClick(go,param)
    local name = go.name
    -- 关闭按钮
    if name == "m_btnClose" then
        self:Close()
    -- 帮助按钮
    elseif name == "m_btnTip" then
        UI_SHOW(UIDefine.UI_TradeWagonsHelp, 70000517)
    -- 刷新品质按钮
    elseif name == "m_btnDice" then
        self:RefreshQuality()
        DOKill(self.ui.m_btnDice.gameObject.transform)
        DOScale(self.ui.m_btnDice.gameObject.transform, 0.6, 0.15, function ()
            DOScale(self.ui.m_btnDice.gameObject.transform, 1.0, 0.15, function()
            end)
        end)
    -- 编辑队伍按钮
    elseif name == "m_btnEditTeam" then
        BattleSceneManager:GotoChooseTradeCarTeam(self.battleTeamType)
    -- 开始运输按钮
    elseif name == "m_btnStartShipping" then
        self:Depart()
    -- 贸易合同按钮
    elseif name == "m_goRefreshFree" then
        UI_SHOW(UIDefine.UI_SlgItemBuyTip, ItemID.TradeContract)
    end
end

function UI_TradeWagonsDepart:BagChange(data)
    self:RefreshResource()
end

function UI_TradeWagonsDepart:BattleTradeWagonSave()
    local team = HeroManager:GetTradeCarBattleListByType(self.battleTeamType)
    self:RefreshHero(team)
end

--- 刷新基本信息
--- @param data table 基本信息
function UI_TradeWagonsDepart:RefreshInfo(data)
    self:PlayDiceAnim(data.quality)

    SetUIImage(self.ui.m_imgQualityBg, TradeWagonsManager:GetQualityBgIcon(data.quality), false)
    SetUIImage(self.ui.m_imgQuality, TradeWagonsManager:GetQualityIcon(data.quality), true)
    SetUIImage(self.ui.m_imgWagon, TradeWagonsManager:GetWagonDetailIcon(data.quality), false)

    SetActive(self.battle_shaoguang_n, data.quality == 1)
    SetActive(self.battle_shaoguang_r, data.quality == 2)
    SetActive(self.battle_shaoguang_sr, data.quality == 3)
    SetActive(self.battle_shaoguang_ssr, data.quality == 4)
    SetActive(self.battle_shaoguang_ur, data.quality == 5)

    -- 今日剩余运输次数
    local departCount = TradeWagonsManager.loadInfo.today_trade_times
    local departCountMax = v2n(TradeWagonsManager:GetTradeSettingConfig(11))
    self.ui.m_txtCountToday.text = string.format("%s%s/%s", LangMgr:GetLang(70000461), departCountMax - departCount, departCountMax)

    self.ui.m_txtWagonName.text = LangMgr:GetLangFormat(70000642, QualityStr[data.quality])

    local config = TradeWagonsManager:GetTradeConfigByTypeAndQuality(1, data.quality)
    if config then
        local time = config.time
        self.ui.m_txtTime.text = LangMgr:GetLang(70000449) .. TimeMgr:ConverSecondToString(time)
    end
end

--- 刷新奖励信息
--- @param rewards table 奖励信息
function UI_TradeWagonsDepart:RefreshReward(rewards)
    local rewardItem = self.ui.m_goRewardItem
    local content = self.ui.m_transReward
    local rewardItemCount = content.transform.childCount
    -- 先全部隐藏
    for i = 1, rewardItemCount, 1 do
        local item = content.transform:GetChild(i - 1)
        SetActive(item, false)
    end
    -- 显示奖励
    for key, value in ipairs(rewards) do
        local itemID = value.reward.code
        local itemNum = value.reward.amount

        local item
        -- 有可用的 item 直接获取
        if key <= rewardItemCount then
            item = content.transform:GetChild(key - 1)
        -- item 不够用，创建新的
        else
            item = CreateGameObjectWithParent(rewardItem, content)
        end

        -- 底框
        local border = GetComponent(item, UEUI.Image)
        local borderIconPath = TradeWagonsManager:GetRewardQualityIcon(ItemConfig:GetSlgQuality(itemID))
        SetUIImage(border, borderIconPath, false)
        -- 图标
        local icon = GetChild(item, "icon", UEUI.Image)
        local iconPath = ItemConfig:GetIcon(itemID)
        SetUIImage(icon, iconPath, false)
        -- 数量
        local textNum = GetChild(item, "num", UEUI.Text)
        textNum.text = "x" .. NumToGameString(itemNum)
        -- 按钮
        local button = GetChild(item, "icon", UEUI.Button)
        button.onClick:RemoveAllListeners()
        button.onClick:AddListener(function ()
            UI_SHOW(UIDefine.UI_ItemTips, itemID)
        end)

        SetActive(item, true)
    end
end

--- 刷新英雄信息
--- @param data table 英雄信息
function UI_TradeWagonsDepart:RefreshHero(data)
    for i = 1, #self.heroItemList, 1 do
        local item = self.heroItemList[i]
        SetActive(item.go, false)
    end

    local filter = {}
    for _, value in ipairs(data) do
        if IsTableNotEmpty(value) then
            table.insert(filter, value)
        end
    end

    for index, value in ipairs(filter) do
        local hero = HeroManager:GetHeroVoById(value.code)
        local heroGo
        if index <= #self.heroItemList then
            heroGo = self.heroItemList[index]
            heroGo:ChangHero(hero)
            SetActive(heroGo.go, true)
            SetActive(heroGo.EmptyRoot, false)
        else
            local item = self.heroItemParentList[index]
            heroGo = GoSlgHeroItem:Create(item, hero)
            heroGo:SetIsNeedShowSelect(false)
            heroGo:SetItem()
            heroGo:SetScale(0.72,0.72)
            table.insert(self.heroItemList, heroGo)
            SetUIPos(heroGo.go, 0, -2)
            SetActive(heroGo.EmptyRoot, false)
        end
    end

    local totalPower = HeroManager:GetBattleListTotalPower(self.battleTeamType, data)
    self.ui.m_txtFight.text = totalPower
end

--- 刷新货车品质
function UI_TradeWagonsDepart:RefreshQuality()
    local refreshTimes = TradeWagonsManager.wagonsRefreshMe[self.index]
    if refreshTimes and refreshTimes > 0 then
        local isEnough, itemID, needNum = self:CheckRefreshEnough()
        if isEnough then
            TradeWagonsManager:RequestRefreshWagonQuality(self.index, function (data)
                self:RefreshInfo(data)
                self:RefreshReward(data.rewards)
                self:RefreshResource()
            end)
        else
            local notEnoughList = {}
            table.insert(notEnoughList, {
                id = itemID,
                needNum = needNum
            })
            UI_SHOW(UIDefine.UI_SlgGetWay, notEnoughList)
        end
    else
        TradeWagonsManager:RequestRefreshWagonQuality(self.index, function (data)
            self:RefreshInfo(data)
            self:RefreshReward(data.rewards)
            self:RefreshResource()
        end)
    end
end

-- 刷新贸易合同资源
function UI_TradeWagonsDepart:RefreshResource()
    local refreshTimes = TradeWagonsManager.wagonsRefreshMe[self.index]
    if refreshTimes and refreshTimes > 0 then
        local curNum
        local needNum = 1
        local config = TradeWagonsManager:GetTradeSettingConfig(13)
        if config then
            local costTable = string.split(config, "|")
            local itemID = v2n(costTable[1])
            needNum = v2n(costTable[2])
            curNum = BagManager:GetBagItemCount(itemID)
        end
        self.ui.m_txtRefreshResourceCount.text = curNum .. "/" .. needNum
        self.ui.m_txtRefreshResourceCount.color = Color.HexToRGB("ffffff")
    else
        self.ui.m_txtRefreshResourceCount.text = LangMgr:GetLang(70000649)
        self.ui.m_txtRefreshResourceCount.color = Color.HexToRGB("6fff6c")
    end
end

--- 检查刷新道具是否足够
function UI_TradeWagonsDepart:CheckRefreshEnough()
    local curNum
    local needNum = 1
    local itemID = 1000005
    local config = TradeWagonsManager:GetTradeSettingConfig(13)
    if config then
        local costTable = string.split(config, "|")
        itemID = v2n(costTable[1])
        needNum = v2n(costTable[2])
        curNum = BagManager:GetBagItemCount(itemID)
        if curNum >= needNum then
            return true
        else
            return false, itemID, needNum
        end
    end
    return false, itemID, needNum
end

--- 开始运输
function UI_TradeWagonsDepart:Depart()
    TradeWagonsManager:RequestWagonDepart(self.index, function (data)
        UI_UPDATE(UIDefine.UI_TradeWagonsWindow, 1)
        UI_UPDATE(UIDefine.UI_TradeWagonsView, 1)
        UI_UPDATE(UIDefine.UI_ActivityRankCenter, 2)
        RedPointMgr:Dirty(RedID.TradeWagons)
        self:Close()
    end)
end

-- 打开新手引导
function UI_TradeWagonsDepart:Guide()

    -- 第一步引导回调，引导快速购买贸易合同
    local function GuideCallback()
        -- 刷新品质
        self:RefreshQuality()

        -- 下一步引导
        local centerPos = UIRectPosFit(self.ui.m_goRefreshFree)
        local rt = GetComponent(self.ui.m_goRefreshFree, typeof(UE.RectTransform))
        local width = rt.rect.width
        local height = rt.rect.height
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCenter, centerPos)
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetShow,{2, 0, 90})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetBtnSize,{0.5, 0.5})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetDialog,{-0.5, 0, 70000683})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetDialogText, LangMgr:GetLangFormat(70000684, LangMgr:GetLang(70000520)))
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetArrow,{centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCallBack,function ()
            UI_CLOSE(UIDefine.UI_GuideMask)
            UI_SHOW(UIDefine.UI_SlgItemBuyTip, ItemID.TradeContract)
        end)
    end

    -- 第一步引导刷新按钮
    local centerPos = UIRectPosFit(self.ui.m_btnDice)
    UI_SHOW(UIDefine.UI_GuideMask, {
        {2, 0, 90},                -- 遮罩类型和大小
        centerPos,                 -- 遮罩位置
        {0.5, 0.5},                    -- 遮罩按钮大小
        0.5,                       -- 缩放动画的时长
        function() GuideCallback() end,   -- 点击回调
        {centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180},   -- 箭头位置
        {-0.5, 0, 70000683},                    -- 对话框位置和内容
        "Sprite/new_hero_0/headFrame_1.png",   -- 对话框头像
        nil,
    })
end

function UI_TradeWagonsDepart:PlayDiceAnim(quality)
    if self.diceAnim.isPlaying then
        self.diceAnim:Stop()
    end
    PlayAnimStatus(self.diceAnim, "UITradeTruckDepartDice", function ()
        self:RefreshDice(quality)
    end)
    PlayAnimStatus(self.truckAnim, "UITradeTruckDepartForm", function ()
        self:RefreshDice(quality)
    end)
    SetActive(self.ui.m_imgDice, false)
end

function UI_TradeWagonsDepart:RefreshDice(quality)
    SetActive(self.ui.m_imgDice, true)
    local diceIndex = quality < 5 and 1 or 5
    SetUIImage(self.ui.m_imgDice, string.format(DiceIconPath, diceIndex))
end

return UI_TradeWagonsDepart