local GoLimit0 = {}
local M = GoLimit0

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit0.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    --self.state2 = GetChild(self.go,"doLimit/bg/state2")
    self.act = GetChild(self.go,"doLimit")
    self.bg = GetChild(self.go,"doLimit/bg",UEUI.Image)
    self.bgRect = GetComponent(self.bg.gameObject, UE.RectTransform)
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.doAni =  GetChild(self.go,"doLimit",TweenAnim)

    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)

end

function M:PlayAni(id)
    if id == self.id then
        self.doAni:DORestart()
    end
end

function M:SetItem(param)
    self.id = param.id
    self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
    local active = self.activeInfo.form.activeMess
    self.totalType = param.totalType
    self.condition = param.condition
    --self.needLevel.text = active.level
    SetImageSprite(self.icon,active.icon,false)
end

function M:ChangState(id)
    --刷新时要判断id
    if id and id ~= self.id then return end
end

function M:ChangeValue()

end

function M:ChangeItem()
end

function M:ClickItem(arg1)
    if self.condition == 1 or self.condition == 4 then
		if self.totalType == ActivityTotal.EasterEgg then
			UI_SHOW(UIDefine.UI_EasterEgg,self.id)
		end
    elseif self.condition == 2 then
    elseif self.condition == 3 then
    end
end

function M:Close()

    UEGO.Destroy(self.go)
end

return M