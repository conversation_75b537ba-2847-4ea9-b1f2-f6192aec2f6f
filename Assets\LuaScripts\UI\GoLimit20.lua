local GoLimit20 = {}
local M = GoLimit20

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit20.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.count = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
    self.red = GetChild(self.go,"doLimit/bg/goPoint")
    self.progress = GetChild(self.go,"doLimit/bg/Limit/Slider",UEUI.Slider)
    self.text_progress = GetChild(self.go,"doLimit/bg/Limit/text_progress",UEUI.Text)

    self:SetProgress()
    
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)

    self:SetRedShow()
    
    EventMgr:Add(EventID.HALEOWEEN_ORE_REFRESHENEGRY,self.SetProgress,self)
    
end

function M:SetProgress()
    local isFirstInit = NetHalloweenOreData:GetDataByKey("isFirstInit")
    local costNum = NetHalloweenOreData:GetCostEnergy()
    local needEnergy = NetHalloweenOreData:GetDataByKey("needEnergy")
    local dropTimes = NetHalloweenOreData:GetDataByKey("dropTimes")
    local curStage = NetHalloweenOreData:GetDataByKey("CurStage")

    if isFirstInit then
        local need_costNum = HalloweenOreManager:GetNeedEnergyByTimes(1)
        costNum = costNum + need_costNum
        needEnergy = HalloweenOreManager:GetNeedEnergyByTimes(1)
        --NetPushViewData:PushView(PushDefine.UI_HalloweenActivity)
    end
    local costStr = costNum .. "/" .. needEnergy
    if (costNum / needEnergy) > 1 then
        local dropItem = HalloweenOreManager:GetObjHalloweenOre()
        if nil ~= dropItem then
            costStr = LangMgr:GetLang(5003)
        end
    end

    --self.progress.fillAmount = costNum / needEnergy
    self.progress.value = costNum / needEnergy
	if curStage >= HalloweenOreManager.MaxCurState then
		costStr = LangMgr:GetLang(5003)
		self.progress.value = 1
	end
	
	self.text_progress.text = costStr
end

function M:SetItem(param)
    self.id = param.id
    NetHalloweenOreData:SetDataByKey("activityId",self.id)
    self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
    local active = self.activeInfo.form.activeMess
    self.totalType = param.totalType
    self.condition = param.condition
    SetImageSprite(self.icon,active.icon,false)
    
end

function M:ChangState(id)

end

---通过 Refresh的方法调用
function M:ChangeValue()

end

---来自timer 的调用
function M:ChangeItem()
    local time = self.activeInfo:GetRemainingTime()
    self.count.text = TimeMgr:CheckHMSNotEmpty(time)
end

function M:SetRedShow()
    local IsShow = NetHalloweenOreData:HasActiveReward() or HalloweenOreManager:HasNewItemInCollection()
    SetActive(self.red,IsShow)
end

function M:ClickItem(arg1)
    UI_SHOW(UIDefine.UI_HalloweenActivity,NetHalloweenOreData:GetActivityScore(),nil,nil,nil)
end

function M:Close()
    UEGO.Destroy(self.go)
    EventMgr:Remove(EventID.HALEOWEEN_ORE_REFRESHENEGRY,self.SetProgress,self)
    EventMgr:Remove(EventID.MAP_ITEM_NEW, self.HandleItemNew, self)
end

return M