local GoTradeTrain = {}
local M = GoTradeTrain
local timerID = "GoTradeTrain"
local prePath = "Assets/ResPackage/Prefab/UI/TradeTrain.prefab"
local preGoldPath = "Assets/ResPackage/Prefab/UI/TradeTrainGold.prefab"

function M:Create(parent, data)
    local item = {}
    setmetatable(item, { __index = M })
    item.data = data
    item.loadFinish = false
    item.loadCallbacks = {}  -- 存储等待加载完成的回调函数

    local isLoadFinish = false
    local isGoldLoadFinish = false
    local isInit = false

    local function checkAndInit()
        if isLoadFinish and isGoldLoadFinish and (not isInit) then
            isInit = true
            item.loadFinish = true
            item:Init()
            -- 执行所有等待的回调
            for _, callback in ipairs(item.loadCallbacks) do
                if callback then
                    callback(item)
                end
            end
            item.loadCallbacks = {}  -- 清空回调列表
        end
    end

    ResMgr:LoadAssetWithCache(prePath, AssetDefine.LoadType.Instant, function (obj)
        local newGo = CreateGameObjectWithParent(obj, parent)
        item.go = newGo
        item.trans = newGo.transform
        SetActive(newGo, true)
        isLoadFinish = true
        checkAndInit()
    end)

    ResMgr:LoadAssetWithCache(preGoldPath, AssetDefine.LoadType.Instant, function (obj)
        local newGoGold = CreateGameObjectWithParent(obj, parent)
        item.goGold = newGoGold
        item.transGold = newGoGold.transform
        SetActive(newGoGold, false)
        isGoldLoadFinish = true
        checkAndInit()
    end)
 
    return item
end

--- 等待加载完成
--- @param callback function 加载完成后的回调函数，参数为 GoTradeTrain 实例
function M:WaitForLoad(callback)
    if self.loadFinish then
        -- 已经加载完成，直接执行回调
        if callback then
            callback(self)
        end
    else
        -- 还未加载完成，添加到回调列表
        if callback then
            table.insert(self.loadCallbacks, callback)
        end
    end
end

--- 检查是否加载完成
--- @return boolean 是否加载完成
function M:IsLoadFinished()
    return self.loadFinish or false
end

--- 安全执行方法（只有在加载完成后才执行）
--- @param methodName string 方法名
--- @param ... any 方法参数
function M:SafeCall(methodName, ...)
    if self.loadFinish and self[methodName] then
        return self[methodName](self, ...)
    end
    return nil
end

--- 初始化
function M:Init()
    local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
---------------------------------------- 普通火车 ----------------------------------------
    -- 车厢
    self.trainBodyIcon = {}
    for i = 1, 4, 1 do
        local img = GetChild(self.trans, string.format("body%s/icon", i), UEUI.Image)
        table.insert(self.trainBodyIcon, img)
    end
    -- 加号按钮
    self.trainAddBtn = {}
    self.btnAddHead = GetChild(self.trans, "head/add", UEUI.Button)
    for i = 1, 4, 1 do
        local btn = GetChild(self.trans, string.format("body%s/add", i), UEUI.Button)
        table.insert(self.trainAddBtn, btn)
    end
    -- 昵称框
    self.border = GetChild(self.trans, "border")
    self.txtName = GetChild(self.trans, "border/name", UEUI.Text)
    self.txtName.text = myPlayerInfo.name
    -- 头像
    local playerHead = GetChild(self.trans, "border/head")
    CreateCommonHeadAsync(playerHead, 0.49, nil, function (go, trans)
        self.headNode = go
        SetMyHeadAndBorderByGo(self.headNode)
    end)
    -- 车头按钮
    self.btnHead = GetChild(self.trans, "btnHead", UEUI.Button)
    -- 火焰特效
    self.effectPlunder = {}

---------------------------------------- 金色火车 ----------------------------------------
    -- 加号按钮
    self.trainAddBtnGold = {}
    self.btnAddHeadGold = GetChild(self.transGold, "head/add", UEUI.Button)
    for i = 1, 4, 1 do
        local btn = GetChild(self.transGold, string.format("body%s/add", i), UEUI.Button)
        table.insert(self.trainAddBtnGold, btn)
    end
    -- 昵称框
    self.borderGold = GetChild(self.transGold, "border")
    self.txtNameGold = GetChild(self.transGold, "border/name", UEUI.Text)
    self.txtNameGold.text = myPlayerInfo.name
    -- 头像
    local playerHeadGold = GetChild(self.transGold, "border/head")
    CreateCommonHeadAsync(playerHeadGold, 0.49, nil, function (go, trans)
        self.headNodeGold = go
        SetMyHeadAndBorderByGo(self.headNodeGold)
    end)
    -- 车头按钮
    self.btnHeadGold = GetChild(self.transGold, "btnHead", UEUI.Button)
    -- 火焰特效
    self.effectPlunderGold = {}
end

--- 改变数据
--- @param data table 火车数据
function M:ChangeData(data)
    self.data = data
    self:SetItem()
end

--- 设置火车
function M:SetItem()

end

--- 切换成普通火车
function M:ChangeNormal()
    SetActive(self.go, true)
    SetActive(self.goGold, false)
end

--- 切换成金色火车
function M:ChangeGold()
    SetActive(self.go, false)
    SetActive(self.goGold, true)
end

--- 检查火车是否为全金色
--- @param trainData table 火车数据
function M:CheckGold(trainData)
    if not trainData then return false end
    local goldCount = 0
    for _, value in ipairs(trainData.train_quality) do
        if value.quality == SLG_QUALITY.UR then
            goldCount = goldCount + 1
        end
    end
    -- 切换成金色火车
    if goldCount >= 5 then
        self:ChangeGold()
        return true
    -- 切换成普通火车
    else
        self:ChangeNormal()
        return false
    end
end

--- 设置车厢图片
--- @param index number 车厢索引
--- @param quality number 品质
function M:SetBodyIcon(index, quality)
    if not self.trainBodyIcon[index] then return end
    SetUIImage(self.trainBodyIcon[index], TradeWagonsManager:GetTrainCarriageIcon(quality), false)
end

--- 根据品质列表设置车厢图片
--- @param data table 品质列表
function M:SetBodyIconByQualityList(data)
    for index, value in ipairs(data) do
        self:SetBodyIcon(index, value)
    end
end

--- 设置动画自动播放
--- @param flag boolean 是否自动播放
function M:SetAnimAuto(flag)
    local anim = GetComponent(self.go, UE.Animation)
    anim.playAutomatically = flag

    local animGold = GetComponent(self.goGold, UE.Animation)
    animGold.playAutomatically = flag

    anim:Play()
    animGold:Play()
end

--- 设置列车长信息
--- @param data table 列车长信息
function M:SetConductorInfo(data)
    self.txtName.text = data.name
    self.txtNameGold.text = data.name
    if self.headNode then
        SetHeadAndBorderByGo(self.headNode, data.icon, data.border)
    end
    if self.headNodeGold then
        SetHeadAndBorderByGo(self.headNodeGold, data.icon, data.border)
    end
end

--- 显示加号按钮
--- @param state boolean 状态
function M:ShowBtnAdd(state)
    for _, value in ipairs(self.trainAddBtn) do
        SetActive(value, state)
    end
    for _, value in ipairs(self.trainAddBtnGold) do
        SetActive(value, state)
    end
end

--- 设置加号按钮的点击事件
--- @param callback function 按钮回调
function M:SetBtnAddClickEvent(callback)
    for _, value in ipairs(self.trainAddBtn) do
        value.onClick:RemoveAllListeners()
        value.onClick:AddListener(function ()
            if callback then callback() end
        end)
    end
    for _, value in ipairs(self.trainAddBtnGold) do
        value.onClick:RemoveAllListeners()
        value.onClick:AddListener(function ()
            if callback then callback() end
        end)
    end
end

--- 显示头像框
--- @param state boolean 状态
function M:ShowBorder(state)
    SetActive(self.border, state)
    SetActive(self.borderGold, state)
end

--- 设置车头按钮点击事件
--- @param callback function 回调
function M:SetHeadButton(callback)
    self.btnHead.onClick:RemoveAllListeners()
    self.btnHead.onClick:AddListener(function ()
        if callback then callback() end
    end)

    self.btnHeadGold.onClick:RemoveAllListeners()
    self.btnHeadGold.onClick:AddListener(function ()
        if callback then callback() end
    end)
end

--- 设置被掠夺火焰特效
--- @param go any 火焰特效
function M:SetEffectPlunder(go)
    for i = 1, 3, 1 do
        local parent = GetChild(self.trans, "effectPlunder" .. i)
        local effect = CreateGameObjectWithParent(go, parent)
        effect.transform.localPosition = Vector3.zero
        table.insert(self.effectPlunder, effect)

        local parentGold = GetChild(self.transGold, "effectPlunder" .. i)
        local effectGold = CreateGameObjectWithParent(go, parentGold)
        effectGold.transform.localPosition = Vector3.zero
        table.insert(self.effectPlunderGold, effectGold)
    end
end

--- 显示被掠夺火焰特效
--- @param trainData table 火车数据
function M:ShowEffectPlunder(trainData)
    local count = trainData.train_attacked_times

    local goldCount = 0
    for _, value in ipairs(trainData.train_quality) do
        if value.quality == SLG_QUALITY.UR then
            goldCount = goldCount + 1
        end
    end

    if goldCount >= 5 then
        for _, value in ipairs(self.effectPlunderGold) do
            SetActive(value, false)
        end

        for i = 1, count, 1 do
            SetActive(self.effectPlunderGold[i], true)
        end
    else
        for _, value in ipairs(self.effectPlunder) do
            SetActive(value, false)
        end

        for i = 1, count, 1 do
            SetActive(self.effectPlunder[i], true)
        end
    end
end

return M