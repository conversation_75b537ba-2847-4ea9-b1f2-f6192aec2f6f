local UI_ActivityCommonBox = Class(BaseView)

function UI_ActivityCommonBox:OnInit()
    self.activityItem = nil
end

function UI_ActivityCommonBox:OnCreate(activityId, push)

    if not activityId then
        Log.Warning("UI_ActivityCommonBox activityId = nil")
        return
    end
	
	self.isPush = push
	self.activityItem = LimitActivityController:GetActiveMessage(activityId)
	local formTable =  self.activityItem:GetForm()

	if formTable.activeMess.total_type == ActivityTotal.DiamondBuff then
		SetImageSprite(self.ui.m_imgIcon, formTable.activeMess.foreshow_icon, true)
		--self.ui.m_imgIcon.transform.localPosition = Vector3.New(0, 0, 0)
		
		self.ui.m_txtTitle.text = LangMgr:GetLang(formTable.title)
		
		self.ui.m_txtContent_1.text = LangMgr:GetLang(8237)
		--self.ui.m_txtContent_1.fontSize = 32
		--self.ui.m_txtContent_1.lineSpacing = 1
		--self.ui.m_txtContent_1.transform.localPosition = Vector3.New(0, 277, 0)
		--self.ui.m_txtContent_1.rectTransform.sizeDelta = Vector2.New(976, 140)
		local outline = GetComponent(self.ui.m_txtContent_1, UEUI.Outline)
		outline.effectColor = Color.New(211/255, 40/255, 0/255, 1)

		self.ui.m_txtContent_red.text = LangMgr:GetLang(8238)
		SetActive(self.ui.m_txtContent_red.gameObject, true)
		SetActive(self.ui.m_btnGo, true)
		
		
		RemoveUIComponentEventCallback(self.ui.m_btnGo, UEUI.Button)
		AddUIComponentEventCallback(self.ui.m_btnGo, UEUI.Button,
			function (go,param)
				UI_SHOW(UIDefine.UI_ActCenter,13)--UI_SHOW(UIDefine.UI_Shop, {isDiamondBuffGo = true})
				self:Close()
			end,
		{})
		
	else
		SetImageSprite(self.ui.m_imgIcon, formTable.img, true)
		
		-- 富文本格式  "Today, you can build a house\n<size=60>20% FASTER Than usual</size>"
		local text = LangMgr:GetLang(formTable.lang_id_1)
		self.ui.m_txtContent_1.text = string.gsub(text,"\\n","\n")
		self.ui.m_txtContent_2.text = LangMgr:GetLang(formTable.lang_id_2)
		self.ui.m_txtTitle.text = LangMgr:GetLang(formTable.lang_id_3)
	end

	--self:SetSpine(formTable.img_role)
	self:UpdateCutDownTime()
	self:SetIsUpdateTick(true)
	EffectConfig:CreateEffect(24, 0, 0, 0, self.uiRectTransform)

end

function UI_ActivityCommonBox:UpdateCutDownTime()
    local endTime = self.activityItem:GetRemainingTime()
    if endTime > 0  then
        self.ui.m_txtTime.text = TimeMgr:CutDownTimeSpace(endTime,3)
    else
        self.ui.m_txtTime.text = LangMgr:GetLang(7077) --"活动已结束"
    end
end

function UI_ActivityCommonBox:TickUI(delta)
    self:UpdateCutDownTime()
end

function UI_ActivityCommonBox:SetSpine(spineName)
    local item = RoleSpineConfig:GetSkeletonDataById(spineName)
    if item then
        local site = string.split(item.mess.task_site,"|")
		--self.ui.m_spuiAnim.transform.localPosition = Vector2.New(site[1],site[2])
        --self.ui.m_spuiAnim.skeletonDataAsset = item.spine
        --self.ui.m_spuiAnim:Initialize(true)
		local function funcW()
			self.ui.m_spuiAnim.transform.localPosition = Vector2.New(site[1],site[2])
		end
		RoleSpineConfig:SetSpineByName(self.ui.m_spuiAnim,spineName,funcW)
    end
	
    --SetSpineAnim(self.ui.m_spuiAnim, "Casual", -1)
end

function UI_ActivityCommonBox:ClosePush()
    if self.isPush then
        self.isPush = false
        NetPushViewData:RemoveViewByIndex(PushDefine.ActivityCommonBox,1)
        NetPushViewData:CheckOtherView(true)
    end
end

function UI_ActivityCommonBox:onDestroy()
    self:ClosePush()
    self.activityItem = nil
end

function UI_ActivityCommonBox:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnClose1" or name == "m_btnClose2" then
        self:Close()
    end
end

return UI_ActivityCommonBox