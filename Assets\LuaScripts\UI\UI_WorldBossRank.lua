local UI_WorldBossRank = Class(BaseView)

local SlideRect = require("UI.Common.SlideRect");
local BaseSlideItem = require("UI.Common.BaseSlideItem");
local RankItem = Class(BaseSlideItem);

local BagItem = require("UI.BagItem")

function UI_WorldBossRank:OnInit()
    self.selectType = 0;
    self.rangeType = 1;
    self.rank = 0;
    self.btnItemList = {};
    self.rewardItemList = {};
    self.rewardSubItemList = {};
end

function UI_WorldBossRank:OnCreate(param)
    self.headObj = CreateCommonHead(self.ui.m_goMyRank, 0.5, Vector2(-280, 2));

    local itemList = {};
    local itemTrans = self.ui.m_goRankItem.transform;
    for i = 1, 8 do
        local item = RankItem.new();
        item:Init(CreateGameObject(itemTrans));
        table.insert(itemList, item);
    end

    self.slideRect = SlideRect.new();
    self.slideRect:Init(self.ui.m_scrollviewRank, 2);
    self.slideRect:SetItems(itemList, 15, Vector2.New(0, 2));

    local list = { { nameStr = LangMgr:GetLang(70000373), iconPath = "Sprite/ui_paihangbang/paihangbang_shijieboss_icon2.png" },
                   { nameStr = LangMgr:GetLang(70000328), iconPath = "Sprite/ui_paihangbang/paihangbang_shijieboss_icon1.png" } };
    local count = #list;
    local num = #self.btnItemList;
    local len = count > num and count or num;
    local rootTrans = self.ui.m_scrollview.content;
    for i = 1, len do
        local item = self.btnItemList[i];
        if item == nil then
            item = CreateGameObjectWithParent(self.ui.m_goBtnItem, rootTrans);
            table.insert(self.btnItemList, item);
        end
        SetActive(item, i <= count);

        if i <= count then
            local bg = GetChild(item, "bg", UEUI.Image);
            local nameTxt = GetChild(item, "bg/nameTxt", UEUI.Text);
            local selectBg = GetChild(item, "selectBg", UEUI.Image);
            local selectTxt = GetChild(item, "selectBg/selectTxt", UEUI.Text);
            local icon = GetChild(item, "icon", UEUI.Image);

            local data = list[i];
            nameTxt.text = data.nameStr;
            selectTxt.text = data.nameStr;
            SetUIImage(icon, data.iconPath, false);
            SetActive(bg, true);
            SetActive(selectBg, false);

            RemoveUIComponentEventCallback(item, UEUI.Button);
            AddUIComponentEventCallback(item, UEUI.Button, function()
                if self:OnSelectBtn(i) then
                    self:OnUpdateInfo();
                end
            end);
        end
    end

    self:OnSelectBtn(1);
    self:OnUpdateInfo();
end

function UI_WorldBossRank:OnRefresh(param)

end

function UI_WorldBossRank:onDestroy()

end

function UI_WorldBossRank:onUIEventClick(go, param)
    local name = go.name
    if name == "closeBtn" then
        self:Close();
    elseif name == "m_goTipWin" then
        SetActive(self.ui.m_goTipWin, false);
    elseif string.startswith(name, "m_tog") then
        if not self.ui[name].isOn then return end
        
        local index = string.gsub(name, "m_tog", "");
        index = v2n(index);
        if index then
            if self:OnSelectType(index) then
                self:OnUpdateInfo();
            end
        end
    end
end

function UI_WorldBossRank:OnSelectBtn(index)
    if self.selectType == index then
        return false;
    end

    if self.selectType > 0 then
        local item = self.btnItemList[self.selectType];
        local bg = GetChild(item, "bg", UEUI.Image);
        local selectBg = GetChild(item, "selectBg", UEUI.Image);
        SetActive(bg, true);
        SetActive(selectBg, false);
    end

    self.selectType = index;

    local item = self.btnItemList[index];
    local bg = GetChild(item, "bg", UEUI.Image);
    local selectBg = GetChild(item, "selectBg", UEUI.Image);
    SetActive(bg, false);
    SetActive(selectBg, true);

    if index == 1 then
        SetUISize(self.ui.m_rtransBg, 974, 972);
    else
        SetUISize(self.ui.m_rtransBg, 974, 1100);
    end
    
    return true;
end

function UI_WorldBossRank:OnSelectType(index)
    if self.rangeType == index then
        return false;
    end
    self.rangeType = index;
    return true;
end

function UI_WorldBossRank:OnUpdateInfo()
    local isRank = self.selectType == 1;
    if isRank then
        WorldBossManager:OnRequestBossRank(self.rangeType, function(respData)
            local rankInfo = respData.ranks;
            local selfRankInfo = rankInfo.self;
            if selfRankInfo then
                local rootObj = self.ui.m_goMyRank;
                local rankTxt = GetChild(rootObj, "rankTxt", UEUI.Text);
                local rankImg = GetChild(rootObj, "rankImg", UEUI.Image);
                local nameTxt = GetChild(rootObj, "nameTxt", UEUI.Text);
                local leagueIcon = GetChild(rootObj, "leagueIcon", UEUI.Image);
                local leagueNameTxt = GetChild(rootObj, "leagueNameTxt", UEUI.Text);
                local hurtTxt = GetChild(rootObj, "hurtBg/hurtTxt", UEUI.Text);
                local lookBtn = GetChild(rootObj, "lookBtn");

                local rank = selfRankInfo.rank;
                if rank > 0 then
                    rankTxt.text = rank;
                else
                    rankTxt.text = LangMgr:GetLang(9056);
                end
                if self.rangeType == 1 then
                    self.rank = rank;
                end

                local isShow = rank > 0 and rank <= 3;
                if isShow then
                    SetUIImage(rankImg, "Sprite/ui_huodongjingsai/paihang_win4_icon" .. rank .. ".png", false);
                end
                SetActive(rankImg, isShow);
                
                local hurtNum = selfRankInfo.value;
                hurtTxt.text = NumToGameString(hurtNum);

                local playerInfo = selfRankInfo.player;
                nameTxt.text = playerInfo.name;
                SetHeadAndBorderByGo(self.headObj, playerInfo.icon, playerInfo.border);

                local leagueInfo = selfRankInfo.league;
                if leagueInfo then
                    leagueNameTxt.text = leagueInfo.name;
                    SetUIImage(leagueIcon, LeagueManager:GetUnionImageById(leagueInfo.icon), false);
                else
                    leagueNameTxt.text = "";
                end
                SetActive(leagueIcon, leagueInfo ~= nil);

                RemoveUIComponentEventCallback(lookBtn, UEUI.Button);
                AddUIComponentEventCallback(lookBtn, UEUI.Button, function()
                    WorldBossManager:OnRequestTeamQuery(playerInfo.id, function(respData)
                        if lookBtn then
                            if hurtNum > 0 then
                                local pos = UIRectPosFit(lookBtn);
                                UI_SHOW(UIDefine.UI_WorldBossHeroTeamTip, respData.team, pos);
                            else
                                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70001065));
                            end
                        end
                    end)
                end);
            end
            SetActive(self.ui.m_goMyRank, selfRankInfo ~= nil);
            
            local list = rankInfo.list or {};
            self.slideRect:SetData(list);
            SetActive(self.ui.m_goTip, #list <= 0);
        end)
    else
        local bossType = WorldBossManager:GetBossType();
        local list = ConfigMgr:GetDataByKey(ConfigDefine.ID.slg_world_boss_rank, "boss_type", bossType, true);
        table.sort(list, function(a, b) 
            return a.id < b.id;
        end)
        local count = #list;
        local num = #self.rewardItemList;
        local len = count > num and count or num;
        local rootTrans = self.ui.m_scrollviewReward.content;
        local isChoose = false;
        for i = 1, len do
            local item = self.rewardItemList[i];
            if not item then
                item = CreateGameObjectWithParent(self.ui.m_goRewardItem, rootTrans);
                table.insert(self.rewardItemList, item);
            end
            SetActive(item, i <= count);

            if i <= count then
                local bg = GetChild(item, "bg");
                local rankTxt = GetChild(item, "rankTxt", UEUI.Text);
                local rankImg = GetChild(item, "rankImg");
                local noneTxt = GetChild(item, "m_txtAuto53241129", UEUI.Text);
                local rewardObj = GetChild(item, "rewardObj");

                local data = list[i];
                rankTxt.text = LangMgr:GetLang(data.lang_id);
                
                local rangeDic = Split1(data.rank, "|");
                local len = #rangeDic;
                local rank = v2n(rangeDic[1]);
                local isShow = len == 1 and rank > 0 and rank <= 3;
                if isShow then
                    SetUIImage(rankImg, "Sprite/ui_huodongjingsai/paihang_win4_icon" .. rank .. ".png", false);
                end
                SetActive(rankImg, isShow);
                
                local isSelect = false;
                if len == 1 then
                    isSelect = rank == self.rank;
                elseif len == 2 then
                    isSelect = rank <= self.rank and v2n(rangeDic[2]) >= self.rank;
                end
                if isSelect then
                    isChoose = true;
                elseif not isChoose and i == count then
                    isSelect = true; -- 不在排名范围内，默认未上榜
                end
                
                if isSelect then
                    rankTxt.color = GetColorByHex("1b72cb");
                    UnifyOutline(noneTxt, "1b72cb");
                    SetUIImage(bg, "Sprite/ui_paihangbang/paihangbang_shijieboss_dilang2.png", false);
                else
                    rankTxt.color = GetColorByHex("aa4f01");
                    UnifyOutline(noneTxt, "aa4f01");
                    SetUIImage(bg, "Sprite/ui_paihangbang/paihangbang_shijieboss_dilang1.png", false);
                end

                if not self.rewardSubItemList[i] then
                    self.rewardSubItemList[i] = {};
                end
                self:OnUpdateReward(rewardObj, self.rewardSubItemList[i], data.reward);
                SetActive(noneTxt, data.reward == nil);
            end
        end
    end
    SetActive(self.ui.m_goRank, isRank);
    SetActive(self.ui.m_goReward, not isRank);
end

function UI_WorldBossRank:OnUpdateReward(rewardObj, itemList, rewardStr)
    if rewardStr then
        local rewardList = Split2(rewardStr, ";", "|")
        local count = #rewardList;
        local num = #itemList;
        local len = count > num and count or num;
        for i = 1, len do
            local item = itemList[i];
            if item == nil then
                item = BagItem.new();
                item:Create(rewardObj);
                item:SetScale(0.71, 0.71);
                table.insert(itemList, item);
            end
            SetActive(item.go, i <= count);

            if i <= count then
                item:UpdateInfo(BagItemModule.new({ id = v2n(rewardList[i].id), num = v2n(rewardList[i].count) }));
            end
        end
    else
        for i = 1, #itemList do
            SetActive(itemList[i], false);
        end
    end
end



----------------------------------- RankItem -----------------------------------
---
function RankItem:OnInit(transform)
    self.bg = GetChild(transform, "bg", UEUI.Image);
    self.rankTxt = GetChild(transform, "rankTxt", UEUI.Text);
    self.rankImg = GetChild(transform, "rankImg", UEUI.Image);
    self.nameTxt = GetChild(transform, "nameTxt", UEUI.Text);
    self.leagueIcon = GetChild(transform, "leagueIcon", UEUI.Image);
    self.leagueNameTxt = GetChild(transform, "leagueNameTxt", UEUI.Text);
    self.hurtBg = GetChild(transform, "hurtBg", UEUI.Image);
    self.hurtTitleTxt = GetChild(transform, "hurtBg/m_txtAuto70000342", UEUI.Text);
    self.hurtTxt = GetChild(transform, "hurtBg/hurtTxt", UEUI.Text);
    self.lookBtn = GetChild(transform, "lookBtn", UEUI.Image);

    self.headObj = CreateCommonHead(transform, 0.5, Vector2(-280, 2));
end

function RankItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec;
end

function RankItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition;
end

function RankItem:UpdateData(data, index)
    local rank = data.rank
    if rank > 0 then
        self.rankTxt.text = rank;
    else
        self.rankTxt.text = LangMgr:GetLang(9056);
    end

    local isShow = rank > 0 and rank <= 3;
    if isShow then
        SetUIImage(self.rankImg, "Sprite/ui_huodongjingsai/paihang_win4_icon" .. rank .. ".png", false);
    end
    SetActive(self.rankImg, isShow);
    
    local hurtNum = data.value;
    self.hurtTxt.text = NumToGameString(hurtNum);

    local playerInfo = data.player;
    local playerId = NetUpdatePlayerData:GetPlayerID();
    self.nameTxt.text = playerInfo.name;
    SetHeadAndBorderByGo(self.headObj, playerInfo.icon, playerInfo.border, function()
        if v2s(playerInfo.id) ~= v2s(playerId) then
            FriendManager:ShowPlayerById(playerInfo.id);
        end
    end);

    local leagueInfo = data.league;
    if leagueInfo then
        self.leagueNameTxt.text = leagueInfo.name;
        SetUIImage(self.leagueIcon, LeagueManager:GetUnionImageById(leagueInfo.icon), false);
    else
        self.leagueNameTxt.text = "";
    end
    SetActive(self.leagueIcon, leagueInfo ~= nil);

    if v2s(playerInfo.id) == v2s(playerId) then
        SetUIImage(self.bg, "Sprite/ui_paihangbang/paihangbang_dilang2.png", false);
        self.rankTxt.color = GetColorByHex("1b72cb");
        self.leagueNameTxt.color = GetColorByHex("0758ab");
        UnifyOutline(self.nameTxt.gameObject, "245598");

        SetUIImage(self.hurtBg, "Sprite/ui_paihangbang/paihangbang_dilang3_1.png", false);
        self.hurtTitleTxt.color = GetColorByHex("4071b4");
        UnifyOutline(self.hurtTxt.gameObject, "245598");

        SetUIImage(self.lookBtn, "Sprite/ui_paihangbang/paihangbangbutton_chakan2.png", false);
    else
        SetUIImage(self.bg, "Sprite/ui_paihangbang/paihangbang_dilang1.png", false);
        self.rankTxt.color = GetColorByHex("aa4f01");
        self.leagueNameTxt.color = GetColorByHex("992e23");
        UnifyOutline(self.nameTxt.gameObject, "a13a3d");

        SetUIImage(self.hurtBg, "Sprite/ui_paihangbang/paihangbang_dilang1_1.png", false);
        self.hurtTitleTxt.color = GetColorByHex("c04e0d");
        UnifyOutline(self.hurtTxt.gameObject, "ae4a00");

        SetUIImage(self.lookBtn, "Sprite/ui_paihangbang/paihangbangbutton_chakan1.png", false);
    end

    RemoveUIComponentEventCallback(self.lookBtn, UEUI.Button);
    AddUIComponentEventCallback(self.lookBtn, UEUI.Button, function()
        WorldBossManager:OnRequestTeamQuery(playerInfo.id, function(respData)
            if self.lookBtn then
                if hurtNum > 0 then
                    local pos = UIRectPosFit(self.lookBtn);
                    UI_SHOW(UIDefine.UI_WorldBossHeroTeamTip, respData.team, pos);
                else
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70001065));
                end
            end
        end)
    end);
end

return UI_WorldBossRank