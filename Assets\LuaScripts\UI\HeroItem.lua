local HeroItem = {}
local M = HeroItem
local tipPath = "Assets/ResPackage/Sprite/ui_public/StatusIcon-wrong.png"
local infoPath = "Assets/ResPackage/Sprite/ui_public/StatusIcon-instructions.png"
local countPath = "Assets/ResPackage/Sprite/ui_public/StatusIcon-wait.png"
local rightPath = "Assets/ResPackage/Sprite/ui_public/StatusIcon-complete.png"
local prePath = "Assets/ResPackage/Prefab/UI/shipHeroItem.prefab"

local pre
function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)
end

function M:Init()
    self.bg = GetChild(self.go,"bg")
    self.bgImage = GetComponent(self.bg,UEUI.Image)
    self.selectBg = GetChild(self.go,"selectBg")
    self.selectBgImage = GetComponent(self.selectBg,UEUI.Image)
    self.tip = GetChild(self.go,"tip",UEUI.Image)
    self.tipAni = GetComponent(self.tip,TweenAnim)
    self.hero = GetChild(self.go,"hero")
    self.speGo = GetChild(self.hero,"speGo")
    self.timer = GetChild(self.hero,"Timer")
    self.heroImg = GetChild(self.hero,"heroIcon",UEUI.Image)
    self.rewardsObj = GetChild(self.hero,"rewards")
    self.unLockList = MapController.m_MapHeroOpenArray
    self.adsGo = GetChild(self.hero,"adImg")
    self.adState = false
    self.isSelect = false
    self.rewards ={}
    for i = 1, 5 do
        self.rewards[i] = GetChild(self.go,"hero/rewards/rd"..i,UEUI.Image)
    end

    self.wait = GetChild(self.go,"wait")
    self.needLevel = GetChild(self.go,"wait/need",UEUI.Text)
    self.unlock = GetChild(self.go,"unlock")
    self.heroWait = GetChild(self.go,"heroWait")
    self.heroWaitImg = GetChild(self.heroWait,"heroIcon",UEUI.Image)
    self.selectScale = Vector3.New(1.3,1.3,1)
	self.normalScale = Vector3.New(1,1,1)
    InitTextLanguage(self.go)
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickShipItem(arg1,arg2)
    end)
    AddUIComponentEventCallback(GetComponent(self.tip,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickShipTip(arg1,arg2)
    end)
end

function M:SetItem(data,need)
    self.data = data
	
	if data == nil or not data.ShipId then
		SetActive(self.go,false)
	else
		SetActive(self.go,true)
	end
    --function SetSpePng(isShow)
        --if isShow then
            --SetImageSprite(self.bgImage,"Sprite/ui2/port_task_listbg_normal1.png",false)
            --SetImageSprite(self.selectBgImage,"Sprite/ui2/port_task_listbg_selected1.png",false)
            --SetActive(self.speGo,true)
        --else
            --SetImageSprite(self.bgImage,"Sprite/ui/port_task_listbg_normal.png",false)
            --SetImageSprite(self.selectBgImage,"Sprite/ui/port_task_listbg_selected.png",false)
            --SetActive(self.speGo,false)
        --end
    --end
    --if data then
        ----当时谱初始状态时只需要判断是否开启特殊订单，其他状态的时候需要判断是否为特殊订单
        --if data.ShipState ~= 1 then
            --SetSpePng(data.isDouble)
        --else
            --SetSpePng(data.isSpe)
        --end
    --else
        --SetSpePng(false)
    --end

    if need then
        SetActive(self.tip,true)
        self.needLevel.text = LangMgr:GetLang(3007)..need
        self:SetState(2)
        SetImageSprite(self.tip,infoPath,false)
    elseif data == nil then
        self:SetState(3)
        SetActive(self.tip,false)
    else
        self:SetState(1)
        if data.ShipId then
            SetActive(self.tip,true)
            self.tipAni:DOPause()
            self.tipAni:DORewind()
            --self.tipAni:DORestart()
            if data.ShipState == 1 then
                if data.isEnough then
                    SetImageSprite(self.tip,tipPath,false)
                else
                    SetActive(self.tip,false)
                end
            elseif data.ShipState == 2 then
                SetImageSprite(self.tip,countPath,false)
                self.tipAni:DORestart()
            else
                SetImageSprite(self.tip,rightPath,false)
            end
            SetActive(self.timer,false)
            SetActive(self.heroImg,true)
            SetActive(self.rewardsObj,true)
			local config = RecipeConfig:GetRecipeById(data.ShipId)
            SetImageSprite(self.heroImg, ItemConfig:GetIcon(config.itemid),false)
            local rewards = RecipeConfig:GetRecipeRewards(data.ShipId)
            local count = 1
            for _, v in ipairs(rewards) do
                for _ = 1,v.Count do
                    SetImageSprite(self.rewards[count],ItemConfig:GetIcon(v.Id),false)
                    SetActive(self.rewards[count],true)
                    count = count + 1
                end
            end
            for i = count,#self.rewards do
                SetActive(self.rewards[i],false)
                count = count + 1
            end
        else
            local count = table.count(MapController.m_MapHeroFly)
            if count >= data.HeroIndex then   --data.ShipIndex
                SetActive(self.timer,true)
                SetActive(self.heroImg,false)
                SetActive(self.rewardsObj,false)
                SetActive(self.tip,false)
            else
                SetImageSprite(self.tip,infoPath,false)
                --SetImageSprite(self.heroWaitImg,string.format("Assets/ResPackage/Sprite/ui/hero_%d_ss.png",data.ShipIndex) ,false)
                self:SetState(4)
            end
        end
    end
end



function M:SetState(index)
    self.state = index
    if index == 1 then--英雄
        SetActive(self.hero,true)
        SetActive(self.wait,false)
        SetActive(self.unlock,false)
        SetActive(self.heroWait,false)
        self.bgImage.color = Color.New(1,1,1,1)
    elseif index == 2 then--待解锁
        SetActive(self.hero,false)
        SetActive(self.wait,true)
        SetActive(self.unlock,false)
        SetActive(self.heroWait,false)
        self.bgImage.color = Color.New(1,1,1,1)
    elseif index == 4 then
        SetActive(self.hero,false)
        SetActive(self.wait,false)
        SetActive(self.unlock,false)
        SetActive(self.heroWait,true)
        self.bgImage.color = Color.New(1,1,1,1)
    else--锁定
        SetActive(self.hero,false)
        SetActive(self.wait,false)
        SetActive(self.unlock,true)
        SetActive(self.heroWait,false)
        self.bgImage.color = Color.New(1,1,1,0.5)
    end
end

function M:ControlAds(isShow)
    if isShow == self.adState then return end
    self.adState = isShow
    if isShow and self.isSelect then
        SetActive(self.adsGo,false)
    else
        SetActive(self.adsGo,isShow)
    end
end

function M:SetSelectBg(isActive)
    SetActive(self.bg,not isActive)
    SetActive(self.selectBg,isActive)
    self.isSelect = isActive
    self.trans.localScale = isActive and self.selectScale or self.normalScale
    if self.adState then
        SetActive(self.adsGo,not isActive)
    end
end

function M:ClickShipTip()
    if self.state == 2 then
        UI_SHOW(UIDefine.UI_PortHelp,1)
    elseif self.state == 4 then
        UI_SHOW(UIDefine.UI_PortHelp,2)
    end
end

function M:ClickShipItem()
    if self.state == 1 then
        self:SetSelectBg(true)
        --UI_UPDATE(UIDefine.UI_PageOrder,2,self.data.ShipIndex)
        UI_UPDATE(UIDefine.UI_PageOrder,2,self.data.HeroIndex)
    elseif self.state == 2 then
        UI_SHOW(UIDefine.UI_PortHelp,1)
    elseif self.state == 4 then
        UI_SHOW(UIDefine.UI_PortHelp,2)
    elseif self.state == 3 then
        UI_UPDATE(UIDefine.UI_PageOrder,2)
    end
end

function M:Clear()
    UEGO.Destroy(self.go)
end

return M