local DailyTaskController = Class()

function DailyTaskController:Init()
    self.stateNormalBg = "Sprite/ui_task/taskbox1.png"
    self.stateFinishBg = "Sprite/ui_task/taskbox2.png"
    
	self.loopTimer = 0
	
    self:LoadConfigData()
	
	-----新逻辑部分
	local bFirstInit = false
	local lastInitTime = NetDailyTaskData:get_LastInitTime()
	if lastInitTime <= 0 then
		--第一次初始化数据
		NetDailyTaskData:InitNetData()
	end
	local lastWeekInitTime = NetDailyTaskData:get_WeekLastInitTime()
	if lastWeekInitTime <= 0 then
		NetDailyTaskData:InitWeekNetData()
	end
	NetDailyTaskData:InitRookieTaskData()
	if lastWeekInitTime <= 0 and lastInitTime <= 0 then
		bFirstInit = true
	end
	
	--走到这里相当于新数据初始化都已经完毕
	if not bFirstInit then
		--检测数据部分
		if NetDailyTaskData:get_SurplusZeroTime() <= 0 then
			NetDailyTaskData:InitNetData()
		end
		if NetDailyTaskData:get_WeekSurplusZeroTime() <= 0 then
			NetDailyTaskData:InitWeekNetData()
		end
	end
	
	--测试部分
	--NetDailyTaskData:InitNetData()
	--NetDailyTaskData:InitWeekNetData()
	
	--每日任务的更新
	EventMgr:Add(EventID.DAILY_TASK,self.onTaskComplete,self)
	--合并地表元素的事件
	EventMgr:Add(EventID.MAP_ITEM_COMBINE,self.ComebineEvent,self)
	--采集的事件
	EventMgr:Add(EventID.MAP_ITEM_COLLECT,self.CutEvent,self)
	
	--使用工人的事件
	EventMgr:Add(EventID.USE_WORKER,self.UseWokerEvent,self)
	--虚拟货币变化的事件
	EventMgr:Add(EventID.CHANGE_RESOURCE,self.SourceChange,self)
	--砍树
	EventMgr:Add(EventID.MAP_BIGTREE_COLLECT,self.CutTree , self)
	--砍完树
	EventMgr:Add(EventID.MAP_CUT_COMPLTET,self.CutTreeComplete,self)
	
	EventMgr:Add(EventID.MAP_ITEM_NEW,self.ItemNewEvent,self)--获得新道具
	
	EventMgr:Add(EventID.ANIMAL_EAT_EVENT,self.AnimalEatEvent,self)--投喂动物
	
	EventMgr:Add(EventID.ANIMAL_EAT_CHEST,self.AnimalEatCHEAT,self)--投喂动物
	
	EventMgr:Add(EventID.OPEN_GUAGUA_KA,self.OpenGuaguaKa,self)--刮刮卡
	
	EventMgr:Add(EventID.BUILD_FINISH_WORKER,self.FinishWoker,self)--完成建筑
	
	EventMgr:Add(EventID.MAP_CLOUD_UNLOCKED,self.UnlockCloud,self)--解锁迷雾
	
	EventMgr:Add(EventID.MAP_ITEM_NEW,self.GetItemBox,self)--获得宝箱
	
	EventMgr:Add(EventID.MAP_HERO_NEW,self.NewHero,self)--获得新英雄

	EventMgr:Add(EventID.CLAIM_REWARD_LIMIT_REWARD,self.OnFinishLimitActivity,self)--领取限时副本通关奖励

	EventMgr:Add(EventID.CHANGE_NAME,self.OnChangeName,self)--换名字
	
	EventMgr:Add(EventID.CHANGE_HEAD,self.OnChangeHead,self)--换头像
	
	EventMgr:Add(EventID.LEVEL_UP,self.OnLevelUp,self)--升级

	EventMgr:Add(EventID.MAP_ITEM_NEW,self.OnItemNew,self)--获得新道具

	--刷新红点儿	
	self:UpdateMainUI()

	--启动一个定时器
	--倒计时功能
	if self.loopTimer <= 0 then
		self.loopTimer = TimeMgr:CreateTimer(self, function()
				self:OnTimer()
			end, 1)
	end
end
--一秒轮询一次的回调
function DailyTaskController:OnTimer()
	local surpTime = NetDailyTaskData:get_SurplusZeroTime()
	if self.curType == TASK_SPLIT.DAILY and self.uiPanel then
		self.uiPanel:SetTotalSurplusTime(surpTime)
	end
	if surpTime < 0 then
		--重置数据
		Log.Info("DailyTaskController:OnTimer() -- 1")
		NetDailyTaskData:InitNetData()
		NetDailyTaskData:sort_TaskInfos()
		if self.curType == TASK_SPLIT.DAILY then
			if self.uiPanel then
				self:SetUI(self.uiPanel , self.curType)
			end
		end
		-- NetMonthCardData:ZeroInit()
		--更新主界面的数据
		self:UpdateMainUI()
	end
	surpTime = NetDailyTaskData:get_WeekSurplusZeroTime()
	if self.curType == TASK_SPLIT.WEEK and self.uiPanel then
		self.uiPanel:SetTotalWeekSurplusTime(surpTime)
	end
	--TODO uiPanel
	if surpTime < 0 then
		Log.Info("DailyTaskController:OnTimer() -- 2")
		NetDailyTaskData:InitWeekNetData()
		NetDailyTaskData:sort_WeekTaskInfos()
		if self.curType == TASK_SPLIT.WEEK then
			if self.uiPanel then
				self:SetUI(self.uiPanel , self.curType)
			end
		end
		--更新主界面的数据
		self:UpdateMainUI()
	end
	
end

--region[[完成任务的一些基本事件监听]]-----START

function DailyTaskController:ItemNewEvent(mapId,id,isBool)
	
	--if not isBool then
		----获得某一个元素
		
	--end
	if NetDailyTaskData:CheckRookieTaskTypeParams(DAY_TEASK_TYPE.UNLOCKED_HOTLE,id) then
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.UNLOCKED_HOTLE,1)
	end
end


------NEW-------
function DailyTaskController:CutTree(_id)
	
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.MINING_FIELD,1)
	
end

function DailyTaskController:FinishWoker()

	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.BUILD_FINISH_WORKER,1)

end

function DailyTaskController:UnlockCloud(map_id,group_id)

	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.CLOUD_UNLOCKED_TASK,1)
	if NetDailyTaskData:CheckRookieTaskTypeParams(DAY_TEASK_TYPE.UNLOCKED_CLOUDID,group_id) then
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.UNLOCKED_CLOUDID,1)
	end
	if NetDailyTaskData:CheckRookieTaskTypeParams(DAY_TEASK_TYPE.UNLOCKED_CLOUDID2,group_id) then
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.UNLOCKED_CLOUDID2,1)
	end
end

function DailyTaskController:GetItemBox(map_id,itemid)
	local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,itemid)
	if nil ~= config and config.type_name == 5 then
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.GET_ITEM_BOX,1)
	end
end

function DailyTaskController:NewHero()
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.OPEN_HERO_TASK,1)
end

function DailyTaskController:OnFinishLimitActivity()
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.FINISH_LIMIT_ACTIVITY,1)
end

function DailyTaskController:OnChangeName()
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.CHANGE_NAME,1)
end

function DailyTaskController:OnChangeHead()
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.CHANGE_HEAD,1)
end

function DailyTaskController:OnLevelUp(lvId)
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.PLAYER_LEVEL,NetUpdatePlayerData:GetLevel())
end

function DailyTaskController:OnItemNew(mapId,id,isBool)
	
	if NetSevenDayData:CheckTaskTypeParams(DAY_TEASK_TYPE.ITEM_NEW,id) then
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.ITEM_NEW,1,{["id"]=id})
	end
	
end

------NEW-------


function DailyTaskController:CutTreeComplete(_id)
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.CUT_TREE_COMPLETE,1)
	local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, _id)
	if config.is_can_move == 1 then
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.CUT_TREE_TYPE2,1)
	end
end

function DailyTaskController:SourceChange(type,_,change)
	--ItemID.ENERGY
	--ItemConfig.ItemIDToKey[tostring(ItemID.ENERGY)]
    if type==ItemConfig.ItemIDToKey[tostring(ItemID.COIN)] then
		--金币变化
        if change>0 then
			--获得金币 --11
            EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.GET_GOLD_NUM,change)
        elseif change<0 then
			--消耗金币 --8
            EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.USE_GOLD_NUM,-change)
        end
    elseif type == ItemConfig.ItemIDToKey[tostring(ItemID.DIAMOND)] then
        -- 获得钻石 -- 56
        if change > 0 then
            -- 如果 UI_Recharge 有动画，这个事件类型会触发两次
            -- EventMgr:Dispatch(EventID.DAILY_TASK, DAY_TEASK_TYPE.GET_DIMAND_NUM, change)
        -- 消耗钻石 -- 9
        elseif change < 0 then
            EventMgr:Dispatch(EventID.DAILY_TASK, DAY_TEASK_TYPE.USE_DIMAND_NUM, -change)
        end
	elseif type == ItemConfig.ItemIDToKey[tostring(ItemID.ENERGY)] then
		if change<0 then
			--体力消耗
			EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.CONSUME_MAIN_STRONG,-change)
		end
	elseif type == ItemConfig.ItemIDToKey[tostring(ItemID.LimitEnergy)] then
		if change<0 then
			--体力消耗
			EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.CONSUME_LIMIT_STRONG,-change)
		end
    end
end

function DailyTaskController:UseWokerEvent(_)
	--使用一次工人精灵 --7
    EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.USE_SPRITE_NUM,1)
end

function DailyTaskController:CutEvent (type)
    --if type==1 then
		--砍树 --5
        EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.CUT_TREE,1)
    --end
end

function DailyTaskController:OpenGuaguaKa()

	--刮刮卡幸运抽奖
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.LUCKY_TIMES,1)
	
end
function DailyTaskController:AnimalEatCHEAT()

	--获得投喂宝箱
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.GET_FEED_CHEST,1)
end
function DailyTaskController:AnimalEatEvent()
	
	--投喂动物一次
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.ANIMAL_EAT_TIMES,1)
end

function DailyTaskController:ComebineEvent(mapId,id,highCount, oldItemId, lowCount,listOut,combineCount)
    if highCount>=2 then
		--任意5合2 --3
        EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.FIVE_FOE_TWO,math.floor(combineCount/5))
		if NetDailyTaskData:CheckRookieTaskTypeParams(DAY_TEASK_TYPE.FIVE_ID_FOR_TWO,id) then
			EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.FIVE_ID_FOR_TWO,math.floor(combineCount/5))
		end
		
		if ItemConfig:GetTypeUse(id) == ItemUseType.ObjSkiingRewardItem then
			local thinkTable = {
				["racing_sledid"] = id,
				["racing_sledcount"] = math.floor(combineCount/5),
				["racing_sledreason"] = 3,
			}
			SdkHelper:ThinkingTrackEvent(ThinkingKey.frostyracing, thinkTable)
		end
	elseif highCount>=1 then					
				if ItemConfig:GetTypeUse(id) == ItemUseType.ObjSkiingRewardItem then
					local thinkTable = {
						["racing_sledid"] = id,
						["racing_sledcount"] = math.floor(highCount),
						["racing_sledreason"] = 2,
					}
					SdkHelper:ThinkingTrackEvent(ThinkingKey.frostyracing, thinkTable)
				end
    end
    if highCount>=1 then
		--任意3合1 --2
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.THREE_FOR_ONE,highCount)
    end
	
	if NetDailyTaskData:CheckTaskTypeParams(DAY_TEASK_TYPE.COMPOSE_KEY , id) then
		--合成了新的物种
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.COMPOSE_KEY,1)
	end
	if NetDailyTaskData:CheckRookieTaskTypeParams(DAY_TEASK_TYPE.COMPOSE_CROPS , id) then
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.COMPOSE_CROPS,1)
	end
	if NetDailyTaskData:CheckRookieTaskTypeParams(DAY_TEASK_TYPE.COMPOSE_MOVE_TREE , id) then
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.COMPOSE_MOVE_TREE,1)
	end
	--是否在动物园
	if MapController.m_MapType == MapType.Zoo then
		--在动物园合成
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.ANIMAL_COMBINE,1)
	end
end

function DailyTaskController:IsCanShowDailyTask()
	--local isOpenTarget = DailyTargetManager:IsOpenDailyTargetSwitch()
	--if isOpenTarget then
	--	return false
	--end
	local condition = GlobalConfig:GetNumber(1049)
	local curLevel = NetUpdatePlayerData:GetLevelMess()
	return curLevel >= condition 
end

function DailyTaskController:onTaskComplete(type,count,level)

	if type == DAY_TEASK_TYPE.CLOUD_UNLOCKED_TASK or
	type == DAY_TEASK_TYPE.UNLOCKED_CLOUDID or 
	type == DAY_TEASK_TYPE.BUILD_FINISH_WORKER or 
	type == DAY_TEASK_TYPE.CUT_TREE_COMPLETE or 
	type == DAY_TEASK_TYPE.UNLOCKED_CLOUDID2 then
		if MapController.m_MapId ~= MAP_ID_MAIN then
			return
		end
	end
	
	local condition = GlobalConfig:GetNumber(1049)
	
	--if not GuideController:IsGuideDone(condition) then
		--Log.Info("引导还没有过去--->无法更新任务")
		--return
	--end
	local curLevel = NetUpdatePlayerData:GetLevelMess()
	--if curLevel < condition then
		----Log.Info("引导还没有过去--->无法更新任务")
		----新手任务
		--local update_list = NetDailyTaskData:update_TaskInfoByType(type , count,true)
		
		----return
	--end
	
	--type 是任务的类型
	--count 是进度增加的个数
	local update_list = NetDailyTaskData:update_TaskInfoByType(type , count,curLevel < condition,level)
	if update_list then
		--有更新了
		if self.uiPanel then
			for i = 1, #update_list do
				self.uiPanel:UpdateItem(update_list[i])
			end
		end
		self:UpdateMainUI()
	end
end

--刷新主界面的进度显示
function DailyTaskController:UpdateMainUI()
	--刷新主界面的进度 主界面的进度还是走的 每日任务的东西
	local totalComplete = NetDailyTaskData:get_TaskCompleteCount()
	local totalTaskCount = NetDailyTaskData:get_TaskAllCount()
	local daily_Count = NetDailyTaskData:get_CompleteAndNotRewardCount()
	local week_Count = NetDailyTaskData:get_WeekCompleteAndNotRewardCount()
	local rookie_Count = NetDailyTaskData:get_RookieCompletAndNotRewardCount()
	local notGetCount = daily_Count + week_Count 
	if NetDailyTaskData:IsShowRookieTask() then
		notGetCount = daily_Count + week_Count + rookie_Count
	end
	-- B包开新手任务 只判断新手红点好了
	--local isOpenTarget = DailyTargetManager:IsOpenDailyTargetSwitch()
	--if isOpenTarget then
	--	notGetCount = rookie_Count
	--end
	
	local type3Count = NetDailyTaskData:get_Type3TaskAllCount()
	local bRed = NetDailyTaskData:get_MainUIRedTipState()
	if notGetCount > 0 then
		bRed = false
	end
	--红点儿
	UIMgr:Refresh(UIDefine.UI_MainFace,6,{MainFaceDefine.DailyTask,bRed})
	
	UIMgr:Refresh(UIDefine.UI_MainFace,3,{now = totalComplete ,target = totalTaskCount - type3Count,show = notGetCount > 0})

	UIMgr:Refresh(UIDefine.UI_MainFace,39)
	
	if nil ~= self.uiPanel then
		self.uiPanel:UpdateButtonHitState(daily_Count , week_Count, rookie_Count)
	end
	
end

--region[[完成任务的一些基本事件监听]]-----END

function DailyTaskController:LoadConfigData()
	--头顶的5个奖励
    local rewardInfos = {}
    for i=1,5 do
        local itemIdStr = ConfigMgr:GetDataByID(ConfigDefine.ID.global_setting,i+1015).value
		local strArr = string.split(itemIdStr , "|")
		local itemId = tonumber(strArr[1])
		local count = strArr[2]
		if not count then
			count = 1
		else
			count = tonumber(count)
		end
        local info = ConfigMgr:GetDataByID(ConfigDefine.ID.item,itemId)
        rewardInfos[#rewardInfos+1] = {
            Id = itemId,
			Count = count,
            Sprite = info.icon_b
        }
    end
    self.dailyRewards =rewardInfos
	
	--每周任务头顶的五个奖励
	local weekRewardInfos = {}
	local allweekExp = 0
	local level = NetUpdatePlayerData:GetLevel()
	for i = 1, 5 do
		local itemStr
		if level <= 9 then
			itemStr = ConfigMgr:GetDataByID(ConfigDefine.ID.global_setting,i+1054).value
		else
			itemStr = ConfigMgr:GetDataByID(ConfigDefine.ID.global_setting,i+1059).value
		end
		local arr = string.split(itemStr , "|")
		local expNum = tonumber(arr[1])
		local itemId = tonumber(arr[2])
		itemId = tonumber(NetSeasonActivity:GetChangeItemId(itemId))
		local itemCount = arr[3]
		if not itemCount then
			itemCount = 1
		else
			itemCount = tonumber(itemCount)
		end
		local info = ConfigMgr:GetDataByID(ConfigDefine.ID.item,itemId)
		if not info then
			Log.Error("loading!!! 任务配置错误 itemId :" .. itemId)
		end
		weekRewardInfos[#weekRewardInfos+1] = {
			Id = itemId,
			Count = itemCount,
			expNum = expNum,
			index = #weekRewardInfos+1,
			Sprite = info.icon_b
		}
		if expNum > allweekExp then
			allweekExp = expNum
		end
		--allweekExp = allweekExp + expNum
	end
	
	self.weekRewards = weekRewardInfos
	self.allWeekExp = allweekExp
	
end

--处理本界面特殊的跳转
function DailyTaskController:OnFindSpecial(itemType)
	if itemType == DAY_TEASK_TYPE.ALL_DAYTASK then
		self.uiPanel:SliderMoveNone()
	end
end

--上面的两个按钮的点击
function DailyTaskController:OnPanelType(_type)
	
	--更换部分
	if not self.uiPanel then
		return false
	end
	
	if self.curType == _type then
		return false
	end
	
	self:SetUI(self.uiPanel , _type)
	
	return true
end

--Scroll中按钮的点击
function DailyTaskController:onItemClick(taskId,pos)
	
    local info = NetDailyTaskData:get_TaskInfoByID(taskId)
	if nil == info then
		info = NetDailyTaskData:get_WeekTaskInfoByID(taskId)
	end
	if nil == info then
		info = NetDailyTaskData:get_RookieTaskInfoByID(taskId)
	end
    local refresh = false
	local updateList = nil
	local isUpdateWeekTop = false
    if info then
		local conf = ConfigMgr:GetDataByID(ConfigDefine.ID.daily_task,taskId)
		if nil  == conf then
			conf = ConfigMgr:GetDataByID(ConfigDefine.ID.rookie_task,taskId)
		end
        if info.taskState == DAY_TASK_STATE.NOT_FINISH then
            --未完成的状态
            AudioMgr:Play(42)
			--如果是清除所有任务的类型，特殊处理UI
			local itemType = conf.task_type
			if itemType == DAY_TEASK_TYPE.ALL_DAYTASK then
				self:OnFindSpecial(itemType)
			elseif conf.look_for then
				if conf.task_type == DAY_TEASK_TYPE.UNLOCKED_CLOUDID2 then		
					FixedBugManager:FixedRookieType41(conf)
				end					
                local look = FindController.LookFindItem(conf.look_for,conf.look_for_value)
				if look ~= FindType.DailyTask then
					self.uiPanel:Close()
				end
            else
                Log.Info("当前任务不存在寻找的ID")
            end
        elseif info.taskState == DAY_TASK_STATE.FINISHED_NOT_REWARD then
			--领取奖励
			--跟新数据
			updateList = NetDailyTaskData:reward_TaskInfoByID(info.taskId)
			if nil == updateList then
				Log.Info("没有领取到任何奖励")
				return
			end
			AudioMgr:Play(3) --领取奖励声音
			
			--判断是否完成全部新手任务
			local isGetRookie = NetDailyTaskData:IsGetRookieBoxReward()
			if isGetRookie and not NetDailyTaskData:GetRookieBoxRewardActive() then
				if nil ~= self.uiPanel then
					self.uiPanel:GetRookieGift()
				end
			end
			--先处理第二个奖励
			Log.Info("领取积分奖励 -->" .. tostring(conf.two_num))
			if nil ~= conf.two_num and 0 ~= conf.two_num then
				NetDailyTaskData:add_WeekExp(tonumber(conf.two_num))
				--local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, ItemID.WEEK_ACTIVE)
				--MapController:AddResourceJumpAnim(pos.x, pos.y, config, tonumber(conf.two_num))
				MapController:AddResourceBoomAnim(pos.x, pos.y, ItemID.WEEK_ACTIVE, tonumber(conf.two_num) , true)
				--这里需要处理是否给每周的奖励
				NetDailyTaskData:WeekThinkingTrackEvent()
				
				local AddReward = {}
				local curExp = NetDailyTaskData:get_WeekCurExp()
				for i = 1, #self.weekRewards do
					if curExp >= self.weekRewards[i].expNum and not NetDailyTaskData:get_CurExpReward(i) then
						NetDailyTaskData:set_CurExpReward(i)
						table.insert(AddReward , self.weekRewards[i])
					end
				end
				if #AddReward > 0 then
					if nil ~= self.uiPanel then	
						self.uiPanel:SetBubbleAni(true)					
						for i = 1, #AddReward do
							if AddReward[i].Id < ItemID._RESOURCE_MAX then							
								local star_trans = self.uiPanel:GetWeekRewardIndexTrans(AddReward[i].index) 
								local fly_pos
								if nil == star_trans then
									fly_pos = Vector3.zero
								else
									fly_pos = MapController:GetUIPosByWorld(star_trans.position)
								end
								self.uiPanel:SetBubbleAni(false)
								MapController:AddResourceBoomAnim(fly_pos.x,fly_pos.y,tonumber(AddReward[i].Id),tonumber(AddReward[i].Count))
								
								NetUpdatePlayerData:AddResource(PlayerDefine[tonumber(AddReward[i].Id)],tonumber(AddReward[i].Count),nil,nil,"UI_DailyTask")
							else
								self.uiPanel:FlyItem(tonumber(AddReward[i].Id),tonumber(AddReward[i].Count),pos)
							end				
						end
					else
						local airList = {}
						for i = 1, #AddReward do
							local count = tonumber(AddReward[i].Count)
							for k = 1, count do
								table.insert(airList , AddReward[i].Id)
							end
						end
						MapController:SendRewardToMap(airList,nil,nil,nil,"UI_DailyTask")
					end		
				end
				isUpdateWeekTop = true
			end

			if conf.task_split == TASK_SPLIT.DAILY then
				if conf.task_type == DAY_TEASK_TYPE.ALL_DAYTASK then
					local curIndex = NetDailyTaskData:get_RewardIndex()
					--新加逻辑
					if nil ~= self.uiPanel then
						self.uiPanel:SetBubbleAni(true)
						self.uiPanel:FlyItem(tonumber(self.dailyRewards[curIndex].Id),self.dailyRewards[curIndex].Count,pos)
					else
						local airList = {}
						for i = 1, #self.dailyRewards[curIndex].Count do
							table.insert(airList , self.dailyRewards[curIndex].Id)
						end
						MapController:SendRewardToMap(airList,nil,nil,nil,"UI_DailyTask")
						--MapController:CreateAircraft(self.dailyRewards[curIndex].Id)
					end
					
					
					--MapController:CreateAircraft(self.dailyRewards[curIndex].Id)
					EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.REWARD_ALL_DAILY_TASK,1)
					--关闭这个界面
					--if nil ~= self.uiPanel then
						--self.uiPanel:Close()
					--end
				else
					MapController:AddResourceBoomAnim(pos.x, pos.y, conf.reward_type, conf.num)
					NetUpdatePlayerData:AddResource(PlayerDefine[conf.reward_type],conf.num,nil,nil,"UI_DailyTask")
					if conf.reward_type == ItemID.COIN then
						NetNotification:NotifyCoin(4,conf.num)
					end
				end
				if self.uiPanel ~= nil then
					self.uiPanel:SliderMoveReward();
				end
				refresh = true
			else
				if conf.task_split == TASK_SPLIT.ROOKIE and conf.task_type ~= DAY_TEASK_TYPE.ROOKIE_LEVEL_TASK then
					--conf
					EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.ROOKIE_LEVEL_TASK,1,conf.level_id)
				end
				
				if self.uiPanel ~= nil then
					if conf.task_split == TASK_SPLIT.ROOKIE then--新手任务
						self.uiPanel:MoveToNextReward();
					elseif conf.task_split == TASK_SPLIT.WEEK then
						self.uiPanel:SliderMoveReward();
					end
				end
				
				
				MapController:AddResourceBoomAnim(pos.x, pos.y, conf.reward_type, conf.num)
				NetUpdatePlayerData:AddResource(PlayerDefine[conf.reward_type],conf.num,nil,nil,"UI_DailyTask")
				if conf.reward_type == ItemID.COIN then
					NetNotification:NotifyCoin(4,conf.num)
				end
				refresh = true
				
			end
        end
    end
    if refresh then
		if nil ~= self.uiPanel then
			--处理是否已经所有的任务都已经领取过了
			if self.curType == TASK_SPLIT.DAILY then
				local bIsGetAll = NetDailyTaskData:get_IsAllTaskConpleted()
				if bIsGetAll then
					--更新头部
					self:UpdateTopRewardInfo()
				else
					if nil ~= updateList then
						for i = 1, #updateList do
							--local data = NetDailyTaskData:get_TaskInfoByID(info.taskId)
							self.uiPanel:UpdateItem(updateList[i])
						end
					end
				end
			else
				--每周任务
				local bIsGetAll = NetDailyTaskData:get_IsAllWeekTaskCompleted()

				if bIsGetAll then
					--更新头部
					self:UpdateWeekTopRewardInfo()
				else
					if isUpdateWeekTop then
						self:UpdateWeekTopRewardInfo()
					end
					if nil ~= updateList then
						for i = 1, #updateList do
							--local data = NetDailyTaskData:get_TaskInfoByID(info.taskId)
							self.uiPanel:UpdateItem(updateList[i])
						end
					end
				end
			end
			
		end
		self:UpdateMainUI()
    end
	
end

function DailyTaskController:ShowUI()
	
	--排序数据
	NetDailyTaskData:sort_TaskInfos()
	
	NetDailyTaskData:sort_WeekTaskInfos()
	
	NetDailyTaskData:set_RedTipState(false)
	
	NetDailyTaskData:set_WeekRedTipState(false)
	
    UI_SHOW(UIDefine.UI_DailyTask)
	
end

function DailyTaskController:RemovePanel()
    self.uiPanel= nil
end

function DailyTaskController:SetUI(uiPanel , _type)
    if not uiPanel then
        Log.Error("################### uiPanel is nil")
    end
	self.uiPanel = uiPanel
	self.curType = _type

	--更新按钮部分
	self.uiPanel:ChangePanelState(self.curType)
	
	--更新每日任务的头部
	if self.curType == TASK_SPLIT.DAILY then
		local bIsGetAll = NetDailyTaskData:get_IsAllTaskConpleted()
		if not bIsGetAll then
			local taskInfos = NetDailyTaskData:get_TaskInfos()
			table.sort(taskInfos, function(a, b)
					if a.taskState ~= b.taskState then
						return a.taskState < b.taskState
					end
					if a.taskState == b.taskState then
						return a.sort < b.sort
					end
				end)
			self.uiPanel:SetDatas(taskInfos)
		end
		self:UpdateTopRewardInfo()
	elseif self.curType == TASK_SPLIT.WEEK then
		local bIsGetAll = NetDailyTaskData:get_IsAllWeekTaskCompleted()
		if not bIsGetAll then
			local taskInfos = NetDailyTaskData:get_WeekTaskInfos()
			self.uiPanel:SetDatas(taskInfos)
		end
		self:UpdateWeekTopRewardInfo()
	elseif self.curType == TASK_SPLIT.ROOKIE then
		local _,all_taskInfos = NetDailyTaskData:get_RookieTaskInfos()
		local inedx = NetDailyTaskData:GetRookieNextIndex()
		self.uiPanel:SetRookieBtn(all_taskInfos,inedx)
		self.uiPanel:UpdateRookieProgress()
	elseif self.curType == TASK_SPLIT.MAIN then
		local list = NetTaskData:GetMainTaskList();
		self.uiPanel:setMainData(list)
	end
	
	self:UpdateMainUI()
	
	self:OnTimer()
end

function DailyTaskController:UpdateWeekTopRewardInfo()
	if nil ~= self.uiPanel then
		
		--当前经验
		local curExp = NetDailyTaskData:get_WeekCurExp()
		
		--设置进度条的进度
		self.uiPanel:SetWeekSliderValue(curExp , self.allWeekExp)
		self.uiPanel:SetWeekSliderValueByNew(curExp/self.allWeekExp)
		local isReward = false
		for i=1,#self.weekRewards do
			local info = self.weekRewards[i]
			isReward = false
			if curExp >= info.expNum then
				isReward = true
			end
			self.uiPanel:SetWeekRewardSprite(i,info.Sprite , info.expNum , info.Count, isReward,info.Id)
			local bIsGetAll = NetDailyTaskData:get_IsAllWeekTaskCompleted()
			self.uiPanel:SetWeekComplete(bIsGetAll)
		end
		
	end
end

--计算进度条要到达的阀值
function DailyTaskController:get_ProgressValue()
	if nil ~= self.uiPanel then
		--当前经验
		local curExp = NetDailyTaskData:get_WeekCurExp()
		--档位起始比值
		local valueStart = {
			[0] = 0,
			[1] = 0.08,
			[2] = 0.295,
			[3] = 0.505,
			[4] = 0.72,
			[5] = 0.935,
			}
		--找到当前到达的档位
		local curStart = 0
		for i=1,#self.weekRewards do
			local info = self.weekRewards[i]
			if curExp > info.expNum then
				curStart = i
			end
		end
		local curNum = 0
		if self.weekRewards[curStart] then
			curNum = self.weekRewards[curStart].expNum
		end
		local nextNum = self.weekRewards[#self.weekRewards].expNum + self.weekRewards[1].expNum
		if self.weekRewards[curStart + 1] then
			nextNum = self.weekRewards[curStart + 1].expNum
		end
		
		local diff = curExp - curNum
		local nextDiff = nextNum - curNum
		local preDiff = diff / nextDiff
		
		local uiPre = 0
		if curStart == 0 or curStart == #self.weekRewards then
			uiPre = 96 * preDiff --算出UI的占比
		else
			uiPre = 290 * preDiff
		end
		
		local valuePro = uiPre / 1360
		return  valueStart[curStart] + valuePro
	end	
	return 0
end

function DailyTaskController:UpdateTopRewardInfo()
	if nil ~= self.uiPanel then
		
		local curIndex = NetDailyTaskData:get_RewardIndex()
		local bIsGetAll = NetDailyTaskData:get_IsAllTaskConpleted()
		--设置列表是否显示
		self.uiPanel:SetComplete(bIsGetAll)
		--设置黄色提示和红点儿是否显示
		self.uiPanel:SetCurTipVisable(not bIsGetAll)
		--设置当前黄色提示和红点儿的位置
		self.uiPanel:SetCurTipIndex(curIndex)
		--这里是更新头顶的奖励部分
		for i=1,#self.dailyRewards do
			local info = self.dailyRewards[i]
			--设置头顶的五个Icon
			self.uiPanel:SetRewardInfoByIndex(i,info.Sprite , info.Count,info.Id)
			if i<curIndex then
				self.uiPanel:SetStateByIndex(i,self.stateNormalBg,1)
			elseif i== curIndex then
				if bIsGetAll then
					--设置黄色提示和红点儿的背景圆点儿
					self.uiPanel:SetStateByIndex(curIndex,self.stateFinishBg,2)
				else
					--设置黄色提示和红点儿的背景圆点儿
					self.uiPanel:SetStateByIndex(curIndex,self.stateFinishBg,3)
				end
			else
				self.uiPanel:SetStateByIndex(i,self.stateNormalBg,0)
			end
		end
	end
end

function DailyTaskController:onDestroy()
    EventMgr:removeAll(EventID.DAILY_TASK)
	if self.uiPanel then
		self.uiPanel:onDestroy()
		self.uiPanel = nil
	end
    
	if self.loopTimer > 0 then
		TimeMgr:DestroyTimer(self.loopTimer)
		self.loopTimer = 0
	end
end


return DailyTaskController