local UI_UnionBossRank = Class(BaseView)

local SlideRect = require("UI.Common.SlideRect");
local BaseSlideItem = require("UI.Common.BaseSlideItem");
local RankItem = Class(BaseSlideItem);

function UI_UnionBossRank:OnInit()
    EventMgr:Add(EventID.UNION_ID_CHANGE, self.Close, self);
end

function UI_UnionBossRank:OnCreate(param)
    self.headObj = CreateCommonHead(self.ui.m_goMyRank, 0.5, Vector2(-280, 0));
    
    local itemList = {};
    local itemTrans = self.ui.m_goRank.transform;
    for i = 1, 6 do
        local item = RankItem.new();
        item:Init(CreateGameObject(itemTrans));
        table.insert(itemList, item);
    end

    self.slideRect = SlideRect.new();
    self.slideRect:Init(self.ui.m_scrollview, 2);
    self.slideRect:SetItems(itemList, 15, Vector2.New(0, 2));

    LeagueManager:OnRequestBossRankDamage(function(respData)
        local data = respData.list;
        local selfRankInfo = data.self;
        if selfRankInfo then
            local obj = self.ui.m_goMyRank;
            local rankTxt = GetChild(obj, "rankTxt", UEUI.Text);
            local rankImg = GetChild(obj, "rankImg", UEUI.Image);
            local nameTxt = GetChild(obj, "nameTxt", UEUI.Text);
            local challengeTxt = GetChild(obj, "challengeBg/challengeTxt", UEUI.Text);
            local hurtTxt = GetChild(obj, "hurtBg/hurtTxt", UEUI.Text);

            local rankInfo = selfRankInfo.rank;
            local rank = tonumber(tostring(rankInfo.rank));
            local isShow = rank > 0 and rank <= 3;
            if isShow then
                SetUIImage(rankImg, "Sprite/ui_huodongjingsai/paihang_win4_icon" .. rank .. ".png", false);
            end
            SetActive(rankImg, isShow);

            if rank > 0 then
                rankTxt.text = rank;
            else
                rankTxt.text = LangMgr:GetLang(9056);
            end

            challengeTxt.text = rankInfo.challenge_count;
            hurtTxt.text = NumToGameString(rankInfo.damage);

            local playerInfo = selfRankInfo.player;
            nameTxt.text = playerInfo.name;
            SetHeadAndBorderByGo(self.headObj, playerInfo.icon, playerInfo.border);
        end
        SetActive(self.ui.m_goMyRank, selfRankInfo ~= nil);

        local rankList = data.list;
        if rankList then
            self.slideRect:SetData(rankList);
        end
        SetActive(self.ui.m_goNone, #rankList <= 0);
    end)
end

function UI_UnionBossRank:OnRefresh(param)
    
end

function UI_UnionBossRank:onDestroy()
    EventMgr:Remove(EventID.UNION_ID_CHANGE, self.Close, self);
    self.slideRect = nil;
end

function UI_UnionBossRank:onUIEventClick(go,param)
    local name = go.name
    if name == "closeBtn" then
        self:Close();
    end
end




----------------------------------- RankItem -----------------------------------
---
function RankItem:OnInit(transform)
    self.bg = GetChild(transform, "bg", UEUI.Image);
    self.rankTxt = GetChild(transform, "rankTxt", UEUI.Text);
    self.rankImg = GetChild(transform, "rankImg", UEUI.Image);
    self.nameTxt = GetChild(transform, "nameTxt", UEUI.Text);
    
    self.challengeBg = GetChild(transform, "challengeBg", UEUI.Image);
    self.challengeTitleTxt = GetChild(transform, "challengeBg/m_txtAuto70000341", UEUI.Text);
    self.challengeTxt = GetChild(transform, "challengeBg/challengeTxt", UEUI.Text);
    
    self.hurtBg = GetChild(transform, "hurtBg", UEUI.Image);
    self.hurtTitleTxt = GetChild(transform, "hurtBg/m_txtAuto70000342", UEUI.Text);
    self.hurtTxt = GetChild(transform, "hurtBg/hurtTxt", UEUI.Text);
    
    self.headObj = CreateCommonHead(transform, 0.5, Vector2(-280, 2));
end

function RankItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec;
end

function RankItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition;
end

function RankItem:UpdateData(data, index)
    local rankInfo = data.rank;
    local rank = tonumber(tostring(rankInfo.rank));
    self.rankTxt.text = rank;
    if rank <= 3 then
        SetUIImage(self.rankImg, "Sprite/ui_huodongjingsai/paihang_win4_icon" .. rank .. ".png", false);
    end
    SetActive(self.rankImg, rank <= 3);
    
    self.challengeTxt.text = rankInfo.challenge_count;
    self.hurtTxt.text = NumToGameString(rankInfo.damage);

    local playerInfo = data.player;
    self.nameTxt.text = playerInfo.name;
    
    local playerId = NetUpdatePlayerData:GetPlayerID();
    if v2s(playerInfo.id) == v2s(playerId) then
        SetUIImage(self.bg, "Sprite/ui_paihangbang/paihangbang_dilang2.png", false);
        self.rankTxt.color = GetColorByHex("1b72cb");
        UnifyOutline(self.nameTxt.gameObject, "245598");
        
        SetUIImage(self.challengeBg, "Sprite/ui_paihangbang/paihangbang_dilang3_1.png", false);
        self.challengeTitleTxt.color = GetColorByHex("4071b4");
        UnifyOutline(self.challengeTxt.gameObject, "245598");

        SetUIImage(self.hurtBg, "Sprite/ui_paihangbang/paihangbang_dilang3_1.png", false);
        self.hurtTitleTxt.color = GetColorByHex("4071b4");
        UnifyOutline(self.hurtTxt.gameObject, "245598");
    else
        SetUIImage(self.bg, "Sprite/ui_paihangbang/paihangbang_dilang1.png", false);
        self.rankTxt.color = GetColorByHex("aa4f01");
        UnifyOutline(self.nameTxt.gameObject, "a13a3d");
        
        SetUIImage(self.challengeBg, "Sprite/ui_paihangbang/paihangbang_dilang1_1.png", false);
        self.challengeTitleTxt.color = GetColorByHex("c04e0d");
        UnifyOutline(self.challengeTxt.gameObject, "ae4a00");

        SetUIImage(self.hurtBg, "Sprite/ui_paihangbang/paihangbang_dilang1_1.png", false);
        self.hurtTitleTxt.color = GetColorByHex("c04e0d");
        UnifyOutline(self.hurtTxt.gameObject, "ae4a00");
    end
    
    SetHeadAndBorderByGo(self.headObj, playerInfo.icon, playerInfo.border, function()
        if v2s(playerInfo.id) ~= v2s(playerId) then
            FriendManager:ShowPlayerById(playerInfo.id);
        end
    end);
end

return UI_UnionBossRank