local RoleSpineConfig = Class(_BaseConfig)
local M = RoleSpineConfig

-------------------------------------------------------

function M:ctor()

end

-------------------------------------------------------
function M:OnReadConfig()
	self.spineCache = {}
	for _, v in pairs(self.data) do
		local item = {}
		if v.is_picture then
			item.img = v.img_spine
		else
		 
			-- 需要删除 	item.spine 
			item.spine_path = v.img_spine
		end
		item.mess = v
		item.straight_alpha = v.straight_alpha
		self.spineCache[v.transfer] = item
	end
end

function M:GetSkeletonDataById(id)
	return self.spineCache[id]
end

function M:SetSpineForObj(obj,name)
	local spine = GetComponent(obj,CS.Spine.Unity.SkeletonAnimation)
	if spine then
		self:SetSpineByName(name)
	else
		return nil
	end
end

function M:SetSpineByName(spine,spineName,callBack)
	local item = self:GetSkeletonDataById(spineName)
 	ResMgr:LoadAssetAsync(item.spine_path,AssetDefine.LoadType.Instant,function (obj)
			if spine and obj then
				spine.skeletonDataAsset = obj
				spine:Initialize(true)
				if callBack then
					callBack()
				end
			end
		end)
end


return M