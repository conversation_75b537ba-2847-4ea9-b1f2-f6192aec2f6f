local UI_KeepCatRank = Class(BaseView)
local ItemBase = require("UI.Common.BaseSlideItem")
local RankItem = Class(ItemBase)
local ItemShowNums = 9
function UI_KeepCatRank:OnInit()
    
end

function UI_KeepCatRank:OnCreate(serverData,isPush,activityId,playerData,myselfData,noRank)
    local icon = "Sprite/ui_activity_maomi/maomi1_maobi.png"
    self.ui.m_txtDesc.text = LangMgr:GetLangFormat(51040070, icon)
    local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.KeepCat)
    self.activityItem = activityItem
    if noRank then
        SetActive(self.ui.m_goNoRank,true)
        SetActive(self.ui.m_goMyItemRank,false)
        SetActive(self.ui.m_goRankScroll,false)

        -- 上榜积分
        local score = KeepCatManager:GetRankPoint()
        self.ui.m_txtNoRank.text = LangMgr:GetLangFormat(90200120,score)
    else
        SetActive(self.ui.m_goRankScroll,true)
        SetActive(self.ui.m_goNoRank,false)
        SetActive(self.ui.m_goMyItemRank,true)
    end
	CreateCommonHead(GetChild(self.ui.m_goItemRank,"head").transform,0.4)
	CreateCommonHead(GetChild(self.ui.m_goMyItemRank,"head").transform,0.4)
	
    self.playAni = false
    self.isPush = isPush
    self.airIndex = 1
    if not noRank then
        if self.isPush then
            if activityId then
                --self.active = LimitActivityController:GetActiveMessage(activityId)
                self.playerListData = playerData
                self.myselfData = myselfData
            end
            self.playAni = true
        else
            self.playerListData = serverData.ranking
            self.myselfData = serverData.player
        end

        local transform = self.uiGameObject.transform
        self.slider = require("UI.Common.SlideRect").new()
        self.slider:Init(transform:Find("bg/m_goRankScroll/ViewPort"):GetComponent(typeof(UEUI.ScrollRect)),2)
        self.playerList = {}
        for i = 1, ItemShowNums do
            self.playerList[i] = RankItem.new()
            self.playerList[i]:Init(UEGO.Instantiate(self.ui.m_goItemRank.transform))
        end
        self.slider:SetItems(self.playerList,5,Vector2.New(-3,0))
        self.slider:SetData(self.playerListData,self.myselfData.rank + 20)

        self:SetMyself()

        -- 结算
        if self.playAni then
            local rankReward = KeepCatManager:GetRankRewardConfig(v2n(self.myselfData.ranking)).reward
            local rewardT = {}
            rankReward = string.split(rankReward,";")
            for k, v in pairs(rankReward) do
                local t = {}
                local arr = string.split(v,"|")
                t[1] = v2n(arr[1])
                t[2] = v2n(arr[2])
                table.insert(rewardT,t)
            end
            UI_SHOW(UIDefine.UI_ItemRankFinishReward, rewardT,5)
        end

        self.slider:MoveToIndex(self.myselfData.rank,2)
    end
    self:SetIsUpdateTick(true)
end

function UI_KeepCatRank:SetMyself()
    local rank = v2n(self.myselfData.rank)
    if self.isPush then
        rank = v2n(self.myselfData.ranking)
    end
    if rank <= 3 then
        local rankSprite
        if rank == 1 then
            rankSprite = "Sprite/ui_activity_maomi/maomi5_cup1.png"
        elseif rank == 2 then
            rankSprite = "Sprite/ui_activity_maomi/maomi5_cup2.png"
        elseif rank == 3 then
            rankSprite = "Sprite/ui_activity_maomi/maomi5_cup3.png"
        end
        if rankSprite then
            SetUIImage(self.ui.m_imgRank,rankSprite,false)
            SetActive(self.ui.m_imgRank.gameObject,true)
        end
    else
        SetActive(self.ui.m_imgRank.gameObject,false)
    end
    self.ui.m_txtRank.text = rank
    self.ui.m_txtScore.text = self.myselfData.point

    local rankReward = KeepCatManager:GetRankRewardConfig(rank).reward
    local rewardT = string.split(rankReward,";")
    local index = table.count(rewardT)

    self.m_rewardCellList = {}
    for i, v in ipairs(rewardT) do
        if self.m_rewardCellList[i] == nil then
            self.m_rewardCellList[i] = {}
            local obj =	UEGO.Instantiate(self.ui.m_goReward,self.ui.m_goRewardList.transform)
            self.m_rewardCellList[i].obj = obj
            local icon = GetChild(obj,"icon",UEUI.Image)
            local num = GetChild(obj,"num",UEUI.Text)
            self.m_rewardCellList[i].icon = icon
            self.m_rewardCellList[i].num = num
        end
    end

    for k, v in pairs(self.m_rewardCellList) do
        if index >= k then
            local config = rewardT[k]
            local arr = string.split(config,"|")
            local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
            local num = v2n(arr[2])
            SetActive(v.obj,true)

            SetUIImage(v.icon, ItemConfig:GetIcon(itemId), false)
            v.num.text = "x"..num

            local btn = v.icon.gameObject.transform:GetComponent(typeof(UEUI.Button))
            RemoveUIComponentEventCallback(btn,UEUI.Button)
            AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
                if itemId > 0  then
                    UI_SHOW(UIDefine.UI_ItemTips,itemId)
                end
            end)
        else
            SetActive(v.obj,false)
        end
    end

    local playerHead = NetUpdatePlayerData:GetPlayerInfo().head
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerHead)
    --if headConfig then
        --SetUIImage(self.ui.m_imgHead, headConfig.icon, false)
    --end
	local customHeadObj = GetChild(self.ui.m_goMyItemRank,"head/CustomHead")
	SetMyHeadAndBorderByGo(customHeadObj)
	
    self.ui.m_txtName.text = NetUpdatePlayerData:GetPlayerInfo().name
end

function UI_KeepCatRank:OnRefresh(param)
    if param == 1 then
        --结算奖励
        SetActive(self.ui.m_goTarget,true)
        self:FlyMidToAir()
    end
end

function UI_KeepCatRank:TickUI(deltaTime)
    if not self.activityItem then
        return
    end
    local time = self.activityItem:GetRemainingTime()
    if time and time > 0 then
        self.ui.m_txtTime.text = TimeMgr:BaseTime(time, 3, 2)
    else
        self.ui.m_txtTime.text = LangMgr:GetLang(2203101)
    end

end

function UI_KeepCatRank:FlyMidToAir(callBack)
    --self.myselfData.ranking
    if nil == self.myselfData.ranking then
        return
    end
    local rankReward = KeepCatManager:GetRankRewardConfig(v2n(self.myselfData.ranking)).reward
    local strArr = string.split(rankReward,";")
    for k, v in pairs(strArr) do
        local arr = string.split(v,"|")
        local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
        local num = v2n(arr[2])

        if self.airIndex <= 5 then
            local rewardGo      = GetChild(self.ui.m_goPanel,"pos" .. self.airIndex ,UEUI.Image)
            self.airIndex = self.airIndex + 1


            SetActive(rewardGo,true)
            SetImageSprite(rewardGo,ItemConfig:GetIcon(itemId),false)
        end
        local assetPath     = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "COM_FlyItem")
        ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant,function(itemListRes)
				local endPos = GetUIPosByVector3(self.ui.m_goTarget.transform)
				local go     = UEGO.Instantiate(itemListRes)	
				if go then
					local goIcon = GetComponent(go,UEUI.Image)
					go.transform:SetParent(self.ui.m_goMidPlant.transform)
					go.transform.localPosition = Vector3(0,0,0)
					go.transform.localScale = Vector3.New(1, 1, 1)
					SetImageSprite(goIcon,ItemConfig:GetIcon(itemId),false)
					SetUISize(go,100,100)
					go.transform:SetParent(self.ui.m_goPanel.transform)
					DOLocalMove(go.transform,Vector3(0,0,0),k*0.4,function ()
							UEGO.Destroy(go)
							if callBack then
								callBack()
							end
							callBack = nil
						end)
				end
		end)            
    end
end

function UI_KeepCatRank:onDestroy()
    self:SetIsUpdateTick(false)
    Tween.Kill("AutoMoveFunc")
    -- 排行榜滑动动画
    if self.isPush then
        NetPushViewData:RemoveViewByIndex(PushDefine.UI_KeepCatRank)
        NetPushViewData:CheckOtherView(true)
        if self.activityItem then
            if self.activityItem:IsActivityEnd() then
                local rankReward = KeepCatManager:GetRankRewardConfig(v2n(self.myselfData.ranking)).reward
                NetGlobalData:GetRewardToMap(rankReward,"UI_KeepCatRank")
                self.activityItem:CloseActive()
            end
        end
    end
    if not IsTableEmpty(self.m_rewardCellList) then
        for i, v in ipairs(self.m_rewardCellList) do
            if v.obj then
                UEGO.Destroy(v.obj)
            end
        end
        self.m_rewardCellList = {}
    end
end

function UI_KeepCatRank:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnClose" then
        self:Close()
    end
end

------------- RankItem -----------------------------
function RankItem:OnInit(transform)
    local obj = transform
    self.trans				= transform
    self.bgImg				= GetChild(obj, "bg", UEUI.Image)
    self.img_rank			= GetChild(obj, "img_rank", UEUI.Image)
    self.scoreBg			= GetChild(obj, "scoreBg", UEUI.Image)
    self.img_head			= GetChild(obj, "head/img_head", UEUI.Image)
    self.txt_name			= GetChild(obj, "txt_name", UEUI.Text)
    self.txt_score			= GetChild(obj, "txt_score", UEUI.Text)
    self.txt_rank			= GetChild(obj, "txt_rank", UEUI.Text)
	self.img_headBG			= GetChild(obj, "head")
    self.rewardCellList = {}
    self.rewardGo = GetChild(obj, "goReward", UE.RectTransform)
    self.rewardListRoot = GetChild(obj, "rewardList/Viewport/Content")
    --local rewardList = GetChild(obj, "rewardList", UE.RectTransform)
    --for i = 1, 4 do
    --    local cell = {}
    --    local obj = GetChild(rewardList, "reward"..i, UE.RectTransform)
    --
    --    local icon = GetChild(obj,"icon",UEUI.Image)
    --    local num = GetChild(obj,"num",UEUI.Text)
    --    cell["obj"] = obj
    --    cell["icon"] = icon
    --    cell["num"] = num
    --    table.insert(self.rewardCellList,cell)
    --end
end

function RankItem:UpdateData(data,index)
    if not data then return end

    if data.rank <= 3 then
        local rankSprite
        if data.rank == 1 then
            rankSprite = "Sprite/ui_activity_maomi/maomi5_cup1.png"
        elseif data.rank == 2 then
            rankSprite = "Sprite/ui_activity_maomi/maomi5_cup2.png"
        elseif data.rank == 3 then
            rankSprite = "Sprite/ui_activity_maomi/maomi5_cup3.png"
        end
        if rankSprite then
            SetUIImage(self.img_rank,rankSprite,false)
            SetActive(self.img_rank.gameObject,true)
        end
    else
        SetActive(self.img_rank.gameObject,false)
    end

    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, data.icon)
    --if headConfig then
        --SetUIImage(self.img_head, headConfig.icon, false)
    --end
	local customHeadObj = GetChild(self.img_headBG,"CustomHead")
	SetHeadAndBorderByGo(customHeadObj,data.icon,data.border)

    local rankReward = KeepCatManager:GetRankRewardConfig(data.rank).reward
    local rewardT = string.split(rankReward,";")
    local count = table.count(rewardT)
    for k, v in ipairs(rewardT) do
        if self.rewardCellList[k] == nil then
            local obj =	UEGO.Instantiate(self.rewardGo,self.rewardListRoot.transform)
            self.rewardCellList[k] = {}
            self.rewardCellList[k].obj = obj
            local icon = GetChild(obj,"icon",UEUI.Image)
            local num = GetChild(obj,"num",UEUI.Text)
            self.rewardCellList[k].icon = icon
            self.rewardCellList[k].num = num
        end
    end
    for k, v in pairs(self.rewardCellList) do
        if count >= k then
            local config = rewardT[k]
            local rewardGo = v
            local arr = string.split(config,"|")
            local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
            local num = v2n(arr[2])
            SetActive(v.obj,true)
            SetUIImage(rewardGo.icon, ItemConfig:GetIcon(itemId), false)
            rewardGo.num.text = "x"..num
            if data.playerId then
                rewardGo.num.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("144691")
            else
                rewardGo.num.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("B6451D")
            end
            local btn = rewardGo.icon.gameObject.transform:GetComponent(typeof(UEUI.Button))
            RemoveUIComponentEventCallback(btn,UEUI.Button)
            AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
                if itemId > 0  then
                    UI_SHOW(UIDefine.UI_ItemTips,itemId)
                end
            end)
        else
            SetActive(v.obj,false)
        end
    end
    self.txt_name.text = data.name
    if data.playerId then
        --棕色
        SetUIImage(self.bgImg,"Sprite/ui_activity_maomi/maomi5_dikuang4.png",false)
        SetUIImage(self.scoreBg,"Sprite/ui_activity_maomi/dikuang5.png",false)

        self.txt_rank.text = string.format("<color=#144691>%s</color>", data.rank)
        self.txt_name.text = string.format("<color=#144691>%s</color>", data.name)
        self.txt_score.text = string.format("<color=#ffffff>%s</color>", data.point)
        self.txt_score.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("144691")
    else
        --蓝色
        self.txt_rank.text = string.format("<color=#933f1f>%s</color>", data.rank)
        self.txt_name.text = string.format("<color=#933f1f>%s</color>", data.name)
        self.txt_score.text = string.format("<color=#ffffff>%s</color>", data.point)
        SetUIImage(self.bgImg,"Sprite/ui_activity_maomi/maomi5_dikuang2.png",false)
        SetUIImage(self.scoreBg,"Sprite/ui_activity_maomi/maomi5_dikuang3.png",false)

        self.txt_score.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("B6451D")
    end
end

function RankItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function RankItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

return UI_KeepCatRank