local UI_OrderRecipe = Class()
local pre
function UI_OrderRecipe:OnInit()
    
end

function UI_OrderRecipe:OnCreate(obj)
	 
	local parent = GetChild(obj,"TableViewH/Viewport/m_goContent")
	local data = RecipeConfig:GetRecipeData()
	self.items = {}
	
	local function callBack(loadObj)
		if pre == nil then
			pre = loadObj
		end
		for i, v in ipairs(data) do
			local go = CreateGameObjectWithParent(pre,parent)

			local icon = GetChild(go,"icon",UEUI.Image)
			local btn = GetChild(go,"btn",UEUI.Image)


			local img = ItemConfig:GetIcon(v.itemid)
			SetUIImage(icon,img,false)
			table.insert(self.items,{obj = go,id = v.itemid})

			SetActive(GetChild(go,"red"),false)
			SetActive(GetChild(go,"count",UEUI.Text),false)
			if v.level > NetUpdatePlayerData:GetLevel() then
				SetActive(GetChild(go,"lv",UEUI.Text),true)
				GetChild(go,"lv",UEUI.Text).text = "LV:"..v.level

				SetUIImageGray(icon,true)
				RemoveUIComponentEventCallback(btn, UEUI.Button)
				AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
					if self.tipGo ~= nil then
						SetActive(self.tipGo , false)
					end
					--UI_SHOW(UIDefine.UI_Conversion,index,img,v.Count)
				end)
			else
				local ricepeData = v
				RemoveUIComponentEventCallback(btn, UEUI.Button)
				AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
					if arg1.name == "btn" then
						--if self.tipGo == nil or not self.tipGo.gameObject.activeSelf then
						self:LoadTips(obj,go,ricepeData)
						--else
						--	SetActive(self.tipGo , false)
						--end
					end
				end)

			end
		end
	end
	ResMgr:LoadAssetAsync("Assets/ResPackage/Prefab/UI/MaterialMod.prefab",AssetDefine.LoadType.Instant,callBack)
end

function UI_OrderRecipe:LoadTips(obj,go,ricepeData)
	local function refreshTip()
		local newpos = obj.transform:InverseTransformPoint(go.transform.position)
		self.tipGo.transform.localPosition = newpos
		self.tipGo.transform.localScale = Vector3.New(1,1,1)

		local scrollrect = GetChild(obj,"TableViewH",UEUI.ScrollRect)
		scrollrect.onValueChanged:RemoveAllListeners()
		scrollrect.onValueChanged:AddListener(function (value)
			if self.tipGo.gameObject.activeSelf then
				SetActive(self.tipGo , false)
			end
		end)

		for i = 1, 3 do
			local n = GET_UI(self.tipGo,"r"..i,TP(UEUI.Image))
			if i<=#ricepeData.needs then
				local img = ItemConfig:GetIcon(ricepeData.needs[i].Id)
				SetImageSprite(n,img,false)
				SetActive(n,true)
			else
				SetActive(n,false)
			end
		end
		SetActive(self.tipGo.transform, true)
	end
	
	self.tipGo = FineFromParent(obj, "OrderTip")
	if self.tipGo == nil then
		local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "OrderTip")
		local function callBack(loadObj)
			local cellObj = loadObj
			self.tipGo = UEGO.Instantiate(cellObj)
			self.tipGo.transform:SetParent(obj.transform)
			self.tipGo.name = "OrderTip"
			InitTextLanguage(self.tipGo)
			refreshTip()
		end
		ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant, callBack)
	else
		refreshTip()
	end
end

function UI_OrderRecipe:onDestroy()
    for _, v in ipairs(self.items) do
        UEGO.Destroy(v.obj)
    end
	self.tipGo = nil
    self.items = {}
end

return UI_OrderRecipe