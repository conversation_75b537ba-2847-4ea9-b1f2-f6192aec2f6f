local UI_UnionBossBest = Class(BaseView)

local SlideRect = require("UI.Common.SlideRect");
local BaseSlideItem = require("UI.Common.BaseSlideItem");
local RankItem = Class(BaseSlideItem);

local SELECT_TYPE;

function UI_UnionBossBest:OnInit()
    EventMgr:Add(EventID.UNION_ID_CHANGE, self.Close, self);
    self.selectBossIdx = 0;
    self.selectBossId = 0;
    self.btnItemList = {};
    
    SELECT_TYPE = 1;
end

function UI_UnionBossBest:OnCreate(param)
    local itemList = {};
    local itemTrans = self.ui.m_goRankItem.transform;
    for i = 1, 8 do
        local item = RankItem.new();
        item:Init(CreateGameObject(itemTrans));
        table.insert(itemList, item);
    end

    self.slideRect = SlideRect.new();
    self.slideRect:Init(self.ui.m_scrollviewRank, 2);
    self.slideRect:SetItems(itemList, 15, Vector2.New(0, 2));
    
    local list = LeagueManager.bossList;
    local count = #list;
    local num = #self.btnItemList;
    local len = count > num and count or num;
    local rootTrans = self.ui.m_scrollview.content;
    local index = 0;
    for i = 1, len do
        local item = self.btnItemList[i];
        if item == nil then
            item = CreateGameObjectWithParent(self.ui.m_goBtnItem, rootTrans);
            table.insert(self.btnItemList, item);
        end
        SetActive(item, i <= count);

        if i <= count then
            local bg = GetChild(item, "bg", UEUI.Image);
            local nameTxt = GetChild(item, "bg/nameTxt", UEUI.Text);
            local selectBg = GetChild(item, "selectBg", UEUI.Image);
            local selectTxt = GetChild(item, "selectBg/selectTxt", UEUI.Text);
            local icon = GetChild(item, "icon", UEUI.Image);

            local monsterHeroVo = LeagueManager:GetMonsterHeroVo(list[i].boss_id);
            if monsterHeroVo then
                local nameStr = monsterHeroVo:GetHeroName();
                nameTxt.text = nameStr;
                selectTxt.text = nameStr;
                SetUIImage(icon, "Sprite/ui_paihangbang/paihangbang_icon_slgrole_" .. monsterHeroVo.heroId .. ".png", false);

                RemoveUIComponentEventCallback(item, UEUI.Button);
                AddUIComponentEventCallback(item, UEUI.Button, function()
                    if self:OnSelectBtn(i, list[i].boss_id) then
                        self:OnUpdateRank();
                    end
                end);
            end
            SetActive(bg, true);
            SetActive(selectBg, false);

            if list[i].boss_id == param then
                index = i;
            end
        end
    end

    if index > 0 then
        self:OnSelectBtn(index, param);
        self:OnUpdateRank();
        self:StartToIndex(index);
    end
end

function UI_UnionBossBest:OnRefresh(param)
    
end

function UI_UnionBossBest:onDestroy()
    EventMgr:Remove(EventID.UNION_ID_CHANGE, self.Close, self);
    self.slideRect = nil;
end

function UI_UnionBossBest:onUIEventClick(go, param)
    local name = go.name
    if name == "closeBtn" then
        self:Close();
    elseif string.startswith(name, "m_tog") then
        if self.ui[name].isOn == false then return end
        
        local index = string.gsub(name, "m_tog", "");
        index = v2n(index);
        if index then
            if self:OnSelectType(index) then
                self:OnUpdateRank();
            end
        end
    end
end

function UI_UnionBossBest:OnSelectBtn(index, bossId)
    if self.selectBossIdx == index then
        return false;
    end
    
    if self.selectBossIdx > 0 then
        local item = self.btnItemList[self.selectBossIdx];
        local bg = GetChild(item, "bg", UEUI.Image);
        local selectBg = GetChild(item, "selectBg", UEUI.Image);
        SetActive(bg, true);
        SetActive(selectBg, false);
    end
    
    self.selectBossIdx = index;
    self.selectBossId = bossId;

    local item = self.btnItemList[index];
    local bg = GetChild(item, "bg", UEUI.Image);
    local selectBg = GetChild(item, "selectBg", UEUI.Image);
    SetActive(bg, false);
    SetActive(selectBg, true);

    return true;
end

function UI_UnionBossBest:OnSelectType(index)
    if SELECT_TYPE == index then
        return false;
    end
    SELECT_TYPE = index;
    return true;
end

function UI_UnionBossBest:OnUpdateRank()
    if self.selectBossId <= 0 or SELECT_TYPE <= 0 then return end
    LeagueManager:OnRequestBossRank(self.selectBossId, SELECT_TYPE, function(respData)
        local data = respData.list;
        local rankList = data.list;
        if rankList then
            self.slideRect:SetData(rankList);

            local count = #rankList;
            if count <= 0 then
                self.ui.m_txtTip.text = SELECT_TYPE == 1 and LangMgr:GetLang(70000347) or LangMgr:GetLang(70000348);
            end
            SetActive(self.ui.m_goTip, count <= 0);
        end
    end)
end

function UI_UnionBossBest:StartToIndex(index)
    if not index or index < 4 then
        return;
    end

    local rootTrans = self.ui.m_scrollview.content;
    local height = 698;
    local cellHeight = 160 - 10;
    local posY = 12 + (index - 1) * cellHeight;
    if posY > height then
        SetUIPos(rootTrans, 0, posY);
    end
end

----------------------------------- RankItem -----------------------------------
---
function RankItem:OnInit(transform)
    self.bg = GetChild(transform, "bg", UEUI.Image);
    self.rankTxt = GetChild(transform, "rankTxt", UEUI.Text);
    self.rankImg = GetChild(transform, "rankImg", UEUI.Image);
    self.unionObj = GetChild(transform, "unionObj");
    self.playerObj = GetChild(transform, "playerObj");

    self.unionTxt = GetChild(self.unionObj, "unionTxt", UEUI.Text);
    self.unionImg = GetChild(self.unionObj, "unionImg", UEUI.Image);
    self.numBg = GetChild(self.unionObj, "numBg", UEUI.Image);
    self.numTitleTxt = GetChild(self.unionObj, "numBg/m_txtAuto9265", UEUI.Text);
    self.numTxt = GetChild(self.unionObj, "numBg/numTxt", UEUI.Text);
    self.timeBg = GetChild(self.unionObj, "timeBg", UEUI.Image);
    self.timeTitleTxt = GetChild(self.unionObj, "timeBg/m_txtAuto70000346", UEUI.Text);
    self.timeTxt = GetChild(self.unionObj, "timeBg/timeTxt", UEUI.Text);

    self.nameTxt = GetChild(self.playerObj, "nameTxt", UEUI.Text);
    self.leagueIcon = GetChild(self.playerObj, "leagueIcon", UEUI.Image);
    self.leagueNameTxt = GetChild(self.playerObj, "leagueNameTxt", UEUI.Text);
    self.hurtBg = GetChild(self.playerObj, "hurtBg", UEUI.Image);
    self.hurtTitleTxt = GetChild(self.playerObj, "hurtBg/m_txtAuto70000342", UEUI.Text);
    self.hurtTxt = GetChild(self.playerObj, "hurtBg/hurtTxt", UEUI.Text);

    self.headObj = CreateCommonHead(self.playerObj, 0.5, Vector2(-280, 2));
end

function RankItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec;
end

function RankItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition;
end

function RankItem:UpdateData(data, index)
    local rankInfo = data.rank;
    local rank = tonumber(tostring(rankInfo.rank));
    self.rankTxt.text = rank;
    if rank <= 3 then
        SetUIImage(self.rankImg, "Sprite/ui_huodongjingsai/paihang_win4_icon" .. rank .. ".png", false);
    end

    local leagueInfo = data.league;
    local playerInfo = data.player;
    if SELECT_TYPE == 1 then
        self.unionTxt.text = leagueInfo.name;
        self.numTxt.text = leagueInfo.player_count;
        self.timeTxt.text = TimeMgr:CheckHMSNotEmpty(rankInfo.score);
        SetUIImage(self.unionImg, LeagueManager:GetUnionImageById(leagueInfo.icon), false);
        
        local leagueId = LeagueManager:GetMyLeagueId();
        if v2n(leagueInfo.id) == leagueId then
            SetUIImage(self.bg, "Sprite/ui_paihangbang/paihangbang_dilang2.png", false);
            self.rankTxt.color = GetColorByHex("1b72cb");
            UnifyOutline(self.unionTxt.gameObject, "245598");

            SetUIImage(self.numBg, "Sprite/ui_paihangbang/paihangbang_dilang3_1.png", false);
            self.numTitleTxt.color = GetColorByHex("4071b4");
            UnifyOutline(self.numTxt.gameObject, "245598");

            SetUIImage(self.timeBg, "Sprite/ui_paihangbang/paihangbang_dilang3_1.png", false);
            self.timeTitleTxt.color = GetColorByHex("4071b4");
            UnifyOutline(self.timeTxt.gameObject, "245598");
        else
            SetUIImage(self.bg, "Sprite/ui_paihangbang/paihangbang_dilang1.png", false);
            self.rankTxt.color = GetColorByHex("aa4f01");
            UnifyOutline(self.unionTxt.gameObject, "a13a3d");

            SetUIImage(self.numBg, "Sprite/ui_paihangbang/paihangbang_dilang1_1.png", false);
            self.numTitleTxt.color = GetColorByHex("c04e0d");
            UnifyOutline(self.numTxt.gameObject, "ae4a00");

            SetUIImage(self.timeBg, "Sprite/ui_paihangbang/paihangbang_dilang1_1.png", false);
            self.timeTitleTxt.color = GetColorByHex("c04e0d");
            UnifyOutline(self.timeTxt.gameObject, "ae4a00");
        end
    else
        self.leagueNameTxt.text = leagueInfo.name;
        self.nameTxt.text = playerInfo.name;
        self.hurtTxt.text = NumToGameString(rankInfo.score);
        SetUIImage(self.leagueIcon, LeagueManager:GetUnionImageById(leagueInfo.icon), false)

        local playerId = NetUpdatePlayerData:GetPlayerID();
        if v2s(playerInfo.id) == v2s(playerId) then
            SetUIImage(self.bg, "Sprite/ui_paihangbang/paihangbang_dilang2.png", false);
            self.rankTxt.color = GetColorByHex("1b72cb");
            self.leagueNameTxt.color = GetColorByHex("0758ab");
            UnifyOutline(self.nameTxt.gameObject, "245598");

            SetUIImage(self.hurtBg, "Sprite/ui_paihangbang/paihangbang_dilang3_1.png", false);
            self.hurtTitleTxt.color = GetColorByHex("4071b4");
            UnifyOutline(self.hurtTxt.gameObject, "245598");
        else
            SetUIImage(self.bg, "Sprite/ui_paihangbang/paihangbang_dilang1.png", false);
            self.rankTxt.color = GetColorByHex("aa4f01");
            self.leagueNameTxt.color = GetColorByHex("992e23");
            UnifyOutline(self.nameTxt.gameObject, "a13a3d");

            SetUIImage(self.hurtBg, "Sprite/ui_paihangbang/paihangbang_dilang1_1.png", false);
            self.hurtTitleTxt.color = GetColorByHex("c04e0d");
            UnifyOutline(self.hurtTxt.gameObject, "ae4a00");
        end
        
        SetHeadAndBorderByGo(self.headObj, playerInfo.icon, playerInfo.border, function()
            if v2s(playerInfo.id) ~= v2s(playerId) then
                FriendManager:ShowPlayerById(playerInfo.id);
            end
        end);
    end
    SetActive(self.unionObj, SELECT_TYPE == 1);
    SetActive(self.playerObj, SELECT_TYPE == 2);
end

return UI_UnionBossBest