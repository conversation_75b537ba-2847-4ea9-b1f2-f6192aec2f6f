function GetChild(root, path, component)
    if root == nil or path == nil then
        return nil
    end
    local transRes = root.transform:Find(path)
    if transRes == nil then
        return nil
    end

    local childObject = transRes.gameObject
    if component then
        local childObjectComponent = childObject:GetComponent(TP(component))
        return childObjectComponent
    end
    return childObject
end

function SetParent(go, parent)
    if go == nil or parent == nil then
        return
    end
    go.transform:SetParent(parent.transform)
end

function GetChildTrans(root, path)
    local transRes = root.transform:Find(path)
    if transRes == nil then
        return nil
    end
    return transRes
end

function AddChild(parentGo, childGO, isResetSize)
    if parentGo == nil or childGO == nil then
        return
    end
    childGO.transform:SetParent(parentGo.transform)
    if isResetSize == nil then
        isResetSize = false
    end
    ResetTransform(childGO, isResetSize)

    InitTextLanguage(childGO)
end

function TP(name)
    return typeof(name)
end

function AddUIComponentEvent(c)
    if c == nil then
        return
    end

    local go = c:GetUIGameObject()
    AddUIComponentEventByGo(c, go)
end

function AddUIComponentEventByGo(c, go)
    AddUIComponentEventCallback(
            go,
            UEUI.Button,
            function(arg1, arg2)
                if c.onUIEventClick then
                    c:onUIEventClick(arg1, arg2)
                end
            end
    )

    AddUIComponentEventCallback(
            go,
            UEUI.Toggle,
            function(arg1, arg2)
                if c.onUIEventClick then
                    c:onUIEventClick(arg1, arg2)
                end
            end
    )

    AddUIComponentEventCallback(
            go,
            UEUI.Dropdown,
            function(arg1, arg2)
                if c.onUIEventClick then
                    c:onUIEventClick(arg1, arg2)
                end
            end
    )

    -- AddUIComponentEventCallback(uiGameObject,UEUI.InputField,function (arg1, arg2)
    --     c:onUIEventClick(arg1,arg2)
    -- end)
end

--移除身上的射线事件
function RemoveUIComponentEventCallback(gameObject, componentType)
    if gameObject == nil then
        return
    end
    local coms = gameObject.transform:GetComponentsInChildren(TP(componentType), true)
    for i = 0, coms.Length - 1 do
        coms[i]:RemoveAllListeners()
    end
end

--param为button传递参数方便区分点击事件，可不传
function AddUIComponentEventCallback(gameObject, componentType, func, param)
    if gameObject == nil then
        return
    end
    local function EventCallback(arg1, arg2)
        local result = nil
        if func then
            if arg1.isOn then
                AudioMgr:Play(6)
            end
            result = func(arg1, arg2)
        end

        if result == nil or result == false then
            return
        end
    end

    local coms = gameObject.transform:GetComponentsInChildren(TP(componentType), true)
    for i = 0, coms.Length - 1 do
        if componentType == UEUI.Button then
            coms[i].onClick:AddListener(
                    function()
                        if (coms[i].name ~= UISpecialDefine.UIAutoCloseX) then
                            Log.Info(coms[i].name)
                            EventCallback(coms[i], param)
                        end
                    end
            )
        elseif componentType == UEUI.Toggle then
            coms[i].isOn = false
            coms[i].onValueChanged:AddListener(
                    function(isOn)
                        EventCallback(coms[i], isOn)
                    end
            )
        elseif componentType == UEUI.InputField then
            coms[i].onValueChanged:AddListener(
                    function(text)
                        EventCallback(coms[i], text)
                    end
            )
        elseif componentType == UEUI.Dropdown then
            coms[i].onValueChanged:AddListener(
                    function(value)
                        EventCallback(coms[i], value)
                    end
            )
        end
    end
end

function RemoveUIComponentEventCallback(gameObject)
    if gameObject == nil then
        return
    end

    local componentBtns = gameObject.transform:GetComponentsInChildren(TP(UEUI.Button), true)
    for i = 0, componentBtns.Length - 1 do
        componentBtns[i].onClick:RemoveAllListeners()
    end
    local componentTogs = gameObject.transform:GetComponentsInChildren(TP(UEUI.Toggle), true)
    for i = 0, componentTogs.Length - 1 do
        componentTogs[i].onValueChanged:RemoveAllListeners()
    end
    local componentInputs = gameObject.transform:GetComponentsInChildren(TP(UEUI.InputField), true)
    for i = 0, componentInputs.Length - 1 do
        componentInputs[i].onValueChanged:RemoveAllListeners()
    end
end

function SetUIComponentDefaultByName(com, comName)
    if string.contains(comName, "Image") then
        com.color = Color.New(0, 0, 0, 0)

    elseif string.contains(comName, "GraphicRaycaster") then
        com.ignoreReversedGraphics = false

    elseif string.contains(comName, "CanvasGroup") then
        com.alpha = 1

    end
end

function AddUIComponentEvents(model, func)
    AddUIComponentEventCallback(model, UEUI.Button, function(arg1, arg2)
        LuaCallback(func, arg1, arg2)
    end, false)

    AddUIComponentEventCallback(model, UEUI.Toggle, function(arg1, arg2)
        LuaCallback(func, arg1, arg2)
    end, false)

    AddUIComponentEventCallback(model, UEUI.Dropdown, function(arg1, arg2)
        LuaCallback(func, arg1, arg2)
    end, false)

    AddUIComponentEventCallback(model, UEUI.InputField, function(arg1, arg2)
        LuaCallback(func, arg1, arg2)
    end)
end

--传递text组件判断是否自动化生成多语言
function SetTextAuto(textCom)
    if textCom == nil then
        return
    end
    local name = textCom.gameObject.name
    local _, index = string.find(name, "m_txtAuto")
    if index then
        local langID = string.sub(name, index + 1)
        if langID == "" then
            langID = textCom.text
        end
        textCom.text = LangMgr:GetLang(langID)
    end
end

function SetTextAutoSDK(textCom)
    if textCom == nil then
        return
    end
    local name = textCom.gameObject.name
    local _, index = string.find(name, "pretxt")
    if index then
        local langID = string.sub(name, index + 1)
        if langID == "" then
            langID = textCom.text
        end
        textCom.text = LangMgr:GetSDKLang(langID)
    end
end

function InitUITextSDK(obj)
    local coms = obj.transform:GetComponentsInChildren(TP(UEUI.Text), true)
    for i = 0, coms.Length - 1 do
        SetTextAutoSDK(coms[i])
        SetTextAuto(coms[i])
    end
end

function InitTextLanguage(obj)
    local coms = obj.transform:GetComponentsInChildren(TP(UEUI.Text), true)
    for i = 0, coms.Length - 1 do
        SetTextAuto(coms[i])
    end
end

function GetComponent(go, componentType)
    if go == nil or componentType == nil then
        Log.Error("GameObject is nil")
        return nil
    end
    return go:GetComponent(TP(componentType))
end

function GetAndAddComponent(go, componentType)
    local componentObject = GetComponent(go, componentType)
    if componentObject ~= nil then
        return componentObject
    end
    return go.gameObject:AddComponent(TP(componentType))
end

function GetComponentsInChildren(go, componentType)
    if go == nil or componentType == nil then
        return nil
    end
    local coms = go:GetComponentsInChildren(TP(componentType))
    if coms and GetTableLength(coms) > 0 then
        return coms
    end
    return nil
end

function ResetTransform(obj, isResetSize)
    if obj == nil then
        return
    end
    local rectTransform = GetComponent(obj.gameObject, UE.RectTransform)
    if isResetSize == nil or isResetSize == true then
        rectTransform.offsetMax = Vector2.zero
        rectTransform.offsetMin = Vector2.zero
    end
    rectTransform.localScale = Vector3.one
    rectTransform.anchoredPosition3D = Vector3.zero

    return rectTransform
end

function ResetRectTransform(go, pos, scale, offsetMin, offsetMax, anchorMin, anchorMax)
    if go then
        if not pos then
            pos = Vector3.New(0, 0, 0)
        end
        if not scale then
            scale = Vector3.New(1, 1, 1)
        end
        if not offsetMin then
            offsetMin = Vector2.New(0, 0)
        end
        if not offsetMax then
            offsetMax = Vector2.New(0, 0)
        end
        if not anchorMin then
            anchorMin = Vector2.New(0, 0)
        end
        if not anchorMax then
            anchorMax = Vector2.New(1, 1)
        end
        return GameUtil.GetRectTransformWithOffsetAndAnchor(go, pos.x, pos.y, pos.z, scale.x, scale.y, scale.z, offsetMin.x, offsetMin.y, offsetMax.x, offsetMax.y, anchorMin.x, anchorMin.y, anchorMax.x, anchorMax.y)
    end
end

function IsUIElem(name)
    local ret = string.find(name, "UI_")
    if not ret then
        return false
    end
    return true
end

function SetActive(go, isActive)
    if IsNil(go) then
        return
    end
    if go.gameObject.activeSelf ~= isActive then
        go.gameObject:SetActive(isActive)
    end
end

--------------------------------------------------------------GGStart---------------------------------------------------------------

--[[
    @desc:通过富文本控制字体颜色
    author:{zhai}
    time:2020-10-14 16:57:29
    --@text:text组件
	--@hexColor:颜色值
	--@str: 文本内容
]]
function SetRichColorText(text, hexColor, str)
    text.text = string.format("<color=#%s>%s</color>", hexColor, str)
end

--spriteimage
--transformName 缓存池中的一级Key
--image UI组件
--imageIconPath 资源路径（可以是全路径，也可以是半路径）
--assetLoadType 加载模式
--isNativeSize 是否重置大小
function SetImageSprite(image, imageIconPath, isNativeSize, callBack)
    local assetLoadType = AssetDefine.LoadType.Sprite
    if _G.IsNilOrEmpty(imageIconPath) then
        Log.Error("[UIUtility] <SetImageSprite> params imageIconPath is nil!", imageIconPath)
        return
    end
    --Sprite的模式只做SpriteAtlas的存储，不做sprite元数据的存储
    -- PoolMgr:CreateCacheObject(transformName,imageIconPath,assetLoadType,false,function (spriteObj)
    --     image.sprite = spriteObj
    --     if isNativeSize then
    --         image:SetNativeSize()
    --     end
    -- end)
    if isNativeSize == nil then
        isNativeSize = true
    end

    if IsSameImage(image, imageIconPath) then
        if isNativeSize then
            image:SetNativeSize()
        end
        if callBack then
            callBack(image.sprite)
        end
        return
    end

    local function fun(spriteAsset)
        if ObjIsNil(image) then
            Log.Info("Has been destroyed and cannot be set")
            return
        end
        image.sprite = spriteAsset
        if isNativeSize then
            image:SetNativeSize()
        end
        if callBack then
            callBack(spriteAsset)
        end
    end
    ResMgr:LoadAssetAsync(imageIconPath, assetLoadType, fun)
end
--
function SetImgSpriteById(image, id, isNativeSize, callBack)
    local assetLoadType = AssetDefine.LoadType.Sprite

    if isNativeSize == nil then
        isNativeSize = true
    end

    local function funcW(imageIconPath)
        if imageIconPath == nil then
            return
        end
        local function fun(spriteAsset)
            if ObjIsNil(image) then
                Log.Info("Has been destroyed and cannot be set")
                return
            end
            image.sprite = spriteAsset
            if isNativeSize then
                image:SetNativeSize()
            end
            if callBack then
                callBack(spriteAsset)
            end
        end
        ResMgr:LoadAssetAsync(imageIconPath, assetLoadType, fun)
    end

    ConfigMgr:GetDataAsync(ConfigDefine.ID.item, function(data)
        if (data[id]) then
            funcW(data[id].img)
        else
            return
        end
    end)
end

function SetImageSpriteByUrl(image, url, isNativeSize, callBack)
    if not GameUtil.LoadSpriteAsyncByUrl then
        return
    end
    assert("function" == type(GameUtil.LoadSpriteAsyncByUrl))

    GameUtil.LoadSpriteAsyncByUrl(url, function(spriteAsset)
        if not IsNil(image) then
            image.sprite = spriteAsset
            if isNativeSize then
                image:SetNativeSize()
            end
        end
        if callBack then
            callBack(spriteAsset)
        end
    end)
end

function ObjIsNil(obj)
    return obj == nil or IsNil(obj) --or obj:Equals(nil)
end

--texture
function SetTexture(tex, texPathName, isNativeSize,callBack)
    local assetLoadType = AssetDefine.LoadType.Texture2D
    if _G.IsNilOrEmpty(texPathName) then
        Log.Error("[UIUtility] <SetTexture> params texPathName is nil!")
        return
    end
    if _G.IsNilOrEmpty(texPathName) then
        Log.Error("[UIUtility] <SetTexture> params texPathName is nil!")
        return
    end
    -- PoolMgr:CreateCacheObject(transformName,texPathName,assetLoadType,false,function (texObj)
    --     tex.texture = texObj
    --     if isNativeSize then
    --         tex:SetNativeSize()
    --     end
    -- end)

    ResMgr:LoadAssetAsync(
            texPathName,
            assetLoadType,
            function(textureAsset)
                tex.texture = textureAsset
                if isNativeSize then
                    tex:SetNativeSize()
                end
                if callBack then
                    callBack()
                end
                --直接卸载元数据，从第二次加载 ，每次都是从AB里面取
                --_G.CS.UnityEngine.Resources.UnloadAsset(textureAsset)
            end
    )
end

--paramType : 0 int  1 string 2 string2 3 gameobject 4 gameobject2
function GetControlExpand(go, paramType)
    if go == nil then
        return nil
    end

    local function GetParam(obj, t)
        if (obj == nil) then
            return nil
        end

        local expand = GetComponent(obj, CS.ControlExpand)
        if expand then
            if t == 0 then
                return expand.ParamInt
            elseif t == 1 then
                return expand.ParamString
            elseif t == 2 then
                return expand.ParamString2
            elseif t == 3 then
                return expand.ParamObject
            elseif t == 4 then
                return expand.ParamObject2
            end
        end
        return nil
    end

    local param = GetParam(go, paramType)
    if param then
        return param
    else
        return GetParam(go.transform.parent, paramType)
    end
end

function SetControlExpand(go, paramValue, paramType)
    if go == nil then
        return nil
    end

    local function SetParam(obj, val, t)
        if (obj == nil) then
            return false
        end

        local expand = GetComponent(obj, CS.ControlExpand)
        if expand then
            if t == 0 then
                expand.ParamInt = v2n(val)
            elseif t == 1 then
                expand.ParamString = v2s(val)
            elseif t == 2 then
                expand.ParamString2 = v2s(val)
            elseif t == 3 then
                expand.ParamObject = val
            elseif t == 4 then
                expand.ParamObject2 = val
            end
            return true
        end
        return false
    end

    local param = SetParam(go, paramValue, paramType)
    if not param then
        SetParam(go.transform.parent, paramValue, paramType)
    end
end

function SetUIText(obj, str)
    if obj == nil then
        Log.Error("[UIUtility] function <SetUIText> params obj is nil")
        return
    end
    local text_com = obj
    if _G.TP(UEUI.Text) ~= _G.TP(obj) then
        text_com = obj.gameObject:GetComponent(_G.TP(UEUI.Text))
    end
    text_com.text = tostring(str)
end

function GetUIText(obj)
    if obj == nil then
        Log.Error("[UIUtility] function <GetUIText> params obj is nil")
        return nil
    end
    local text_com = obj
    if _G.TP(UEUI.Text) ~= _G.TP(obj) then
        text_com = obj.gameObject:GetComponent(_G.TP(UEUI.Text))
    end

    return text_com.text
end

--imagePath 全路径或者半路径
function SetUIImage(obj, imagePath, isNativeSize, callBack)
    if obj == nil then
        Log.Error("[UIUtility] function <SetUIImage> params obj is nil")
        return nil
    end
    if imagePath == nil then
        Log.Error("[UIUtility] function <SetUIImage> params imagePath is nil", imagePath)
        return nil
    end
    local tImage = obj
    if _G.TP(UEUI.Image) ~= _G.TP(obj) then
        tImage = obj.gameObject:GetComponent(_G.TP(UEUI.Image))
    end
    SetImageSprite(tImage, imagePath, isNativeSize, callBack)
    return tImage
end

function SetImageSync(image, imagePath, isNativeSize)
    if not image then
        Log.Error("[UIUtility] function <SetImageSync> params image is nil", imagePath)
        return
    end
    local function nativeSize(image, isNativeSize)
        if isNativeSize == nil then
            isNativeSize = true
        end
        if isNativeSize then
            image:SetNativeSize()
        end
    end
    if IsSameImage(image, imagePath) then
        nativeSize(image, isNativeSize)
        return
    end

    local function fun(spriteAsset)
        if ObjIsNil(image) then
            Log.Info("Has been destroyed and cannot be set")
            return
        end
        image.sprite = spriteAsset
        nativeSize(image, isNativeSize)
    end
    ResMgr:LoadAssetAsync(imagePath, AssetDefine.LoadType.Sprite, fun)


    --nativeSize(image, isNativeSize)
end

function IsSameImage(image, imagePath)
    if not image.sprite then
        return false
    end
    local name = image.sprite.name
    local cloneName = "(Clone)"
    if string.contains(name, cloneName) then
        name = string.sub(name, 1, string.len(name) - 7)
    end
    if imagePath == name then
        return true
    end
    return false
end

function SetUIImageMaskGray(obj, bIsGray)
    if obj == nil then
        Log.Error("[UIUtility] function <SetUIImageGray> params obj is nil")
        return
    end
    --local tImage = obj
    --if _G.TP(UEUI.Image) ~= _G.TP(obj) then
    --tImage = obj.gameObject:GetComponent(_G.TP(UEUI.Image))
    --end

    --if bIsGray == true then
    --tImage.material = GameUtil.GetMaterial("UI/UIMaskGray","shared")
    --else
    --tImage.material = GameUtil.GetMaterial("UI/UIMask","shared")
    --end
end

function SetUIImageGray(obj, bIsGray, bIsBlack, black)
    if obj == nil then
        Log.Error("[UIUtility] function <SetUIImageGray> params obj is nil")
        return
    end
    local tImage = obj
    if _G.TP(UEUI.Image) ~= _G.TP(obj) then
        tImage = obj.gameObject:GetComponent(_G.TP(UEUI.Image))
    end
    if nil == tImage.material then
        if bIsGray == true then
            local shaderGray = UE.Shader.Find("UIShader/UIShader_Gray")
            local material = UE.Material(shaderGray)
            if bIsBlack then
                material:SetFloat(UE.Shader.PropertyToID("_IsBlack"), black or 1)
            end
            tImage.material = material
        else
            local shaderDefault = UE.Shader.Find("UI/Default")
            local material = UE.Material(shaderDefault)
            tImage.material = material
        end
    else
        if bIsGray == true then
            if tImage.material.shader.name ~= "UIShader/UIShader_Gray" then
                local shaderGray = UE.Shader.Find("UIShader/UIShader_Gray")
                local material = UE.Material(shaderGray)
                if bIsBlack then
                    material:SetFloat(UE.Shader.PropertyToID("_IsBlack"), black or 1)
                end
                tImage.material = material
            end
        else
            if tImage.material.shader.name ~= "UI/Default" then
                local shaderDefault = UE.Shader.Find("UI/Default")
                local material = UE.Material(shaderDefault)
                tImage.material = material
            end
        end
    end
end

function SetUITextGray(obj, bIsGray)
    if obj == nil then
        Log.Error("[UIUtility] function <SetUIImageGray> params obj is nil")
        return
    end
    local txt = obj
    if _G.TP(UEUI.Text) ~= _G.TP(obj) then
        txt = obj.gameObject:GetComponent(_G.TP(UEUI.Text))
    end
    if nil == txt.material then
        if bIsGray == true then
            local shaderGray = UE.Shader.Find("UIShader/UIShader_Gray")
            local material = UE.Material(shaderGray)
            txt.material = material
        else
            local shaderDefault = UE.Shader.Find("UI/Default")
            local material = UE.Material(shaderDefault)
            txt.material = material
        end
    else
        if bIsGray == true then
            if txt.material.shader.name ~= "UIShader/UIShader_Gray" then
                local shaderGray = UE.Shader.Find("UIShader/UIShader_Gray")
                local material = UE.Material(shaderGray)
                txt.material = material
            end
        else
            if txt.material.shader.name ~= "UI/Default" then
                local shaderDefault = UE.Shader.Find("UI/Default")
                local material = UE.Material(shaderDefault)
                txt.material = material
            end
        end
    end
end

function GetUIImageIsGray(obj)
    if obj == nil then
        Log.Error("[UIUtility] function <GetUIImageIsGray> params obj is nil")
        return nil
    end
    local tImage = obj
    if _G.TP(UEUI.Image) ~= _G.TP(obj) then
        tImage = obj.gameObject:GetComponent(_G.TP(UEUI.Image))
    end
    if tImage == nil then
        return nil
    end
    if nil == tImage.material then
        return false
    else
        if tImage.material.shader.name ~= "UIShader/UIShader_Gray" then
            return true
        end
        return false
    end
end

--tComponent 可以是Image Text 等，主要是带有color属性的组件
function SetUIColor(tComponent, r, g, b, a)
    if nil == tComponent then
        Log.Error("[UIUtility] function <SetUIColor> params tComponent is nil")
        return
    end
    if nil ~= tComponent.color then
        local pColor = Color.New(r, g, b, a)
        tComponent.color = pColor
        return
    end
    Log.Error("[UIUtility] function <SetUIColor> params tComponent is not have <<color>> attribute!")
end

function SetUISize(tComponent, w, h)
    if nil == tComponent then
        Log.Error("[UIUtility] function <SetUISize> params tComponent is nil")
        return
    end
    if tComponent.gameObject then
        local rectTrans = tComponent.gameObject:GetComponent(_G.TP(UE.RectTransform))
        if rectTrans then
            rectTrans.sizeDelta = Vector2.New(w, h)
            return
        end
    end
    Log.Error("[UIUtility] function <SetUISize> params tComponent is not have <<sizeDelta>> attribute!")
end

function GetUISize(tComponent)
    if nil == tComponent then
        Log.Error("[UIUtility] function <SetUISize> params tComponent is nil")
        return nil
    end
    if tComponent.gameObject then
        local rectTrans = tComponent.gameObject:GetComponent(_G.TP(UE.RectTransform))
        if rectTrans then
            return rectTrans.sizeDelta
        end
    end
    return nil
end

function SetUIZPos(obj, gridX, gridY)
    if nil == obj then
        return
    end
    if obj.transform then
        local tf = obj.transform
        local tX, tY = tf:GetLocalPosition()
        tf:SetLocalPosition(tX, tY, GetGridZ(gridX, gridY))
        return
    end
    Log.Error("[UIUtility] function <SetUIZPos> params obj is not have <<Transform>> attribute!")
end

function SetUIPos(obj, x, y)
    if nil == obj then
        return
    end
    if obj.gameObject then
        local rectTrans = obj.gameObject:GetComponent(_G.TP(UE.RectTransform))
        if rectTrans then
            rectTrans.anchoredPosition = Vector2.New(x, y)
            return
        end
    end
    Log.Error("[UIUtility] function <SetUIPos> params obj is not have <<RectTransform>> attribute!")
end

function SetUIPosByVector2(obj, pos)
    if nil == obj then
        Log.Error("[UIUtility] function <SetUISize> params obj is nil")
        return
    end
    if obj.gameObject then
        local rectTrans = obj.gameObject:GetComponent(_G.TP(UE.RectTransform))
        if rectTrans then
            rectTrans.anchoredPosition = pos
            return
        end
    end
    Log.Error("[UIUtility] function <SetUIPos> params obj is not have <<RectTransform>> attribute!")
end

function GetUIPos(obj)
    if nil == obj then
        Log.Error("[UIUtility] function <SetUISize> params obj is nil")
        return nil
    end
    if obj.gameObject then
        local rectTrans = obj.gameObject:GetComponent(_G.TP(UE.RectTransform))
        if rectTrans then
            return rectTrans.anchoredPosition
        end
    end
    return nil
end

function GetUIPosByVector3(obj)
    local curPos = GetUIPos(obj)
    return Vector3(curPos.x, curPos.y, 0)
end

function SetUIAnchors(obj, minX, minY, maxX, maxY)
    if nil == obj then
        Log.Error("[UIUtility] function <SetUIAnchors> params obj is nil")
        return
    end
    if obj.gameObject then
        local rectTrans = obj.gameObject:GetComponent(_G.TP(UE.RectTransform))
        if rectTrans then
            rectTrans.anchorMax = Vector2.New(maxX, maxY)
            rectTrans.anchorMin = Vector2.New(minX, minY)
            return
        end
    end
    Log.Error("[UIUtility] function <SetUIAnchors> params obj is not have <<RectTransform>> attribute!")
end

function SetUIPivot(obj, pivotX, pivotY)
    if nil == obj then
        Log.Error("[UIUtility] function <SetUIPivot> params obj is nil")
        return
    end
    if obj.gameObject then
        local rectTrans = obj.gameObject:GetComponent(_G.TP(UE.RectTransform))
        if rectTrans then
            rectTrans.pivot = Vector2.New(pivotX, pivotY)
            return
        end
    end
    Log.Error("[UIUtility] function <SetUIPivot> params obj is not have <<RectTransform>> attribute!")
end

function SetUIBtnEnable(obj, bIsEnable)
    if nil == obj then
        Log.Error("[UIUtility] function <SetUIBtnEnable> params obj is nil")
        return
    end
    if obj.gameObject then
        local tBtn = obj.gameObject:GetComponent(_G.TP(UEUI.Button))
        if tBtn then
            tBtn.interactable = bIsEnable
            return
        end
    end
    Log.Error("[UIUtility] function <SetUIBtnEnable> params obj is not have <<RectTransform>> attribute!")
end
function SetUIBtnGray(obj, bIsGray)
    if nil == obj then
        Log.Error("[UIUtility] function <SetUIBtnEnable> params obj is nil")
        return
    end
    if obj.gameObject then
        local tBtnImage = obj.gameObject:GetComponent(_G.TP(UEUI.Image))
        if tBtnImage then
            SetUIImageGray(tBtnImage, not bIsGray)
            return
        end
    end
    Log.Error("[UIUtility] function <SetUIBtnEnable> params obj is not have <<RectTransform>> attribute!")
end

function SetUIObjGray(obj, bIsGray)
    if nil == obj then
        Log.Error("[UIUtility] function <SetUIBtnEnable> params obj is nil")
        return
    end
    if obj.gameObject then
        local imgs = GET_OBJECT_CHILD(obj.transform, typeof(UEUI.Image))
        if imgs then
            for k, v in pairs(imgs) do
                SetUIImageGray(v, bIsGray)
            end
            return
        end
    end
    Log.Error("[UIUtility] function <SetUIBtnEnable> params obj is not have <<RectTransform>> attribute!")
end

function SetUIBtnGrayAndEnable(tBtn, tIsEnable, tIsText)
    local tOldEnable = tBtn.interactable
    if tOldEnable == tIsEnable then
        return
    end

    SetUIBtnEnable(tBtn, tIsEnable)
    local btnImage = tBtn.gameObject:GetComponent("Image")
    if btnImage then
        SetUIImageGray(btnImage, not tIsEnable)
    end
    if tIsText then
        local coms = tBtn.transform:GetComponentsInChildren(TP(UEUI.Text))
        for i = 0, coms.Length - 1 do
            if coms[i] then
                local textOut = coms[i].gameObject:GetComponent(typeof(UEUI.Outline))
                if textOut then
                    textOut.effectColor = tIsEnable and Color.HexToRGB(tIsText) or Color.HexToRGB("5d5d5d")
                end

                local shadow = coms[i].gameObject:GetComponent(typeof(CS.Coffee.UIEffects.UIShadow))
                if shadow and shadow.effectColor then
                    shadow.effectColor = tIsEnable and Color.HexToRGB(tIsText) or Color.HexToRGB("5d5d5d")
                end
            end
        end
    end
end
--主要是C#绑定在物体上的参数赋值
function SetUIParam(obj, tParam)
    if nil == obj then
        Log.Error("[UIUtility] function <SetUIParam> params obj is nil")
        return
    end
    if obj.gameObject then
        local paramSaver = obj.gameObject:GetComponent(_G.TP(_G.CS.ControlExpand))
        if paramSaver then
            local paramType = type(tParam)
            if paramType == "number" then
                paramSaver.ParamInt = tParam
            elseif paramType == "string" then
                if paramSaver.ParamString ~= tParam then
                    paramSaver.ParamString = tParam
                end
            elseif paramType == "userdata" then
                paramSaver.ParamObject = tParam
            else
                paramSaver.ParamInt = 0
                paramSaver.ParamString = ""
                paramSaver.ParamObject = nil
            end
            return
        end
    end
    Log.Error("[UIUtility] function <SetUIParam> params obj is not have <<ControlExpand>> attribute!")
end

--获取类型参数
--pType 参数顺序，0:number 1:string 2:userdata
function GetUIParam(obj, pType)
    if nil == obj then
        Log.Error("[UIUtility] function <SetUIParam> params obj is nil")
        return nil
    end
    if obj.gameObject then
        local paramSaver = obj.gameObject:GetComponent(_G.TP(_G.CS.ControlExpand))
        if pType == 0 then
            return paramSaver.ParamInt
        elseif pType == 1 then
            return paramSaver.ParamString
        elseif pType == 3 then
            return paramSaver.ParamObject
        end
    end
    return nil
end

--设置UI为层级（0 - childcount-1）
function SetUIIndex(obj, index)
    if nil == obj then
        Log.Error("[UIUtility] function <SetUIIndex> params obj is nil")
        return
    end
    if obj.transform then
        obj.transform:SetSiblingIndex(index)
        return
    end
    Log.Error("[UIUtility] function <SetUIIndex> params obj is not have <<Transform>> attribute!")
end

--设置UI为最上层(以属性面板为基准)
function SetUIFirstSibling(obj)
    if IsNil(obj) then
        Log.Error("[UIUtility] function <SetUIFirstSibling> params obj is nil")
        return
    end
    if obj.transform then
        obj.transform:SetAsFirstSibling()
        return
    end
    Log.Error("[UIUtility] function <SetUIFirstSibling> params obj is not have <<Transform>> attribute!")
end

--设置UI为最下层(以属性面板为基准)
function SetUILastSibling(obj)
    if nil == obj then
        Log.Error("[UIUtility] function <SetUILastSibling> params obj is nil")
        return
    end
    if obj.transform then
        obj.transform:SetAsLastSibling()
        return
    end
    Log.Error("[UIUtility] function <SetUILastSibling> params obj is not have <<Transform>> attribute!")
end

function GetComponentInLua(tTrans, componentName)
    if _G.g_MapTypeName == nil then
        _G.g_MapTypeName = {}
    end

    local typeID = _G.g_MapTypeName[componentName]
    if typeID == nil or IsNil(typeID) then
        local component = tTrans:GetComponent(componentName)
        if component then
            _G.g_MapTypeName[componentName] = component:GetType()
            return component
        end
        return nil
    end
    return tTrans:GetComponent(typeID)
end

function GET_UI(parentObj, name, component)
    -- body
    local tUi = nil
    if parentObj == nil or IsNil(parentObj) then
        return tUi
    end

    parentObj = parentObj.gameObject
    tUi = FineFromParent(parentObj, tostring(name))
    if component and tUi then
        tUi = GetComponentInLua(tUi, component)
    end
    if nil == tUi then
        Log.Error("Not have component form " .. parentObj.name)
    end
    return tUi
end

function FineFromParent(parentObj, childName)
    local transform = parentObj.transform
    if transform == nil then
        return nil
    end
    if transform.name == childName then
        return transform
    end
    local queue = {}
    local current, index = 1, 1
    queue[index] = transform
    while true do
        if current > index then
            break
        end
        transform = queue[current]
        queue[current] = nil
        current = current + 1
        local count = transform.childCount
        for i = 1, count do
            local child = transform:GetChild(i - 1)
            if child.name == childName then
                return child
            elseif child.childCount ~= 0 then
                index = index + 1
                queue[index] = child
            end
        end
    end
    return nil
end

function GET_UI_CHILD(parentTrans, index, componentName)
    --this is not null, needed important !!!!!
    if parentTrans == nil then
        return nil
    end

    if parentTrans:GetType():Equals(_G.TP(UE.Transform)) == false then
        parentTrans = parentTrans.transform
    end

    if index < 0 or index >= parentTrans.childCount then
        return nil
    end

    local tUi = parentTrans:GetChild(index)

    if componentName and tUi then
        tUi = GetComponentInLua(tUi, componentName)
    end

    return tUi
end

--获取子节点所有此组件（包括自己）
function GET_OBJECT_CHILD(parentTrans, component)
    -- body
    if nil == parentTrans then
        Log.Info("GET_OBJECT_CHILD parentTrans is None~~")
    end
    local childs = {}
    GetChildsComponent(parentTrans, childs, component)
    return childs
end

function GetChildsComponent(parentTrans, tabl, component)
    -- body
    local com = parentTrans:GetComponent(component)
    if nil ~= com then
        table.insert(tabl, com)
    end

    for i = 0, parentTrans.childCount - 1 do
        GetChildsComponent(parentTrans:GetChild(i), tabl, component)
    end
end

-------------------------------------------------Map------------------------------------------------------------------------
--添加树的血条
function AddBlood(blood, parent, nowBlood, isReduce)
    --初始化物体
    local pre
    if blood == 1 then
        pre = ResMgr:LoadAssetSync("Prefab/Map/OneBlood.prefab", AssetDefine.LoadType.Instant)
    elseif blood == 2 then
        pre = ResMgr:LoadAssetSync("Prefab/Map/TwoBlood.prefab", AssetDefine.LoadType.Instant)
    else
        pre = ResMgr:LoadAssetSync("Prefab/Map/MultiBlood.prefab", AssetDefine.LoadType.Instant)
    end

    local item
    if parent then
        item = CreateGameObjectWithParent(pre, parent.transform)
    else
        item = CreateGameObject(pre)
        MapController:AddUIToWorld(item.transform)
        SetUIFirstSibling(item)
    end

    local maxTrans = GetChild(item, "max").transform

    if isReduce then
        maxTrans.localScale = Vector3.New(0.35, 0.45, 1)
    end
    --设置血量
    if blood > 2 then
        local cen = GetChild(item, "max/m_goCenter")
        local sumLength = 250 --血条实际宽度
        local offset = 4
        local offsetSum = (blood - 1) * offset
        local length = (sumLength - offsetSum) / blood
        --当血量大于3时自动生成新增的物体
        for i = 1, blood - 3 do
            CreateGameObjectWithParent(cen, maxTrans)
        end

        --设置子物体的宽度
        local count = maxTrans.childCount
        local origin = 0 - sumLength / 2
        for i = 0, count - 1, 1 do
            local rect = GetComponent(maxTrans:GetChild(i), UE.RectTransform)
            rect.sizeDelta = Vector2.New(length, rect.rect.height)
            if i > 1 then
                local newX = origin + (length + offset) * (i - 1)
                --deviant偏移直为当前位置-2+一半的偏移值
                rect.anchoredPosition = Vector2.New(newX, rect.anchoredPosition.y)
            end
        end
        SetUILastSibling(GetChild(maxTrans, "m_goRight"))
    end

    --获取物体的tween组件
    local tweens = {}
    local gos = {}
    local count = maxTrans.childCount
    for i = 0, count - 1 do
        table.insert(tweens, GetComponent(maxTrans:GetChild(i), TweenAnim))
        table.insert(gos, maxTrans:GetChild(i).gameObject)
    end

    nowBlood = nowBlood or blood
    if nowBlood then
        for i = blood, nowBlood + 1, -1 do
            SetActive(gos[i], false)
        end
        if tweens[nowBlood] then
            tweens[nowBlood]:DOPlay()
        else
            Log.Error(#tweens, "tweens列表不包含", nowBlood)
        end
    end

    return item, tweens, gos
end

--添加破冰进度条
function AddIceSlider(parent)
    --初始化物体
    local pre = ResMgr:LoadAssetSync("Prefab/Map/IceSlider.prefab", AssetDefine.LoadType.Instant)

    local item
    if parent then
        item = CreateGameObjectWithParent(pre, parent.transform)
    else
        item = CreateGameObject(pre)
        MapController:AddUIToWorld(item.transform)
        SetUIFirstSibling(item)
    end

    return item
end


--------------------------------------------------------------GGEnd---------------------------------------------------------------

---Mike
function SetPositionTrans(trans, x, y, z)
    if trans == nil then
        return
    end
    if x and y and z then
        trans:SetPosition(x, y, z)
    elseif x and y then
        trans:SetPositionXY(x, y)
    end
end

function SetLocalPositionTrans(trans, x, y, z)
    if trans == nil then
        return
    end
    if x and y and z then
        trans:SetLocalPosition(x, y, z)
    elseif x and y then
        trans:SetLocalPositionXY(x, y)
    end
end

function SetSprite3D(renderer, sp)
    if renderer == nil or sp == nil then
        return
    end
    GameUtil.SetSprite(renderer, sp)
end

function LoadSprite3D(renderer, path, fun, funCancel, isForce)
    if renderer and renderer.sprite then
        if not isForce and string.contains(path, renderer.sprite.name) then
            if funCancel then
                funCancel()
            end
            return
        end
    end
    local function onLoaded(sp)
        SetSprite3D(renderer, sp)
        if fun then
            fun(sp)
        end
    end
    ResMgr:LoadAssetBaseAsync(path, AssetDefine.LoadType.Sprite, onLoaded)
end

function CreateGameObject(prefab)
    return UEGO.Instantiate(prefab)
end

function CreateGameObjectWithParent(prefab, parent)
    if parent then
        return UEGO.Instantiate(prefab, parent.transform)
    else
        return CreateGameObject(prefab)
    end
end

--two params return back GO and Transform
function CreateGOAndTrans(prefab)
    return GameUtil.Instantiate(prefab)
end

function SearchChild(trans, childName, type)
    if type then
        return GameUtil.SearchChild(trans, childName, typeof(type))
    end
    return GameUtil.SearchChild(trans, childName, nil)
end

function SetUIImgEnable(btn, enabled)
    local image = GetComponent(btn.gameObject, UEUI.Image)
    image.raycastTarget = enabled
end

function SetUIForceRebuildLayout(go)
    if not go then
        return
    end
    local rectTrans = GetComponent(go, UE.RectTransform)
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(rectTrans)
end

function GetAllCanSortOrderComponents(parentObj, outList)
    if parentObj == nil then
        return
    end
    GetOrderComponent(parentObj, outList)

    local count = parentObj.transform.childCount
    if count == 0 then
        return
    end
    for i = 0, count - 1, 1 do
        GetAllCanSortOrderComponents(parentObj.transform:GetChild(i), outList)
    end
end

function GetOrderComponent(gameObj, outList)
    local canvas = GetComponent(gameObj, UE.Canvas)
    if canvas then
        table.insert(outList, canvas)
        return
    end

    --maybe particleSystem maybe spine
    local renderer = GetComponent(gameObj, UE.Renderer)
    if renderer then
        table.insert(outList, renderer)
        return
    end
end

function UI_SHOW(name, ...)
    -- 检查是否需要改变 UI 界面名称
    name = UIMgr:ChangeUIName(name, ...)
    UIMgr:Show(name, ...)
end
function UI_CLOSE(name, isRecycle)
    UIMgr:Close(name, isRecycle)
end
function UI_UPDATE(name, ...)
    UIMgr:Refresh(name, ...)
end

function SetUIObjBtnGrayAndEnable(obj, bIsEnable)
    SetUIBtnEnable(obj, bIsEnable)
    SetUIBtnGray(obj, bIsEnable)
end

function SetSpineShader(spine, type)
    local strShaders = { [1] = "Spine/Skeleton", [2] = "Spine/Special/Skeleton Grayscale",
                         [3] = "Spine/Special/Skeleton Blackscale", [4] = "Spine/Skeleton Fill",
                         [5] = "Spine/Skeleton Lit", [6] = "Spine/Skeleton Custom Color" }
    GameUtil.SetSpineMaterial(spine, strShaders[type])
end

function SetSpineLight(spine, type)
    -- 1 light  0 not
    GameUtil.SetShaderProperty(spine, "_IsLight", type)
end

function SetSpineColor(spine, color, phase)
    if spine and color then
        GameUtil.SetSpineColor(spine, color.r, color.g, color.b, color.a, phase)
    end
end

function SetSpineGray(spine, gray, phase)
    gray = gray or 0
    if spine then
        GameUtil.SetSpineGray(spine, gray, phase)
    end
end
--[[
    @desc:Set Spine EventCallBack
    author:TD
    time:2021-12-08 17:02
    --@spine: spine
    --@animName: animation Name
	--@loop: -1 is True Other is False
    --@isAdd: is Add to Play Or Set to Play
    --@durtime: delay time to Play animation
]]--
function SetSpineAnim(spine, animName, loop, isAdd, durtime)
    loop = loop or -1
    if not durtime then
        durtime = 0
    end
    if spine and animName and spine.AnimationState then
        if isAdd then
            --0 delay
            spine.AnimationState:AddAnimation(0, animName, loop == -1, durtime)
        else
            spine.AnimationState:SetAnimation(0, animName, loop == -1)
        end
    end
end
--[[
    @desc:Set Spine EventCallBack
    author:TD
    time:2021-12-08 17:02
    一.Start 当动画开始播放时触发。
     1)当调用SetAnimation应用成功时触发。
     2)我也可以在一个队列动画开始播放时触发。
    二.End 当动画被清除(或中断)时触发:
     1)这适用于，当前动画快要完成时，你可以调用SetAnimation去打断它。
     2)当你使用ClearTrack或ClearTracks清理Track时也会被触发。
     3)(3.0) 在混合/淡入淡出期间，在混合完成时End将触发。
     4)永远不要在End事件中调用SetAnimation。请看下面的警告。
    三.Interrupt (3.0) 当新的动画被设置并且当前有一个动画还在播放时触发。
     1)当一个动画开始混合/淡入淡出到另一个动画时触发。
    四.Complete 当动画完成时触发。
     1)当一个非循环的动画播放完毕时触发，无论是否有下一个动画在排队。
     2)在循环动画结束循环后，也会触发。
    --@spine: spine
    --@typeCallBack: CallBack Type
	--@funCallBack: Add CallBackFunction
]]--
function SetSpineCallBack(spine, typeCallBack, funCallBack)
    local function spineCallBack(trackentry)
        funCallBack()
        if typeCallBack == SPINE_CALLBACK_TYPE.Start then
            spine.AnimationState:Start("-", spineCallBack)
        elseif typeCallBack == SPINE_CALLBACK_TYPE.End then
            spine.AnimationState:End("-", spineCallBack)
        elseif typeCallBack == SPINE_CALLBACK_TYPE.Interrupt then
            spine.AnimationState:Interrupt("-", spineCallBack)
        elseif typeCallBack == SPINE_CALLBACK_TYPE.Complete then
            spine.AnimationState:Complete("-", spineCallBack)
        else
            Log.Error("SPINE_CALLBACK_TYPE is not have")
        end
    end
    if typeCallBack == SPINE_CALLBACK_TYPE.Start then
        spine.AnimationState:Start("+", spineCallBack)
    elseif typeCallBack == SPINE_CALLBACK_TYPE.End then
        spine.AnimationState:End("+", spineCallBack)
    elseif typeCallBack == SPINE_CALLBACK_TYPE.Interrupt then
        spine.AnimationState:Interrupt("+", spineCallBack)
    elseif typeCallBack == SPINE_CALLBACK_TYPE.Complete then
        spine.AnimationState:Complete("+", spineCallBack)
    else
        Log.Error("SPINE_CALLBACK_TYPE is not have")
    end
end

--[[
    @desc:Set Spine EventCallBack
    author:TD
    time:2021-12-08 17:02
    Event 触发 任何 被监听到的用户自定义事件.
     1)这些事件点在Spine的动画中设置。它们是一些紫色的关键帧。一个紫色的icon也可以在层级书中找到。
     2)为了区分不同的事件，你需要检查Spine.Event e的Name参数。(或者Data引用)
     3)当你想要按照动画节点去播放声音时，这是非常有用的，就像footsteps。他也可以按照Spine动画去使用同步或者信号这种非Spine系统，比如Unity例子系统或者生成单独效果，甚至是游戏逻辑。比如子弹发射的时间里。(如果你真的想这么做的话)
    --@spine: spine
    --@eventName: event Name
	--@funCallBack: Add CallBackFunction
]]--
function SetSpineEventCallBack(spine, eventName, funCallBack)
    local function eventCallBack(trackentry, spineEvent)
        if spineEvent.Data.name == eventName then
            funCallBack()
        end
        spine.AnimationState:Start("-", eventCallBack)
    end
    spine.AnimationState:Event("+", eventCallBack)
end

function SetRendererOrder(render, layerId, order)
    if render and layerId and order then
        GameUtil.SetOrder(render, layerId, order)
    end
end

function UIRefreshLayout(go)
    if not go then
        return
    end
    local rectTrans = GetComponent(go, UE.RectTransform)
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(rectTrans)
end

function GetRectTransRect(go)
    if not go then
        Log.Warning("go is nil in function GetRectTransRect ")
        return nil
    end
    local rectTrans = GetComponent(go, UE.RectTransform)
    return rectTrans.rect
end

function ResetAnimation(anim, animName)
    local state = anim:get_Item(animName)
    state.time = 0
    anim:Sample()
    anim:Stop(animName)
end

function PlayAnimStatus(anim, statusName, callBack)
    if not anim then
        Log.Warning("anim is nil in function PlayAnimStatus !!!")
    end
    GameUtil.PlayAnimationState(anim, statusName)
    if callBack then
        local animEvent = GetComponent(anim.gameObject, CS.AnimEvent)
        if animEvent then
            animEvent.callBack = function()
                callBack()
            end
        end
    end
end

function PlayAnimStatusIndex(anim, statusName, callBack1, callBack2, callBack3)
    if not anim then
        Log.Warning("anim is nil in function PlayAnimStatus !!!")
    end
    GameUtil.PlayAnimationState(anim, statusName)
    local animEvent = GetComponent(anim.gameObject, CS.AnimEvent)
    if animEvent then
        animEvent.callBackIndex = function(index)
            if index == 1 then
                if callBack1 then
                    callBack1()
                end
            end
            if index == 2 then
                if callBack2 then
                    callBack2()
                end
            end
            if index == 3 then
                if callBack3 then
                    callBack3()
                end
            end
        end
    end
end

function CheckMainFaceUI(obj)
    local is_show = DailyTargetManager:IsOpenDailyTargetSwitch()
    if is_show then
        obj.ui.m_goEnergy = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy")
        obj.ui.m_doEnergy = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_doEnergy", TweenAnim)
        obj.ui.m_imgEnergyBuy = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_doEnergy/m_imgEnergyBuy", UEUI.Image)
        obj.ui.m_goEnergyNormal = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_goEnergyNormal")
        obj.ui.m_imgEnergySlider = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_goEnergyNormal/m_imgEnergySlider", UEUI.Image)
        obj.ui.m_txtEnergy = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_goEnergyNormal/m_txtEnergy", UEUI.Text)
        obj.ui.m_imgEnergyTime = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_imgEnergyTime", UEUI.Image)
        obj.ui.m_txtEnergyTime = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_imgEnergyTime/m_txtEnergyTime", UEUI.Text)
        obj.ui.m_goEnergyPoint = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_goEnergyPoint")
        obj.ui.m_imgEnergyInfinite = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_imgEnergyInfinite", UEUI.Image)
        obj.ui.m_txtEnergyInTime = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_imgEnergyInfinite/m_txtEnergyInTime", UEUI.Text)

        obj.ui.m_btnDailyTargetRoot = GetChild(obj.uiGameObject, "LeftTop/m_btnDailyTargetRoot", UEUI.Button)
        obj.ui.m_goDailyTarget = GetChild(obj.uiGameObject, "LeftTop/m_btnDailyTargetRoot/m_goDailyTarget")
        obj.ui.m_sliderDailyTarget = GetChild(obj.uiGameObject, "LeftTop/m_btnDailyTargetRoot/m_goDailyTarget/m_sliderDailyTarget", UEUI.Slider)
        obj.ui.m_txtTargetProgress = GetChild(obj.uiGameObject, "LeftTop/m_btnDailyTargetRoot/m_goDailyTarget/m_sliderDailyTarget/m_txtTargetProgress", UEUI.Text)
        obj.ui.m_txtDailyTargetTime = GetChild(obj.uiGameObject, "LeftTop/m_btnDailyTargetRoot/m_txtDailyTargetTime", UEUI.Text)
        obj.ui.m_doCurDailyIcon = GetChild(obj.uiGameObject, "LeftTop/m_btnDailyTargetRoot/m_goDailyTarget/targetBg1/m_doCurDailyIcon", TweenAnim)
        obj.ui.m_btnDailyTargetIcon = GetChild(obj.uiGameObject, "LeftTop/m_btnDailyTargetRoot/m_goDailyTarget/targetBg2/m_btnDailyTargetIcon", UEUI.Button)
        obj.ui.m_txtTarget = GetChild(obj.uiGameObject, "LeftTop/m_btnDailyTargetRoot/m_goDailyTarget/targetBg2/m_txtTarget", UEUI.Text)
        obj.ui.m_goTargetCache = GetChild(obj.uiGameObject, "LeftTop/m_btnDailyTargetRoot/m_goDailyTarget/targetBg2/m_goTargetCache")
        obj.ui.m_txtTargetCacheNum = GetChild(obj.uiGameObject, "LeftTop/m_btnDailyTargetRoot/m_goDailyTarget/targetBg2/m_goTargetCache/m_txtTargetCacheNum", UEUI.Text)

        obj.ui.m_btnDelete = GetChild(obj.uiGameObject, "LeftTop/m_goSkinBtnRoot/m_btnDelete", UEUI.Button)
        obj.ui.m_btnSkin = GetChild(obj.uiGameObject, "LeftTop/m_goSkinBtnRoot/m_btnSkin", UEUI.Button)

        obj.ui.m_goPrivilege = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goPrivilege")
        obj.ui.m_imgMagnet = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goPrivilege/m_imgMagnet", UEUI.Image)
        obj.ui.m_btnMagnet = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goPrivilege/m_btnMagnet", UEUI.Button)
        obj.ui.m_imgPrivilegeHand = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goPrivilege/m_imgPrivilegeHand", UEUI.Image)
        obj.ui.m_imgPrivilegeTime = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goPrivilege/m_imgPrivilegeTime", UEUI.Image)
        obj.ui.m_txtPrivilegeTime = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goPrivilege/m_imgPrivilegeTime/m_txtPrivilegeTime", UEUI.Text)
        obj.ui.m_btnEnergy = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goPrivilege/m_btnEnergy", UEUI.Button)
        obj.ui.m_goPrivilegeRed = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goPrivilege/m_goPrivilegeRed")

        obj.ui.m_goMonthCard = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goMonthCard")
        obj.ui.m_btnMonthCard = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goMonthCard/m_btnMonthCard", UEUI.Button)
        obj.ui.m_txtMonthTime = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goMonthCard/m_btnMonthCard/doLimit/bg/CountDown/m_txtMonthTime", UEUI.Text)
        obj.ui.m_imgMonthRed = GetChild(obj.uiGameObject, "LeftBottom/m_goLeftBottomList/m_goMonthCard/m_btnMonthCard/doLimit/bg/m_imgMonthRed", UEUI.Image)

        obj.ui.m_txtUnLockDesc = GetChild(obj.uiGameObject, "LeftTop/m_btnDailyTargetRoot/lock/m_txtUnLockDesc", UEUI.Text)

        obj.ui.m_txtEnergyFullTime = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_imgEnergyTime/m_txtEnergyFullTime", UEUI.Text)
    end
end

function CheckCopyMainFace2UI(obj)
    local is_show = DailyTargetManager:IsOpenDailyTargetSwitch()
    if is_show then
        obj.ui.m_goEnergyDay = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay")
        obj.ui.m_goEnemyIcon = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_goEnemyIcon")
        obj.ui.m_txtEnergyNumber = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_goEnemyIcon/m_txtEnergyNumber", UEUI.Text)
        obj.ui.m_txtEnergyBoxTime = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_goEnemyIcon/Image/m_txtEnergyBoxTime", UEUI.Text)
        obj.ui.m_sliderBackGround = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_sliderBackGround", UEUI.Slider)
        obj.ui.m_goEnergy1 = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy1")
        obj.ui.m_txtBox1 = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy1/m_txtBox1", UEUI.Text)
        obj.ui.m_goEnergy2 = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy2")
        obj.ui.m_txtBox2 = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy2/m_txtBox2", UEUI.Text)
        obj.ui.m_goEnergy3 = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy3")
        obj.ui.m_txtBox3 = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy3/m_txtBox3", UEUI.Text)
        obj.ui.m_goShowData = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_goShowData")
        obj.ui.m_imgEnergyData = GetChild(obj.uiGameObject, "UpCenter/m_goEnergyDay/m_goShowData/Image/m_imgEnergyData", UEUI.Image)

        obj.ui.m_goEnergy = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy")
        obj.ui.m_imgEnergySlider = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_imgEnergySlider", UEUI.Image)
        obj.ui.m_goEnergyTime = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_imgEnergySlider/m_goEnergyTime")
        obj.ui.m_txtEnergyTime = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_imgEnergySlider/m_goEnergyTime/m_txtEnergyTime", UEUI.Text)
        obj.ui.m_doEnergy = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_doEnergy", TweenAnim)
        obj.ui.m_imgEnergyBuy = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_imgEnergyBuy", UEUI.Image)
        obj.ui.m_txtEnergy = GetChild(obj.uiGameObject, "RightTop/ResourceItems/m_goEnergy/m_txtEnergy", UEUI.Text)

        obj.ui.m_goPrivilege = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege")
        obj.ui.m_imgMagnet = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_imgMagnet", UEUI.Image)
        obj.ui.m_btnMagnet = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_btnMagnet", UEUI.Button)
        obj.ui.m_imgPrivilegeHand = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_imgPrivilegeHand", UEUI.Image)
        obj.ui.m_imgPrivilegeTime = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_imgPrivilegeTime", UEUI.Image)
        obj.ui.m_txtPrivilegeTime = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_imgPrivilegeTime/m_txtPrivilegeTime", UEUI.Text)
        obj.ui.m_btnEnergy = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_btnEnergy", UEUI.Button)
        obj.ui.m_goPrivilegeRed = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_goPrivilegeRed")
    end
end

function CheckZooMainFace2UI(obj)
    local is_show = DailyTargetManager:IsOpenDailyTargetSwitch()
    if is_show then
        obj.ui.m_goPrivilege = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege")
        obj.ui.m_imgMagnet = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_imgMagnet", UEUI.Image)
        obj.ui.m_btnMagnet = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_btnMagnet", UEUI.Button)
        obj.ui.m_imgPrivilegeHand = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_imgPrivilegeHand", UEUI.Image)
        obj.ui.m_imgPrivilegeTime = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_imgPrivilegeTime", UEUI.Image)
        obj.ui.m_txtPrivilegeTime = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_imgPrivilegeTime/m_txtPrivilegeTime", UEUI.Text)
        obj.ui.m_btnEnergy = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_btnEnergy", UEUI.Button)
        obj.ui.m_goPrivilegeRed = GetChild(obj.uiGameObject, "LeftBottom/m_goPrivilege/m_goPrivilegeRed")
    end
end

--将Hex颜色码转成color类型
---@param hex string  例如:"3676BE"
function GetColorByHex(hex)
    if not string.find(hex, "#") then
        hex = "#" .. hex
    end
    local _, color = ColorUtility.TryParseHtmlString(hex, nil);
    return color
end

--将描边组件的颜色和UIShadow组件的颜色进行同步
--@param txtObj GameObject 文本组件所在预制体
---@param hex string  例如:"3676BE"
function UnifyOutline(txtObj, hex)
    if IsNil(txtObj) then
        return
    end
    local outlineColor = GetColorByHex(hex)
    local outline = txtObj:GetComponent(typeof(UEUI.Outline))
    if outline then
        outline.effectColor = outlineColor
    end
    local shadow = txtObj:GetComponent(typeof(CS.Coffee.UIEffects.UIShadow))
    if shadow and shadow.effectColor then
        shadow.effectColor = outlineColor
    end
end

--- UI 节点位置适配计算
--- @param go any UI 节点
--- @return table result 转换坐标结果
function UIRectPosFit(go)
    if IsNil(go) then
        Log.Error("传入的 UI 节点为空")
        return { 0, 0 }
    end
    -- 获取 UI 相机和画布
    local cam = UIMgr:GetCamera()
    local canvasRectTrans = UIMgr:GetCanvasRectTrans()
    -- UI 节点位置转成屏幕坐标
    local screenPoint = UE.RectTransformUtility.WorldToScreenPoint(cam, go.transform.position)
    -- 以画布为父节点，把屏幕坐标转成 UI 局部坐标
    local _, pos = UE.RectTransformUtility.ScreenPointToLocalPointInRectangle(canvasRectTrans,
            Vector2.New(screenPoint.x, screenPoint.y), cam)
    local result = { pos.x, pos.y }
    return result
end

function SetMyHeadAndBorderByGo(go)
    SetHeadByGo(go, NetUpdatePlayerData:GetPlayerInfo().head)
    SetHeadBorderByGo(go, NetUpdatePlayerData:GetPlayerInfo().headBorder)
end

function SetHeadAndBorderByGo(go, head, border, clickCall)
    SetHeadByGo(go, head, clickCall)
    SetHeadBorderByGo(go, border)
end

function SetHeadBorderByGo(go, id)
    local _imgHeadBorder = GetChild(go, "imgHeadBorder", UEUI.Image)
    local _imgHeadTop = GetChild(_imgHeadBorder, "imgHeadTop", UEUI.Image)
    local _spuiHeadBorder = GetChild(_imgHeadBorder, "spuiHeadBorder", CS.Spine.Unity.SkeletonGraphic)
    local _spuiHeadTop = GetChild(_imgHeadBorder, "spuiHeadTop", CS.Spine.Unity.SkeletonGraphic)
    local cfgHeadSet = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, id)
    if cfgHeadSet then
        local str = cfgHeadSet["icon_behind"]
        if str then
            local isImg = string.startswith(str, "Sprite/")
            if isImg then
                SetUIImage(_imgHeadBorder, str, false)
            else
                SetUIImage(_imgHeadBorder, "Sprite/ui_public/0_touming.png", false)

                local strDic = Split1(str, "|")

                local function funcW()
                    SetUIPos(_spuiHeadBorder, strDic[2], strDic[3], strDic[4])
                end
                RoleSpineConfig:SetSpineByName(_spuiHeadBorder, strDic[1], funcW)
                --local roleconfig = RoleSpineConfig:GetSkeletonDataById(strDic[1])
                --if roleconfig and roleconfig.spine then
                --_spuiHeadBorder.skeletonDataAsset = roleconfig.spine
                --_spuiHeadBorder:Initialize(true)

                --end
            end
            SetActive(_spuiHeadBorder, not isImg)
        end

        str = cfgHeadSet["icon_front"]
        if str then
            local isImg = string.startswith(str, "Sprite/")
            if isImg then
                SetUIImage(_imgHeadTop, str, false)
            else
                SetUIImage(_imgHeadTop, "Sprite/ui_public/0_touming.png", false)

                local strDic = Split1(str, "|")

                local function funcW()
                    SetUIPos(_spuiHeadTop, strDic[2], strDic[3], strDic[4])
                end
                RoleSpineConfig:SetSpineByName(_spuiHeadTop, strDic[1], funcW)

                --local roleconfig = RoleSpineConfig:GetSkeletonDataById(strDic[1])
                --if roleconfig and roleconfig.spine then
                --_spuiHeadTop.skeletonDataAsset = roleconfig.spine
                --_spuiHeadTop:Initialize(true)
                --SetUIPos(_spuiHeadTop, strDic[2], strDic[3], strDic[4])
                --end
            end
            SetActive(_spuiHeadTop, not isImg)
        end
    else
        SetUIImage(_imgHeadBorder, "Sprite/ui_gexinghua/zb_touxiang1.png", false)
        SetUIImage(_imgHeadTop, "Sprite/ui_public/0_touming.png", false)
        SetActive(_spuiHeadBorder, false)
        SetActive(_spuiHeadTop, false)
    end
end

function SetHeadByGo(go, id, clickCall)
    local _imgHeadBorder = GetChild(go, "imgHeadBorder", UEUI.Image)
    local _imgHead = GetChild(_imgHeadBorder, "imgHead", UEUI.Image)
    local cfgHeadSet = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, id)
    if cfgHeadSet and cfgHeadSet["icon"] then
        SetUIImage(_imgHead, cfgHeadSet["icon"], false)
    else
        SetUIImage(_imgHead, "Sprite/new_hero/headFrame_1.png", false)
    end

    local animator = GetComponent(_imgHeadBorder.gameObject, UE.Animator)
    if animator then
        --只有在有点击事件时,才显示点击动效
        if clickCall then
            animator.enabled = true
        else
            animator.enabled = false
        end
    end

    if clickCall then
        local btn = GetComponent(_imgHeadBorder.gameObject, UEUI.Button)
        RemoveUIComponentEventCallback(btn, UEUI.Button)
        AddUIComponentEventCallback(btn, UEUI.Button,
                function(go, param)
                    if clickCall then
                        clickCall()
                    end
                end)
    end
end

function SetChatBorder(img, id, isSelf)
    local cfgHeadSet = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, id)
    if cfgHeadSet and cfgHeadSet["icon"] and id ~= "110" then
        --110默认皮肤
        SetUIImage(img, cfgHeadSet["icon"], false)
    else
        if isSelf then
            SetUIImage(img, "Sprite/ui_gexinghua/zb_qipao2.png", false)
        else
            SetUIImage(img, "Sprite/ui_gexinghua/zb_qipao1.png", false)
        end
    end
end

function GetChatBorderTextColor(id, isSelf)
    local cfgHeadSet = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, id)
    if cfgHeadSet and cfgHeadSet["color"] and id ~= "110" then
        --110默认皮肤
        return Color.HexToRGB(cfgHeadSet["color"])
    else
        if isSelf then
            return Color.HexToRGB("0A800D")
        else
            return Color.HexToRGB("5189c2")
        end
    end
    return Color.HexToRGB("0A800D")
end

function CreateCommonHead(parent, vt3WholeHeadSize, pos)
    local CustomHeadPre = ResMgr:LoadAssetSync("Assets/ResPackage/Prefab/UI/CustomHead.prefab", AssetDefine.LoadType.Instant)
    local newGo, newTrans = CreateGOAndTrans(CustomHeadPre)
    newGo.name = "CustomHead"
    if parent then
        newTrans:SetParent(parent.transform)
    end
    if pos then
        SetUIPos(newGo, pos.x, pos.y)
    else
        newTrans.localPosition = Vector3.zero
    end
    if vt3WholeHeadSize then
        newTrans.localScale = Vector3(vt3WholeHeadSize, vt3WholeHeadSize, vt3WholeHeadSize)
    else
        newTrans.localScale = Vector3.one
    end
    return newGo, newTrans
end

--- 异步加载通用头像框并设置缩放和位置
--- @param parent any 父节点
--- @param vt3WholeHeadSize any 缩放
--- @param pos any 位置
--- @param callback any 加载完成回调
function CreateCommonHeadAsync(parent, vt3WholeHeadSize, pos, callback)
    ResMgr:LoadAssetWithCache("Prefab/UI/CustomHead.prefab", AssetDefine.LoadType.Instant, function (obj)
        local newGo, newTrans = CreateGOAndTrans(obj)
        newGo.name = "CustomHead"
        if parent then
            newTrans:SetParent(parent.transform)
        end
        if pos then
            SetUIPos(newGo, pos.x, pos.y)
        else
            newTrans.localPosition = Vector3.zero
        end
        if vt3WholeHeadSize then
            newTrans.localScale = Vector3(vt3WholeHeadSize, vt3WholeHeadSize, vt3WholeHeadSize)
        else
            newTrans.localScale = Vector3.one
        end
        if callback then
            callback(newGo, newTrans)
        end
    end)
end

--异步加载通用头像框预制体引用
function LoadCommonHeadAsync(callback)
    ResMgr:LoadAssetAsync("Assets/ResPackage/Prefab/UI/CustomHead.prefab", AssetDefine.LoadType.Instant, function(obj)
        if callback then
            callback(obj)
        end
    end)
end

--设置通用头像框显示
function CreatCommonHeadView(template, parent, vt3WholeHeadSize, pos)
    local newGo, newTrans = CreateGOAndTrans(template)
    newGo.name = "CustomHead"
    if parent then
        newTrans:SetParent(parent.transform)
    end
    if pos then
        SetUIPos(newGo, pos.x, pos.y)
    else
        newTrans.localPosition = Vector3.zero
    end
    if vt3WholeHeadSize then
        newTrans.localScale = Vector3(vt3WholeHeadSize, vt3WholeHeadSize, vt3WholeHeadSize)
    else
        newTrans.localScale = Vector3.one
    end
    return newGo, newTrans
end

function SetHeadGrayByGo(obj, bIsGray, bIsBlack, black)
    local list = obj.transform:GetComponentsInChildren(TP(UEUI.Image), true)
    for i = 0, list.Length - 1 do
        SetUIImageGray(list[i], bIsGray, bIsBlack, black)
    end

    list = obj.transform:GetComponentsInChildren(TP(CS.Spine.Unity.SkeletonGraphic), true)
    for i = 0, list.Length - 1 do
        local spine = list[i]
        if spine.material == nil then
            if bIsGray == true then
                local shaderGray = UE.Shader.Find("UIShader/UIShader_Gray")
                local material = UE.Material(shaderGray)
                if bIsBlack then
                    material:SetFloat(UE.Shader.PropertyToID("_IsBlack"), black or 1)
                end
                spine.material = material
            else
                local shaderDefault = UE.Shader.Find("UI/Default")
                local material = UE.Material(shaderDefault)
                spine.material = material
            end
        else
            if bIsGray == true then
                if spine.material.shader.name ~= "UIShader/UIShader_Gray" then
                    local shaderGray = UE.Shader.Find("UIShader/UIShader_Gray")
                    local material = UE.Material(shaderGray)
                    if bIsBlack then
                        material:SetFloat(UE.Shader.PropertyToID("_IsBlack"), black or 1)
                    end
                    spine.material = material
                end
            else
                if spine.material.shader.name ~= "UI/Default" then
                    local shaderDefault = UE.Shader.Find("UI/Default")
                    local material = UE.Material(shaderDefault)
                    spine.material = material
                end
            end
        end
    end
end

--获取指定属性多语言
function GetAttrName(id)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_global_setting, id)
    if config then
        return LangMgr:GetLang(v2n(config.lang_id))
    end
    return ""
end

--- 获取百分比属性多语言
--- @param id number 属性 ID
--- @return string 多语言
function GetAttrPercentName(id)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_global_setting, id)
    if config then
        local lang_id2 = v2n(config.lang_id2)
        if lang_id2 then
            return LangMgr:GetLang(lang_id2)
        else
            return LangMgr:GetLang(v2n(config.lang_id))
        end
    end
    return ""
end

--显示指定属性图标
function ShowAttrIcon(id, icon)
    if not icon then
        return
    end
    local sprite
    if id == 1 then
        --攻击
        sprite = "Sprite/ui_slg_beibao/zb_shuxing_gongji.png"
    elseif id == 2 then
        --血量
        sprite = "Sprite/ui_slg_beibao/zb_shuxing_shengming.png"
    elseif id == 3 then
        --防御
        sprite = "Sprite/ui_slg_beibao/zb_shuxing_fangyu.png"
    end
    if sprite then
        SetUIImage(icon, sprite, false)
    end
end



