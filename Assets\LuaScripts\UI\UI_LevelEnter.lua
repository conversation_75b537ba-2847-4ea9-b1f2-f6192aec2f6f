local UI_LevelEnter = Class(BaseView)
local DOTween = CS.DG.Tweening.DOTween

--关卡状态
local NodeStatus = {
   Current = 0;--当前关卡
   Lock = -1;--未解锁
   Finish = 1;--已通关
}

--宝箱状态
local BoxStatus = {
    Empty = 1;
    Half = 2;
    Full = 3;
}

local QUALITY_UNDER_BG = {
    [2] = "Sprite/ui_huodongjingsai/jingsai_libao_touxiangdikuang_bule.png",
    [3] = "Sprite/ui_huodongjingsai/jingsai_libao_touxiangdikuang_puple.png",
    [4] = "Sprite/ui_huodongjingsai/jingsai_libao_touxiangdikuang_gold.png",
}

local maxConfigLevel = 30
local maxTimeLimit = 14400 --挂机时间
local str1  = ""

function UI_LevelEnter:OnInit()
    self.canTimer = false
    self.canChallengeTimer = false
    self.boxStatus = -1
    self.challengeCount = 0
    self.challengeCountTime = 0
    str1 = "<color=#F0C601>"..LangMgr:GetLang(70000086).."</color>"
    self.giftTimerTxtList = {}
    ----------初始化界面相关逻辑----------
    SetActive(self.ui.m_goPrefab,false)
    SetActive(self.ui.m_goTimerNode,false)
    maxTimeLimit = DungeonManager:GetMaxHangTime()
    self.hangUpTime1,self.hangUpTime2 = DungeonManager:GetHangConfigTime()
    SetActive(self.ui.m_goBoxDot,false)
    SetActive(self.ui.m_goTip,false)

    local btnRank = GetChild(self.uiGameObject,"main/btnLevelRank")
    SetActive(btnRank,true)
    self:CheckQuickDot()
    self:CheckQuickStatusTxt()
    EventMgr:Add(EventID.ROLE_GIFT_CHANGE, self.CheckRoleGift, self)
end

function UI_LevelEnter:OnCreate(param)
    self.configData = ConfigMgr:GetData(ConfigDefine.ID.slg_dungeon)
    if not self.configData then
        Log.Error("slg_dungeon 配表数据读取错误！")
        return
    end
    
    maxTimeLimit = DungeonManager:GetSettingValue(2,43200)
    self.challengeConfigCount = DungeonManager:GetSettingValue(19,20)
    self.challengeConfigCountTime = DungeonManager:GetSettingValue(20,1800)
    self.challengeConfigCountTime = 1200
    maxConfigLevel = table.count(self.configData)
    --当前挑战关卡
    self.levelNodeRect = GetComponent(self.ui.m_goLevelNode,UE.RectTransform)
    --战斗
	self.isVisible = true
    self.battleFiled = self:CreateBattleFiled(self.ui.m_goBattleContainer.transform)
	--self.battleFiled:AddListener(BATTLE_FILED_EVENT_ID.TEAM_DIE,self,self.OnBattleTeamDie)
    ----------定时器----------
    --self.timer = TimeMgr:CreateTimer(self, function()
    --     self:HangUpTimerLogic()
    --     --self:ChallengeCountTimerLogic()
    --     --self:ShowGiftTimerValue()
    --
    --end, 1)

    ----------初始化业务逻辑----------
    self.curLevelId = 1--当前挑战关卡
    self:ShuffleTipList()
    self:UpdateLevelEnter()
    
    DungeonManager:OnRequestDungeonLoad()
	self:SetIsUpdateTick(true)
    
	--BGM
	local bgm = GlobalConfig:GetNumber(10904,0)
	if bgm and bgm > 0 then
		MapController:PlayBGM(bgm)
	end
    
    local BattleEnterItem = require("UI.BattleEnterItem");
    local obj = GetChild(self.uiGameObject, "main/BattleEnter2");
    self.battleEnterItem = BattleEnterItem.new(obj, 100, function()
        --UI_CLOSE(UIDefine.UI_ActivityRankCenter);
    end);
    self.battleEnterItem:OnInit();
    
    self:CheckRoleGift()
    
    self:OnRefreshTickInfo()
    self:RefreshBtnCostTickInfo()
    self:RefreshLevelRewardInfo()

    SetUIImage(self.ui.m_imgTickIcon,ItemConfig:GetIcon(ItemID.SlgDungeonTickServer),false)
    SetUIImage(self.ui.m_imgTickIcon1,ItemConfig:GetIcon(ItemID.SlgDungeonTickServer),false)
    SetUIImage(self.ui.m_imgTickIcon2,ItemConfig:GetIcon(ItemID.SlgDungeonTickServer),false)
    SetUIImage(self.ui.m_imgTickIcon3,ItemConfig:GetIcon(ItemID.SlgDungeonTickServer),false)
    SetUIImage(self.ui.m_imgSweepFightTickIcon,ItemConfig:GetIcon(ItemID.SlgDungeonTickServer),false)

    local flyPos = UIMgr:GetUIPosByWorld(self.ui.m_imgTickIcon.transform.position)
    EquipmentManager:SetFlyEndPosByID(ItemID.SlgDungeonTickServer,flyPos)
    local isRed = RedPointMgr:GetServerRed(DungeonManager:GetDailyRedId()) == 1
    SetActive(self.ui.m_imgBuyTickRed, isRed)
    SetActive(self.ui.m_imgLevelRewardRed, DungeonManager:CheckDungeonRewardRedDot())
    EventMgr:Add(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
    EventMgr:Add(EventID.BAG_CHANGE, self.BagChange, self)
end

function UI_LevelEnter:OnRedPointDirty(dirtyIdSet)
    local dailyRedId = DungeonManager:GetDailyRedId()
    if dirtyIdSet[dailyRedId] then
        SetActive(self.ui.m_imgBuyTickRed, RedPointMgr:IsRed(dailyRedId))
    end
    if dirtyIdSet[RedID.DungeonReward] then
        SetActive(self.ui.m_imgLevelRewardRed, RedPointMgr:IsRed(RedID.DungeonReward))
    end
end

function UI_LevelEnter:OnRefreshTickInfo()
    local num = BagManager:GetBagItemCount(ItemID.SlgDungeonTickServer)
    self.ui.m_txtTickNum.text = num or 0
end

function UI_LevelEnter:RefreshBtnCostTickInfo()
    local onceCost = DungeonManager:GetCostDungeonTick()
    self.ui.m_txtCostTick1.text = onceCost or 0
    self.ui.m_txtCostTick2.text = onceCost or 0
    self.ui.m_txtCostTick3.text = onceCost or 0
    self.ui.m_txtSweepFightCostTick.text = onceCost or 0
end

function UI_LevelEnter:BagChange()
    self:OnRefreshTickInfo()
end

function UI_LevelEnter:RefreshLevelRewardInfo()
    local curConfig = DungeonManager:GetCurNotGetDungeonReward()
    local curRewardLevel = curConfig.level
    if curRewardLevel then
        SetImageSprite(self.ui.m_imgCurRewardIcon,curConfig.reward_icon)
        local color = "ff5757"
        if (self.curLevelId - 1) >= curRewardLevel then
            color = "35f900"
        end
        self.ui.m_txtCurProgress.text = string.format("<color=#%s>%s</color>", color, (self.curLevelId - 1)) .. "/" .. curRewardLevel
    end
end

function UI_LevelEnter:OnRefresh(param)
    if param == 1 then
        --战斗完后，关卡刷新
        self:UpdateLevelEnter()
        self:RefreshLevelRewardInfo()
    elseif param == 2 then
        --更新角色礼包
        self:CheckRoleGift()
    elseif param == 3 then
        self:OnRefreshTickInfo()
    elseif param == 4 then
        self:RefreshLevelRewardInfo()
    end
end

function UI_LevelEnter:onDestroy()
    if self.sequence then
        self.sequence:Pause()
        self.sequence:Kill();
        self.sequence = nil;
    end

    self.giftTimerTxtList = nil
    if self.timer then
        TimeMgr:DestroyTimer(self,self.timer)
    end

	--BGM
	local bgm = GlobalConfig:GetNumber(10904,0)
	if bgm and bgm > 0 then
		MapController:PlayBGM()
	end
    EventMgr:Remove(EventID.ROLE_GIFT_CHANGE, self.CheckRoleGift, self)
    EventMgr:Remove(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
    EventMgr:Remove(EventID.BAG_CHANGE, self.BagChange, self)

    if self.battleEnterItem then
        self.battleEnterItem:OnDestroy();
        self.battleEnterItem = nil;
    end
end

function UI_LevelEnter:OnVisibleChange(isVisible)
	self.isVisible = isVisible
end

function UI_LevelEnter:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnFight1" or name == "m_btnFight2" or name == "m_btnFight3" then
        if self.curLevelId > maxConfigLevel then
            --"您已通关最高关卡"
            UI_SHOW(UIDefine.UI_WidgetTip,LangMgr:GetLang(70000015))
        else
            local cost = DungeonManager:GetCostDungeonTick()
            local num = BagManager:GetBagItemCount(ItemID.SlgDungeonTickServer)
            if num < cost then
                UI_SHOW(UIDefine.UI_SlgBuyDungeonTickView)
                return
            end
            --开始挑战
			BattleSceneManager:SetTeamType(BATTLE_TEAM_TYPE.DUNGEON)
			BattleSceneManager:SetLevelData({["id"] = self.curLevelId})
			BattleSceneManager:ChangeSceneType(BATTLE_SCENE_TYPE.CHOOSE)
        end
    elseif name == "btnRewardPreview" then
        --奖励预览
        UI_SHOW(UIDefine.UI_OfflineRewardTip)
    elseif name == "btnHangUpReward" then
        --if self.curLevelId == 1 then
        --    --"暂无挂机奖励"
        --    UI_SHOW(UIDefine.UI_WidgetTip,LangMgr:GetLang(70000186))
        --else
        --    --挂机奖励
        --    DungeonManager:CheckExistReward(function(data)
        --        UI_SHOW(UIDefine.UI_OfflineRewardGet,data)
        --    end
        --    ,function(data)
        --                --"暂无挂机奖励"
        --                UI_SHOW(UIDefine.UI_WidgetTip,LangMgr:GetLang(70000186))
        --            end)
        --end
        
        --挂机奖励
        local timerStr = self.ui.m_txtTimer.text
        DungeonManager:CheckExistReward(function(data) UI_SHOW(UIDefine.UI_OfflineRewardGet,data,timerStr) end
        ,function(data) UI_SHOW(UIDefine.UI_OfflineRewardGet,{},timerStr) end)
    elseif name == "btnLevelRank" then
        local tagIndex = 1
        DungeonManager:OnRequestDungeonRank(tagIndex,function(data)
            UI_SHOW(UIDefine.UI_LevelRank,tagIndex,data)
        end)
    elseif name == "m_btnQuickFight" then
        DungeonManager:OnReqDungeonFastChallenge(self.curLevelId,function(data)
            if data and data.rewards and next(data.rewards) ~= nil then
                local list = {}
                for k,v in ipairs(data.rewards) do
                    table.insert(list,{code = v.code;amount = v.amount})
                end
                UI_SHOW(UIDefine.UI_EquipmentRecharge,list)
            end
        end)
        
    elseif name == "btnQuickReward" then
        local freeCount = DungeonManager:GetFreeCount() --免费快速挂机次数
        local payCount = DungeonManager:GetChargeCount()--付费快速挂机次数
        if freeCount > 0 or payCount > 0 then
            UI_SHOW(UIDefine.UI_OfflineRewardQuick)
        else
            --"今日次数已用完"
            local content = LangMgr:GetLang(70000013)
            UI_SHOW(UIDefine.UI_WidgetTip,content)
        end
    elseif name == "m_btnSweepFight" then
        UI_SHOW(UIDefine.UI_SlgDungeonSweepTip)
    elseif name == "m_btnBuyTick" then
        UI_SHOW(UIDefine.UI_SlgBuyDungeonTickView)
    elseif name == "m_btnLevelReward" then
        UI_SHOW(UIDefine.UI_SlgDungeonRewardTip)
    end
end
--********************************事件刷新********************************
--更新关卡系统界面
function UI_LevelEnter:UpdateLevelEnter()
    self.curLevelId = DungeonManager:GetLevelId()
    self.challengeCount = DungeonManager:GetChallengeCount()
    self:ShowLevelList()
    local noStart = self.curLevelId <= 1
    SetActive(self.ui.m_goTimerNode,not noStart)
    --第一关或者到达挂机时长限制
    self.canTimer = not noStart
    
    local hookTime = DungeonManager:GetTimestamp()
    if self.curLevelId == 1 or hookTime == 0 then
        self.startTime = 0
        self.ui.m_txtTimer.text = "00:00:00"
    else
        self.startTime = TimeMgr:GetServerTime() - DungeonManager:GetTimestamp()
        self.ui.m_txtTimer.text = self.startTime >= maxTimeLimit and str1 or TimeMgr:ConverSecondToString(self.startTime)
    end
    --self:CheckBoxStatus(self.startTime)
    
    
    self:ShowBattleAnim()
    self:ShowChallengeCountMsg()
    self:CheckQuickFight()
    self:CheckQuickDot()
    self:CheckQuickStatusTxt()
end

--********************************业务逻辑********************************
--------------------------关卡列表--------------------------
--展示有限关卡范围
function UI_LevelEnter:CalculateLevelRange()
    local configRange = 8
    local min = self.curLevelId - configRange
    min = (min < 1) and 1 or min
    local max = self.curLevelId + configRange
    max = (max > maxConfigLevel) and maxConfigLevel or max
    return min,max
end

--展示关卡列表
function UI_LevelEnter:ShowLevelList()
    local curLevelId = self.curLevelId
    local initTrans = self.ui.m_goLevelNode.transform
    self:DestroyAllChild(initTrans)
    local configMax = maxConfigLevel
    local min,max = self:CalculateLevelRange()
    local hasFindCur = false --是否找到当前战斗节点
    local normalCount = 0
    local bossCount = 0
    for i = min,max do
        local data = self.configData[i]
        local type = v2n(data.dungeon_icon_type)
        local template
        if type == DUNGEON_NODE_TYPE.Normal then
            template = self.ui.m_goNormalItem
            if not hasFindCur then
                normalCount = normalCount + 1
            end
        elseif type == DUNGEON_NODE_TYPE.Boss or type == DUNGEON_NODE_TYPE.BigBoss then
            template = self.ui.m_goBossItem
            if not hasFindCur then
                bossCount = bossCount + 1
            end
        end

        if template then
            local obj = UEGO.Instantiate(template,initTrans)
            local status
            if curLevelId > maxConfigLevel then
                status = NodeStatus.Finish
            else
                if data.id == curLevelId then
                    hasFindCur = true
                    status = NodeStatus.Current
                elseif data.id > curLevelId then
                    status = NodeStatus.Lock
                elseif data.id < curLevelId then
                    status = NodeStatus.Finish
                end
            end
            self:SetLevelNodeStatus(obj,v2n(data.dungeon_icon_type),status,data.id,configMax)
        end
    end
    local sum = (normalCount-1)*-190+bossCount*-160+(normalCount+bossCount)*-10 + (-60)
    self.levelNodeRect.anchoredPosition = Vector2.New(sum,0)
end

--关卡节点状态显示
--状态status：0：当前  -1：未解锁  1：已通关
function UI_LevelEnter:SetLevelNodeStatus(obj,type,status,index,configMax)
    local arrow = GetChild(obj,"arrow",UEUI.Image)
    SetUIImage(arrow,status == NodeStatus.Finish
            and "Sprite/ui_huodongjingsai/dmx_pve_guakajt1.png"
            or "Sprite/ui_huodongjingsai/dmx_pve_guakajt2.png")
    
    SetActive(arrow,index ~= configMax)
    
    if type == DUNGEON_NODE_TYPE.Normal then
        local curBg = GetChild(obj,"curBg")
        local lockBg = GetChild(obj,"lockBg")
        local finishBg = GetChild(obj,"finishBg")
        local txt = GetChild(obj,"txt",UEUI.Text)

        if status == NodeStatus.Current then
            txt.color = GetColorByHex("#000000")
        elseif status == NodeStatus.Lock then
            txt.color = GetColorByHex("#DDDDDD")
        elseif status == NodeStatus.Finish then
            txt.color = GetColorByHex("#FFFFFF")
        end

        txt.text = index
        SetActive(curBg,status == NodeStatus.Current)
        SetActive(lockBg,status == NodeStatus.Lock)
        SetActive(finishBg,status == NodeStatus.Finish)
    elseif type == DUNGEON_NODE_TYPE.Boss or type == DUNGEON_NODE_TYPE.BigBoss then
        local bg = GetChild(obj,"bg")
		local bg1 = GetChild(obj,"bg/bg1")
		local bg2 = GetChild(obj,"bg/bg2")

        local icon = GetChild(obj,"icon",UEUI.Image)
        local mark = GetChild(obj,"mark")
        
        local reward = GetChild(obj,"reward")
        local rewardIcon = GetChild(obj,"reward/icon",UEUI.Image)
        local txt = GetChild(obj,"reward/txt",UEUI.Text)
        local select = GetChild(obj,"reward/select",UEUI.Image)
        local btn = GetChild(obj,"reward/btn",UEUI.Button)

        RemoveUIComponentEventCallback(btn,UEUI.Button)
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_dungeon,index)
        SetActive(reward,config and config.reward_first)
        if config then
            local info = Split1(config.reward_first,";")
            info = Split1(info[1],"|")
            local itemId = v2n(info[1])
            local itemNum = v2n(info[2])

            SetUIImage(rewardIcon, ItemConfig:GetIcon(itemId), false)
            txt.text = "x"..itemNum

            AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
                UI_SHOW(UIDefine.UI_ItemTips,itemId)
            end)
        end
        
        SetActive(select,status == NodeStatus.Finish)
        SetActive(mark,status == NodeStatus.Finish)
        SetActive(icon, status == NodeStatus.Lock)

        SetActive(bg, status ~= NodeStatus.Lock)
		SetActive(bg1,type == DUNGEON_NODE_TYPE.Boss)
		SetActive(bg2,type == DUNGEON_NODE_TYPE.BigBoss)
    end
end

--删除所有子节点
function UI_LevelEnter:DestroyAllChild(trans)
    local childCount = trans.childCount
    if childCount <= 0 then
        return
    end

    for i = childCount-1,0,-1 do
        local child = trans:GetChild(i)
        if child then
            UEGO.Destroy(child.gameObject)
        end
    end
end

--------------------------横幅显示--------------------------
--每打开一次界面，打乱配置横幅列表
function UI_LevelEnter:ShuffleTipList()
    local tipConfig = ConfigMgr:GetData(ConfigDefine.ID.slg_dungeon_slogan)
    if not tipConfig then
        return
    end
    
    local sumCount = table.count(tipConfig)
    local list = {}
    for i = 1,sumCount do
        table.insert(list,i)
    end
   
    local count = #list
    while count > 0 do
        local index = Random(1,count)
        list[count], list[index] = list[index], list[count]
        count = count - 1
    end
    
    if self.sequence then
        self.sequence:Pause()
        self.sequence:Kill();
        self.sequence = nil;
    end

    local settingConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_dungeon_setting,12)
    local interval = v2n(settingConfig.value) or 10
    self.sequence = DOTween.Sequence()
    for k,v in ipairs(list)do
        if k > 1 then
            self.sequence:AppendInterval(interval)
        end
        self.sequence:AppendCallback(function()
            SetUIImage(self.ui.m_imgTipHead,tipConfig[v].head_res,false)
            self.ui.m_txtTip.text = LangMgr:GetLang(tipConfig[v].desc)
        end)
    end
    self.sequence:SetLoops(-1,LoopType.Restart)
end

--------------------------挂机收益--------------------------
--倒计时
function UI_LevelEnter:HangUpTimerLogic()
    if self.canTimer then
        self.startTime = self.startTime + 1
        local str
        if self.startTime >= maxTimeLimit then
            str = str1
            self.canTimer = false
        else
            str = TimeMgr:ConverSecondToString(self.startTime)
        end
        self.ui.m_txtTimer.text = str
        self:CheckBoxStatus(self.startTime)
        UI_UPDATE(UIDefine.UI_OfflineRewardGet,4,str)
    end
end

--检测宝箱状态
--在0-1小时之间，显示空宝箱；
--在1-4小时之间，显示开启一半的宝箱
--在4-挂机时间上限，显示满载宝箱
function UI_LevelEnter:CheckBoxStatus(time)
    local status = self.boxStatus
    if time < self.hangUpTime1 then
        status = BoxStatus.Empty
    elseif time >= self.hangUpTime1 and time < self.hangUpTime2 then
        status = BoxStatus.Half
    else
        status = BoxStatus.Full
    end

    if status == self.boxStatus then
       return    
    end
    self.boxStatus = status
    local sprite = ""
    if time < self.hangUpTime1 then
        sprite = "Sprite/ui_huodongjingsai/dmx_pve_jianli_guaji1.png"
        self.boxStatus = BoxStatus.Empty
    elseif time >= self.hangUpTime1 and time < self.hangUpTime2 then
        sprite = "Sprite/ui_huodongjingsai/dmx_pve_jianli_guaji2.png"
        self.boxStatus = BoxStatus.Half
    else
        sprite = "Sprite/ui_huodongjingsai/dmx_pve_jianli_guaji3.png"
        self.boxStatus = BoxStatus.Full
    end
    SetUIImage(self.ui.m_imgBoxIcon,sprite,false)
    SetActive(self.ui.m_goBoxDot,time >= self.hangUpTime1)
    UI_UPDATE(UIDefine.UI_ActivityRankCenter, 2)
end

--------------------------战斗--------------------------
function UI_LevelEnter:TickUI(delta)
	if self.isVisible then
		self.battleFiled:Tick(delta)
	end
end

function UI_LevelEnter:CreateBattleFiled(parent)
	local posA,posB = self:GetBattlePos()
	local battleFiled = require("Game.Battle.BattleFiled").new()
	battleFiled:Init(posA,posB,parent,self:GetViewSortingOrder()-50)

	-- battleFiled:CreateTeam(1,1001)
	-- battleFiled:CreateTeam(11,1001)

	local uiParent = GetChild(self.uiGameObject,"main")
	local canvas = GetComponent(uiParent, TP(UE.Canvas))
	if canvas then
		canvas.overrideSorting = true
		canvas.sortingOrder = self:GetViewSortingOrder()
	end

	return battleFiled
end

function UI_LevelEnter:GetBattlePos()
	local posA = {
		self.ui.m_goPosition_A_1,
		self.ui.m_goPosition_A_2,
		self.ui.m_goPosition_A_3,
		self.ui.m_goPosition_A_4,
		self.ui.m_goPosition_A_5
	}
	local posB = {
		self.ui.m_goPosition_B_1,
		self.ui.m_goPosition_B_2,
		self.ui.m_goPosition_B_3,
		self.ui.m_goPosition_B_4,
		self.ui.m_goPosition_B_5
	}
	return posA,posB
end

function UI_LevelEnter:GetSortingOrderSpace()
	return 100
end

--展示战斗动画
function UI_LevelEnter:ShowBattleAnim()
	if self.battleFiled and self.battleFiled.simulatorCtrl.running then
		return
	end
	
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_dungeon,self.curLevelId)
    if config then
        local config1 = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_dungeon_setting,config.monster_show)
        if config1 then
            --我方阵营
            local leftTeam = {} -- {1001,1002,1003}
			local list = HeroManager:GetHeroVoList(HERO_KIND.ALL,true,true)
            
            table.sort(list,function(a, b) 
                return a.power > b.power
            end)
            
			for i, v in ipairs(list) do
				table.insert(leftTeam,v.heroId)
				if i >= 5 then
					break
				end
			end
            --敌方阵营
            local enemy = Split1(config1.value,"|")
            local rightTeam = {}
            for k,v in ipairs(enemy) do
               table.insert(rightTeam,v2n(v))    
            end
            self.battleFiled.simulatorCtrl:SetData(BATTLE_SIDE.LEFT,leftTeam)
            self.battleFiled.simulatorCtrl:SetData(BATTLE_SIDE.RIGHT,rightTeam)
            self.battleFiled.simulatorCtrl:Run(true)
        end
    end
end

function UI_LevelEnter:OnBattleTeamDie(team)
	local go = self.battleFiled:GetTeamPosGo(team.teamUid)
	if go then
		if UIMgr:GetNormalTopView() ~= self then return end

		local pos = UIMgr:GetObjectScreenPos(go.transform)
		local pos2 =UIMgr:GetObjectScreenPos(self.ui.m_imgBoxIcon.transform)

		local itemId = 5001
		local cnt = math.random(3,5)
		if math.random(0,100) > 50 then
			itemId = 6000001
		end
		MapController:FlyUIAnim(pos.x,pos.y,itemId,cnt,pos2.x,pos2.y)
	end
end

--------------------------挑战次数---------------------------
--展示挑战次数信息
function UI_LevelEnter:ShowChallengeCountMsg()
    local countTime = DungeonManager:GetChallengeCountTime()
    self.challengeCountTime = DungeonManager:GetChallengeCountTime() + self.challengeConfigCountTime - TimeMgr:GetServerTime()
    local rightStr = self.challengeConfigCount
    if self.challengeCountTime > 0 then
        local target = TimeMgr:ConverSecondToString(self.challengeCountTime)
        rightStr = rightStr.."<color=#70fdff>"..LangMgr:GetLangFormat(70000171,target).."</color>"
    end 
    self:SetChallengeCountTxt(self.challengeCount,rightStr)
    if countTime == 0 then
        self.canChallengeTimer = false
    else
        if self.challengeCount >= self.challengeConfigCountTime then
            self.canChallengeTimer = false
        else
            self.canChallengeTimer = true
        end
    end
end

--挑战次数倒计时逻辑
function UI_LevelEnter:ChallengeCountTimerLogic()
    if self.canChallengeTimer then
        self.challengeCountTime = self.challengeCountTime - 1
        local rightStr = self.challengeConfigCount
        if self.challengeCountTime > 0 then
            local target = TimeMgr:ConverSecondToString(self.challengeCountTime)
            rightStr = rightStr.."<color=#70fdff>"..LangMgr:GetLangFormat(70000171,target).."</color>"
        end
        self:SetChallengeCountTxt(self.challengeCount,rightStr)
        if self.challengeCountTime == 0 then
            self.challengeCount = self.challengeCount + 1
            local isFull = self.challengeCount >= self.challengeConfigCount
            if isFull then
                self.canChallengeTimer = false
            else
                self.challengeCountTime = self.challengeConfigCountTime
            end
            self:SetChallengeCountTxt(self.challengeCount,isFull and self.challengeConfigCount or rightStr)
        end
    end
end

--设置挑战次数文本
function UI_LevelEnter:SetChallengeCountTxt(txt1,txt2)
    self.ui.m_txtFightCount.text = LangMgr:GetLangFormat(70000170,txt1,txt2)
end

--计算购买挑战次数的花费
function UI_LevelEnter:CalculateBuyCountCost()
    local value1 = DungeonManager:GetSettingValue(22,30)
    local value2 = DungeonManager:GetSettingValue(23,10)
    local configSum = DungeonManager:GetSettingValue(21,5)
    local count = DungeonManager:GetChallengeChargeCount()
    local target = configSum - count+1
    return value1+value2*(target-1)
end

--------------------------碾压逻辑---------------------------
--判断是否可碾压
function UI_LevelEnter:CheckQuickFight()
    local canQuick = false
	local bossType = DUNGEON_NODE_TYPE.Normal
    local limitLevel = DungeonManager:GetSettingValue(25,4)
    if self.curLevelId > limitLevel then
        --需要通关配置关卡才能解锁碾压功能
        local list = HeroManager:GetHeroVoList(HERO_KIND.ALL,true,true)
        table.sort(list,function(a, b)
            return a.power > b.power
        end)

        local sumPower = 0
        for i = 1,5 do
            if list[i] then
                sumPower = sumPower + list[i].power
            end
        end

        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_dungeon,self.curLevelId)
        if config then
            canQuick = sumPower >= v2n(config.overwhelming)
			bossType = config.dungeon_icon_type or DUNGEON_NODE_TYPE.Normal
        end
    end

	--if canQuick then
	--	SetActive(self.ui.m_btnQuickFight,true)
	--	SetActive(self.ui.m_btnFight1,false)
	--	SetActive(self.ui.m_btnFight2,false)
	--	SetActive(self.ui.m_btnFight3,false)
	--else
	--	SetActive(self.ui.m_btnQuickFight,false)
	--	SetActive(self.ui.m_btnFight1,bossType == DUNGEON_NODE_TYPE.Normal)
	--	SetActive(self.ui.m_btnFight2,bossType == DUNGEON_NODE_TYPE.Boss)
	--	SetActive(self.ui.m_btnFight3,bossType == DUNGEON_NODE_TYPE.BigBoss)
	--end

    SetActive(self.ui.m_btnQuickFight,canQuick)
    SetActive(self.ui.m_btnFight1,bossType == DUNGEON_NODE_TYPE.Normal)
    SetActive(self.ui.m_btnFight2,bossType == DUNGEON_NODE_TYPE.Boss)
    SetActive(self.ui.m_btnFight3,bossType == DUNGEON_NODE_TYPE.BigBoss)
    local isCanSweep = DungeonManager:GetIsOpenSweepFight()
    SetActive(self.ui.m_btnSweepFight,isCanSweep)
end

--------------------------角色礼包---------------------------
function UI_LevelEnter:CheckRoleGift()
    self.giftTimerTxtList = {}
    local hasGift = false
    local msg = BagManager:GetRoleGiftList() or {}
    local info = {}
    --for _,v in ipairs(msg) do
    --    local isOverTime = self:IsOverTime(v.gift_id,v.open_timestamp)
    --    if not isOverTime then
    --        table.insert(info,v)
    --    end
    --end
    for _,v in ipairs(msg) do
        if v.open_timestamp and v.open_timestamp == 0 then
            table.insert(info,v)
        end
    end
    hasGift = table.count(info) > 0
    local GetDrawType = function(id) 
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_role_gift,id)
        if config then
            return v2n(config.draw_type)
        end
        return 1
    end
    
    table.sort(info,function(a, b)
        local flag_a = GetDrawType(a.gift_id)
        local flag_b = GetDrawType(b.gift_id)
        if flag_a == flag_b then
            return a.gift_id < b.gift_id
        else
            return flag_a < flag_b
        end
    end)

    local list = {}
    for i = 1,3 do
        if info[i] then
            table.insert(list,info[i])
        end
    end
    local parent = self.ui.m_goGiftNode.transform
    self:DestroyAllChild(parent)

    for _,v in ipairs(list) do
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_role_gift,v.gift_id)
        if config then
            local obj = UEGO.Instantiate(self.ui.m_goGiftEnter,parent)
            local bg = GetChild(obj,"bg",UEUI.Image)
            local txt = GetChild(obj,"time/txt",UEUI.Text)
            local icon = GetChild(obj,"icon",UEUI.Image)
            local name_2 = GetChild(obj,"name_2",UEUI.Text)
            local name_3 = GetChild(obj,"name_3",UEUI.Text)
            local name_4 = GetChild(obj,"name_4",UEUI.Text)
            local btn = GetChild(obj,"btn",UEUI.Button)
            local time = GetChild(obj,"time")
            RemoveUIComponentEventCallback(btn,UEUI.Button)
            AddUIComponentEventCallback(btn,UEUI.Button, function(arg1,arg2)
                UI_SHOW(UIDefine.UI_RoleGiftList,v.gift_id)
            end)
            local existTime = v.open_timestamp and v.open_timestamp ~= 0
            SetActive(time,existTime)
            if existTime then
                txt.text = TimeMgr:CheckHMS(self:GetGiftTimerValue(v.gift_id,v.open_timestamp))
                table.insert(self.giftTimerTxtList,{gift_id = v.gift_id;open_timestamp = v.open_timestamp;txt = txt})
            end
            name_2.text = LangMgr:GetLangFormat(config.gift_langid,LangMgr:GetLang(config.langid))
            name_3.text = LangMgr:GetLangFormat(config.gift_langid,LangMgr:GetLang(config.langid))
            name_4.text = LangMgr:GetLangFormat(config.gift_langid,LangMgr:GetLang(config.langid))
            SetActive(name_2,config.quality == 2)
            SetActive(name_3,config.quality == 3)
            SetActive(name_4,config.quality == 4)
            SetUIImage(icon,config.gift_role,false)
            SetUIImage(bg,QUALITY_UNDER_BG[config.quality],false)
        end
    end

    SetActive(self.ui.m_goGift,hasGift)
    SetActive(self.ui.m_goTip,not hasGift)
end

--判断礼包是否过期
function UI_LevelEnter:IsOverTime(giftId,timestamp)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_role_gift,giftId)
    if config then
        local existTime = config.count and config.count ~= 0
        if existTime then
            return self:GetGiftTimerValue(giftId,timestamp) <= 0
        else
            return false
        end
    end
    return false
end

--获取礼包倒计时剩余时间
function UI_LevelEnter:GetGiftTimerValue(giftId,timestamp)
    local nowTime = TimeMgr:GetServerTimestamp()
    local nextTime = timestamp - nowTime
    return  nextTime <= 0 and 0 or nextTime
    
    --local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_role_gift,giftId)
    --if config then
    --    local existTime = config.count and config.count ~= 0
    --    if existTime then
    --        local nowTime = TimeMgr:GetServerTimestamp()
    --        local pastTime = nowTime - timestamp
    --        local result = config.count - pastTime
    --        result = result <= 0 and 0 or result
    --        return  result
    --    end
    --end
    --return 0
end

--展示各个角色礼包倒计时
function UI_LevelEnter:ShowGiftTimerValue()
    for k,v in ipairs(self.giftTimerTxtList) do
        local timeValue = self:GetGiftTimerValue(v.gift_id,v.open_timestamp)
        if timeValue == 0 then
            UI_UPDATE(UIDefine.UI_LevelEnter,2)
            break;
        end
        if v.txt then
            v.txt.text = TimeMgr:CheckHMS(timeValue)
        end
    end
end

--判断快速奖励红点
function UI_LevelEnter:CheckQuickDot()
    local freeCount = DungeonManager:GetFreeCount() --免费快速挂机次数
    SetActive(self.ui.m_goQuickDot,freeCount > 0)
end

--判断快速奖励状态文本
function UI_LevelEnter:CheckQuickStatusTxt()
    local freeCount = DungeonManager:GetFreeCount() --免费快速挂机次数
    local payCount = DungeonManager:GetChargeCount()--付费快速挂机次数

    if freeCount > 0 then
        --"本次免费"
        self.ui.m_txtQuick.text = LangMgr:GetLang(70000649)
    else
        local configSum = DungeonManager:GetSettingValue(4,10)
        self.ui.m_txtQuick.text = payCount.."/"..configSum
    end
end
 
return UI_LevelEnter