---@class UIActRankItem
local UIActRankItem = {}
local M = UIActRankItem

local prePath = "Assets/ResPackage/Prefab/UI/UI_ActivityRankItem.prefab"
local pre
--local duration = 0.7

function M:Create(parent,id,callBack)
    local item = {}
    setmetatable(item,{__index = M})
 
	local function funcW(prefab)
		local newGo = CreateGameObjectWithParent(prefab,parent)
		item.go = newGo
		item.trans = newGo.transform
		item.go.name = id
		item:Init()
		if callBack then
			callBack(item,item.go)
		end
	end
	ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,funcW)

    --return item
end

function M:Init()
	self.bg =  GetChild(self.go, "obj/bg", UEUI.Image);
	self.icon =  GetChild(self.go, "obj/icon", UEUI.Image);
	self.nameTxt =  GetChild(self.go, "obj/nameTxt", UEUI.Text);
	self.nameOutLine = GetComponent(self.nameTxt, UEUI.Outline);
	
    self.btn = GetChild(self.go,"obj/bg", UEUI.Button);
	self.redPoint = GetChildTrans(self.go, "obj/red");

	RemoveUIComponentEventCallback(self.btn,UEUI.Button)
    AddUIComponentEventCallback(self.btn,UEUI.Button, function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
	EventMgr:Add(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
end

function M:OnRedPointDirty(dirtyIdSet)
	if not self:ShowActive() then
		return 
	end
	local redId
	if self.id == 6 then
		--关卡系统
		if dirtyIdSet[RedID.DungeonEntry] then
			SetActive(self.redPoint, RedPointMgr:IsRed(RedID.DungeonEntry))
		end
	elseif self.id == 10 then
		if dirtyIdSet[RedID.WorldBoss] then
			SetActive(self.redPoint, RedPointMgr:IsRed(RedID.WorldBoss))
		end
	elseif self.id == 11 then
		if dirtyIdSet[RedID.TopFight] then
			SetActive(self.redPoint, RedPointMgr:IsRed(RedID.TopFight) or NetLimitActGift:IsShowRed(TopFightManager.GIFT_TYPE))
		end
	end
end

function M:SetItem(config,selectFun)
	if nil == config then
		return
	end
	self.config = config;

	self.selectFun = selectFun
	self.viewName = config.viewname
	self.id = config.id
	SetImageSprite(self.icon, config.icon, false)

	local openArr = string.split(config.open, "|");
	self.openType = v2n(openArr[1]);
	self.openParm = v2n(openArr[2]);

	if self.openType == 1 and self.openParm == ActivityTotal.Rank then
		local _, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Rank);
		if active and active.form then
			self.nameTxt.text = LangMgr:GetLang(active.form.title);
		else
			self.nameTxt.text = LangMgr:GetLang(v2n(config.name));
		end
	else
		self.nameTxt.text = LangMgr:GetLang(v2n(config.name));
	end

	self:CheckUiItemRed()
	self:ShowActive(true)
end

function M:CheckUiItemRed()
	if not self:ShowActive() then
		return false
	end
	local isShow = false
	if self.openType == 1 then
		if self.openParm == ActivityTotal.BowlingBattle then
			isShow = BowlingBattleManager:CheckRedPoint()
		elseif self.openParm == ActivityTotal.OneToOne then
			isShow = NetOneToOneData:IsShowRedPoint()
		elseif self.openParm == ActivityTotal.AthleticTalent then
			isShow = NetAthleticTalentData:IsShowRedPoint()
		elseif self.openParm == ActivityTotal.Rank then
			isShow = NetRankData:IsShowRedPoint(ActivityTotal.Rank) or NetUpdatePlayerData:GetPlayerRankOneRewardRed();
		end
	end

    -- 贸易货车
    if self.id == 8 then
        isShow = TradeWagonsManager:CheckRedPoint()
    elseif self.id == 9 then
		--竞技场
		isShow = JJcManager:CheckArenaEntryRedDot()
	elseif self.id == 6 then
		--关卡系统
		isShow = DungeonManager:CheckDungeonRedDot()
	elseif self.id == 10 then
		-- 世界Boss
		isShow = RedPointMgr:IsRed(RedID.WorldBoss);
	elseif self.id == 11 then
		-- 争霸赛
		isShow = RedPointMgr:IsRed(RedID.TopFight) or NetLimitActGift:IsShowRed(TopFightManager.GIFT_TYPE)
	end
	SetActive(self.redPoint,isShow) 
	return isShow
end

function M:ShowActive(log)
	local isShow = false
	local activityOpen ,activity = NetGlobalData:GetIsOpenActivityRank(self.config)

	isShow = activityOpen
	if self.id == 11 then
		if TopFightManager.state and TopFightManager.state == 0 then
			isShow = false
		end
	end
    SetActive(self.go,isShow)
	return isShow
end

function M:IsSelect(value)
	if value then
		SetUIImage(self.bg, "Sprite/ui_huodongjingsai/jingsai_menu2.png", true);
		self.nameTxt.color = Color.HexToRGB("FFFFFF"); -- 00761f
		--self.nameOutLine.effectColor = Color.New(254 / 255, 1, 222 / 255, 1); -- feffde
	else
		SetUIImage(self.bg, "Sprite/ui_huodongjingsai/jingsai_menu1.png", true);
		self.nameTxt.color = Color.HexToRGB("C3F9FF"); -- 00761f
		--self.nameOutLine.effectColor = Color.New(169 / 255, 248 / 255, 1, 1); -- A9F8FF
	end
	UnifyOutline(self.nameTxt,value and "#1b5300" or "#273468")
end

function M:TickUI(delta)
  
end


function M:ClickItem(arg1,arg2)
	local actView = UIMgr:GetUIItem(UIDefine.UI_ActivityRankCenter)
	if nil ~= actView then
		if self.openType == 1 then
			if self.openParm == ActivityTotal.OneToOne then
				local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.OneToOne)
				if activityItem:IsActivityEnd() then
					if activityItem.info.activeId then
						NetOneToOneData:CheckEndPush(activityItem.info.activeId)
					end
					return
				end
			elseif self.openParm == ActivityTotal.AthleticTalent then
				local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.AthleticTalent)
				if activityItem:IsActivityEnd() then
					if activityItem.info.activeId then
						NetAthleticTalentData:CheckEndPush(activityItem.info.activeId)
					end
					return
				end
			end
		end

		actView:SetShowIndex(self.id)
		actView:ShowNowView()
	end
	if self.selectFun then
		self.selectFun(arg1,self.id)
	end
end

function M:OpenView(isPush,param)
	local actView = UIMgr:GetUIItem(self.viewName)
	if nil ~= actView and actView.isShow then
		return
	end
	UI_SHOW(self.viewName, isPush, param)
	UI_UPDATE(UIDefine.UI_ActivityRankCenter,5,self.viewName)
end

function M:RefreshView(param)
	local actView = UIMgr:GetUIItem(self.viewName)
	if nil ~= actView and actView.isShow then
		UI_UPDATE(self.viewName, param)
	end
end

function M:Close()
	local actView = UIMgr:GetUIItem(self.viewName)
	if nil ~= actView then
		if actView.isShow then
			actView:Close()
		end
	end
	EventMgr:Remove(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
end

function M:CloseNoAnima()
	local actView = UIMgr:GetUIItem(self.viewName)
	if nil ~= actView then
		if actView.isShow then
			UIMgr:Close(self.viewName)
		end
	end
end

return M