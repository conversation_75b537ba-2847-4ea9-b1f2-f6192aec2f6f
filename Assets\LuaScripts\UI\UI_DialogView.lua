local UI_DialogView = Class(BaseView)
local M = UI_DialogView

local itemPath = "Assets/ResPackage/Prefab/UI/rewardItemBig.prefab"
local itemSmallPath = "Assets/ResPackage/Prefab/UI/rewardItemSmall.prefab"
local newTaskPath = "Assets/ResPackage/Prefab/UI/NewTaskItem.prefab"
local itemPre
local itemSmallPre
local newTaskPre
local initScale

local strNarration = "Narration"

--手机信息
local CanvasGroup_PhoneTips
local CanvasGroup_btnCheckAll

--软件聊天
local go_ChatContent
local ScrollRect_Chat
local VerticalLayout_Chat
local CanvasGroup_btnChatFinish

--选择按钮聊天
--local lPos_origin_Role	--角色原始位置
--local lPos_origin_Msg	--聊天框原始位置(左、右、旁白)
local tb_ChatBtnTxt = {}
local tb_ChatBtnDlg = {}

local isInAutoNext = false --是否处于自动下一步

RoleSite = {
    Left = "left",
    Left2 = "left2",
    Right = "right",
    Right2 = "right2"
}
function M:OnInit()
    initScale = Vector3.one
	self.m_BlockWall = require "Game.MapLogic.TutorialBlockController".new()
end

function M:OnCreate(taskParam, logs,call, isNotDelay, isBlackCover, onAfterShowCall)
    self.callBack = call
	self.onAfterShowCall = onAfterShowCall
    if logs == nil then
        Log.Error("UI_DialogView需要传递logs为空",taskParam)
		NetPushViewData:RemoveViewByIndex(PushDefine.NewTask)
		NetPushViewData:CheckOtherView(true)
		self:Close()
    end
    self.findItem = nil
    self.isWait = false
    self.delta = 0
    self.nowTaskParam = taskParam
	self.m_skip = false
	self.isHideMainFace = false
	self.guideline = nil
    --子物体体初始化
 
    if ObjIsNil(newTaskPre) then
        newTaskPre = ResMgr:LoadAssetSync(newTaskPath, AssetDefine.LoadType.Instant)
    end
    ---信息初始化
    self.items = {}
    self.newItems = {}
    self.lastShow = nil -- 上一个显示
	self.cantclose = nil
    --通过位置整合
    self.showGo = { left = self.ui.m_goLeft, right = self.ui.m_goRight, left2 = self.ui.m_goLeft2, right2 = self.ui.m_goRight2 }
    self.showSpine = { left = self.ui.m_spuiLeft, right = self.ui.m_spuiRight, left2 = self.ui.m_spuiLeft2, right2 = self.ui.m_spuiRight2 }
    self.showAni = {
        left = GetComponent(self.ui.m_spuiLeft.transform, TweenAnim),
        right = GetComponent(self.ui.m_spuiRight.transform, TweenAnim),
        left2 = GetComponent(self.ui.m_spuiLeft2.transform, TweenAnim),
        right2 = GetComponent(self.ui.m_spuiRight2.transform, TweenAnim),
    }
	local leftTxt = GetChild(self.ui.m_goLeftMess,"bg/txtContent",UEUI.Text)
	local rightTxt = GetChild(self.ui.m_goRightMess,"bg/txtContent",UEUI.Text)
	local leftNameTxt = GetChild(self.ui.m_goLeftMess,"headBg/nameBg/txtName",UEUI.Text)
	local rightNameTxt = GetChild(self.ui.m_goRightMess,"headBg/nameBg/txtName",UEUI.Text)
    self.showTit = { left = leftNameTxt, right = rightNameTxt, left2 = leftNameTxt, right2 = rightNameTxt }
    self.showTxt = { left = leftTxt, right = rightTxt, left2 = leftTxt, right2 = rightTxt, Narration = self.ui.m_txtNarration }
    self.showMess = { left = self.ui.m_goLeftMess, right = self.ui.m_goRightMess, left2 = self.ui.m_goLeftMess, right2 = self.ui.m_goRightMess, Narration = self.ui.m_goNarrationMess}
	self.showChatMsg = {left = self.ui.m_goChatMsgLeft, right = self.ui.m_goChatMsgRight } 
	self.showStaticImgGo = { left = self.ui.m_goLeftStatic, right = self.ui.m_goRightStatic, left2 = self.ui.m_goLeftStatic, right2 = self.ui.m_goRightStatic }
	self.showStaticImage = { left = self.ui.m_imgLeftStatic, right = self.ui.m_imgRightStatic, left2 = self.ui.m_imgLeftStatic, right2 = self.ui.m_imgRightStatic }
	self:InitLog(logs)
	
	local fX,fY,fZ = self.ui.m_goMessPoss.transform:GetLocalPosition()
	--lPos_origin_Role = Vector3.New(fX,fY,fZ)
	
	fX,fY,fZ = self.ui.m_goLeftMess.transform:GetLocalPosition()
	--lPos_origin_Msg = Vector3.New(fX,fY,fZ)

	
	CanvasGroup_PhoneTips = self.ui.m_goPhoneTips:GetComponent("CanvasGroup")
	CanvasGroup_btnCheckAll = self.ui.m_btnCheckAll:GetComponent("CanvasGroup")
	
	ScrollRect_Chat = GetChild(self.ui.m_goChat,"viewPort", typeof(UEUI.ScrollRect)) 
	go_ChatContent = GetChild(self.ui.m_goChat,"viewPort/content")
	VerticalLayout_Chat = go_ChatContent:GetComponent(typeof(CS.UnityEngine.UI.VerticalLayoutGroup))
	CanvasGroup_btnChatFinish = self.ui.m_btnChatFinish:GetComponent("CanvasGroup")
	
	SetActive(self.ui.m_goBg,true)
	local goBg = GetComponent(self.ui.m_goBg,UEUI.Image)
	goBg.color = Color.New(0,0,0,1/255)
	--self:CreateScheduleFun(function() self:NextStep() end,0.3,1)
	local fDelay = 0.3
	if isNotDelay then
		fDelay = 0
	end
 
	self:TryMoveCamera(self.stepNow,function ()
		self:PresetRole(self.stepNow)
		
		self:CreateScheduleFun(function() 
			goBg.color = Color.New(0,0,0,199/255)
			self:NextStep() 
		end,fDelay,1)
	end)
	
    self:SetIsUpdateTick(true)
	
	--if isBlackCover then
		--SetActive(self.ui.m_imgBlackBg, true)
	--end
end

function M:TryMoveCamera(index,callBack)

	self:ClearHollowMap(index)
	local log = self.logList[index]

	self:HideMainFace(log.type == 1)

	if log and log.guideline then
		self.guideline = log.guideline
	end

	if log and log.zoom then
		MapController:SetCameraSizeAnim(log.zoom, 0.5)
	end

	if log and log.camera_pos then
		MapController:SetMoveCameraToPos(GuideConfig:ReadVt2(log.camera_pos), function ()
			self:TryHollowMap(index)
			if callBack then
				callBack()
			end
		end, true, "guide")
	else
		self:TryHollowMap(index)
		if callBack then
			callBack()
		end
	end
end

function M:TryHollowMap(index)
	local log = self.logList[index]
	if log and log.map_hollow_info then
		self.m_BlockWall:SetLayer(SortingLayerInGame.UI_TOP,600)
		self.m_BlockWall:SetGrids(GuideConfig:ReadHollow(log.map_hollow_info),nil,true)
		SetActive(self.ui.m_goBg,false)
	else
		SetActive(self.ui.m_goBg,true)
	end
end

function M:ClearHollowMap(index)
	local log = self.logList[index]
	if log and log.map_hollow_info then
		SetActive(self.ui.m_goBg,false)
	else
		SetActive(self.ui.m_goBg,true)
	end
	self.m_BlockWall:SetGrids()
end

function M:InitLog(logs)
    SetActive(self.ui.m_goNewTask,false)
    SetActive(self.ui.m_goTaskComplete,false)
    SetActive(self.ui.m_goTaskUnlock,false)
    SetActive(self.ui.m_goTaskHero,false)
	SetActive(self.ui.m_goPhoneMess,false)
	SetActive(self.ui.m_goPhoneChat, false)
	SetActive(self.ui.m_goChooseChatBtn, false)

    self.logList = logs
    self.stepNow = 1
    self.stepOver = #self.logList
    
end

function M:HideRoleStaticImage()
	for k, v  in pairs(self.showStaticImgGo) do
		SetActive(v, false)
	end
end
function M:HideRoleSpine()
	for k, v  in pairs(self.showGo) do
		SetActive(v, false)
	end
end

--设置预加载人物
function M:PresetRole(index)
    --下个阶段开始之前预加载人物图片
    local init = { left = false, left2 = false, right = false, right2 = false }
    for i = index, self.stepOver do
        local log = self.logList[i]
        if log.type == 1 or log.type == 1001 then
            if i == 1 then
				if log.type == 1 then
					self.showTit[log.npc].text = LangMgr:GetLang(log.who)
					--SetActive(self.showMess[log.npc],true)
					--SetUILastSibling(self.showGo[log.npc])
				end
            else
				if log.img_use then
                    if log.id == 1501 then
                        self.showSpine[log.npc].color = Color.New(0,0,0,1)
                    else
                        self.showSpine[log.npc].color = Color.New(0.5,0.5,0.5,1)
                    end
				elseif log.static_img then
					self.showStaticImage[log.npc].color = Color.New(0.5,0.5,0.5,1)
				end
            end
            if init[log.npc] == false then
                init[log.npc] = true
				
				if log.img_use then
					if self.showGo[log.npc].activeSelf == false then
						SetActive(self.showGo[log.npc], true)
					end
					self:SetSpineUI(log.npc,log.img,log.img_use,true)
				else
					self:HideRoleSpine()
				end

				if log.static_img then
					if self.showStaticImgGo[log.npc].activeSelf == false then
						SetActive(self.showStaticImgGo[log.npc], true)
					end
					SetUIImage(self.showStaticImage[log.npc], log.static_img, false)
				else
					self:HideRoleStaticImage()
				end

            end
        end
    end
    if self.stepOver == 1 and self.logList[1].type ~= 1 then
        SetActive(self.ui.m_goBlack,false)
    else
        SetActive(self.ui.m_goBlack,true)
    end
end

function M:SetSpineUI(dir,spineName,spineAni,isStart)
    if self.stepOver == 1 then
        isStart = true
    end
    local spine = self.showSpine[dir]
    if spineName == nil then
        spine:Clear()
        --spine:Initialize(true)
        return
    end
    local item = RoleSpineConfig:GetSkeletonDataById(spineName)
	
	if item.mess.is_picture then
		SetActive(spine,false)
		--SetActive(img,true)
		--SetImageSprite(img,item.img,false)
	else
		SetActive(spine,true)
		local function funcW()
			local size = item.mess.task_size or 1
			spine.transform.localScale = Vector3.New(size,size,1)
			SetSpineAnim(spine,spineAni)
		end
		RoleSpineConfig:SetSpineByName(spine,spineName,funcW)
		
		--SetActive(img,false)
		
		--spine.skeletonDataAsset = item.spine
		--spine:Initialize(true)
		--local size = item.mess.task_size or 1
		--spine.transform.localScale = Vector3.New(size,size,1)

		--if item.straight_alpha == 0 then
			--local ma =  GetComponent(spine.gameObject,CS.MaterialSelect)
			--if ma then
				--local ma1 = ma:GetMaterial(1)
				--if ma1 then
					--spine.material = ma1
				--end
			--end
		--end
		--SetSpineAnim(spine,spineAni)
	end
	
    if isStart then
        local parent = self.showGo[dir]
        local site = string.split(item.mess.task_site,"|")
        local pos = parent.transform.localPosition
        if dir == RoleSite.Right or dir == RoleSite.Right2 then
            site[1] = -site[1]
        end
        parent.transform.localPosition = Vector3.New(pos.x + site[1],pos.y+site[2],0)
    end

end

function M:NextStep(temp)
	if self.loadtex == true and isInAutoNext == false then
		self.loadtex = false
		self.tweener:Kill()
		self.tex.text = self.texstr
		TimeMgr:DestroyTimer(self,self.textimeID)
		return
	end

	if self.m_skip then
		while self.stepNow <= self.stepOver do
			local log = self.logList[self.stepNow]
			if v2n(log.skip) == 1 then--对话可以跳过
				self.stepNow = self.stepNow + 1
			else
				break
			end
		end
	end

    --判断流程结束
    if self.stepNow > self.stepOver then
        for dir, v in pairs(self.showGo) do
            if v.activeSelf then
                self.showAni[dir]:DOPlayBackwards()
            end
        end
        if temp and #temp > 0 then
            UIMgr:SetUILock(true, UIDefine.UI_DialogView,true)
            --AudioMgr:Play(8)
            self:CreateScheduleFun(function()
                UIMgr:CloseByView(self)
                UIMgr:RefreshAllMainFace(1,15,temp)
					
				self:CreateScheduleFun(function()	
						UIMgr:SetUILock(false, UIDefine.UI_DialogView,true)
						NetPushViewData:CheckOtherView(true,true)
				 end,self:GetTaskMotionSpeed(),1)
            end,self:GetTaskMotionSpeed(),1)
        else
            self:Close()
        end
		
		if self.tweener and not self.tweener:IsComplete() then
			self.tweener:Kill()
		end

        return
    end

    local step = self.logList[self.stepNow]

    --判段上一次状态
    if self.lastShow and self.lastShow.type ~= 1 then
		if self.lastShow.type == 1001 then
			SetActive(self.showMess[strNarration], false)
		elseif self.lastShow.type == 1002 then
			SetActive(self.ui.m_goPhoneMess, false)
		elseif self.lastShow.type == 1004 then
			SetActive(self.ui.m_goChooseChatBtn, false)
			--self:SetChatPosition(step.type)
		end
		
        self:SetOtherHide()
        self:PresetRole(self.stepNow)
        for _, v in pairs(self.items) do
            UEGO.Destroy(v)
        end
        self.items = {}

    end

	self:TryMoveCamera(self.stepNow,function ()
		self:DoNextStep(step)
	end)

end

function M:DoNextStep(step)

	SetActive(self.ui.m_btnSkip,v2n(step.skip) == 1)
	
	local thinkTable = {}
	thinkTable.log_id = step.id
	SdkHelper:ThinkingTrackEvent(ThinkingKey.Dialog,thinkTable)
    if step.type == 1 then
        --self:Say(step.npc, step.img, step.who, step.dlg,step.img_use, step.static_img,step.id)
        self:NewSay(step.npc, step.role_icon, step.who, step.dlg,step.img_use, step.static_img,step.id)
    else
        self:SetOtherActive(step)
		local goBg = GetComponent(self.ui.m_goBg,UEUI.Image)
		goBg.color = Color.New(0,0,0,199/255)
        if step.type == 2 then
			goBg.color = Color.New(0,0,0,1/255)

            function CreateNewTask(id,img,mess)
                AudioMgr:Play(8)
				local target = NetTaskData:GetTarget(id)
				target = ((target == 0 or target == nil) and {1} or {target})[1]
				local item = CreateGameObject(newTaskPre)
				AddChild(self.ui.m_goTaskList, item)
				table.insert(self.newItems,{id = id,item = item})
            end
            if type(self.nowTaskParam) == "table" then
                for _, id in ipairs(self.nowTaskParam) do
                    local task = TaskConfig:GetDataByID(id)
                    CreateNewTask(id,task.icon,LangMgr:GetLang(task.conditions_lid) )
					if NetUpdatePlayerData:GetLevel() >= 5 then
						UIMgr:PopupPromptUi(PopupPromptUiType.TASK_ACCEPT,{id,task.icon,LangMgr:GetLang(task.conditions_lid)})
					end
                end
            else
                local task = RandomTaskConfig:GetDataByID(self.nowTaskParam)
                CreateNewTask(task.id,task.icon,LangMgr:GetLang(task.conditions_lid))
				if NetUpdatePlayerData:GetLevel() >= 5 then
					UIMgr:PopupPromptUi(PopupPromptUiType.TASK_ACCEPT,{task.id,task.icon,LangMgr:GetLang(task.conditions_lid)})
				end
            end
			TimeMgr:CreateTimer(self, function()
				self:onUIEventClick(nil,nil,true)
			end, self:GetTaskMotionSpeed(),1)

		elseif step.type == 1001 then
			self:NarrationSay(step.dlg, step.auto_next_step_delay)
		elseif step.type == 1002 then
			self:PhoneTipsMsg(step.dlg, step.head, step.who, step.tm, step.date)
		elseif step.type == 1003 then
			self:ChatMsg(step.dlg, step.title_head, step.head, step.who, step.npc, step.btn_end, step.auto_next_step_delay)
		elseif step.type == 1004 then
			--self:SetChatPosition(step.type)
			self:ChooseChatBtn(step.npc, step.img, step.who, step.dlg,step.img_use, step.static_img, step.chat_btn_txt, step.chat_btn_dlg)
        else
            local mess = NetTaskData:GetTaskItem(self.nowTaskParam)
            local target,targetItem = NetTaskData:GetTarget(self.nowTaskParam)
            if target == nil then
                --Log.Error(self.nowTaskParam,"id target为空随机任务")
				Log.Error(" .type = " .. tostring(step.type) .. "  error  id = " .. step.id  )
                Log.Info(NetTaskData:GetRandomTask())
				NetPushViewData:RemoveViewByIndex(PushDefine.FinishTask,1)
				NetPushViewData:CheckOtherView(true,true)
                self:Close()
                return
            end
            if step.type == 3 then
				goBg.color = Color.New(0,0,0,1/255)
				local function callBack(assetItem)
					--self:SetRewards(self.nowTaskParam, GetChild(self.ui.m_goTaskComplete,"Item/m_rtransMask/rewards"),itemSmallPre,false)
					self:SetRewards(self.nowTaskParam, GetChild(self.ui.m_goTaskComplete,"Item/m_rtransMask/rewards"),assetItem,false)
				end
				ResMgr:LoadAssetAsync(itemSmallPath, AssetDefine.LoadType.Instant,callBack)
				
				UIMgr:PopupPromptUi(PopupPromptUiType.TASK_FINISH,{self.nowTaskParam})
				TimeMgr:CreateTimer(self, function()
					self:onUIEventClick(nil,nil,true)
				end, 0.3,1)

            elseif step.type == 4 then
                SetActive(self.ui.m_goTaskUnlock, true)
                SetImageSprite(self.ui.m_imgRew, ItemConfig:GetImg(targetItem),true)
                --self:SetRewards(self.nowTaskParam, self.ui.m_goCont,itemPre,true)
				local function callBack(assetItem)
					self:SetRewards(self.nowTaskParam, self.ui.m_goCont,assetItem,true)
				end
				ResMgr:LoadAssetAsync(itemPath, AssetDefine.LoadType.Instant,callBack)
                self.ui.m_txtUnlockName.text = ItemConfig:GetLangByID(targetItem)

            elseif step.type == 5 then
				local itemconfig = ItemConfig:GetDataByID(targetItem)
                --local item = RoleSpineConfig:GetSkeletonDataById(HeroFallingConfig:GetDataByID(targetItem).role)
                --self.ui.m_spuiHero.skeletonDataAsset = item.spine
                --self.ui.m_spuiHero:Initialize(true)
				--local function funcW()
				--	SetSpineAnim(self.ui.m_spuiHero,"idle")
				--	self.ui.m_spuiHero.transform.localScale = GetVector3ByStr(itemconfig.scale)*2.7
				--end
				--RoleSpineConfig:SetSpineByName(self.ui.m_spuiHero,HeroFallingConfig:GetDataByID(targetItem).role,funcW)
				
				self.ui.m_txtStory.text = LangMgr:GetLang( itemconfig.explain)
				self.cantclose = true
				TimeMgr:CreateTimer(self, function()
						self.cantclose = nil
					end, 2,0.5)
                SetActive(self.ui.m_goTaskHero, true)
                SetImageSprite(self.ui.m_imgHero, ItemConfig:GetIcon(targetItem),false)
				local function callBack(assetItem)
					self:SetRewards(self.nowTaskParam, self.ui.m_goReward,assetItem,true)
				end
				ResMgr:LoadAssetAsync(itemPath, AssetDefine.LoadType.Instant,callBack)
                self.ui.m_txtHeroName.text = ItemConfig:GetLangByID(targetItem)
            elseif step.type == 7 then
                function FindItem(item)
                    if item.m_Config.id == targetItem then
                        self.findItem = item
                        --item:setVisible(false)
                        return true
                    end
                end
                MapController:ForeachItems(FindItem)
                local item = RoleSpineConfig:GetSkeletonDataById(step.img)
                if item then
                    if item.spine_path then--item.spine
						local function funcW()
							SetActive(self.ui.m_spuiZooHero, true)
							self.ui.m_imgZooUnlock.enabled = false
						end
						RoleSpineConfig:SetSpineByName(self.ui.m_spuiZooHero,item.spine_path,funcW)
                        --self.ui.m_spuiZooHero.skeletonDataAsset = item.spine
                        --self.ui.m_spuiZooHero:Initialize(true)
                        --SetActive(self.ui.m_spuiZooHero, true)
                        --self.ui.m_imgZooUnlock.enabled = false
                    else
                        SetUIImage(self.ui.m_imgZooUnlock, item.img)
                        SetActive(self.ui.m_spuiZooHero, false)
                        self.ui.m_imgZooUnlock.enabled = true
                    end
                else
                    Log.Error("动物 spine 或图片不存在", step)
                end
                SetActive(self.ui.m_goUnlockZoo, true)
                local data = ItemConfig:GetDataByID(targetItem)
                self.ui.m_txtZooName.text = LangMgr:GetLang(data.id_lang)
                local zooConfig = ItemConfig:GetZooType(targetItem)
				
                local rarity = 0
				if zooConfig then 
					rarity = zooConfig.rarity
				end
                if rarity == 1 then
                	AudioMgr:Play(67)
                elseif rarity == 2 then
                	AudioMgr:Play(68)
                elseif rarity == 3 then
                	AudioMgr:Play(69)
				else
					
                end

				local text_num = 7054
				local curMapID = NetUpdatePlayerData.playerInfo.curMap
				if curMapID ~= 7 then
					text_num = 8113
				end
                self.ui.m_txtZooTitle.text = LangMgr:GetLangFormat(text_num,ItemConfig:GetLangByID(targetItem))
				local function callBack(assetItem)
					self:SetRewards(self.nowTaskParam, self.ui.m_goZooReward,assetItem,true)
				end
				ResMgr:LoadAssetAsync(itemPath, AssetDefine.LoadType.Instant,callBack)
            end
        end
    end
	
	if not self.lastShow then
		_G.CS.StartGame.Instance:CloseUISplashLogo()
		UIMgr:Close(UIDefine.UI_SceneLoading)
	end
	
	if step.bg_prefab ~= nil then
		if step.bg_prefab ~= "stay" then
			self.ui.m_transbg:DestroyChildren()
			self.prefabBG = nil
			local function onLoad(prefab)
				if prefab then
					local newGo, newTrans = CreateGOAndTrans(prefab)
					newTrans:SetParent(self.ui.m_transbg)
					local rtSize = newTrans.transform:GetComponent(typeof(UE.RectTransform))
					rtSize.sizeDelta = self.ui.m_transbg.transform:GetComponent(typeof(UE.RectTransform)).sizeDelta
					newTrans.localScale = Vector3.one
					newTrans.localPosition = Vector3.zero
					self.prefabBG = newTrans
				end
			end
			ResMgr:LoadAssetBaseAsync(step.bg_prefab, AssetDefine.LoadType.Instant, onLoad)
		else
			
		end
	else
		self.ui.m_transbg:DestroyChildren()
		self.prefabBG = nil
	end
	
	if step.bg then
		SetActive(self.ui.m_imgbg,true)
		SetUIImage(self.ui.m_imgbg,step.bg,false);
		if GuideController.m_GuideInfNow and GuideController.m_GuideInfNow.closeSplashType then
			_G.CS.StartGame.Instance:CloseUISplashLogo()
			UIMgr:Close(UIDefine.UI_SceneLoading)
		end
	else
		SetActive(self.ui.m_imgbg,false)
	end
	
    self.stepNow = self.stepNow + 1
    self.lastShow = step
	
	--if self.ui.m_imgBlackBg.gameObject.activeSelf == true then
		--SetActive(self.ui.m_imgBlackBg, false)
		
		if self.onAfterShowCall then
			self.onAfterShowCall()
		end
	--end

end

function M:SetRewards(id, parent,prefab,isNativeSize)
    local rewards = NetTaskData:GetReward(id)
    for _, v in ipairs(rewards) do
		--todo需要一个字段判断是否显示该奖励
		if v.id == 73005 then
			if not LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.MonthlySeason) then
				goto continue
			end
		end
        local item = CreateGameObject(prefab)
        GetChild(item, "img/txt", UEUI.Text).text = v.count
		
        SetImageSprite(GetChild(item, "img", UEUI.Image), v.img, isNativeSize)
		if v.id == 73005 then
			if LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.MonthlySeason) then
				SetImageSprite(GetChild(item, "img", UEUI.Image), ItemConfig:GetIcon(73005), isNativeSize)
				
				if NetMonthlySeasonData.data.buyVip then
					local addNum = " +"..math.floor(v.count*MonthlySeasonManager:GetVipBuff())
					GetChild(item, "img/txt", UEUI.Text).text = v.count..addNum
				end
			end
		end
		
        AddChild(parent, item)
        self.items[v.id] = item
        ::continue::
    end
end

--打开对话时需要隐藏的UI
function M:SetOtherHide()
    SetActive(self.ui.m_goNewTask, false)
    SetActive(self.ui.m_goTaskComplete, false)
    SetActive(self.ui.m_goTaskUnlock, false)
    SetActive(self.ui.m_goTaskHero, false)
	SetActive(self.ui.m_goPhoneMess,false)
	SetActive(self.ui.m_goPhoneChat, false)
	SetActive(self.ui.m_goChooseChatBtn, false)
	
	SetActive(self.ui.m_btnContinue, true)
	isInAutoNext = false
end

--打开其他类型界面时隐藏
function M:SetOtherActive(step)
    SetActive(self.ui.m_goBlack,false)
    if self.lastShow then
        SetActive(self.showMess[self.lastShow.npc], false)
		self:ChangeRoleActive(false)
    end
    if step.npc then
		if step.img_use then
			SetActive(self.showGo[step.npc],true)
			self:SetSpineUI(step.npc,step.img,step.img_use)
		end
		
		if step.static_img then
			SetActive(self.showStaticImgGo[step.npc],true)
			SetUIImage(self.showStaticImage[step.npc], step.static_img, false)
		end

    else
        if step.img ~= nil or type(step.img) == "string" then
            --Log.Info("任务图片出错....id",step.id)
	        local strs = string.split(step.img,":")
	        local anis = string.split(step.img_use,"|")
	        for i, val in ipairs(strs) do
	            local dir,img = string.match(val,"(.+)|(.+)")
	            if self.stepOver<=1 then
	                SetActive(self.showGo[dir],true)
	            end
	            if dir then
	                self:SetSpineUI(dir,img,anis[i])
	            end
	        end
		end
    end
    --SetUIFirstSibling(self.ui.m_btnContinue)
    for _, v in pairs(self.showSpine) do
        v.color = Color.New(1,1,1,1)
    end
end

--[[
    @desc: 对话
    author:{author}
    time:2020-10-16 15:48:33
    --@dir:方向
	--@img:图片
	--@tit:标题文字
	--@str: 文字
]]

--如果没有对话了，角色都隐藏
function M:ChangeRoleActive(active)
	SetActive(self.ui.m_goMessPoss,active)
end

function M:Say(dir, img, tit, str, ani, staticImg, dialogId)
	self:ChangeRoleActive(true)
    local function ShowNewMess()
		if ani then
			self:SetSpineUI(dir,img,ani)
            if dialogId and dialogId == 1501 then
                self.showSpine[dir].color = Color.New(0,0,0,1)
            else
                self.showSpine[dir].color = Color.New(1,1,1,1)
            end
			--SetUILastSibling(self.showGo[dir])
		else
			self:HideRoleSpine()
		end
		
		if staticImg then
			if self.showStaticImgGo[dir].activeSelf == false then
				SetActive(self.showStaticImgGo[dir], true)
			end
			
			SetUIImage(self.showStaticImage[dir], staticImg, false)
			self.showStaticImage[dir].color = Color.New(1,1,1,1)
		else
			self:HideRoleStaticImage()
		end
		
		SetActive(self.showMess[dir], true)
    end
    SetActive(self.ui.m_goBlack,true)
    tit = LangMgr:GetLang(tit)
    local strContent = LangMgr:GetLang(str)

    if self.showGo[dir].activeSelf == false then
        SetActive(self.showGo[dir], true)
    end
    --SetUIImage(self.showImg[dir], img,false)
    self.showTit[dir].text = tit
    self.showTxt[dir].text = ""
    if strContent ~=nil and strContent ~= "" then
        self.tweener = self.showTxt[dir]:DOText(strContent,1):SetEase(Ease.InOutQuad)
		self.loadtex = true
		self.tex = self.showTxt[dir]
		self.texstr = strContent
		self.textimeID = TimeMgr:CreateTimer(self, function()
				self.loadtex = false
			end, 1, 1)
    end
    if self.lastShow and self.lastShow.npc then
        if self.lastShow.npc ~= dir
			or (self.lastShow.npc == dir and img and self.lastShow.img ~= img)
			or (self.lastShow.npc == dir and staticImg and self.lastShow.static_img ~= staticImg)
			or (self.lastShow.npc == dir and str and self.lastShow.dlg ~= str and (img or staticImg))
		then
			SetActive(self.showMess[self.lastShow.npc], false)
			
			if img then
				self.showSpine[self.lastShow.npc].color = Color.New(0.5,0.5,0.5,1)
				--SetUIFirstSibling(self.showGo[self.lastShow.npc])
			end
			
			if staticImg then
				self.showStaticImage[self.lastShow.npc].color = Color.New(0.5,0.5,0.5,1)
			end

            ShowNewMess()
        end
    else
        ShowNewMess()
    end
end

function M:NewSay(dir, role_icon, tit, str, ani, staticImg, dialogId)
	self:ChangeRoleActive(true)
	SetActive(self.ui.m_goBlack,true)
	tit = LangMgr:GetLang(tit)
	local strContent = LangMgr:GetLang(str)
	local obj = self.showMess[dir]
	local sayObj = UEGO.Instantiate(obj,self.ui.m_scrollviewMessList.content.transform)
	SetActive(sayObj,true)
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_scrollviewMessList.content.transform)

	local txtContent = GetChild(sayObj, "bg/txtContent", UEUI.Text);
	local txtName = GetChild(sayObj, "headBg/nameBg/txtName", UEUI.Text);
	local headIcon = GetChild(sayObj, "headBg/headIcon", UEUI.Image);
	txtName.text = tit
	txtContent.text = ""
	if not IsNilOrEmpty(role_icon) and headIcon then
		SetImageSprite(headIcon,role_icon)
	end
	if strContent ~= nil and strContent ~= "" then
		self.tweener = txtContent:DOText(strContent,1):SetEase(Ease.InOutQuad)
		self.loadtex = true
		self.tex = txtContent
		self.texstr = strContent
		self.textimeID = TimeMgr:CreateTimer(self, function()
			self.loadtex = false
		end, 1, 1)
	end
end

function M:NarrationSay(iDlgId, fAutoNextStepDelay)
	self:ChangeRoleActive(false)
	if iDlgId then
		local strContent = LangMgr:GetLang(iDlgId)

		self.showTxt[strNarration].text = ""
		if strContent ~=nil and strContent ~= "" then
			self.tweener = self.showTxt[strNarration]:DOText(strContent,1):SetEase(Ease.InOutQuad)
			self.loadtex = true
			self.tex = self.showTxt[strNarration]
			self.texstr = strContent
			self.textimeID = TimeMgr:CreateTimer(self, function()
					self.loadtex = false
				end, 1, 1)
		end

		SetActive(self.showMess[strNarration], true)
	end
	
	if fAutoNextStepDelay then
		SetActive(self.ui.m_btnContinue, false)
		isInAutoNext = true
		self:CreateScheduleFun(function() self:NextStep() end, fAutoNextStepDelay, 1)
	end
	
end


function M:OnRefresh(param)
end

function M:onDestroy()
	self:ClearHollowMap(-1)
	self:HideMainFace(false)

	self.cantclose = nil
	self.tex = nil
	self.loadtex = nil
	self.texstr = nil
	self.textimeID = nil

	if self.guideline and BackgroundConfig:GetIsGuidLine(MapController.m_MapId) then
		local component = MapController:GetComponent(MapComponentId.MapP2PNavigateComponent,true)
		if component then
			component:FindWithParam(self.guideline)
		end
	end

	if self.m_BlockWall then
		UEGO.Destroy(self.m_BlockWall.m_Go)
		self.m_BlockWall = nil
	end

   -- UI_UPDATE(UIDefine.UI_MainFace,10,false)
end

function M:onDestroyBack()
	-- UI_UPDATE(UIDefine.UI_MainFace,10,false)
	if self.callBack then
		self.callBack()
	end
end

function M:PlayReward()

    local rewards = NetTaskData:GetReward(self.nowTaskParam)
    for _, v in ipairs(rewards) do
        if v.id <= ItemID._RESOURCE_MAX then
			if self.items[v.id] then
				local pos = MapController:GetUIPosByWorld(self.items[v.id].transform.position)
				MapController:AddResourceBoomAnim(pos.x, pos.y, v.id, v.count)
			end
            NetUpdatePlayerData:AddResource(PlayerDefine[v.id], v.count,nil,nil,"UI_DialogView")
            if v.id == ItemID.COIN then
                if NetTaskData:IsNowRandom(self.nowTaskParam) then
                    NetNotification:NotifyCoin(6,v.count)
                else
                    NetNotification:NotifyCoin(5,v.count)
                end
            end
        end
		if v.id == 73003 then
			local pos = MapController:GetUIPosByWorld(self.items[v.id].transform.position)
			LimitActivityController:TryAddRankIntegralByTask(ActivityTotal.LimitRank,pos.x, pos.y,73003,v.count)
		end
		if v.id == 73005 then
			local isOpen,active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.MonthlySeason)
			if not isOpen then return end
			local pos = MapController:GetUIPosByWorld(self.items[v.id].transform.position)
			LimitActivityController:CreateFlyScoreRun(v.id,pos.x,pos.y,active,v.count,v.id,nil,self.nowTaskParam)
		end
    end
    local openCloud = TaskConfig:GetOpenCloud(self.nowTaskParam)
    if openCloud then
        local list = string.split(openCloud,"|",nil,nil,1)
        for _, v in ipairs(list) do
            local group = MapController:GetGroupById(v)
            if group then
                group:SetConditionDone()
            end
        end
    end
    --local mapId = NetUpdatePlayerData.playerInfo.curMap
    --if BackgroundConfig:GetUITypeByMapId(mapId) == 3 then
    --    MapController:AddResourceBoomAnim(0,0, 91099, 1)
    --    UIMgr:RefreshCopyMainFace(101)
    --end
end

function M:onUIEventClick(go, param,ignoreDeltaCheck)
	if self.cantclose == true then
		return nil
	end
	local name = nil
	if go and go.name then
		name = go.name
	end
	if name == "m_btnSkip" then
		self.m_skip = true
		self:NextStep()
		return
	end
	
	if name == "m_btnChooseChatRight" or name == "m_btnChooseChatLeft" then
		self:ClickChatBtn((name == "m_btnChooseChatLeft"))
		return
	end
	
	--
	
    if (not ignoreDeltaCheck) and self.delta < 1.5 then return end
    if self.tweener and not self.tweener:IsComplete() then
        self.tweener:Kill()
    end
	if self.lastShow == nil then
		Log.Error("异常 ！ 关闭UI_DialogView")
		self:NextStep()
		return
	end
    if  self.isWait then
        return
    end
    local tempTab = {}
    --传递item
    if self.lastShow.type == 2 then
        self.isWait = true
        NetPushViewData:RemoveViewByIndex(PushDefine.NewTask,1)
        for _, v in ipairs(self.newItems) do
            table.insert(tempTab,1,{id = v.id,pos = v.item.transform.position})
            UEGO.Destroy(v.item)
            NetTaskData:NewTaskProgress(v.id,true)
            --if v.id == "1002" then
                --WorkerController:SetWorkAni(true)
            --end
        end
        self.newItems = {}
    elseif self.lastShow.type == 3 or self.lastShow.type == 4 or self.lastShow.type == 5 or self.lastShow.type ==7 then
        self.isWait = true
        self:PlayReward()
        NetTaskData:FinishTask(self.nowTaskParam)
        if self.lastShow.type == 7 then
            local _,targetItem = NetTaskData:GetTarget(self.nowTaskParam)
            MapController:PushGroupOpen(0, targetItem)
            local item = self.findItem
            if item then
				local curMapID = NetUpdatePlayerData.playerInfo.curMap 
				if curMapID == 7 then
                	MapController:SetItemByGridUnsafe(nil,item.m_GridX,item.m_GridY)
                	item:destroyNode()
				end
            end
        end
	--elseif self.lastShow.type == 1004 then
		--self:ShowChatBtn()
		--return
    end

    self:NextStep(tempTab)

end

function M:TickUI(deltaTime)
    self.delta = deltaTime + self.delta
end

function M:PhoneTipsMsg(iDlgId, strHead, iWho, strTime, strDate)
	SetActive(self.ui.m_goPhoneMess, true)
	SetActive(self.ui.m_btnContinue, false)
	
	
	local strContent = LangMgr:GetLang(iDlgId)
	local strName = LangMgr:GetLang(iWho)
	
	SetImageSprite(self.ui.m_imgPhoneTipsHead, strHead, false)
	self.ui.m_txtPhoneTipsName.text = strName
	self.ui.m_txtPhoneTipsMsg.text = strContent
	self.ui.m_txtPhoneTime.text = strTime
	self.ui.m_txtPhoneDate.text = strDate

	DOFadeAlpha(CanvasGroup_PhoneTips, 0.01, 1, 0.5, 1, 0, Ease.Linear)
	DOFadeAlpha(CanvasGroup_btnCheckAll, 0, 1, 0.5, 2.5, 0, Ease.Linear)
	SetActive(self.ui.m_btnCheckAll, true)
	
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_txtPhoneTipsMsg.transform)
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_goPhoneTips.transform)
end

function M:ChatMsg(iDlgId, strTitleHead, strChatHead, iWho, npcPos, btn_end, fAutoNextStepDelay)
	SetActive(self.ui.m_goPhoneChat, true)
	SetActive(self.ui.m_btnContinue, false)
	
	if strTitleHead then
		--local strTitleHeadPath = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, iTitleHead)
		--SetImageSprite(self.ui.m_imgChatTitleHead, strTitleHeadPath.icon, false)
		
		SetImageSprite(self.ui.m_imgChatTitleHead, strTitleHead, false)
	end
	if iWho then
		local strName = LangMgr:GetLang(iWho)
		self.ui.m_txtChatTitleName.text = strName
	end
	
	local strContent = LangMgr:GetLang(iDlgId)
	--local strChatHeadPath = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, iChatHead)	
	local prefabChat = self.showChatMsg[npcPos]
	
	local goNewChat, newTrans = CreateGOAndTrans(prefabChat)
	SetParent(goNewChat, go_ChatContent)
	goNewChat.transform:SetLocalScale(1, 1, 1)
	--goNewChat.transform:SetLocalPosition(0, 0, 0)
	
	local imgHead = GetChild(goNewChat,"goChatHead",UEUI.Image)
	local goChat = GetChild(goNewChat,"goChat")
	local txtChat = GetChild(goNewChat,"goChat/txtChat",UEUI.Text)
	local CanvasGroup = goNewChat:GetComponent("CanvasGroup")
	
	--SetImageSprite(imgHead, strChatHeadPath.icon, false)
	SetImageSprite(imgHead, strChatHead, false)
	txtChat.text = strContent
	
	SetActive(goNewChat, true)
	
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(goChat.transform)
	
	TimeMgr:CreateTimer(UIDefine.UI_DialogView,
		function()
			
			local height = math.floor(goChat.transform.rect.height) + 20
			local rectTf = goNewChat.transform:GetComponent(typeof(UE.RectTransform))
			rectTf.sizeDelta = Vector2.New(0, height)

			DOFadeAlpha(CanvasGroup, 0.01, 1, 0.5, 1, 0, Ease.Linear)
			VerticalLayout_Chat:SetLayoutVertical()
			UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(go_ChatContent.transform)
			
			TimeMgr:CreateTimer(UIDefine.UI_DialogView,
				function()
					local ViewHeight = self.ui.m_goChat.transform.sizeDelta.y
					local ChildsHeight = ScrollRect_Chat.content.transform.sizeDelta.y
					DOLocalMoveY(ScrollRect_Chat.content,
						ChildsHeight,
						1,
						function ()
							if btn_end == 1 then
								SetActive(self.ui.m_btnChatFinish, true)
								DOFadeAlpha(CanvasGroup_btnChatFinish, 0.01, 1, 0.5, 2, 0, Ease.Linear)
							else
								--self:CreateScheduleFun(function() self:NextStep() end, 1.7, 1)
								isInAutoNext = true
								self:CreateScheduleFun(function() self:NextStep() end, fAutoNextStepDelay, 1)
								
							end
						end,
						Ease.Linear)

				end,
				0.1, 1)
		end,
		0.1, 1)
end

--function M:SetChatPosition(type)
	--if type == 1004 then
		--self.ui.m_goMessPoss.transform:SetLocalPosition(50, -160, 0)
		--self.ui.m_goLeftMess.transform:SetLocalPosition(0, -212, 0)
		--self.ui.m_goRightMess.transform:SetLocalPosition(0, -212, 0)
		--self.ui.m_goNarrationMess.transform:SetLocalPosition(0, -212, 0)
	--else
		--self.ui.m_goMessPoss.transform:SetLocalPosition(lPos_origin_Role.x, lPos_origin_Role.y, lPos_origin_Role.z)
		--self.ui.m_goLeftMess.transform:SetLocalPosition(lPos_origin_Msg.x, lPos_origin_Msg.y, lPos_origin_Msg.z)
		--self.ui.m_goRightMess.transform:SetLocalPosition(lPos_origin_Msg.x, lPos_origin_Msg.y, lPos_origin_Msg.z)
		--self.ui.m_goNarrationMess.transform:SetLocalPosition(lPos_origin_Msg.x, lPos_origin_Msg.y, lPos_origin_Msg.z)
	--end
--end

function M:ShowChatBtn()
	SetActive(self.ui.m_btnContinue, false)
	
	if self.tweener and not self.tweener:IsComplete() then
		self.tweener:Kill()
	end
	
	SetActive(self.ui.m_goChooseChatBtn, true)
end

function M:ClickChatBtn(isLeft)
	local tbDlg = {}
	if isLeft then
		tbDlg = NetPushViewData:SplitProcess(tb_ChatBtnDlg[1], true)
	else
		tbDlg = NetPushViewData:SplitProcess(tb_ChatBtnDlg[2], true)
	end
	
	--local listLog = self.logList
	for i = GetTableLength(tbDlg), 1, -1 do
		table.insert(self.logList, self.stepNow, tbDlg[i])
	end
	
	self.stepOver = #self.logList
	self:NextStep()
end

function M:ParseStr_ChatBtnTxt(value)
	if value == nil then
		return nil
	end
	local tab = {}
	for val in string.gmatch(value, "(%d+)") do
		local id = string.match(val, "(%d+)")
		table.insert(tab, v2n(id))
	end
	return tab
end
function M:ParseStr_ChatBtnDlg(value)
	if value == nil then
		return nil
	end
	
	local iPos = string.find(value, ";")
	local strLeft = string.sub(value, 1, iPos-1)
	local strRight = string.sub(value, iPos+1, value:len())
	
	local tab = {strLeft,strRight}
	
	return tab
end

function M:onChatBtnContent(strTxt, strDlg)
	tb_ChatBtnTxt = self:ParseStr_ChatBtnTxt(strTxt)
	tb_ChatBtnDlg = self:ParseStr_ChatBtnDlg(strDlg)
	
	self.ui.m_txtChooseChatLeft.text = LangMgr:GetLang(tb_ChatBtnTxt[1])
	self.ui.m_txtChooseChatRight.text = LangMgr:GetLang(tb_ChatBtnTxt[2])
end

function M:ChooseChatBtn(npcPos, img, tit, str, ani, staticImg, chat_btn_txt, chat_btn_dlg)
	SetActive(self.ui.m_btnContinue, false)
	
	self:onChatBtnContent(chat_btn_txt, chat_btn_dlg)
	self:Say(npcPos, img, tit, str, ani, staticImg)

	local function onShowBtn()
		self:ShowChatBtn()
	end
	self.tweener:OnComplete(onShowBtn)
end

function M:HideMainFace(hide)

	if self.isHideMainFace == hide then
		return
	end
	self.isHideMainFace = hide

	if not UIMgr:ViewIsShow(UIDefine.UI_CopyMainFace2) then
		return
	end
	if hide then
		UI_UPDATE(UIDefine.UI_CopyMainFace2,10,true)-- 只有在副本中使用
	else
		UI_UPDATE(UIDefine.UI_CopyMainFace2,10,false)
	end
end

function M:GetTaskMotionSpeed()
	if NetUpdatePlayerData:GetLevel() < 5 then
		return 0.0001
	end
	return 0.2
end

return M
