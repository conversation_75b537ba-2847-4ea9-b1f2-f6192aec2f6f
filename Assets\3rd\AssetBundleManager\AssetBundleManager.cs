﻿using System;
using UnityEngine;
#if UNITY_EDITOR	
using UnityEditor;
#endif
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.U2D;
using UnityEngine.Networking;
/*
 	In this demo, we demonstrate:
	1.	Automatic asset bundle dependency resolving & loading.
		It shows how to use the manifest assetbundle like how to get the dependencies etc.
	2.	Automatic unloading of asset bundles (When an asset bundle or a dependency thereof is no longer needed, the asset bundle is unloaded)
	3.	Editor simulation. A bool defines if we load asset bundles from the project or are actually using asset bundles(doesn't work with assetbundle variants for now.)
		With this, you can player in editor mode without actually building the assetBundles.
	4.	Optional setup where to download all asset bundles
	5.	Build pipeline build postprocessor, integration so that building a player builds the asset bundles and puts them into the player data (Default implmenetation for loading assetbundles from disk on any platform)
	6.	Use WWW.LoadFromCacheOrDownload and feed 128 bit hash to it when downloading via web
		You can get the hash from the manifest assetbundle.
	7.	AssetBundle variants. A prioritized list of variants that should be used if the asset bundle with that variant exists, first variant in the list is the most preferred etc.
*/

namespace AssetBundles
{
        // Loaded assetBundle contains the references count which can be used to unload dependent assetBundles automatically.
        public class LoadedAssetBundle
        {
                public AssetBundle m_AssetBundle;
                public int m_ReferencedCount;
                internal event System.Action unload;

                public LoadedAssetBundle(AssetBundle assetBundle)
                {
                        m_AssetBundle = assetBundle;
                        m_ReferencedCount = 1;
                }

                internal void Unload(bool unloadAllLoadedObjects)
                {
                        if (m_AssetBundle != null)
                                m_AssetBundle.Unload(unloadAllLoadedObjects);
                        if (unload != null)
                                unload();
                }
        }

        // Class takes care of loading assetBundle and its dependencies automatically, loading variants automatically.
        public class AssetBundleManager
        {
                public enum LogMode { All, JustErrors };
                public enum LogType { Info, Warning, Error };

                static LogMode m_LogMode = LogMode.All;
                static string m_BaseDownloadingURL = "";
                static string[] m_ActiveVariants = { };
                static AssetBundleManifest m_AssetBundleManifest = null;

                static Dictionary<string, LoadedAssetBundle> m_LoadedAssetBundles = new Dictionary<string, LoadedAssetBundle>();
               // static Dictionary<string, AssetBundleCreateRequest> m_DownloadingWWWs = new Dictionary<string, AssetBundleCreateRequest>();

                static Dictionary<string, UnityWebRequest> m_DownloadingWWWs = new Dictionary<string, UnityWebRequest>();


                static Dictionary<string, string> m_DownloadingErrors = new Dictionary<string, string>();
                static List<AssetBundleLoadOperation> m_InProgressOperations = new List<AssetBundleLoadOperation>();
                static Dictionary<string, string[]> m_Dependencies = new Dictionary<string, string[]>();

                public static LogMode logMode
                {
                        get { return m_LogMode; }
                        set { m_LogMode = value; }
                }

                public static Dictionary<string, LoadedAssetBundle> GetLoadedABDic()
                {
                        return m_LoadedAssetBundles;
                }

                // The base downloading url which is used to generate the full downloading url with the assetBundle names.
                public static string BaseDownloadingURL
                {
                        get { return m_BaseDownloadingURL; }
                        set { m_BaseDownloadingURL = value; }
                }

                // Variants which is used to define the active variants.
                public static string[] ActiveVariants
                {
                        get { return m_ActiveVariants; }
                        set { m_ActiveVariants = value; }
                }

                // AssetBundleManifest object which can be used to load the dependecies and check suitable assetBundle variants.
                public static AssetBundleManifest AssetBundleManifestObject
                {
                        get { return m_AssetBundleManifest; }
                        set { m_AssetBundleManifest = value; }
                }

                private static void Log(LogType logType, string text)
                {
                        //if (logType == LogType.Error)
                        //    Debug.LogError("[AssetBundleManager] " + text);
                        //else if (m_LogMode == LogMode.All)
                        //    Debug.Log("[AssetBundleManager] " + text);
                }

                private string GetStreamingAssetsPath()
                {
                        if (Application.isEditor)
                                return "file://" + System.Environment.CurrentDirectory.Replace("\\", "/"); // Use the build output folder directly.
                                                                                                           //else if (Application.isWebPlayer)
                                                                                                           //	return System.IO.Path.GetDirectoryName(Application.absoluteURL).Replace("\\", "/")+ "/StreamingAssets";
                        else if (Application.isMobilePlatform || Application.isConsolePlatform)
                                return Application.streamingAssetsPath;
                        else // For standalone player.
                                return "file://" + Application.streamingAssetsPath;
                }

                public void SetSourceAssetBundleDirectory(string relativePath)
                {
                        BaseDownloadingURL = GetStreamingAssetsPath() + relativePath;
                }

                public static void SetSourceAssetBundleURL(string absolutePath)
                {
                        BaseDownloadingURL = absolutePath + Utility.GetPlatformName() + "/";
                }

                public void SetDevelopmentAssetBundleServer()
                {
                        TextAsset urlFile = Resources.Load("AssetBundleServerURL") as TextAsset;
                        string url = (urlFile != null) ? urlFile.text.Trim() : null;
                        if (url == null || url.Length == 0)
                        {
                                Debug.LogError("Development Server URL could not be found.");
                                //AssetBundleManager.SetSourceAssetBundleURL("http://localhost:7888/" + UnityHelper.GetPlatformName() + "/");
                        }
                        else
                        {
                                AssetBundleManager.SetSourceAssetBundleURL(url);
                        }
                }

                // Get loaded AssetBundle, only return vaild object when all the dependencies are downloaded successfully.
                public static LoadedAssetBundle GetLoadedAssetBundle(string assetBundleName, out string error)
                {
                        if (m_DownloadingErrors.TryGetValue(assetBundleName, out error))
                                return null;

                        LoadedAssetBundle bundle = null;
                        m_LoadedAssetBundles.TryGetValue(assetBundleName, out bundle);
                        if (bundle == null)
                                return null;

                        // No dependencies are recorded, only the bundle itself is required.
                        string[] dependencies = null;
                        if (!m_Dependencies.TryGetValue(assetBundleName, out dependencies))
                                return bundle;

                        // Make sure all dependencies are loaded
                        foreach (var dependency in dependencies)
                        {
                                if (m_DownloadingErrors.TryGetValue(assetBundleName, out error))
                                        return bundle;

                                // Wait all the dependent assetBundles being loaded.
                                LoadedAssetBundle dependentBundle;
                                m_LoadedAssetBundles.TryGetValue(dependency, out dependentBundle);
                                if (dependentBundle == null)
                                        return null;
                        }

                        return bundle;
                }

                public AssetBundleLoadManifestOperation Initialize()
                {
                        return Initialize(Utility.GetPlatformName());
                }


                // Load AssetBundleManifest.
                public AssetBundleLoadManifestOperation Initialize(string manifestAssetBundleName)
                {
                        AssetBundle.SetAssetBundleDecryptKey(AssetBundleConfig.GetABCode());
                        LoadAssetBundle(manifestAssetBundleName, true);
                        var operation = new AssetBundleLoadManifestOperation(manifestAssetBundleName, "AssetBundleManifest", typeof(AssetBundleManifest));
                        m_InProgressOperations.Add(operation);

                        return operation;
                }

                // Load AssetBundle and its dependencies.
                protected (int res,string[] dependencies) LoadAssetBundle(string assetBundleName, bool isLoadingAssetBundleManifest = false, bool isAsyc = true)
                {
                        Log(LogType.Info, "Loading Asset Bundle " + (isLoadingAssetBundleManifest ? "Manifest: " : ": ") + assetBundleName);


                        if (!isLoadingAssetBundleManifest)
                        {
                                if (m_AssetBundleManifest == null)
                                {
                                        Debug.LogError("Please initialize AssetBundleManifest by calling AssetBundleManager.Initialize()");
                                        return (-999, null);
                                }
                        }

                        // Check if the assetBundle has already been processed.
                        int ret = LoadAssetBundleInternal(assetBundleName, isLoadingAssetBundleManifest, isAsyc);

                        // Load dependencies.
                        string[] deps = null;
                        if (ret == 0 && !isLoadingAssetBundleManifest)
                                deps = LoadDependencies(assetBundleName, isAsyc);

                        //Load dependencies 相关图集一并加载
                        if (assetBundleName.StartsWith("prefab/ui/") && deps != null && deps.Length > 0)
                        {
                                List<string> atlasList = new List<string>();
                                for (int i = 0; i < deps.Length; i++)
                                {
                                        string atlasABPath = SpriteAtlasMgr.Instance.RequestAtlasBySpriteABName(deps[i], null);
                                        if (atlasABPath != string.Empty)
                                                atlasList.Add(atlasABPath);
                                }
                                if (atlasList.Count > 0)
                                {
                                        atlasList.AddRange(deps.ToList());
                                        deps = atlasList.ToArray();
                                        // Debug.LogError("@AB@ 额外增加了" + assetBundleName + "_" + atlasList.Count);
                                }
                        }
                        
                        return (ret, deps);
                }
                
                public AssetBundleLoadBeforehandOperation PreLoadAssetBundle(string assetBundleName)
                {
                        return PreLoadAssetBundleInternal(assetBundleName);
                }

                public AssetBundleLoadBeforehandOperation PreLoadAssetBundleInternal(string assetBundleName)
                {
                        assetBundleName = GetVariant(assetBundleName);

                        LoadAssetBundle(assetBundleName, false);
                        var operation = new AssetBundleLoadBeforehandOperation(assetBundleName);
                        m_InProgressOperations.Add(operation);
                        return operation;
                }

                // Remaps the asset bundle name to the best fitting asset bundle variant.
                protected string RemapVariantName(string assetBundleName)
                {
                        string[] bundlesWithVariant = m_AssetBundleManifest.GetAllAssetBundlesWithVariant();

                        string[] split = assetBundleName.Split('.');

                        int bestFit = int.MaxValue;
                        int bestFitIndex = -1;
                        // Loop all the assetBundles with variant to find the best fit variant assetBundle.
                        for (int i = 0; i < bundlesWithVariant.Length; i++)
                        {
                                string[] curSplit = bundlesWithVariant[i].Split('.');
                                if (curSplit[0] != split[0])
                                        continue;

                                int found = System.Array.IndexOf(m_ActiveVariants, curSplit[1]);

                                // If there is no active variant found. We still want to use the first 
                                if (found == -1)
                                        found = int.MaxValue - 1;

                                if (found < bestFit)
                                {
                                        bestFit = found;
                                        bestFitIndex = i;
                                }
                        }

                        if (bestFit == int.MaxValue - 1)
                        {
                                Debug.LogWarning("Ambigious asset bundle variant chosen because there was no matching active variant: " + bundlesWithVariant[bestFitIndex]);
                        }

                        if (bestFitIndex != -1)
                        {
                                return bundlesWithVariant[bestFitIndex];
                        }
                        else
                        {
                                return assetBundleName;
                        }
                }

                // Where we actuall call WWW to download the assetBundle.
                protected int LoadAssetBundleInternal(string assetBundleName, bool isLoadingAssetBundleManifest, bool isAsyc = true)
                {
                        // Already loaded.
                        LoadedAssetBundle bundle = null;
                        m_LoadedAssetBundles.TryGetValue(assetBundleName, out bundle);
                        if (bundle != null)
                        {
                                bundle.m_ReferencedCount++;
                                return 1;
                        }

                        // @TODO: Do we need to consider the referenced count of WWWs?
                        // In the demo, we never have duplicate WWWs as we wait LoadAssetAsync()/LoadLevelAsync() to be finished before calling another LoadAssetAsync()/LoadLevelAsync().
                        // But in the real case, users can call LoadAssetAsync()/LoadLevelAsync() several times then wait them to be finished which might have duplicate WWWs.
                        if (m_DownloadingWWWs.ContainsKey(assetBundleName))
                                return -1;

                        string url = AssetBundleConfig.GetAssetBundlePath(assetBundleName);
                        if (isAsyc)
                        {
                               // AssetBundleCreateRequest abCreateRequest = AssetBundle.LoadFromFileAsync(url);
                                 UnityWebRequest abCreateRequest = UnityWebRequestAssetBundle.GetAssetBundle(url);     
                                 abCreateRequest.SendWebRequest();


                                m_DownloadingWWWs.Add(assetBundleName, abCreateRequest);
                        }
                        else
                        {
                                Debug.LogError("同步加载H5不适用，强制异步加载：" + assetBundleName);
                                UnityWebRequest abCreateRequest = UnityWebRequestAssetBundle.GetAssetBundle(url);
                                abCreateRequest.SendWebRequest();
                                while (!abCreateRequest.isDone)
                                {
                                        AssetBundle ab = DownloadHandlerAssetBundle.GetContent(abCreateRequest);
                                        m_LoadedAssetBundles.Add(assetBundleName, new LoadedAssetBundle(ab));
                                }
                        }

                        return 0;
                }

                // Where we get all the dependencies and load them all.
                protected string[] LoadDependencies(string assetBundleName, bool isAsyc = true)
                {
                        if (m_AssetBundleManifest == null)
                        {
                                Debug.LogError("Please initialize AssetBundleManifest by calling AssetBundleManager.Initialize()");
                                return null;
                        }

                        // Get dependecies from the AssetBundleManifest object..
                        string[] dependencies = m_AssetBundleManifest.GetAllDependencies(assetBundleName);
                        if (dependencies.Length == 0)
                                return null;

                        for (int i = 0; i < dependencies.Length; i++)
                                dependencies[i] = RemapVariantName(dependencies[i]);

                        // Record and load all dependencies.
                        m_Dependencies.Add(assetBundleName, dependencies);
                        for (int i = 0; i < dependencies.Length; i++)
                        {
                                LoadAssetBundleInternal(dependencies[i], false, isAsyc);
                        }

                        return dependencies;

                }

                // Unload assetbundle and its dependencies.
                public void UnloadAssetBundle(string assetBundleName)
                {
                        Debug.Log(m_LoadedAssetBundles.Count + " assetbundle(s) in memory before unloading " + assetBundleName);

                        UnloadAssetBundleInternal(assetBundleName);
                        UnloadDependencies(assetBundleName);

                        //Debug.Log(m_LoadedAssetBundles.Count + " assetbundle(s) in memory after unloading " + assetBundleName);
                }

                protected void UnloadDependencies(string assetBundleName)
                {
                        assetBundleName = GetVariant(assetBundleName);

                        string[] dependencies = null;
                        if (!m_Dependencies.TryGetValue(assetBundleName, out dependencies))
                                return;

                        // Loop dependencies.
                        foreach (var dependency in dependencies)
                        {
                                UnloadAssetBundleInternal(dependency);
                        }

                        m_Dependencies.Remove(assetBundleName);
                }

                protected void UnloadAssetBundleInternal(string assetBundleName)
                {
                        string error;
                        LoadedAssetBundle bundle = GetLoadedAssetBundle(assetBundleName, out error);
                        if (bundle == null)
                                return;

                        if (--bundle.m_ReferencedCount == 0)
                        {
                                bundle.m_AssetBundle.Unload(false);
                                m_LoadedAssetBundles.Remove(assetBundleName);

                                Log(LogType.Info, assetBundleName + " has been unloaded successfully");
                        }
                }

                public void Cleanup()
                {
                        var bundles_enum = m_LoadedAssetBundles.GetEnumerator();
                        while (bundles_enum.MoveNext())
                        {
                                string bundle_name = bundles_enum.Current.Key;
                                LoadedAssetBundle loaded_bundle = bundles_enum.Current.Value;
                                loaded_bundle.Unload(false);
                        }

                        m_DownloadingWWWs.Clear();
                        m_LoadedAssetBundles.Clear();
                        m_DownloadingErrors.Clear();
                        m_InProgressOperations.Clear();
                        m_Dependencies.Clear();
                        m_AssetBundleManifest = null;
                }

                private string GetVariant(string assetBundleName)
                {
                        string abName = assetBundleName;
                        if (AssetBundleConfig.ABVariant.Length > 0)
                                abName = assetBundleName + "." + AssetBundleConfig.ABVariant;

                        return abName;
                }

                public void Update()
                {
                        // Collect all the finished WWWs.
                        var keysToRemove = new List<string>();
                        foreach (var keyValue in m_DownloadingWWWs)
                        {
                               // AssetBundleCreateRequest download = keyValue.Value;
                                UnityWebRequest download = keyValue.Value;

                                //// If downloading fails.
                                //if (download.error != null)
                                //{
                                //	m_DownloadingErrors.Add(keyValue.Key, string.Format("Failed downloading bundle {0} from {1}: {2}", keyValue.Key, download.url, download.error));
                                //	keysToRemove.Add(keyValue.Key);
                                //	continue;
                                //}

                                // If downloading succeeds.
                                if (download.isDone)
                                {
                                       // AssetBundle bundle = download.assetBundle;
                                         AssetBundle bundle = DownloadHandlerAssetBundle.GetContent(download);

                                        if (bundle == null)
                                        {
                                                if (!m_DownloadingErrors.ContainsKey(keyValue.Key))
                                                        m_DownloadingErrors.Add(keyValue.Key, string.Format("{0} is not a valid asset bundle.", keyValue.Key));
                                                else
                                                        Debug.LogError(string.Format("{0} is not a valid asset bundle.", keyValue.Key));
                                                        keysToRemove.Add(keyValue.Key);
                                                continue;
                                        }
                                        //Debug.Log("Downloading " + keyValue.Key + " is done at frame " + Time.frameCount);
                                        //m_LoadedAssetBundles.Add(keyValue.Key, new LoadedAssetBundle(download.assetBundle));
                                        if(!m_LoadedAssetBundles.ContainsKey(keyValue.Key)) 
                                                m_LoadedAssetBundles.Add(keyValue.Key, new LoadedAssetBundle(bundle));
                                        keysToRemove.Add(keyValue.Key);
                                }
                        }

                        // Remove the finished WWWs.
                        foreach (var key in keysToRemove)
                        {
                               // AssetBundleCreateRequest download = m_DownloadingWWWs[key];
                                UnityWebRequest download = m_DownloadingWWWs[key];
                                m_DownloadingWWWs.Remove(key);
                                //download.Dispose();
                        }

                        // Update all in progress operations
                        for (int i = 0; i < m_InProgressOperations.Count;)
                        {
                                if (m_InProgressOperations[i] != null && !m_InProgressOperations[i].Update())
                                {
                                        m_InProgressOperations.RemoveAt(i);
                                }
                                else
                                        i++;
                        }
                }

                // Load asset from the given assetBundle.
                public AssetBundleLoadAssetOperation LoadAssetAsync(string assetBundleName, string assetName, System.Type type)
                {
                        Log(LogType.Info, "LoadAssetAsync Loading <" + assetName + "> from <" + assetBundleName + "> bundle");

                        assetBundleName = GetVariant(assetBundleName);

                        AssetBundleLoadAssetOperation operation = null;
                        {
                        //Debug.Log("=> AssetBundle Path = " + assetBundleName + " : " + assetName);
                        assetBundleName = RemapVariantName(assetBundleName);
                        var(ret,deps) = LoadAssetBundle(assetBundleName,false,true);
                        operation = new AssetBundleLoadAssetOperationFull(assetBundleName, assetName, type, deps);

                        m_InProgressOperations.Add(operation);
                        }

                        return operation;
                }

                // Load level from the given assetBundle.
                public AssetBundleLoadOperation LoadLevelAsync(string assetBundleName, string levelName, bool isAdditive)
                {
                        Log(LogType.Info, "Loading " + levelName + " from " + assetBundleName + " bundle");

                        assetBundleName = GetVariant(assetBundleName);

                        AssetBundleLoadOperation operation = null;
                        {
                                assetBundleName = RemapVariantName(assetBundleName);
                                LoadAssetBundle(assetBundleName);
                                operation = new AssetBundleLoadLevelOperation(assetBundleName, levelName, isAdditive);

                                m_InProgressOperations.Add(operation);
                        }

                        return operation;
                }

                public T LoadAssetSync<T>(string assetBundleName, string assetName) where T : UnityEngine.Object
                {
                        AssetBundle bundle = LoadBundleAsync(assetBundleName);
                        if (bundle != null)
                        {
                                return bundle.LoadAsset<T>(assetName);
                        }
                        return null;
                }

                public AssetBundle LoadBundleAsync(string assetBundleName)
                {
                        Log(LogType.Info, "LoadBundleAsync: " + assetBundleName);
                        assetBundleName = GetVariant(assetBundleName);
                        assetBundleName = RemapVariantName(assetBundleName);

                        var(ret,deps) = LoadAssetBundle(assetBundleName, false, false);

                        if (ret == -1)
                        {
                                Log(LogType.Error, "###### LoadBundleAsync: " + assetBundleName);
                                return null;
                        }

                        LoadedAssetBundle bundle = null;
                        m_LoadedAssetBundles.TryGetValue(assetBundleName, out bundle);
                        if (bundle != null)
                                return bundle.m_AssetBundle;

                        return null;
                }


        } // End of AssetBundleManager.
}