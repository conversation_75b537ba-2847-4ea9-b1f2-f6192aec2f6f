---@class BlueCountDown
local BlueCountDown = {}
local M = BlueCountDown

local prePath = "Assets/ResPackage/Prefab/Map/BlueCountDown.prefab"
local pre

function M:Create(pos, callback)
    local item = {}
    setmetatable(item,{__index = BlueCountDown})

    -- 异步加载预制体
    ResMgr:LoadAssetWithCache(prePath, AssetDefine.LoadType.Instant, function(prefab)
        if not prefab then
            Log.Error("Failed to load BlueCountDown prefab")
            if callback then
                callback(nil)
            end
            return
        end

        pre = prefab -- 缓存预制体引用
        local newGo, newTrans = CreateGOAndTrans(pre)
        item.go = newGo
        MapController:AddUIToWorld(newTrans)
        SetUIPos(newGo, pos.x, pos.y)

        item:Init()

        if callback then
            callback(item)
        end
    end)

    return item -- 注意：此时 item.go 可能还未初始化
end

--初始化
function M:Init()
    self.count = GetChild(self.go,"count",UEUI.Text)
	
end

function M:Hide()
	if not self.go then return end
    SetActive(self.go,false)
end

function M:Show(x,y)
    if not self.go then return end
    SetActive(self.go,true)
    if x and y then
        SetUIPos(self.go,x,y)
    end
end

function M:SetTimeShow(time)
	self.count.text = TimeMgr:GetHMS(math.floor(time+0.5))
end

function M:Timer()
end

function M:Destroy()
    UEGO.Destroy(self.go)
end

return M