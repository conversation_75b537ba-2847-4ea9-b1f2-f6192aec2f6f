local UI_JJcFightEnd = Class(BaseView)

function UI_JJcFightEnd:OnInit()
    
end

function UI_JJcFightEnd:OnCreate(before_data,config)
	self.b_rank_id = before_data.rank_id
	self.act_id = before_data.act_id
	self.b_rank_star = before_data.rank_star
	self.b_config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_rank,self.b_rank_id)
	self.lastSeasonNum = v2n(self.act_id) - 100
	local data = JJcManager:GetData()
	self.rank_id = data and data.arena.rank_id or 10
	self.rank_star = data and data.arena.rank_star or 1
	self.config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_rank,self.rank_id)
	
	--self.b_rank_id = 8
	--self.act_id = 101
	--self.b_rank_star = 3
	--self.b_config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_rank,self.b_rank_id)
	--self.lastSeasonNum = v2n(self.act_id) - 100
	----local data = JJcManager:GetData()
	--self.rank_id = 4
	--self.rank_star = 1
	--self.config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_rank,self.rank_id)
	
	self:LastRank()
	--self.ui.m_txtTitle.text = LangMgr:GetLang(70000529)
	--self.ui.m_txtName.text = LangMgr:GetLang(config.langid)
	JJcManager:UpdateJJcItem(self.ui.m_goJJcRankItem,self.rank_id,self.rank_star)
	--SetActive(self.ui.m_goJJcRankItem,true)
end

function UI_JJcFightEnd:LastRank()
	SetActive(self.ui.m_goJJcRankItem,false)
	local imgRank = GetChild(self.ui.m_goJJcRankItem,"imgRank",UEUI.Image)
	SetActive(imgRank,false)
	SetActive(self.ui.m_goSpine,true)
	local spine = GetChild(self.ui.m_goSpine, "spine", CS.Spine.Unity.SkeletonGraphic)
	local function LastRankEnd()	
		TimeMgr:CreateTimer(self, function() self:NowRank() end, 0.5, 0.5)		
		--SetActive(self.ui.m_goSpine,false)
		--SetActive(self.ui.m_goJJcRankItem,true)
		--SetSpineAnim(spine,"idle",-1)
	end
	local config = self.b_config
	self.ui.m_txtTitle.text = LangMgr:GetLangFormat(70000679,self.lastSeasonNum)
	self.ui.m_txtName.text = LangMgr:GetLang(config.langid)
	local mat = "Spine/arena_badge_"..config.spine_2.."/duanwei_"..config.spine_2.."_Material.mat"
	local asset = "Spine/arena_badge_"..config.spine_2.."/duanwei_"..config.spine_2.."_SkeletonData.asset"
	--spine:Initialize(true)
	--SetSpineAnim(spine,"closed",1)
	--SetSpineCallBack(spine,SPINE_CALLBACK_TYPE.Complete,LastRankEnd)
	
	local function funcW(mat)
		spine.material = mat
	end
	local function funcW2(asset)
		spine.skeletonDataAsset = asset
		spine:Initialize(true)
		SetSpineAnim(spine,"closed",1)
		SetSpineCallBack(spine,SPINE_CALLBACK_TYPE.Complete,LastRankEnd)
	end
	ResMgr:LoadAssetAsync(mat, AssetDefine.LoadType.Instant,funcW)
	ResMgr:LoadAssetAsync(asset, AssetDefine.LoadType.Instant,funcW2)
end

function UI_JJcFightEnd:NowRank()
	if not self.ui or not self.ui.m_goSpine then
		return
	end
	
	local spine = GetChild(self.ui.m_goSpine, "spine", CS.Spine.Unity.SkeletonGraphic)
	local function NowRankEnd()
		SetActive(self.ui.m_goJJcRankItem,true)
		--SetActive(self.ui.m_goSpine,false)
		--SetActive(self.ui.m_goJJcRankItem,true)
		SetSpineAnim(spine,"idle",-1)
	end
	local config = self.config
	self.ui.m_txtTitle.text = LangMgr:GetLangFormat(70000680,self.lastSeasonNum+1)
	self.ui.m_txtName.text = LangMgr:GetLang(config.langid)
	local mat = "Spine/arena_badge_"..config.spine.."/duanwei_"..config.spine.."_Material.mat"
	local asset = "Spine/arena_badge_"..config.spine.."/duanwei_"..config.spine.."_SkeletonData.asset"

	--spine:Initialize(true)
	--SetSpineAnim(spine,"open",1)
	--SetSpineCallBack(spine,SPINE_CALLBACK_TYPE.Complete,NowRankEnd)
	
	local function funcW(mat)
		spine.material = mat
	end
	local function funcW2(asset)
		spine.skeletonDataAsset = asset
		spine:Initialize(true)
		SetSpineAnim(spine,"open",1)
		SetSpineCallBack(spine,SPINE_CALLBACK_TYPE.Complete,NowRankEnd)
	end
	ResMgr:LoadAssetAsync(mat, AssetDefine.LoadType.Instant,funcW)
	ResMgr:LoadAssetAsync(asset, AssetDefine.LoadType.Instant,funcW2)
end

function UI_JJcFightEnd:OnRefresh(param)
    
end

function UI_JJcFightEnd:onDestroy()
    
end

function UI_JJcFightEnd:onUIEventClick(go,param)
    local name = go.name
end

return UI_JJcFightEnd