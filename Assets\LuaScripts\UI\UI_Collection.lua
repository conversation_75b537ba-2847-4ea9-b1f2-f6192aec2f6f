local UI_Collection = Class(BaseView)

local togSelectedIndex = 101
local curHeroIndex = 1
local infoTypeSprites = {}
local cellrewardIdx = 0
local jumpId = 0

function UI_Collection:OnInit()
	self.TJ_Slider = GetChild(self.uiGameObject,"bg/goodObj/TJprogress/TJ_Slider",UEUI.Slider)
	self.TJProgress = GetChild(self.uiGameObject,"bg/goodObj/TJprogress/TJ_Slider/Fill Area/TJProgress",UEUI.Text)
	self.NotAnytxt = GetChild(self.uiGameObject,"bg/m_txtAuto5008")
	SetActive(self.NotAnytxt,false)
	
	self.canvasGroup = GetComponent(self.ui.m_togGroup,UE.CanvasGroup)
	self.content = GetChild(self.uiGameObject,"bg/goodObj/scrollview/Viewport/itemsContent",UE.RectTransform)
end

function UI_Collection:OnCreate(itemId,hideFlag,seasonCtrl)
	--UIMgr:RefreshAllMainFace(12,12,{isShow = true,list={PlayerDefine.Coin}})
	self.recordCount = {}
	self.HeroPanel = GET_UI(self.uiGameObject, "heroPanel", "RectTransform")
	self.GoodPanel = GET_UI(self.uiGameObject, "goodObj", "RectTransform")
	self.uiDataObj = {}
	self.heroDataObj = {}
	self.heroList = {}
	self.moveTimer = 0
	self.seasonCtrl = seasonCtrl
	
	--根据当前地图ID初始化相对应的英雄toggleId(collection.xlsx)
	local curMap = NetUpdatePlayerData.playerInfo.curMap
	self.heroIndex = curMap == MAP_ID_SECOND and 111 or 101
	self.curHeroIndex = curMap == MAP_ID_SECOND and 21 or 1
	curHeroIndex = self.curHeroIndex
	self.uiIsInit = false
	
	self.toggle_prefab = GET_UI(self.uiGameObject, "m_tog_tmp", "RectTransform")
	SetActive(self.toggle_prefab, false)
	self.toggle_perant = GET_UI(self.uiGameObject, "m_togGroup", "RectTransform")
	self.toggleTab = {}
	--根据当前地图ID初始化相对应的toggle
	self:InitToggleList()
	self.hideFlag = (hideFlag~=nil) and hideFlag or false
	self.uiIsInit = true
	if itemId then
		local toggleId = CollectionItems:get_CollectionIdByItemId(NetUpdatePlayerData.playerInfo.curMap , itemId)
		if toggleId then
			if toggleId ~= 1 then
				togSelectedIndex = toggleId
				jumpId = itemId
			else
				togSelectedIndex = self.heroIndex
				--有一个隐患 ，收集表 的 英雄ID 改变的时候 对应不上
				curHeroIndex = CollectionItems:GetCollectId(itemId) --现在的是收集表的ID	
			end
		end
	end	
	
	if togSelectedIndex == self.heroIndex then
		SetActive(self.HeroPanel, true)
		SetActive(self.GoodPanel, false)
		--self:CreateItems(togSelectedIndex)
	else
		SetActive(self.HeroPanel, false)
		SetActive(self.GoodPanel, true)
		self:CreateItems(togSelectedIndex)
	end
	self:ShowLight(togSelectedIndex)
	
	
	--更新 左边Toggle的红点儿
	self:InitRedPoint()

	-----------------角色图鉴部分由新图鉴展示(UI_CollectionNew)
	--[[
	--加载六个英雄
	local Herolen = CollectionItems:get_CollectionMapDataById(self.heroIndex)
	for i = 1, #Herolen.collection_ids do
		self:CreateHeroItem(self.heroIndex , Herolen.collection_ids[i])
	end
	
	--刷新英雄身上的红点儿
	self:refreshHeroPoint()
	
	--选中当前的 英雄
	--self:SetHeroDetail(curHeroIndex)
	self:UpdateHero(curHeroIndex, true)
	--]]
	
	local tog = self.toggleTab[togSelectedIndex]:GetComponent(typeof(UEUI.Toggle))
	--GET_UI(self.uiGameObject, "m_tog_" .. togSelectedIndex, TP(UEUI.Toggle))
	if tog == nil then
		Log.Error("xcc","报错打印，togSelectedIndex:",togSelectedIndex)
	else
		if tog.isOn then
			tog.onValueChanged:Invoke(true)
		else
			tog.isOn = true
		end
	end

end

function UI_Collection:FlushTJProgress(toggleId)
	local scrollview = GetChild(self.uiGameObject, "bg/goodObj/scrollview")
	local TJprogress = GetChild(self.uiGameObject, "bg/goodObj/TJprogress")
	local bg2 = GetChild(self.uiGameObject, "bg/bg2")
	--if toggleId == nil then
		--SetActive(TJprogress,false)
		--SetUISize(scrollview.transform,1350,735)
		--return 
	--end
	local config = CollectionItems:get_CollectionMapDataById(toggleId)
	if config == nil then
		return
	end

	if self.hideFlag then
		SetActive(TJprogress,false)
		--SetUISize(scrollview.transform,1386,860)
		--SetUISize(bg2.transform,1470,995)
		self.canvasGroup.alpha = 0
		self.canvasGroup.interactable = false;
		return 
	end
	
	if config.percentage_show ~= 1 then
		SetActive(TJprogress,false)
		--SetUISize(scrollview.transform,1386,794)
		--SetUISize(bg2.transform,1470,925)
		self.canvasGroup.alpha = 1
		self.canvasGroup.interactable = true;
		return 
	end

	local image_bg = GetChild(TJprogress, "TJ_Slider/Image",UEUI.Image)
	SetUIImage(image_bg,config.percentage_bg,false)
	--wyl
	SetActive(TJprogress,false)--true
	--SetUISize(scrollview.transform,1386,580)
	--SetUISize(bg2.transform,1470,995)
	self.canvasGroup.alpha = 0
	self.canvasGroup.interactable = false;
	local _, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.LimitIns)
	local activeTaskCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_task,active.info.activeId)
	local nowCount,maxCount = CollectionItems:GetCollectionProgress(activeTaskCfg.collection_id)
	if self.TJ_Slider then
		local num = string.format("%.2f",nowCount/maxCount)
		self.TJ_Slider.value = num
		self.TJProgress.text = (num*100).."%"
	end
end

function UI_Collection:InitToggleList()
	
	local curMapID = NetUpdatePlayerData.playerInfo.curMap
	local listData = CollectionItems:get_CollectionMapData(curMapID)
	local indexDef = 0
	--初始化toggle列表
	table.sort(listData, function(a, b)
			return a.id < b.id
		end)
	for key, value in pairs(listData) do
		local go = UEGO.Instantiate(self.toggle_prefab)
		--Log.Error("instance")
		go.transform:SetParent(self.toggle_perant)
		go.transform.localScale = Vector3.New(1,1,1)
		go.transform:SetLocalPosition(0, 0, 0)
		SetActive(go, true)
		local normalImg = GET_UI(go, "defaultIcon", TP(UEUI.Image))
		local selectImg = GET_UI(go, "m_imgSelectedIcon", TP(UEUI.Image))
		
		SetImageSprite(normalImg,value.toggle_normal,true)
		SetImageSprite(selectImg,value.toggle_selected,true)
		go.name = "m_tog_" .. tostring(value.id)
		
		self.toggleTab[value.id] =  go
		if indexDef == 0 then
			togSelectedIndex = value.id
			indexDef = indexDef + 1
			
		end
		
		RemoveUIComponentEventCallback(go, UEUI.Toggle)
		AddUIComponentEventCallback(go, UEUI.Toggle, function(arg1,arg2)
			if not self.uiIsInit then
				return
			end
				
			self:onUIEventClick(arg1 , arg2)
		end)
	end
	
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.toggle_perant)	
end

function UI_Collection:InitRedPoint(params, context)
	
	for key, value in pairs(self.toggleTab) do
		local red = GET_UI(value, "redPoint", TP(UEUI.Image))
		local isVisible = false
		if key == self.heroIndex then
			--英雄部分
			local heroRed = CollectionItems:RedPointHero(self.heroIndex)
			local redPoint = CollectionItems:HeroMatRedPoint(self.heroIndex)
			isVisible = heroRed or redPoint
		else
			isVisible = CollectionItems:RedPoint(key)
		end
		SetActive(red, isVisible)
	end	
end

function UI_Collection:refreshHeroPoint()
	
	for collectId, obj in pairs(self.heroList) do
		local heroRed = GET_UI(obj.ui_obj, "heroredDot", TP(UEUI.Image))
		local hasPoint = CollectionItems:HeroStatePoint(collectId)
		SetActive(heroRed, hasPoint)
		self:UpdateHero(collectId, false)
	end	
end

function UI_Collection:OnRefresh(param)
	
end

function UI_Collection:onDestroy()
	UIMgr:RefreshAllMainFace(12,12,{isShow = false,list={PlayerDefine.Coin}})

	self.recordCount = nil
	self.HeroPanel = nil
	self.GoodPanel = nil
	self.uiDataObj = nil
	self.heroDataObj = nil
	self.heroList = nil
	self.ActiveData = nil
	
	
	togSelectedIndex = self.heroIndex
	curHeroIndex = 1
	infoTypeSprites = nil
	infoTypeSprites = {}
	cellrewardIdx = 0
	jumpId = 0
	self.uiIsInit = false
end

function UI_Collection:ExchangePanelContent(index)
	if index == self.heroIndex then
		SetActive(self.HeroPanel, true)
		SetActive(self.GoodPanel, false)
	else
		SetActive(self.HeroPanel, false)
		SetActive(self.GoodPanel, true)
	end
end

--初始化左边六个英雄的列表   101，1/2/3/4/5/6
function UI_Collection:CreateHeroItem(toggleId , collectId)
	local Content = GET_UI(self.uiGameObject, "Content", "RectTransform")
	local cellObj = nil
	local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_collectHero")
	local cellObj = ResMgr:LoadAssetSync(assetPath, AssetDefine.LoadType.Instant)
	local go = UEGO.Instantiate(cellObj)
	--Log.Error("instance2")
	go.transform:SetParent(Content.transform)
	go.transform.localScale = Vector3.New(1,1,1)
	go.transform:SetLocalPosition(0, 0, 0)
	go:SetActive(true)
	
	local ImgSel = GET_UI(go, "ImgSelected", TP(UEUI.Image))
	SetActive(ImgSel, false)
	
	local cell_combon = {}
	cell_combon.ui_obj = go
	self.heroList[tonumber(collectId)] = cell_combon
	
	RemoveUIComponentEventCallback(go, UEUI.Button)
	AddUIComponentEventCallback(go, UEUI.Button, function(arg1,arg2)
		--self:SetHeroDetail(collectId)
		self:UpdateHero(collectId, true)
		--self:InitHeroList(go,toggleId,collectId)
	end)
	self:InitHeroList(go,toggleId,collectId)
	self:UpdateHero(collectId, false)	
end
--初始化英雄链表
function UI_Collection:InitHeroList(obj,toggleId,collectId)
	--英雄物品链
	local config = CollectionItems:get_CollectionCfgsByToggleId(toggleId)
	local hasHero = MapController:GetHeroIdThatWillUnlock()
	local heroConfig
	for _, value in ipairs(config) do
		if value.id == collectId then
			heroConfig = value
			break
		end
	end
	local isHaveHero = MapController:GetHeroFly(heroConfig.hero_id)
	local iscreatInstance = false
	if not isHaveHero then
		if heroConfig.hero_id <= hasHero then
			iscreatInstance = true
		end
	else
		iscreatInstance = true
	end
	if iscreatInstance then
		self:HeroCreateAllItems(obj,heroConfig)
	end
	--刷新名字
	local heroName = GET_UI(obj, "itemName", TP(UEUI.Text))
	local collectCfg = CollectionItems:get_CollectData(collectId) --collectionitemcfg
	local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, collectCfg.hero_id)

	local hero = CollectionItems:get_ItemCountByMapScope(collectCfg.scope_list , collectCfg.hero_id)
	local getHero = CollectionItems:get_ItemCountByMapScope(collectCfg.scope_list , collectCfg.hero_id)
	local hero_desc = GET_UI(obj, "desc_text", TP(UEUI.Text))
	hero_desc.text = LangMgr:GetLang(collectCfg.descript_id)
	
	local change_button = GET_UI(obj, "change_button", TP(UEUI.Button))--切换信息按钮
	if hero > 0 then
		heroName.text = LangMgr:GetLang(itemConfig.id_lang)
	else
		heroName.text = LangMgr:GetLang(7017)
	end
	local herostate = NetCollection:get_ItemState(collectCfg.hero_id)
	local scrollview = GetChild(obj , "scrollview")

	
	local desc_text = GetChild(obj , "desc_text")
	if herostate then
		hero_desc.text = LangMgr:GetLang(collectCfg.descript_id)
	else
		hero_desc.text = LangMgr:GetLang(7018)
	end
	
	
	SetActive(change_button,herostate)
	SetActive(scrollview,herostate)
	SetActive(desc_text,not herostate)
	if nil == hasHero then
		hasHero = 100000
	end
	if collectCfg.hero_id <= hasHero then
		SetActive(scrollview,true)
		SetActive(desc_text,false)
		if herostate then
			SetActive(scrollview,false)
			SetActive(desc_text,true)
		end
	end
	
	
	
	AddUIComponentEventCallback(change_button, UEUI.Button, function()
			SetActive(scrollview,not scrollview.activeSelf)
			SetActive(desc_text,not desc_text.activeSelf)
		end)
end

--更新左边 英雄的显示
function UI_Collection:UpdateHero(collectId, isShowSelect)	
	--隐藏所有hero的选中状态
	for k, v in pairs(self.heroList) do
		local selImg = GET_UI(v.ui_obj, "ImgSelected", TP(UEUI.Image))
		local heroHead = GET_UI(v.ui_obj, "heroHeader", TP(UEUI.Image))
		SetActive(selImg, false)
		heroHead.transform:SetLocalScale(1.0, 1.0, 1.0)
	end
	--当前选中的hero索引
	if self.heroList[collectId] then
		local selImg = GET_UI(self.heroList[collectId].ui_obj, "ImgSelected", TP(UEUI.Image))
		local heroHead = GET_UI(self.heroList[collectId].ui_obj, "heroHeader", TP(UEUI.Image))
		if isShowSelect == true then
			SetActive(selImg, true)
			--heroHead.transform:SetLocalScale(1.1, 1.1, 1.1)
		end
		
	end
	
	--已有英雄的列表
	local collectCfg = CollectionItems:get_CollectData(collectId)
	
	--播放 有奖励的动画
	local reward = GET_UI(self.heroList[collectId].ui_obj, "heroReward", TP(UEUI.Image))
	local function ani()
		local Heroani = GetComponent(reward, UE.Animation)
		--这个英雄是否有奖励		
		local getHero = CollectionItems:get_ItemCountByMapScope(collectCfg.scope_list , collectCfg.hero_id)
		
		local herostate = NetCollection:get_ItemState(collectCfg.hero_id)
		if getHero > 0 and not herostate then
			Heroani:Play()
		end
	end
	ani()
	local uiObj = self.heroList[collectId].ui_obj
	local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, collectCfg.hero_id)
	local heroHead = GET_UI(uiObj, "heroHeader", TP(UEUI.Image))
	local unknow = GET_UI(uiObj, "unknown", TP(UEUI.Image))
	local effect = GetChild(uiObj , "heroItem/effect_UI_xuanzhuang")
	local heroReward = GET_UI(uiObj, "heroReward", TP(UEUI.Button))
	local Heroani = GetComponent(heroReward, UE.Animation)
	local heroRed = GET_UI(uiObj, "heroredDot", TP(UEUI.Image))
	local hasHero = MapController:GetHeroIdThatWillUnlock()
	SetUIImage(heroHead, itemConfig.icon_m, false)
	
	local hero_desc = GET_UI(uiObj, "desc_text", TP(UEUI.Text))
	
	local herState = NetCollection:get_ItemState(collectCfg.hero_id)
	if herState then
		hero_desc.text = LangMgr:GetLang(collectCfg.descript_id)
	else
		hero_desc.text = LangMgr:GetLang(7018)
	end
	local getHero = CollectionItems:get_ItemCountByMapScope(collectCfg.scope_list , collectCfg.hero_id)
	
	if collectCfg.hero_id == hasHero then
		SetActive(unknow, false)
	else
		SetActive(unknow, true)
	end
	
	
	
	local herostate = NetCollection:get_ItemState(collectCfg.hero_id)
	
	local change_button = GET_UI(uiObj, "change_button", TP(UEUI.Button))
	if getHero > 0 then
		SetUIImageGray(heroHead, false)
		heroHead.color = Color.New(1, 1, 1, 1)
		if not herostate then
			SetActive(heroReward, true)
			SetActive(effect , true)
			SetActive(heroRed, true)
			SetActive(heroHead, false)
			Heroani:Play()
		else
			SetActive(heroReward, false)
			SetActive(effect , false)
			SetActive(unknow, false)
			SetActive(heroRed, false)
			SetActive(heroHead, true)
			SetActive(change_button,true)
		end
	else
		--问号
		SetActive(change_button,false)
		SetUIImageGray(heroHead, true,1)
		SetActive(effect , false)
		SetActive(heroReward, false)
		heroHead.color = Color.New(1, 150/255, 1, 255/255)	
	end
	
	RemoveUIComponentEventCallback(heroReward, UEUI.Button)
	AddUIComponentEventCallback(heroReward, UEUI.Button, function(arg1,arg2)
		if getHero > 0 and not herostate then
			self:getHero(uiObj, itemConfig.unlock_coins , collectCfg.hero_id, reward)
			--self:SetHeroDetail(collectId)
		else
			--self:SetHeroDetail(collectId)
				
		end
	end)
end

function UI_Collection:getHero(obj, num, hero_id, rewardObj) 
	local flyPos = MapController:GetUIPosByWorld(rewardObj.transform.position)
	MapController:AddResourceBoomAnim(flyPos.x, flyPos.y, ItemID.COIN, num , true)
	NetUpdatePlayerData:AddResource(PlayerDefine.Coin, num,true,nil,"UI_Collection")
	NetNotification:NotifyCoin(1,num)
	NetCollection:set_ItemState(hero_id , true)
	self:UpdateHeroData(obj, hero_id)
end

function UI_Collection:UpdateHeroData(obj, hero_id)
	local heroGiftIcon = GET_UI(obj, "heroReward", TP(UEUI.Button))
	local effect = GetChild(obj , "heroItem/effect_UI_xuanzhuang")
	local unknow = GET_UI(obj, "unknown", TP(UEUI.Image))
	local heroHead = GET_UI(obj, "heroHeader", TP(UEUI.Image))
	local herState = NetCollection:get_ItemState(hero_id)
	if not herState then
		SetActive(heroGiftIcon, true)
		SetActive(effect , true)
		SetActive(heroHead, false)
	else
		SetActive(heroGiftIcon, false)
		SetActive(effect , false)
		SetActive(unknow, false)
		SetActive(heroHead, true)
	end
	
	
	
	--领取了 英雄奖励身上的奖励后 更新红点儿
	--Log.Error("领取")
	self:InitRedPoint()
	self:refreshHeroPoint()
end
--获取 并 初始化第三层的数据 idx 传进来的是 英雄的下标
function UI_Collection:SetHeroDetail(collectId)
	--获取当前英雄的数据	
	--cfg , collectCfg
	local collectCfg = CollectionItems:get_CollectData(collectId) --collectionitemcfg
	
	local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, collectCfg.hero_id)
	
	local HeroItemObj = GET_UI(self.uiGameObject, "compoundItems", "RectTransform")
	
	local hasHero = MapController:GetHeroIdThatWillUnlock()
	
	local getHero = NetMapNoteData:AddNoteCount(MapController.m_MapId, NetMapNoteData.ID.item_has, itemId, collectCfg.hero_id)
		
	local hero = CollectionItems:get_ItemCountByMapScope(collectCfg.scope_list , collectCfg.hero_id)
	
	if collectCfg.hero_id == hasHero then
		--SetActive(HeroItemObj, true)
		SetActive(self.ui.m_imgUnknown, false)
		self.ui.m_txtHeroName.text = LangMgr:GetLang(7017)
	else
		SetActive(HeroItemObj, false)
		SetActive(self.ui.m_imgUnknown, true)
	end
	if hero > 0 then
		--SetActive(HeroItemObj, true)
		SetUIImageGray(self.ui.m_imgHeader, false)
		SetActive(self.ui.m_imgUnknown, false)
		self.ui.m_txtHeroName.text = LangMgr:GetLang(itemConfig.id_lang)
		self.ui.m_txtHeroIntroduction.text = LangMgr:GetLang(collectCfg.descript_id)
	else
		SetUIImageGray(self.ui.m_imgHeader, true,1)
		self.ui.m_txtHeroName.text = LangMgr:GetLang(7017)
		self.ui.m_txtHeroIntroduction.text = LangMgr:GetLang(7018)
	end
	SetUIImage(self.ui.m_imgHeader, itemConfig.icon_ss)

	--奖励
	self.heroDataObj = nil
	self.heroDataObj = {}
	local matTab = collectCfg.materialList
	local childCount = HeroItemObj.transform.childCount
	for i = 1, childCount do
		local ItemObj = self.ui["m_imgitem" .. i]
		
		local item = GET_UI(ItemObj,"imgItemIcon", TP(UEUI.Image))
		local row = GET_UI(ItemObj, "imgArrow", TP(UEUI.Image))
		local effect = GetChild(ItemObj , "obj/effect_UI_xuanzhuang")
		local reward = GET_UI(ItemObj, "reward", TP(UEUI.Image))
		local imgUnknow = GET_UI(ItemObj, "imgQue", TP(UEUI.Image))
		local ani = GetComponent(reward, UE.Animation)
		
		if tonumber(hasHero) == tonumber(collectCfg.hero_id) then
			SetActive(imgUnknow, false)
		else
			SetActive(imgUnknow, true)
		end
		
		if i <= #matTab then
			SetActive(ItemObj, true)
			if matTab[i] == matTab[#matTab] then
				SetActive(row, false)
			else
				SetActive(row, true)
			end
			SetActive(item, true)
						
			local hasItem = CollectionItems:get_ItemCountByMapScope(collectCfg.scope_list , matTab[i])
			
			if hasItem > 0 then
				SetUIImageGray(item, false)
				ani:Play()
				
				local state = NetCollection:get_ItemState(matTab[i])
				if not state then
					SetActive(reward, true)
					SetActive(effect , true)
					SetActive(item, false)
				else
					SetActive(reward, false)
					SetActive(effect , false)
					SetActive(item, true)
					SetActive(imgUnknow, false)
				end
			else
				SetActive(reward, false)
				SetActive(effect , false)
				SetUIImageGray(item, true)
			end
			SetUIImage(item, ItemConfig:GetIcon(tonumber(matTab[i])), false)
		else
			SetActive(ItemObj, false)
		end
		
		local heroCellData = {}
		heroCellData.uiObj = ItemObj
		heroCellData.itemId = matTab[i]
		table.insert(self.heroDataObj, heroCellData)

		RemoveUIComponentEventCallback(reward, UEUI.Button)
		AddUIComponentEventCallback(reward, UEUI.Button, function(arg1,arg2)
			local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, matTab[i])
			local coinNum = 0
			if itemConfig.unlock_coins ~= nil then
				coinNum = itemConfig.unlock_coins
			end
			self:GetHeroReward(ItemObj, coinNum, matTab[i])
			SetActive(imgUnknow, false)
		end)
	end
end

--设置第三层的数据
function UI_Collection:GetHeroReward(obj, num, matId)
	local flyPos = MapController:GetUIPosByWorld(obj.transform.position)
	MapController:AddResourceBoomAnim(flyPos.x, flyPos.y, ItemID.COIN, num, true)
	NetUpdatePlayerData:AddResource(PlayerDefine.Coin, num,true,nil,"UI_Collection")
	NetNotification:NotifyCoin(1,num)
	NetCollection:set_ItemState(matId , true)
	self:UpdataHeroState(matId)
end

function UI_Collection:UpdataHeroState(matId)
	self:InitRedPoint()
	self:refreshHeroPoint()
	for k, v in pairs(self.heroDataObj) do
		if matId == v.itemId then
			local heroItemIcon = GET_UI(v.uiObj, "imgItemIcon", TP(UEUI.Image))
			local effect = GetChild(v.uiObj , "obj/effect_UI_xuanzhuang")
			local heroRewardIcon = GET_UI(v.uiObj, "reward", TP(UEUI.Image))
			local imgUnknow = GET_UI(v.uiObj, "imgQue", TP(UEUI.Image))
			local state = NetCollection:get_ItemState(matId)
			if not state then
				SetActive(heroRewardIcon, true)
			else
				SetActive(heroRewardIcon, false)
				SetActive(effect , false)
				SetActive(imgUnknow, false)
				SetActive(heroItemIcon, true)
			end
		end
	end
	--如果是最后一个奖励的话 ， 应该更新Hero上的奖励部分
	
end

function UI_Collection:CreateItems(toggleId)
	local itemContent = GET_UI(self.uiGameObject, "itemsContent", "RectTransform")
	local childCount = itemContent.childCount
	for i = 0, childCount - 1 do
		local child =  GET_UI_CHILD(itemContent , i  , "RectTransform")
		SetActive(child , false)
	end
	--清理数据
	self.uiDataObj = nil
	self.uiDataObj = {}

	local showList = {}

	--每个页签的数据 collection.cfg
	local config = CollectionItems:get_CollectionCfgsByToggleId(toggleId)
	self.all_progress = 0
	local isshow = false
	self.showIndexDic = {};
	local index = 0;
	local loadEnd = false
	for i = 1, #config do
		local Show = false
		if toggleId == 107 or toggleId == 117 then
			local open,active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Season)
			if open then
				local con = ConfigMgr:GetDataByID(ConfigDefine.ID.item, config[1].materialList[1])
				if active.form.activeMess.item_kind == con.subclass .. "" then
					Show = true
				end
			end
			
			for k,v in pairs(config[i].materialList) do			
					--if	NetMapNoteData:GetNoteCount(MapType.Main,NetMapNoteData.ID.item_has,v) > 0  
					--or NetMapNoteData:GetNoteCount(MapType.Main2,NetMapNoteData.ID.item_has,v) > 0 then
				local count = CollectionItems:get_ItemCountByMapScope(config[i].scope_list , v)
				if count > 0 then
					Show = true
					break
				end
			end
		else
			Show = true
		end
		if Show then
			isshow = true
			local child =  GET_UI_CHILD(itemContent , i - 1 , "RectTransform")
			if child == nil then

				local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_CollectCell")
				local cellObj = ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant,function(cellObj)
						child = UEGO.Instantiate(cellObj)
						--Log.Error("instance3")
						child.transform:SetParent(itemContent.transform)
						child.transform.localScale = Vector3.New(1,1,1)
						child.transform:SetLocalPosition(0, 0, 0)
						
						SetActive(child , true)
						local title = GET_UI(child, "itemName", TP(UEUI.Text))
						title.text = LangMgr:GetLang(config[i].name_id)
						self:CreateAllItems(child, config[i])

						index = index + 1;
						self.showIndexDic[i] = index;

						table.insert(showList,config[i])
				end)
			else
				SetActive(child , true)
				local title = GET_UI(child, "itemName", TP(UEUI.Text))
				title.text = LangMgr:GetLang(config[i].name_id)
				self:CreateAllItems(child, config[i])

				index = index + 1;
				self.showIndexDic[i] = index;

				table.insert(showList,config[i])
			end				
		end
	end
	

		SetActive(self.NotAnytxt,not isshow)
		self:FlushTJProgress(toggleId)
		UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(itemContent)
		
		if not self.seasonCtrl then
			return
		end
		local focusIndex = -1
		for k,v in ipairs(showList) do
			--初始化 里面小的cell
			for m,n in ipairs(v.materialList) do
				local itemCount = CollectionItems:get_ItemCountByMapScope(v.scope_list , n)
				if itemCount > 0  then
					local matState = NetCollection:get_ItemState(n)
					if not matState then
						focusIndex = k
						break;
					end
				end
			end
	
			if focusIndex ~= -1 then
				break;
			end
		end
		
		if focusIndex == -1 or focusIndex == 1 then
			self.content.anchoredPosition = Vector2.New(0,1)
			return
		end
		
		local contentTrans = self.content.transform
		local checkIndex = focusIndex - 2
		checkIndex = checkIndex <= 0 and 0 or checkIndex
		local height = 0
		for i = 0,checkIndex do
			local child = contentTrans:GetChild(i)
			local rect = GetComponent(child,UE.RectTransform)
			height = height + rect.sizeDelta.y
		end
		
		if height > 0 then
			self.content.anchoredPosition = Vector2.New(0,height)
		end
end

function UI_Collection:GetHas(config)
	return false
end

function UI_Collection:TriggerSrcoll(toggleId, isZero,isHero)
	local config = CollectionItems:get_CollectionCfgsByToggleId(toggleId)
	local isChange = false
	local jumpCount = 0
	for k = 1, #config do
		local count = config[k].materialList
		if (toggleId == 107 or toggleId == 117) and jumpId ~= 0 then
			for k,v in pairs(config[k].materialList) do
				if	NetMapNoteData:GetNoteCount(MapType.Main,NetMapNoteData.ID.item_has,v) > 0  
					or NetMapNoteData:GetNoteCount(MapType.Main2,NetMapNoteData.ID.item_has,v) > 0 then
					jumpCount = jumpCount + 1
					break
				end
			end
		end
		for i = 1, #count do
			local matId = count[i]
			if jumpId ~= 0 then
				if tonumber(matId) == jumpId then
					if NetMapNoteData:GetNoteCount(MapType.Main,NetMapNoteData.ID.item_has,jumpId) <= 0  or 
						NetMapNoteData:GetNoteCount(MapType.Main2,NetMapNoteData.ID.item_has,jumpId) <= 0 then						
						self:ScrollToIdxVertical(1, isZero,isHero,i)
					else
						self:ScrollToIdxVertical(jumpCount, isZero,isHero,i)
					end
					return
				end
			else
				local curMapID = NetUpdatePlayerData.playerInfo.curMap
				local heroIndex = curMapID == MAP_ID_SECOND and 111 or 101
				local hasHero = MapController:GetHeroIdThatWillUnlock()
				local hero_id = count[#count]
				local hasItem = CollectionItems:get_ItemCountByMapScope(config[k].scope_list , matId)
				if ((toggleId == heroIndex and nil ~= hasHero and hero_id <= hasHero) or (toggleId ~= heroIndex)) and hasItem > 0 then
					local matState = NetCollection:get_ItemState(matId)
					if not matState  then
						self:ScrollToIdxVertical(k, isZero,isHero,i)
						return
					end
				end
			end
		end
	end
	self:ScrollToIdxVertical(1, isZero,isHero)
	if self.moveTimer > 0 then
		TimeMgr.DestroyTimer(self , self.moveTimer)
	end
	local itemContent = GET_UI(self.uiGameObject, "itemsContent", "RectTransform")
	DOKill(itemContent.transform)
	self.ui.m_scrollview.verticalNormalizedPosition = 1
end
--index toggle标签
--obj 一条Obj
--id 这一条的数据
function UI_Collection:HeroCreateAllItems(obj, cellConfig)
	local objRect = GetComponentInLua(obj, "RectTransform") -- 这个是需要根据业务更改高
	--local topHight = GET_UI(obj, "topInfo", "RectTransform").rect.height --头部的高
	local goodsItemRect = GET_UI(obj, "heroitemsContent", "RectTransform") --自动扩容的部分

	local childCount = goodsItemRect.childCount
	for i = 0, childCount - 1 do
		local child =  GET_UI_CHILD(goodsItemRect , i , "RectTransform")
		SetActive(child , false)
	end
	--初始化 里面小的cell
	local matList = cellConfig.materialList
	for i = 1, #matList do
		local itemId = matList[i]
		local child =  GET_UI_CHILD(goodsItemRect , i - 1 , "RectTransform")
		if nil == child then
			local items = GET_UI(obj, "IconContent", "RectTransform")
			child = UEGO.Instantiate(items)
			--Log.Error("instance4")
			child.transform:SetParent(goodsItemRect.transform)
			child.transform.localScale = Vector3.New(1,1,1)
			child.transform:SetLocalPosition(0, 0, 0)
		end
		SetActive(child, true)

		local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemId)
		
		if itemConfig == nil then
			return
		end

		local icon = GET_UI(child, "icon", TP(UEUI.Image))
		--local name = GET_UI(child, "name", TP(UEUI.Text))
		local signType = GET_UI(child, "signType", TP(UEUI.Image))
		local rewardIcon = GET_UI(child, "rewardIcon", TP(UEUI.Image))
		local rewardCount = GET_UI(child, "count", TP(UEUI.Text))
		local Goodsreward = GET_UI(child, "Goodsreward", TP(UEUI.Button))
		local effect = GetChild(child , "Obj/effect_UI_xuanzhuang")
		local ItemsAni = GetComponent(Goodsreward, UE.Animation)
		local imgRow = GET_UI(child, "row", TP(UEUI.Image))
		local bgSpe = GET_UI(child, "specialBg", TP(UEUI.Image))
		local imgUnknow = GET_UI(child, "unknown", TP(UEUI.Image))
		local bg = GET_UI(child, "bg", TP(UEUI.Image))
		local iconAni = GetComponent(icon, UE.Animation)

		infoTypeSprites[1] = ResMgr:LoadAssetSync("Sprite/ui_public/StatusIcon-harvest.png",AssetDefine.LoadType.Sprite)
		infoTypeSprites[2] = ResMgr:LoadAssetSync("Sprite/ui_public/StatusIcon-collect.png",AssetDefine.LoadType.Sprite)
		SetUIImage(icon, itemConfig.icon_b, false)

		local showName = cellConfig.show_name
		--if not showName then
			--name.text = ""
		--else
			--name.text = LangMgr:GetLang(itemConfig.id_lang)
		--end

		local idUse = itemConfig.id_use3
		local typeUse = itemConfig.type_use
		if idUse and idUse > 1 and idUse < 5 then
			--SetActive(name, false)
			SetActive(rewardIcon, true)
			rewardCount.text = itemConfig.id_use2
			local usConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, idUse)
			if usConfig then
				SetUIImage(rewardIcon, usConfig.icon_b, false)
			end
		else
			SetActive(rewardIcon, false)
			--SetActive(name, true)
		end

		local itemCount = CollectionItems:get_ItemCountByMapScope(cellConfig.scope_list , itemId)

		--是否已经有了这个元素
		if itemCount > 0  then
			icon.color = Color.New(1, 1, 1, 1)
			SetUIImageGray(icon, false)
			local matState = NetCollection:get_ItemState(itemId)
			if not matState then
				ItemsAni:Play()
				SetActive(Goodsreward, true)
				SetActive(effect , true)
				SetActive(icon, false)
				SetActive(imgUnknow, false)
			else
				SetActive(Goodsreward, false)
				SetActive(effect , false)
				SetActive(icon, true)
				SetActive(imgUnknow, false)
			end
		else
			SetActive(icon, true)
			SetActive(Goodsreward, false)
			SetActive(effect , false)
			SetActive(imgUnknow, true)
			SetUIImageGray(icon, true)
			icon.color = Color.New(1, 1, 1, 0.5)
		end

		if typeUse == 12 then
			SetActive(signType, true)
			signType.sprite = infoTypeSprites[1]
		elseif typeUse == 2 then
			SetActive(signType, true)
			signType.sprite = infoTypeSprites[2]
		else
			SetActive(signType, false)
		end

		SetActive(imgRow, true)
		if i == #matList then
			SetActive(imgRow, false)
			--if togSelectedIndex == 2 then
			SetActive(bgSpe , true)
			SetActive(bg , false)
			--else
				--SetActive(bgSpe , false)
				--SetActive(bg , true)
			--end

		else
			SetActive(imgRow, true)
			SetActive(bgSpe , false)
			SetActive(bg , true)
		end


		local uiData = {}
		uiData.uiObj = child
		uiData.cellpos = i --三级列表
		uiData.cellItem = itemId
		table.insert(self.uiDataObj, uiData)

		SetActive(child , true)

		local coinNum = 0
		if itemConfig.unlock_coins ~= nil then
			coinNum = itemConfig.unlock_coins
		end
		RemoveUIComponentEventCallback(Goodsreward, UEUI.Button)
		AddUIComponentEventCallback(Goodsreward, UEUI.Button, function(arg1,arg2)
				self:GetGoodsReward(Goodsreward, coinNum, itemId, itemConfig)
				SetActive(Goodsreward, false)
				SetActive(effect , false)
				SetActive(imgUnknow, false)
				SetActive(icon, true)
				iconAni:Play()
				self:refreshHeroPoint()
			end)
	end
end
function UI_Collection:CreateAllItems(obj, cellConfig)	
	local objRect = GetComponentInLua(obj, "RectTransform") -- 这个是需要根据业务更改高
	--local topHight = GET_UI(obj, "topInfo", "RectTransform").rect.height --头部的高
	local goodsItemRect = GET_UI(obj, "goodsItem", "RectTransform") --自动扩容的部分
	
	local childCount = goodsItemRect.childCount
	for i = 0, childCount - 1 do
		local child =  GET_UI_CHILD(goodsItemRect , i , "RectTransform")
		SetActive(child , false)
	end
	--初始化 里面小的cell
	local matList = cellConfig.materialList
	for i = 1, #matList do
		local itemId = matList[i]
		local child =  GET_UI_CHILD(goodsItemRect , i - 1 , "RectTransform")
		if nil == child then
			local items = GET_UI(obj, "IconContent", "RectTransform")
			child = UEGO.Instantiate(items)
			--Log.Error("instance5")
			child.transform:SetParent(goodsItemRect.transform)
			child.transform.localScale = Vector3.New(1,1,1)
			child.transform:SetLocalPosition(0, 0, 0)
		end
		SetActive(child, true)
		
		local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemId)
		if itemConfig == nil then
			return 
		end
		self.all_progress = self.all_progress + 1
		local icon = GET_UI(child, "icon", TP(UEUI.Image))
		local name = GET_UI(child, "name", TP(UEUI.Text))
		local signType = GET_UI(child, "signType", TP(UEUI.Image))
		local rewardIcon = GET_UI(child, "rewardIcon", TP(UEUI.Image))
		local rewardCount = GET_UI(child, "count", TP(UEUI.Text))
		local Goodsreward = GET_UI(child, "Goodsreward", TP(UEUI.Button))
		local effect = GetChild(child , "Obj/effect_UI_xuanzhuang")
		local ItemsAni = GetComponent(Goodsreward, UE.Animation)
		local imgRow = GET_UI(child, "row", TP(UEUI.Image))
		local bgSpe = GET_UI(child, "specialBg", TP(UEUI.Image))
		local imgUnknow = GET_UI(child, "unknown", TP(UEUI.Image))
		local bg = GET_UI(child, "bg", TP(UEUI.Image))
		local iconAni = GetComponent(icon, UE.Animation)
		
		infoTypeSprites[1] = ResMgr:LoadAssetSync("Sprite/ui_public/StatusIcon-harvest.png",AssetDefine.LoadType.Sprite)
		infoTypeSprites[2] = ResMgr:LoadAssetSync("Sprite/ui_public/StatusIcon-collect.png",AssetDefine.LoadType.Sprite)
		SetUIImage(icon, itemConfig.icon_b, false)
		
		local showName = cellConfig.show_name
		if not showName then
			local curMapID = NetUpdatePlayerData.playerInfo.curMap
			if curMapID == MAP_ID_ZOO then
				if itemConfig.id_lang then
					name.text = LangMgr:GetLang(itemConfig.id_lang)
				end
			else
				name.text = ""
			end
		else
			name.text = LangMgr:GetLang(itemConfig.id_lang)
		end
		
		local idUse = itemConfig.id_use3
		local typeUse = itemConfig.type_use
		if idUse and idUse > 1 and idUse < 5 then
			SetActive(name, false)
			SetActive(rewardIcon, true)
			rewardCount.text = itemConfig.id_use2
			local usConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, idUse)
			if usConfig then
				SetUIImage(rewardIcon, usConfig.icon_b, false)
			end
		else
			SetActive(rewardIcon, false)
			SetActive(name, true)
		end
				
		local itemCount = CollectionItems:get_ItemCountByMapScope(cellConfig.scope_list , itemId)
		
		--是否已经有了这个元素
		if itemCount > 0  then
			icon.color = Color.New(1, 1, 1, 1)		
			SetUIImageGray(icon, false)
			local matState = NetCollection:get_ItemState(itemId)
			if not matState then
				ItemsAni:Play()
				SetActive(Goodsreward, true)
				SetActive(effect , true)
				SetActive(icon, false)
				SetActive(imgUnknow, false)
			else
				SetActive(Goodsreward, false)
				SetActive(effect , false)
				SetActive(icon, true)
				SetActive(imgUnknow, false)
			end
		else
			SetActive(icon, true)
			SetActive(Goodsreward, false)
			SetActive(effect , false)
			SetActive(imgUnknow, true)
			SetUIImageGray(icon, true)
			icon.color = Color.New(1, 1, 1, 0.5)
		end
		
		if typeUse == 12 then
			SetActive(signType, true)
			signType.sprite = infoTypeSprites[1]
		elseif typeUse == 2 then
			SetActive(signType, true)
			signType.sprite = infoTypeSprites[2]
		else
			SetActive(signType, false)
		end
		
		SetActive(imgRow, true)
		if i == #matList then
			SetActive(imgRow, false)
			--if togSelectedIndex == 2 then
			SetActive(bgSpe , true)
			SetActive(bg , false)
			--else
				--SetActive(bgSpe , false)
				--SetActive(bg , true)
			--end
			
		else
			SetActive(imgRow, true)
			SetActive(bgSpe , false)
			SetActive(bg , true)
		end
		
		
		local uiData = {}
		uiData.uiObj = child
		uiData.cellpos = i --三级列表
		uiData.cellItem = itemId
		table.insert(self.uiDataObj, uiData)

		SetActive(child , true)
	
		local coinNum = 0
		if itemConfig.unlock_coins ~= nil then
			coinNum = itemConfig.unlock_coins
		end
		RemoveUIComponentEventCallback(Goodsreward, UEUI.Button)
		
		AddUIComponentEventCallback(Goodsreward, UEUI.Button, function(arg1,arg2)
			self:GetGoodsReward(Goodsreward, coinNum, itemId, itemConfig)
			SetActive(Goodsreward, false)
			SetActive(effect , false)
			self:FlushTJProgress()
			SetActive(imgUnknow, false)
			SetActive(icon, true)
			iconAni:Play()
		end)
	end
	
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(goodsItemRect)
end


function UI_Collection:GetGoodsReward(obj, num, itemId, config)
	local flyPos = UIMgr:GetObjectScreenPos(obj.transform)
	
	--local flyPos = MapController:GetUIPosByWorld(obj.transform.position)
	MapController:AddResourceBoomAnim(flyPos.x, flyPos.y, ItemID.COIN, num , true)
	MapController:AddResourceJumpAnim(flyPos.x, flyPos.y, config, num)
	NetUpdatePlayerData:AddResource(PlayerDefine.Coin, num,true,nil,"UI_Collection")
	NetNotification:NotifyCoin(1,num)
	NetCollection:set_ItemState(itemId , true)
	self:RefreshData(itemId)
	
end

function UI_Collection:RefreshData(itemId)
	self:InitRedPoint()
	if self.uiDataObj == nil then
		return
	end
	for k, v in pairs(self.uiDataObj) do
		if v.cellItem == itemId then
			local icon = GET_UI(v.uiObj, "icon", TP(UEUI.Image))
			local reward = GET_UI(v.uiObj, "Goodsreward", TP(UEUI.Button))
			local effect = GetChild(v.uiObj , "Obj/effect_UI_xuanzhuang")
			local imgUnknow = GET_UI(v.uiObj, "unknown", TP(UEUI.Image))
			local matState = NetCollection:get_ItemState(itemId)
			if not matState then
				SetActive(reward, true)
				SetActive(effect , true)
			else
				SetActive(reward, false)
				SetActive(effect , false)
				SetActive(imgUnknow, false)
				SetActive(icon, true)
			end
			break
		end	
	end
end
function UI_Collection:ScrollToHorizontalByHeroIndex(scrollview,heroRewardIndex)
	local contentCount = scrollview.content.childCount
	if contentCount <= 4 then return end

	local moveIndex = Mathf.Min(heroRewardIndex - 1,contentCount - 4)
	local targetValue = moveIndex / (contentCount - 4)
	Tween.To(function(value)
		scrollview.horizontalNormalizedPosition = value
	end,0,targetValue,targetValue / 1.5)

end

function UI_Collection:ScrollToIdxVertical(cellIndex, isZero,isHero,heroRewardIndex)
	if self.seasonCtrl then
		return
	end
	--延时移动的计算
	if self.moveTimer > 0 then
		TimeMgr.DestroyTimer(self , self.moveTimer)
	end
	
	local scrollRect = GetChild(self.uiGameObject, "bg/goodObj/scrollview", UEUI.ScrollRect)
	local itemContent = GET_UI(self.uiGameObject, "itemsContent", "RectTransform")
	
	if isHero then
		itemContent = GET_UI(self.uiGameObject, "Content", "RectTransform")
		scrollRect = GetChild(self.uiGameObject, "bg/heroPanel/m_scrollview", UEUI.ScrollRect)
	end

	DOKill(itemContent.transform)
	
	local function onDelayDone()
		local index = cellIndex - 1;
		local cellY = GET_UI_CHILD(itemContent, index, "RectTransform")
		
		if cellY == nil and self.showIndexDic and self.showIndexDic[cellIndex] then
			index = self.showIndexDic[cellIndex] - 1;
			cellY = GET_UI_CHILD(itemContent, index, "RectTransform")
		end
		
		local finish = function()
			scrollRect.enabled = true
			if isHero and heroRewardIndex then
				self:ScrollToHorizontalByHeroIndex(
						cellY:GetChild(3):GetComponent(typeof(UEUI.ScrollRect)),heroRewardIndex)
			end

		end
		if cellY then
			local posY = -cellY.anchoredPosition.y
			local contentHeight = scrollRect.content.rect.height
			local viewportHeight = scrollRect.viewport.rect.height
			if contentHeight > viewportHeight and contentHeight - viewportHeight < posY then
				posY = contentHeight - viewportHeight
			end
			scrollRect.enabled = false

			local yNow = 0
			if not isZero then
				yNow = itemContent.anchoredPosition.y
			else
				itemContent.anchoredPosition = Vector2.New(0, 0)
			end

			local function toT(lerpValue)
				itemContent.anchoredPosition = Vector2.New(0, lerpValue)
			end

			DOLocalMoveY(itemContent.transform , posY , 0.5 ,finish, Ease.InOutSine):SetDelay(0.1)	
		else
			DOLocalMoveY(itemContent.transform , 0 , 0.5 ,finish, Ease.InOutSine):SetDelay(0.1)
		end
		
	end
	TimeMgr:CreateTimer(UIDefine.UI_Collection, onDelayDone, 0.2, 1)	
end

function UI_Collection:ShowLight(toggleId)
	if nil == self.toggleTab[toggleId] then
		print("不存在的Toggle");
		return
	end
	
	for key, value in pairs(self.toggleTab) do
		local togNormal = GET_UI(value, "defaultIcon", TP(UEUI.Image))
		local togSelect = GET_UI(value, "m_imgSelectedIcon", TP(UEUI.Image))

		if key == toggleId then
			SetActive(togNormal, false)
			SetActive(togSelect, true)
		else
			SetActive(togNormal, true)
			SetActive(togSelect, false)
		end
	end
end

function UI_Collection:onUIEventClick(go,param)
	local name = go.name
	if name == "m_btnClose" then
		self:Close()
	elseif string.find(name, "m_tog_") > 0 then
		if go.isOn == false then
			return
		end
		local toggleId = tonumber(string.match(name, "m_tog_(%d+)"))
		if toggleId == togSelectedIndex then
			local function onDelayDone()
				self:TriggerSrcoll(toggleId, false,togSelectedIndex == self.heroIndex)
				jumpId = 0
			end
			TimeMgr:CreateTimer(UIDefine.UI_Collection, onDelayDone, 0.3, 1)
			return
		end
		
		togSelectedIndex = toggleId
				
		self:ExchangePanelContent(toggleId)
		
		if togSelectedIndex == self.heroIndex then
			--self:UpdateHero(1)
			--self:SetHeroDetail(1)
			self:TriggerSrcoll(toggleId, true,true)
		else
			self:CreateItems(toggleId)
			self:TriggerSrcoll(toggleId, true)
		end
		self:FlushTJProgress(toggleId)
		self:ShowLight(togSelectedIndex)	
	end
end

return UI_Collection