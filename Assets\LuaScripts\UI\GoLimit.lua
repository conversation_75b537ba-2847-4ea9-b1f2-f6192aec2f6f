local GoLimit = {}
local M = GoLimit
local timerId = "golimit"

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)
    
    --return item
end

function M:Init()
    self.state1 = GetChild(self.go,"doLimit/bg/state1")
    self.state2 = GetChild(self.go,"doLimit/bg/state2")
    self.state3 = GetChild(self.go,"doLimit/bg/state3")
    self.act = GetChild(self.go,"doLimit")
    self.bg = GetChild(self.go,"doLimit/bg",UEUI.Image)
    self.bgRect = GetComponent(self.bg.gameObject, UE.RectTransform)
    self.scheduleImg = GetChild(self.state1,"scheduleImg")
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.countGo = GetChild(self.go,"doLimit/bg/CountDown")
    self.count = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
    self.timeLang = GetChild(self.state2,"countDown",UEUI.Text)
    self.needLevel = GetChild(self.state3,"Image/needLevel",UEUI.Text)
    self.intIcon = GetChild(self.state1,"icon",UEUI.Image)
    self.effect = GetChild(self.go,"doLimit/effect")
    self.point = GetChild(self.go,"doLimit/bg/goPoint")
    self.adImg = GetChild(self.go,"doLimit/bg/adImg")
    self.doAni =  GetChild(self.go,"doLimit",TweenAnim)
    self.limitGo = GetChild(self.state1,"Limit")
    self.normalGo = GetChild(self.state1,"Normal")
    self.arrowGo = GetChild(self.go,"arrow")
	self.pass_icon = GetChild(self.state1,"Normal/icon",UEUI.Image)
	self.pass_icon2 = GetChild(self.state1,"Limit/icon",UEUI.Image)
	
	self.goEnergy = GetChild(self.go,"doLimit/bg/goEnergy")
    self.adState = false
	self.needPush = true
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
    EventMgr:Add(EventID.CHANGE_ACTIVE_STATE,self.ChangState,self)
    EventMgr:Add(EventID.ADD_INTEGRAL,self.PlayAni,self)

end

function M:PlayAni(id)
    if id == self.id then
        self.doAni:DORestart()
    end
end

function M:SetItem(param)
    self.id = param.id
    self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
    local active = self.activeInfo.form.activeMess
    self.totalType = param.totalType
    self.condition = param.condition
    local arrowShow = false
    if self.totalType == ActivityTotal.LimitIns then
        SetActive(self.limitGo,true)
        SetActive(self.normalGo,false)
        --self.progress = GetChild(self.limitGo,"Image/progress",UEUI.Image)
        self.progress = GetChild(self.limitGo,"Slider",UEUI.Slider)
        self.scheduleText = GetChild(self.limitGo,"Text",UEUI.Text)
		self.rankStageText = GetChild(self.limitGo,"icon/rank",UEUI.Text)
        if NetUpdatePlayerData.playerInfo.curMap == MAP_ID_MAIN then
            if LimitActivityController:GetIsFirstShow() then
                arrowShow = true
            end
        end
        local imgPath = "Sprite/ui_mainface/mainface2_huodon1_di1.png"
        if not IsHomeMap(NetUpdatePlayerData.playerInfo.curMap) then
            imgPath = "Sprite/ui_mainface/mainface2_menu_iconbox_fuben.png"
        end
        SetImageSprite(self.bg,imgPath,true)
    else
        SetActive(self.limitGo,false)
        SetActive(self.normalGo,true)
        self.progress = GetChild(self.normalGo,"Slider",UEUI.Slider)
        self.scheduleText = GetChild(self.normalGo,"Text",UEUI.Text)
		self.rankStageText = GetChild(self.normalGo,"icon/rank",UEUI.Text)
        SetImageSprite(self.bg,"Sprite/ui_mainface/mainface2_huodon1_di1.png",true)
    end
	
    if arrowShow then
        SetActive(self.arrowGo,true)
        TimeMgr:CreateTimer(timerId,function()
            SetActive(self.arrowGo,false)
        end,30,1)
    else
        SetActive(self.arrowGo,false)
    end
    
    self.needLevel.text = active.level
    self.adId = active.advertisement_id
    SetImageSprite(self.icon,active.icon,false)
	if active.pass_icon2 then
		SetImageSprite(self.pass_icon,active.pass_icon2,false)
		SetImageSprite(self.pass_icon2,active.pass_icon2,false)
	end
	--Log.Error("xxx",active.id,active.pass_icon2)
    self:ChangeItem()
    self:ChangState()
end

function M:ChangState(id)
    --刷新时要判断id
    if id and id ~= self.id then return end
    local condition = self.condition
    if condition == 1 then
        SetActive(self.state1,true)
        SetActive(self.state2,false)
        SetActive(self.state3,false)
        SetActive(self.countGo,true)
        self:ChangeValue()
    elseif condition == 2 then
        SetActive(self.state1,false)
        SetActive(self.state2,false)
        SetActive(self.state3,true)
        SetActive(self.countGo,false)
    else
        SetActive(self.state1,false)
        SetActive(self.state2,true)
        SetActive(self.state3,false)
        SetActive(self.countGo,true)
        if condition == 3 then
            self.timeLang.text = LangMgr:GetLang(7073)
        else
            self.timeLang.text = LangMgr:GetLang(7074)
        end
    end
    local actInfo = self.activeInfo.info
    if self.activeInfo:IsBuyVip() then
        if actInfo.energySch > actInfo.vipSchedule then
            SetActive(self.effect,true)
            SetActive(self.point,true)
        else
            SetActive(self.effect,false)
            SetActive(self.point,false)
        end
	elseif self.activeInfo:IsBuyVip3() then
		if actInfo.energySch > actInfo.vip3Schedule then
			SetActive(self.effect,true)
			SetActive(self.point,true)
		else
			SetActive(self.effect,false)
			SetActive(self.point,false)
		end
    else
        if actInfo.energySch > actInfo.schedule then
            SetActive(self.effect,true)
            SetActive(self.point,true)
        else
            SetActive(self.effect,false)
            SetActive(self.point,false)
        end
    end
	local maxLimitEnergy = EnergyModule:get_CurScopeEnergyLimit()
	local info = NetUpdatePlayerData:GetPlayerInfo()
	local limitEnergy =info[PlayerDefine.LimitEnergy]
	local curmap = NetUpdatePlayerData.playerInfo.curMap
	--local energyImage = GetConsumeImage()
	if curmap == MAP_ID_MAIN and limitEnergy >= maxLimitEnergy and actInfo.subtype == ActivitySubtype.ChristmasLimit then
		SetActive(self.goEnergy,true) 
		SetActive(self.point,false)
	end
end

function M:ChangeValue()
    local active = self.activeInfo
    local actInfo = active.info
    if self.condition ~= actInfo.state then
        self.condition = actInfo.state
        self:ChangState()
    end

    --self.progress.fillAmount = actInfo.integral/active:GetMaxIntegral()
    self.progress.value = actInfo.integral/active:GetMaxIntegral()
    --self.scheduleText.text = actInfo.energySch or 0
	local str = actInfo.integral .."/".. active:GetMaxIntegral()
	self.scheduleText.text = str
	self.rankStageText.text = self.activeInfo.info.energySch
    --SetUISize(self.scheduleImg,13 * actInfo.energySch,12)
end

function M:CountDown5M()
	local oneMinute = 60
	if self.activeInfo.info.totalType == ActivityTotal.LimitIns then
		local time = self.activeInfo:GetRemainingTime()
		if time < 1 then
			time = self.activeInfo:GetWaitTime()
		end
		--if time <= oneMinute*5 then
			--if not self.needPush then

			--else
				--self.needPush = false
			--end
		--end
		if time > oneMinute*5 or time <=0 then
			return 
		end
		local str = nil
		if time <= oneMinute*2 then
			if time == oneMinute then
				str = "1min"
			end
			if time == oneMinute*2 then
				str = "2min"
			end
			if time == oneMinute*1/2 then
				str = "30s"
			end
			if time <= 10 then
				str = math.floor(time).."s"
			end
			if str ~= nil then
				local text = string.format(LangMgr:GetLang(8118),str)
				local curMapID = NetUpdatePlayerData.playerInfo.curMap
				if not curMapID ~= 7 and not IsHomeMap(curMapID) then
					UIMgr:Show(UIDefine.UI_WidgetTip,text)
				end
			end
		end

	end
end

function M:ChangeItem()
    if self.condition == 2 then return end
    if self.condition == 1 then
        self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetRemainingTime())
		self:CountDown5M()
    elseif self.condition == 3 then
        self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetStartRemainingTime())
    elseif self.condition == 4 then
        self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetWaitTime())
		self:CountDown5M()
    end

    function SetAdShow(isActive)
        if self.adState == isActive then return  end
        self.adState = isActive
        SetActive(self.adImg,isActive)
    end

    if self.totalType == ActivityTotal.LimitIns or self.totalType == ActivityTotal.Normal then
        local isCanShow = ADMovieModule:StateCache(self.adId)
        if self.activeInfo:IsOpen() and isCanShow then
            SetAdShow(true)
        else
            SetAdShow(false)
        end
    else
        SetAdShow(false)
    end
end

function M:ClickItem(arg1)
    if self.condition == 1 or self.condition == 4 then
        if self.totalType == 2 then
            UI_SHOW(UIDefine.UI_GoblinRace,self.id)
        elseif self.totalType == 3 then
            UI_SHOW(UIDefine.UI_ActivityCommonBox,self.id)
        elseif self.totalType == ActivityTotal.LimitIns then
            UI_SHOW(UIDefine.UI_TreasureHunt,self.id)
        end
    elseif self.condition == 2 then
        UI_SHOW(UIDefine.UI_WidgetTip,LangMgr:GetLangFormat(206681,self.activeInfo.form.activeMess.level))
    elseif self.condition == 3 then
        UI_SHOW(UIDefine.UI_UpcomingEvents,self.id)
    end
end

function M:Close()
    TimeMgr:DeleteTimer(timerId)
    EventMgr:Remove(EventID.CHANGE_ACTIVE_STATE,self.ChangState,self)
    EventMgr:Remove(EventID.ADD_INTEGRAL,self.PlayAni,self)
    UEGO.Destroy(self.go)
end

return M