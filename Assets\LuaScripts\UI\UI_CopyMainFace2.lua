local UI_CopyMainFace2 = Class(BaseView)
local M = UI_CopyMainFace2
local taskItem = require("UI.TaskItem")
local GoActivity = require("UI.GoActivity")
local GoLimit = require("UI.GoLimit")
local GoLimit4 = require("UI.GoLimit4")
local GoLimit5 = require("UI.GoLimit5")
local GoLimit20 = require("UI.GoLimit20")
local GoLimit21 = require("UI.GoLimit21")
local GoLimit24 = require("UI.GoLimit24")
local GoLimit25 = require("UI.GoLimit25")
local GoLimit27 = require("UI.GoLimit27")
local GoLimit28 = require("UI.GoLimit28")
local GoLimit30 = require("UI.GoLimit30")
local prePath = "Assets/ResPackage/Prefab/UI/FlyTask.prefab"
local pre
local maxLimitEnergy
local GoTask = require("UI.GoTask")
local GoSmallStorageBox = require("UI.GoSmallStorageBox")
local tabNormalImg = "Sprite/ui_mainface/mainface2_weixuanzhong.png"
local tabSelectImg = "Sprite/ui_mainface/mainface2_xuanzhong.png"
MainFaceDefine2 = 
{
	Market = "m_goMarketPoint"
}

-- 刷新类型
local RefreshType = {
    ResourcePosWorker = 46,  -- 工人资源位置
    RefreshAthleticTalent = 47,  -- 刷新竞技达人
    RefreshRank = 48,  -- 刷新排行榜
    SwitchTab = 49,    -- 切换活动竞赛页签
}

local ActivityRankCenterType = {
    AthleticTalent = 2,  -- 竞技达人
}

local cpCustomHead = require "UI.CustomHead"

--function M:GetPathName()
--	local is_show = DailyTargetManager:IsOpenDailyTargetSwitch()
--	local name = "UI_CopyMainFace2"
--	if is_show then
--		name = "UI_CopyMainFace2_2"
--	end
--	return name
--end

function M:OnInit()
	DungeonManager:OnRequestDungeonLoad()
	PurchaseManager:Initialize()
end

function M:OnCreate(param)
	EnergyModule:EnergyRecoveryTime()
    self.limits = {}
    self.limitsGo = {}
	self.orderList = {}
	self.limitsSpeGo = {}
    LimitActivityController:SetActivePos(self.limitsGo)
    --self.setAni = GetChild(self.uiGameObject, "LeftTop/Setlists", UE.Animation)
    self.aAni = GetComponent(self.uiGameObject, UE.Animation)
    --设置初始位置
    self.taskView = GetChild(self.ui.m_goTask,"taskList",UEUI.ScrollRect)
    self.taskCont = self.taskView.content
    self.defaultTaskPos = self.taskCont.anchoredPosition
    self.workState = {false,false,false}
    --self.setIsOpen = false
    self.gifts = {}
    self.waitClose = false
    self.limit = nil
    self.timerTime = {}
	self.tempShow = {}

	self.m_CustomHead = cpCustomHead.new()
	self.m_CustomHead:CreateHead(self.ui.m_imgHead.transform)
	self.m_CustomHead:SetClickCall(
		function()
			if UIMgr:GetUIOpen(UIDefine.UI_MapDressPreview) then
				return
			end
			UI_SHOW(UIDefine.UI_Setting)
		end)
	
    -- 竞技达人
    self.athleticTalent = {}
    self.isRefreshAthleticTalentRank = false
    local posScreen = UIMgr:GetUIPosByWorld(self.ui.m_scrollviewActivityEnter.transform.position)
    LimitActivityController:SetAthleticTalentPos(posScreen)

    -- 活动竞赛倒计时文本
    self.oneToOneTimeBg = GetChild(self.ui.m_goActivityTabGroup1, "time", UEUI.Image);
	self.oneToOneTimeTxt = GetChild(self.ui.m_goActivityTabGroup1, "time/timeTxt", UEUI.Text);
	self.athleticTalentTimeTxt = GetChild(self.ui.m_goActivityTabGroup2, "time/timeTxt", UEUI.Text);
	self.bowlingTimeTxt = GetChild(self.ui.m_goActivityTabGroup3, "time/timeTxt", UEUI.Text);
	self.rankTimeTxt = GetChild(self.ui.m_goActivityTabGroup4, "time/timeTxt", UEUI.Text);
	self.levelTimeTxt = GetChild(self.ui.m_goActivityTabGroup6, "time/timeTxt", UEUI.Text);
	self.towerTimeBg = GetChild(self.ui.m_goActivityTabGroup7, "time", UEUI.Image);
	self.towerTimeTxt = GetChild(self.ui.m_goActivityTabGroup7, "time/timeTxt", UEUI.Text);

	self.smallStorageBox = nil
    --主界面的特效容器
    self.mUIEffects = {}
    self.mUIEffects[MainFaceDefine.Collect] = GetChild(self.uiGameObject,"RightBottom/m_goCollect/effect_UI_xuanzhuang")
    self.mUIEffects[MainFaceDefine.Market] = GetChild(self.uiGameObject,"RightBottom/m_goMarket/effect_UI_xuanzhuang")
	self.mUIEffects[MainFaceDefine.ChristmasTask] = GetChild(self.uiGameObject,"LeftBottom/m_doFinish/effect_UI_xuanzhuang")
    self.taskPos = {}
    for i = 3, 1,-1 do
        local child = GetChildTrans(self.ui.m_goTaskPos,"Pos"..i)
        self.taskPos[i] = Vector3.New(child.position.x,child.position.y,0)
    end
    self.canvas = {}
    self.canvas[PlayerDefine.LimitEnergy] = {canvas = GetComponent(self.ui.m_goEnergy,UE.Canvas),count = 0}
    self.canvas[PlayerDefine.Coin] =  {canvas = GetComponent(self.ui.m_goCoin,UE.Canvas),count = 0}
    --self.canvas[PlayerDefine.Diamond] =  {canvas = GetComponent(self.ui.m_goDiamond,UE.Canvas),count = 0}
	self.canvas[-1] =  {canvas = GetComponent(self.ui.m_goBack,UE.Canvas),count = 0}
	if Game.Channel_IOS_Notch then
		self:IosInit()
	end
    self:BindingUIToVar()
	MarketModule:refreshTime()
    --倒计时功能
    --任务
    self.taskShowArrow = false
    self.taskItems = {}
 
	-- 主界面礼包集合倒计时
	self.triggerGiftTime = nil
	-- 主界面比拼活动集合入口轮播
	self.activityRankTabList = {}
    self.activityRankTabIndexList = {}
	self.curActivityRankTabPage = 0
	self.maxActivityRankTabPage = 0
	self.tabDeltaTime = 0
	self.rankSubType = nil;

    --EventMgr:Add(EventID.USE_WORKER, self.ChangeWorker, self)
	self:SetLevel()
    self:SetHead()

    --初始化触发礼包列表
    NetGiftBox:PushOpenBag()
    --初始化活动
    LimitActivityController:PushOpenLimit()
    --待推送礼包推送
    NetGiftBox:PushAllWaitGift()

	self:InitLevelTimer()
    --初始化界面红点
    self:InitRedPoint()

	--限时体力部分  由于限时副本当中没有刮刮卡 所以不会有红点和提示 --只有一个箭头 使用一次的箭头
	self.energyRow = GET_UI(self.uiGameObject, "rowEnergy", TP(UEUI.Image))
	--ui.m_imgEnergyBuy --按钮的图片
	self.energyRowPos = self.energyRow.transform:GetPosition()
	--处理按钮和箭头的状态
	self:OnEnergyPoint(nil)
	
    --
    --CollectionItems:ReflushMainPoint()
    --MarketConfig:redPoint()
	self:CheckGiftPackShow()
    self:CreateScheduleFun(function() self:Timer() end,1)
    self:SetIsUpdateTick(true)
    self:InitResourcePos()
    self:SortOrderAllCom(true)

	self:InitEnergyLogic()

    self:InitUIDrag()
    --初始化工人
    --self:ChangeWorker()
    --资源设置
    self:ChangeResource()
    --初始化任务列表
    NetTaskData:InitTask()
    --注册事件
    EventMgr:Add(EventID.CHANGE_RESOURCE, self.ChangeResource, self)
    EventMgr:Add(EventID.BEAST_TOUCH_RESULT, self.TouchResult, self)
	--EventMgr:Add(EventID.MAP_ITEM_NEW,self.ItemNewEvent,self)
	EventMgr:Add(EventID.USING_RESOURCE,self.ResourseChange,self)
	EventMgr:Add(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
	EventMgr:Add(EventID.DUNGEON_UPDATE, self.OnDungeonUpdate, self)
    EventMgr:Add(EventID.BAG_CHANGE, self.BagChange, self)
    self:Timer()

	self:MagnetInit()
 
	self:ShowActiveTaskUI()
	self:ShowSmallStorageBoxUI()
	
	--self:UpdateChristmasTaskNum()
	--MarketConfig:ChristmasredPoint()
	MarketConfig:FreshRedPoint(5)
	self:BombState()
	if not self.showActive then
		Log.Error("副本里- 没有副本活动")
		return
	end
	self.activityTaskConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_task, self.showActive.info.activeId)
	if not self.activityTaskConfig then
		Log.Error("副本里- 没有配置副本任务表 activityTaskConfig")
		return
	end
	self.Maxnum = self.activityTaskConfig.EnergyNum[3]
	self.EnergyCanGet={}
	self.EnergyCanGet[1]=false
	self.EnergyCanGet[2]=false
	self.EnergyCanGet[3]=false
	self.showActive:SetDayEnergy(0,PlayerDefine.LimitEnergy)
	self.EnergyShowIconList={}
	self.EnergyShowIconMaxCount=0
	--self:SetEnergyData()
	EventMgr:Add(EventID.BAG_REFRESH, self.BombState, self)
	
	self:updateActivityGroup()
	self:InitActivityRankTab()

    SetActive(self.ui.m_goTradeWagonsRedPoint, RedPointMgr:IsRed(RedID.TradeWagons))
end

function M:OnRedPointDirty(dirtyIdSet)
	if dirtyIdSet[RedID.MailRoot] then
		self:SetListRedPoint()
	end
    if dirtyIdSet[RedID.TradeWagons] then
        SetActive(self.ui.m_goTradeWagonsRedPoint, RedPointMgr:IsRed(RedID.TradeWagons))
    end
end

function M:OnDungeonUpdate()
	self:updateActivityGroup()
end

function M:ResourseChange(param,param2)
	local num = param
	local type = param2
	if num < 0 then
		NetGiftBox:SetTodayResourceUse(type,num)
	end
end

function M:SetLevel()
	local level, exp, maxExp = NetUpdatePlayerData:GetLevel()
	SetActive(self.ui.m_goPrivilege.gameObject,level >= GlobalConfig.OPEN_PRIVILEGECARD_LEVEL)
	SetActive(self.ui.m_goRightBottomList.gameObject,level >= GlobalConfig.OPEN_PRIVILEGECARD_LEVEL)
	SetActive(self.ui.m_goActCenter,level >= GlobalConfig.OPEN_ACTCENTER_LEVEL)
end

function M:MagnetInit()
	self.magnetPress = self.ui.m_btnMagnet.transform:GetComponent(typeof(CS.ButtonPressed))
	self.magnetPress.buttonPressed = function()
		if NetPrivilegeCardData:IsHaveFreeTime() then
			MapController:SetIsMagnetValue(true)
			CombinePopMgr:InitMagnet(NetPrivilegeCardData:IsHaveFreeTime())
			return
		end
		if NetPrivilegeCardData.data.magnetState == PRIVILEGE_CARD.LOCK then
			UI_SHOW(UIDefine.UI_PrivilegeCard)
			return
		end
		MapController:SetIsMagnetValue(true)
		CombinePopMgr:InitMagnet()

	end

	self.magnetPress.buttonPressedCallBack = function()
		if CombinePopMgr:IsMagneting()then
			CombinePopMgr:EndMagnetState()
		else
			UI_SHOW(UIDefine.UI_PrivilegeCard)
		end
		MapController:SetIsMagnetValue(false)

	end
	self:RefreshMagnetState()
end

function M:RefreshMagnetState()
	local img = GetChild(self.ui.m_goPrivilege,"m_btnMagnet",UEUI.Image)
	if NetPrivilegeCardData.data.magnetState == PRIVILEGE_CARD.LOCK then
		if NetPrivilegeCardData:IsHaveFreeTime() then
			--SetUIImage(img,"Sprite/ui_mainface/icon_tq_citieshenqi.png",false)
			--
			--
			--SetUIImage(self.ui.m_imgMagnet,"Sprite/ui_mainface/iconbox6.png",false)
			--SetUIImage(self.ui.m_imgPrivilegeTime,"Sprite/ui_mainface/iconbox6_time.png",false)
			SetUIImageGray(img,false)
			SetUIImageGray(self.ui.m_imgMagnet,false)
			SetUIImageGray(self.ui.m_imgPrivilegeTime,false)
		else
			--SetUIImage(img,"Sprite/ui_mainface/icon_tq_citieshenqi_1.png",false)
			--
			--SetUIImage(self.ui.m_imgMagnet,"Sprite/ui_mainface/iconbox6_1.png",false)
			--SetUIImage(self.ui.m_imgPrivilegeTime,"Sprite/ui_mainface/iconbox6_time_1.png",false)
			SetUIImageGray(img,true)
			SetUIImageGray(self.ui.m_imgMagnet,true)
			SetUIImageGray(self.ui.m_imgPrivilegeTime,true)
		end
	else
		--SetUIImage(img,"Sprite/ui_mainface/icon_tq_citieshenqi.png",false)
		--
		--SetUIImage(self.ui.m_imgMagnet,"Sprite/ui_mainface/iconbox6.png",false)
		--SetUIImage(self.ui.m_imgPrivilegeTime,"Sprite/ui_mainface/iconbox6_time.png",false)
		SetUIImageGray(img,false)
		SetUIImageGray(self.ui.m_imgMagnet,false)
		SetUIImageGray(self.ui.m_imgPrivilegeTime,false)
	end
end

--ios适配
function M:IosInit()
	local trans = GetComponent(self.uiGameObject.transform , UE.RectTransform)
	local off = (70/(trans.rect.size.x))
	trans.anchorMin = Vector2.New( off, 0)
	trans.anchorMax = Vector2.New( 1-off, 1)
end

function M:CheckGiftPackShow()
	local level = LevelConfig:GetLevel()
	SetActive(self.ui.m_goCarousel,level >= GlobalConfig.OPEN_GIFTPACK_LEVEL)
	self.ui.m_txtGiftPack.text =  TimeMgr:CheckHMS(GiftPackModule:getLastTime())
	SetActive(self.ui.m_goPrivilege.gameObject,level >= GlobalConfig.OPEN_PRIVILEGECARD_LEVEL)
	--SetActive(self.ui.m_goGiftDaily,level >= GlobalConfig.OPEN_GIFT_DAILY)
	
	
	SetActive(self.ui.m_goDailyGiftRed,not NetGiftDaily.data.getFreeDay)
	
end

function M:ChangeActCenterRed()
	local actCenter,num = self:CheckActCenterRed()
	SetActive(self.ui.m_goActPoint,actCenter)
	--SetActive(self.ui.m_txtActPointNum.gameObject,num > 1)
	--self.ui.m_txtActPointNum.text = num
	local actView = UIMgr:GetUIItem(UIDefine.UI_ActCenter)
	if nil ~= actView and actView.isShow then
		actView:UpdateRedPoint()
	end
end

function M:CheckActCenterRed()
	local redNum = 0
	local monthRed = NetMonthCardData:CheckMonthRedPoint()
	if monthRed then
		redNum = redNum + 1
		--return true
	end

	--local firstGiftRed = NetFirstGiftData:GetFirstGifyRed()
	--if firstGiftRed then
	--redNum = redNum + 1
	----return true
	--end
	if not NetGiftDaily.data.getFreeDay then
		redNum = redNum + 1
	end

	if NetGrowthFund:IsOpenActivity() then
		local GrowthFundRed = NetGrowthFund:CheckShowRed()
		if GrowthFundRed then
			redNum = redNum + 1
			--return true
		end
	else
		local GrowthFundRedNew = NetGrowthFundNew:GetIsShowRed()
		if GrowthFundRedNew then
			redNum = redNum + 1
			--return true
		end
	end
	return redNum > 0,redNum
end

function M:ShowActiveTaskUI()
	local isOpen,active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.LimitIns)
	if isOpen then
		self.showActive = active
		local activeMess = active.form.activeMess
		if activeMess.task_ui == nil then
			SetActive(self.ui.m_doFinish,false)
			
		elseif activeMess.task_ui == 2 then
			self:SetRedPoint(MainFaceDefine.ChristmasTask, NetExploreData:IsHaveReward())
			self:UpdateLimitTaskNum()
		else
			if activeMess.task_ui == 1 then
				self:SetRedPoint(MainFaceDefine.ChristmasTask,NetChristamsTask:IsHaveReward())
			else
				self:SetRedPoint(MainFaceDefine.ChristmasTask,false)
				
				local item
				if LimitActivityController:IconIsCanCreate(activeMess.id) then
					if self.limitsSpeGo[activeMess.id] then return end
					local function callBack(obj)
						item = obj
						self.limitsSpeGo[activeMess.id] = item
						item:SetItem(activeMess,1)
						SetActive(self.ui.m_doFinish,false)
					end
					GoTask:Create(self.ui.m_goTaskList,callBack)
				end
			end
			self:UpdateTaskNum()
			SetActive(self.ui.m_goBombShop,false)
		end
		if activeMess.task_ui_icon then
			SetImageSprite(self.ui.m_imgTask,activeMess.task_ui_icon,false)
		end
		SetActive(self.ui.m_goMaxReward, active:GetCompleteReward() and true or false)
		
		--UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_goLimit)
	else
		Log.Error("当前没有正在进行的限时活动")
	end
	
end

function M:ShowSmallStorageBoxUI()
	MapController:GetComponent(MapComponentId.SmallStorageBoxComponent):ShowUI()
end

function M:CloseSpeLimit(id)
	local item = self.limitsSpeGo[id]
	if item then
		item:Close()
		self.limitsSpeGo[id] = nil
	end
end

function M:TouchResult(itemId,isFinish)
    if itemId == ItemID.Boom and isFinish then
        NetInfoData:AddItemToBag(MapController.m_MapId,NetInfoData.BagID.Boom,-1)
    end
end

function M:TickUI(deltaTime)
    for _, v in pairs(self.taskItems) do
        v:TickUI(deltaTime)
    end

	--if MapController:GetMagnetState() then
		--CombinePopMgr:IsShowMagnet()
	--end
end

function M:ChangePrivilegeCardRed()
	local isShow = NetPrivilegeCardData:CheckEnergyRed()
	SetActive(self.ui.m_goPrivilegeRed,isShow)
end

function M:InitUIDrag()
    local uiDrag = GetComponent(self.ui.m_imgBomb,CS.UIDrag)

    uiDrag.m_BeginDrag = function( eventData, go , x, y )
        local count = NetInfoData:GetItemToBag(MapController.m_MapId,NetInfoData.BagID.Boom)
        if count > 0 then
            EventMgr:Dispatch(EventID.BEAST_TOUCH_BEGIN,x, y,ItemID.Boom)
        end
    end

    uiDrag.m_OnDrag = function( eventData, go , x, y )
        EventMgr:Dispatch(EventID.BEAST_TOUCH_MOVE,x, y, ItemID.Boom)
    end

    uiDrag.m_EndDrag = function( eventData, go , x, y )
        EventMgr:Dispatch(EventID.BEAST_TOUCH_END,x, y, ItemID.Boom)
    end
end

function M:BombState(mapid, itemId)
	local itemid = itemId
	if itemid == nil then
		itemid = NetInfoData.BagID.Boom
	end
	local CurmapId = NetUpdatePlayerData.playerInfo.curMap
	local buyBombCount = NetInfoData:GetItemToBag(CurmapId, itemid)
	if mapid == CurmapId and itemId == itemid then
	
	end
	
	if buyBombCount == 0 or buyBombCount == nil then
		SetActive(self.ui.m_imgBomb1, true)
		SetActive(self.ui.m_imgAdd, true)
		SetActive(self.ui.m_imgBomb, false)
	else
		SetActive(self.ui.m_imgAdd, false)
		self.ui.m_txtBumbNum.text = "x" ..buyBombCount
		SetActive(self.ui.m_imgBomb, true)
		SetActive(self.ui.m_imgBomb1, false)
	end
end

--初始化体力部分
function M:InitEnergyLogic()
    maxLimitEnergy = EnergyModule:get_CurScopeEnergyLimit()
    local span = EnergyModule:get_PowerRecoveryTimeLeft()
		self:AddTimerCom(PlayerDefine.LimitEnergy, 1)
	if span > 0 then
	else
		self:RemoveTimerCom(PlayerDefine.LimitEnergy)
	end
end

function M:PlayGrayBtn()
	UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(1056))
end

function M:UpdateTaskNum(num, total)
	local mapId = 0
	local config = nil
	local count = 0
	local numStr
	local index
	local active = self.showActive
	if active then
		local activeId = active.info.activeId
		local formTable =  active:GetForm()
		mapId = formTable.map
		config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_task, activeId)
		if config then
			numStr = SplitString(config.progress, "|")
			count = #numStr
		end
		local nowTask = NetTaskData:GetFinishTaskCount(mapId)
		if config then
			index = SplitString(config.index, "|")
		end

		for i = 1, count do
			if i > 4 then return end
			local status = NetChristamsTask:GetRewardStatus(index[i])
			if status == CHRISTMAS_REWARD_STATUS.CLOSE then
				if num == nil or total == nil then
					num = nowTask
					total = numStr[1]
				end
			else
				if i == count then
					total = numStr[i]
				else
					total = numStr[i + 1]
				end
				num = nowTask
			end
		end

	 	if config then
			local nowCount,maxCount = CollectionItems:GetCollectionProgress(config.collection_id) 
	
			self.ui.m_txtScore.text = string.format("%d/%d", nowCount or 0, maxCount or 0)
			local conReward = active:GetCompleteReward()
			if conReward then
				local rewardConfig = CompleteRewardConfig:GetDataByID(conReward)
				local maxActive = v2n(rewardConfig.progress)
				self.ui.m_txtReward.text = string.format("%d/%d", num or 0, maxActive or 0)
				
				
				SetActive(self.ui.m_goChristamasTaskPoint,num >= maxActive and not active.info.freeReward)
				if num >= maxActive and not active.info.freeReward then
					NetPushViewData:PushView(PushDefine.MaxReward)
				end
	
				--箭头逻辑
				local goArrow = GetChild(self.ui.m_goMaxReward,"RewardBG/Arrow")
				if not self.maxRewardArrowRecord then self.maxRewardArrowRecord = 0 end
				if self.maxRewardArrowRecord < (num or 0) then
					SetActive(goArrow,not active.info.freeReward)
					self.maxRewardArrowRecord = (num or 0)
				end
				
			end
			
		end
	end
	
	--self.ui.m_imgProgress.fillAmount = num / count
	
end

function  M:UpdateLimitTaskNum(num, total)
	local config = ConfigMgr:GetData(ConfigDefine.ID.explore_reward)
	local count = #config
	local rewardNum = NetExploreData:getRewardIndex()
	if rewardNum == 0 then
		num = 0
	else
		num = rewardNum
	end

	self.ui.m_txtScore.text = string.format("%d/%d", num, count)
end

--打开界面的时候需要提高某一个节点的层级99
function M:SetButtonGrayState(btnType,showState)
	self.tempShow[btnType] = (self.tempShow[btnType] or 0) + showState
	if self.tempShow[btnType] > 0 then
		SetActive(self.ui[btnType],false)
	else
		SetActive(self.ui[btnType],true)
	end
end

--更新界面体力相关的数据
function M:OnEnergyPoint(param)

	--如果是界面打开的状态不更新任何的状态
	local bViewDisplay = NetEnergyLotteryData:get_ViewDisplay()
	if bViewDisplay then
		
		SetActive(self.energyRow, false)

	else
		--按钮部分
		local isStart = NetEnergyLotteryData:get_IsStart()
		--箭头
		if isStart then
			local isFreeHigh = NetEnergyLotteryData:get_IsHighFreeOver()
			SetActive(self.energyRow, not isFreeHigh)
			if not isFreeHigh then
				self.energyRow.transform.anchoredPosition = Vector2.New(298 , -128)
				DOKill(self.energyRow.transform)
				DOLocalMoveY(self.energyRow.transform , -90 , 0.5 , nil ,Ease.InOutSine):SetLoops(-1 , LoopType.Yoyo)
			end
		else
			SetActive(self.energyRow, false)
		end
	end
end

function M:InitResourcePos()
	local ResourceItemsGo = GetChild(self.uiGameObject, "RightTop/ResourceItems")
	SetUIForceRebuildLayout(ResourceItemsGo)
    local pos
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doEnergy.transform.position)
    MapController:SetUIResourcePos(ItemID.ENERGY, pos.x, pos.y)
    MapController:SetUIResourcePos(ItemID.LimitEnergy, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doDiamond.transform.position)
    MapController:SetUIResourcePos(ItemID.DIAMOND, pos.x, pos.y)
	MapController:SetUIResourcePos(ItemID.RED_DIAMOND, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doCoin.transform.position)
    MapController:SetUIResourcePos(ItemID.COIN, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_goMagic.transform.position)
    MapController:SetUIResourcePos(ItemID.MAGIC, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doWork1.transform.position)
    MapController:SetUIResourcePos(ItemID.Worker1, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doWork2.transform.position)
    MapController:SetUIResourcePos(ItemID.Worker2, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doFinish.transform.position)
    MapController:SetUIResourcePos(ItemID.TaskFinishStar, pos.x, pos.y)
	pos = UIMgr:GetUIPosByWorld(self.ui.m_goBombShop.transform.position)
	MapController:SetUIResourcePos(ItemID.Boom, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_goBack.transform.position)
    MapController:SetUIResourcePos(FlyId.Air, pos.x, pos.y)
	
	pos = UIMgr:GetUIPosByWorld(self.ui.m_goCollect.transform.position)
	MapController:SetUIResourcePos(ItemID.COLLECTION, pos.x, pos.y)
end

function M:BindingUIToVar()
    local list = {}
    --list[1] = self.ui.m_goDiamond--宝石
    list[2] = self.ui.m_goCoin--金币
    --list[3] = self.ui.m_goLevel--等级
    --list[4] = self.ui.m_goMagic--魔棒
    list[5] = self.ui.m_goEnergy--体力
    --list[6] = self.ui.m_goWorker--工人
    --list[7] = self.ui.m_goDailyTask--每日
    list[8] = self.ui.m_goCollect--藏品
    list[9] = self.ui.m_goMarket--市场
    list[10] = self.ui.m_goDeleted--删除
    --list[11] = self.ui.m_goOrder--港口
    list[12] = self.ui.m_goTip --提示
    list[13] = self.ui.m_goTask --任务
    list[14] = self.ui.m_goSetlist --信息列表
    --list[15] = self.ui.m_btnEnergyTask -- 体力任务
    list[16] = self.ui.m_goLimit--通行证入口
    list[17] = self.ui.m_goBack--返回主界面
    list[18] = self.ui.m_doFinish--任务按钮
    list[25] = self.ui.m_goBombShop--任务按钮
    self.m_ArrUI = list
end

function M:SetGuideShow(param)
    param = param or {}
    for i, v in pairs(self.m_ArrUI) do
        local strIndex = tostring(i)
        local isShow = param[strIndex] ~= nil
		if (i == 17 or i == 9) and NetUpdatePlayerData:GetLevel() >= 7 then isShow = true end
        SetActive(v, isShow)
    end
end


function M:ChangeWorker(id)
    Log.Info("==============ChangeWorker==============",id)
    for i = 1,3 do
        local isWork = WorkerController:GetWorkingById(i)
        if isWork then
            if not self.workState[i] then
                self.workState[i] = true
                SetActive(self.ui["m_doWorkCount"..i],true)
                SetActive(self.ui["m_doWork"..i],false)
                self.ui["m_doWorkCount"..i]:DORestart()
            end
        else
            if self.workState[i] then
                self.workState[i] = false
                SetActive(self.ui["m_doWorkCount"..i],false)
                SetActive(self.ui["m_doWork"..i],true)
                self.ui["m_doWork"..i]:DORestart()
            end
        end
    end
end
function M:ChangeResource(type, num, changeValue)
    function SetEnergy(energy)
        if energy == 0 then
            self.ui.m_txtEnergy.text = GetStrRichColor(0, "ff0000") .. "/" .. maxLimitEnergy
        elseif energy > maxLimitEnergy then
            self.ui.m_txtEnergy.text = GetStrRichColor(math.floor(energy), "00842F") .. "/" .. maxLimitEnergy
        else
            self.ui.m_txtEnergy.text = math.floor(energy) .. "/" .. maxLimitEnergy
        end
        self.ui.m_imgEnergySlider.fillAmount = energy / maxLimitEnergy
    end

    function SetEnergyAct(isShow)
        SetActive(self.ui.m_goBackEnergy,isShow)
    end

    local info = NetUpdatePlayerData:GetPlayerInfo()
    if type == nil then
        self.ui.m_txtDiamond.text = math.floor(info["diamond"])
		self.ui.m_txtCoin.text = math.floor(info["coin"])
        SetEnergy(info["limitenergy"])
        local maxEnergy = LevelConfig:GetLimitEnergy()
        SetEnergyAct(info[PlayerDefine.Energy] >= maxEnergy)
    end
 
	--时间恢复的逻辑
	if type == PlayerDefine.LimitEnergy then
		local span = EnergyModule:get_PowerRecoveryTimeLeft()
		if span <= 0 then
			if info.limitenergy < maxLimitEnergy then
				self:AddTimerCom(PlayerDefine.LimitEnergy, 1)
			else
				self:RemoveTimerCom(PlayerDefine.LimitEnergy)
			end
		end
		local span2 = EnergyModule:get_PowerTimeLeft(GIFT_SCOPE.LIMIT_LEVEL)
		if span2 <= 0 then
			if info.limitenergy < maxLimitEnergy then
				EnergyModule:set_StartReciveTime(nil,GIFT_SCOPE.LIMIT_LEVEL)
			end
		end
	end

    function FinishOrder(type)
        
    end

    if type == PlayerDefine.Diamond then
        local diamond = info["diamond"]
        self.ui.m_doDiamond:DORestart()
        AddDOTweenNumberDelay(diamond - changeValue, diamond, 0.3, 0.8, function(value)
            
            self.ui.m_txtDiamond.text = math.floor(value)
        end,function() FinishOrder(PlayerDefine.Diamond) end)
		if changeValue >0 then
			AudioMgr:Play(12)
		end
    elseif type == PlayerDefine.Coin then
        local coin = info["coin"]
        self.ui.m_doCoin:DORestart()
        AddDOTweenNumberDelay(coin - changeValue, coin, 0.3, 0.8, function(value)
          
            self.ui.m_txtCoin.text = math.floor(value)
        end,function() FinishOrder(PlayerDefine.Coin) end)
		if changeValue >0 then
			AudioMgr:Play(10)
		end
    elseif type == PlayerDefine.LimitEnergy then
        local energy = info["limitenergy"]
        self.ui.m_doEnergy:DORestart()
        AddDOTweenNumberDelay(energy - changeValue, energy, 0.3, 0.8, function(value)
            
            SetEnergy(math.floor(value))
        end,function() FinishOrder(PlayerDefine.LimitEnergy) end)
		if changeValue >0 then
			AudioMgr:Play(11)
		end
    elseif type == PlayerDefine.Energy then
        local energy = info[PlayerDefine.Energy]
        local maxEnergy = LevelConfig:GetLimitEnergy()
        SetEnergyAct(energy >= maxEnergy)
    end
end

function M:ChangeTask(id, progress, isInit)
    if id then
        if progress == TaskProgress.Finish or (self.taskItems[id] and progress == nil) then
            self:RemoveTaskItem(id)
        elseif isInit then
            self:AddTaskItem(id, progress)
        else
            self:ChangeTaskValue(id, progress)
        end
    end
end

--处理任务item
function M:AddTaskItem(id, count)
    local item = taskItem:Create(self.ui.m_goTasks)
    item:SetItem(id, function(arg1, arg2)
        self:onUIEventClick(arg1, arg2)
    end, count)
    self.taskItems[id] = item

    if not self.taskShowArrow and GetTableLength(self.taskItems) > 3 then
        self.taskShowArrow = true
        SetActive(self.ui.m_goUp, false)
        SetActive(self.ui.m_goDown, false)
    end
    return item
end

function M:RemoveTaskItem(id)
    local item = self.taskItems[id]
    if item == nil then
        return
    end
    item:Close()
    self.taskItems[id] = nil
    if self.taskShowArrow and GetTableLength(self.taskItems) <= 3 then
        self.taskShowArrow = false
        SetActive(self.ui.m_goUp, false)
        SetActive(self.ui.m_goDown, false)
    end
end

function M:ChangeTaskValue(id, count)
    local item = self.taskItems[id]
    if item == nil then
        Log.Warning(id, ":task already not have")
        return
    end
    item:ChangeItem(count)
end

--控制计时器
function M:AddTimerCom(type, time)
	if type == PlayerDefine.LimitEnergy then
		local span = EnergyModule:get_PowerRecoveryTimeLeft() --拿到当前的剩余时间
		self.ui.m_txtEnergyTime.text = TimeMgr:GetSpecialMS(span)
		SetActive(self.ui.m_goEnergyTime, true)
		self.timerTime[type] = time
	end
end

function M:RemoveTimerCom(type)
    if type == PlayerDefine.LimitEnergy then
        SetActive(self.ui.m_goEnergyTime, false)
        self.timerTime[type] = nil
    end
end

function M:Timer()
	--限时副本体力部分
	--local span = EnergyModule:get_PowerRecoveryTimeLeft()
	for k, v in pairs(self.timerTime) do
		if v then
			
			if k == PlayerDefine.LimitEnergy then
				local span = EnergyModule:get_PowerRecoveryTimeLeft() --拿到当前的剩余时间
				self.ui.m_txtEnergyTime.text = TimeMgr:GetSpecialMS(span)
				if DailyTargetManager:IsOpenDailyTargetSwitch() then
					local allSpan = EnergyModule:get_PowerRecoveryAllTimeLeft()
					if self.ui.m_txtEnergyFullTime then
						self.ui.m_txtEnergyFullTime.text = TimeMgr:GetSpecialMS(allSpan)
					end
				end
				if span <= 0 then
					self:RemoveTimerCom(k)
				end
			end
		end
	end
	
    for _, v in pairs(self.gifts) do
        v:ChangeItem()
    end

    for _, v in pairs(self.limits) do
        v:ChangeItem()
    end
	local tim1=TimeZoneMgr:GetNextDayTime()
	if tim1<0 then
		tim1=0
	end
	self.ui.m_txtEnergyBoxTime.text = TimeMgr:ConverSecondToString(tim1)
	self.ui.m_txtGiftPack.text =  TimeMgr:CheckHMS(GiftPackModule:getLastTime())
	
	if NetPrivilegeCardData:ClockReached() then
		self:RefreshMagnetState()
	end

	--if not NetPrivilegeCardData:IsHaveFreeTime() and NetPrivilegeCardData.data.magnetState == PRIVILEGE_CARD.LOCK then
	--	local time = NetPrivilegeCardData.data.magnetFreeTime - TimeMgr:GetServerTimestamp()
	--	--self.ui.m_txtPrivilegeTime.text =  TimeMgr:CheckHMS(time)
	--	self.ui.m_txtPrivilegeTime.text =  TimeMgr:GetHMS(time)
	--else
	--	self.ui.m_txtPrivilegeTime.text = "00:00"--LangMgr:GetLang(8319)
	--end

	self.ui.m_txtPrivilegeTime.text = NetPrivilegeCardData:GetMagnetTitleStr()
	--月度赛季建造buff
	-- if NetMonthlySeasonData.data.powerBuidTime - TimeMgr:GetServerTimestamp() > 0 then
	-- 	local time = NetMonthlySeasonData.data.powerBuidTime - TimeMgr:GetServerTimestamp()
	-- 	self.ui.m_txtMsBuff.text =  TimeMgr:CheckHMS(time)
	-- 	SetActive(self.ui.m_goMsBuff,true)
	-- else
	-- 	SetActive(self.ui.m_goMsBuff,false)
	-- end
	self:ActivityRankTabTimer()
	self:UpdateDownTimer()
	self:RefreshTriggerGiftTime()
	self:GiftDailyFreeTimer()
end

function M:AddItemBtn(btn, param)
    AddUIComponentEventCallback(btn, UEUI.Button, function(arg1, arg2)
        self:onUIEventClick(arg1, arg2)
    end, param)
end

function M:InitRedPoint()
	
	--市场的红点儿
	--local isReceived = MarketConfig:redPoint()
	--self:SetRedPoint(MainFaceDefine.Market, false)

	CollectionItems:ReflushMainPoint()
	
	--[[
    --市场的红点儿
    local isReceived = MarketConfig:redPoint()
    self:SetRedPoint(MainFaceDefine.Market, isReceived)

    --收集的红点儿
    local isShow = CollectionItems:MainRedPoint()
    self:SetRedPoint(MainFaceDefine.Collect, isShow)

    local count = NetMailData:GetPointCount()
    if count > 0 then
        self:SetRedPoint(MainFaceDefine.Mail, true)
        self:SetRedPoint(MainFaceDefine.SetList, true)
    else
        self:SetRedPoint(MainFaceDefine.Mail, false)
        self:SetRedPoint(MainFaceDefine.SetList, false)
    end

    local havePush = NetContactData:IsHavePushMsg()
    self:SetRedPoint(MainFaceDefine.Setting, havePush)

    local collectionEffect = GET_UI(self.uiGameObject, "CollEffect", "RectTransform")
    SetActive(collectionEffect, isShow)
	
    EffectConfig:CreateEffect(17, 0, 0, 0, collectionEffect.transform, function(data, tGo, go)
        self:SortOrderAllCom(true)
    end)

    EffectConfig:CreateEffect(28, 0, 0, 0, collectionEffect.transform, function(data, tGo, go)
        self:SortOrderAllCom(true)
    end)
	--]]
	local curMapID = NetUpdatePlayerData.playerInfo.curMap
	local isReceived = MarketConfig:isHaveRedPoint(curMapID, 5)
	self:SetRedPoint(MainFaceDefine.Market, isReceived)
	SetActive(self.ui.m_goMarketPoint,isReceived)

	local isHaveChrTaskReward = NetChristamsTask:IsHaveReward()
	SetActive(self.ui.m_goChristamasTaskPoint,isHaveChrTaskReward)

	local isHaveExploreTaskReward = NetExploreData:IsHaveReward()
	self:SetRedPoint(MainFaceDefine2.ChristmasTask, isHaveExploreTaskReward)
	SetActive(self.ui.m_goChristamasTaskPoint, isHaveExploreTaskReward)
	
	--活动中心红点
	self:ChangeActCenterRed()
	--特权月卡
	self:ChangePrivilegeCardRed()
	-- 设置红点
	self:SetListRedPoint()
end
function M:GetLimits(ActivityID)
	return self.limits[ActivityID]
end
function M:AddLimits(param)
    local item
	local function loadCallBack(obj)
		item = obj

		if not item then
			return
		end
		item:SetItem(param)
		self.limits[param.id] = item
		self.limitsGo[param.id] = item.go
		self:SortOrderAllCom(true)

		local sortList = {}
		for _, v in pairs(self.limits) do
			local config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_sorting,v.totalType)
			v.sortIndex = config and config.sort or 1
			table.insert(sortList,v)
		end

		if table.length(sortList) > 1 then
			table.sort(sortList,function(a,b)
				return a.sortIndex < b.sortIndex
			end)
			local specialObj;
			for _, v in ipairs(sortList) do
				SetUIFirstSibling(v.go)
				if not specialObj and v.totalType == ActivityTotal.LimitIns then
					specialObj = v.go
				end
			end
			--特殊处理(将限时副本调到活动列表开头)
			if specialObj then
				SetUIFirstSibling(specialObj)
			end
		end
		if param.totalType == ActivityTotal.LimitRank then -- or param.totalType == ActivityTotal.Rank
			self:CheckRankAutoOpen(item, param)
		end
	end
    if param.totalType == ActivityTotal.LimitRank then
        GoLimit4:Create(self.ui.m_goLimit, loadCallBack)
    elseif param.totalType == ActivityTotal.Rank then
    	self.limitsGo[param.id] = self.ui.m_scrollviewActivityEnter.gameObject;
   		self:CheckRankAutoOpen(nil, param)
	elseif param.totalType == ActivityTotal.saving_bank then
		--item = GoLimit5:Create(self.ui.m_goDown)
	elseif param.totalType == ActivityTotal.HalloweenOre then
		if NetHalloweenOreData:IsGetCompleteReward() and HalloweenOreManager:IsGetCompleteCollection() then
			return
		end	
		GoLimit20:Create(self.ui.m_goLimit,loadCallBack)
	elseif param.totalType == ActivityTotal.MonthlySeason then
		GoLimit21:Create(self.ui.m_goLimit,loadCallBack)
	elseif param.totalType == ActivityTotal.RollDice then
		GoLimit24:Create(self.ui.m_goLimit,loadCallBack)
	elseif param.totalType == ActivityTotal.ContinuousRecharge then
		if ContinuousRechargeManager:HasCanReward() then
			item = GoLimit25:Create(self.ui.m_transLimitParent)
		else
			return
		end
	elseif param.totalType == ActivityTotal.Relic then
		GoLimit27:Create(self.ui.m_goLimit,loadCallBack)
    elseif param.totalType == ActivityTotal.OneToOne then
        --item = GoLimit28:Create(self.ui.m_transLimitParent)
		NetOneToOneData:InitActiveData(param.id)
    elseif param.totalType == ActivityTotal.AthleticTalent then
        NetAthleticTalentData:InitActiveData(param.id)
	elseif param.totalType == ActivityTotal.KeepCat then
		GoLimit30:Create(self.ui.m_goLimit,loadCallBack)
	elseif param.totalType == ActivityTotal.LimitIns then
		GoLimit:Create(self.ui.m_goLimitEnter,loadCallBack)
    else
        GoLimit:Create(self.ui.m_goLimit,loadCallBack)
    end

end

function M:CheckRankAutoOpen(item, param)
	local active = LimitActivityController:GetActiveMessage(param.id);
	if active.info.isNew then
		active:SetIsNewFalse()
		if item then
			item:ClickItemPush()
		end
		return
	end

	--local active = LimitActivityController:GetActiveMessage(item.id)
	--if active:GetRemainingTime() <= 0 then
		--item:ClickItemPush()
	--end
end

function M:CloseLimits(id)
    local item = self.limits[id]
	if item then
		self.limitsGo[id] = nil
		item:Close()
		self.limits[id] = nil
	end
	self:CloseSpeLimit(id)
end

function M:OnRefresh(type,param)
    if type == 1 or type == 9 then
        if param.state == 1 then
            self:AddLimits(param)
            --LimitActivityController:EnterActivity(self.limit.id)
			if param.totalType == ActivityTotal.BowlingBattle or param.totalType == ActivityTotal.OneToOne
					or param.totalType == ActivityTotal.AthleticTalent or param.totalType == ActivityTotal.Rank then
				self:updateActivityGroup()
				UI_UPDATE(UIDefine.UI_ActivityRankCenter, 1)
			end
        elseif param.state == 2 then
            local item = self.limits[param.id]
            if item then
                item:ChangeValue()
            end
			local totalType = LimitActivityController:GetTotalTypeById(param.id)
			if totalType and totalType == ActivityTotal.BowlingBattle or totalType == ActivityTotal.OneToOne
					or totalType == ActivityTotal.AthleticTalent or totalType == ActivityTotal.Rank then
				self:updateActivityGroup();
				UI_UPDATE(UIDefine.UI_ActivityRankCenter, 2)
			end
        elseif param.state == 3 then
            self:CloseLimits(param.id)

			local totalType = LimitActivityController:GetTotalTypeById(param.id)
			if totalType and totalType == ActivityTotal.BowlingBattle or totalType == ActivityTotal.OneToOne
					or totalType == ActivityTotal.AthleticTalent or totalType == ActivityTotal.Rank then
				self:updateActivityGroup()
				UI_UPDATE(UIDefine.UI_ActivityRankCenter, 1)
			end
        end
    elseif type == 2 then
		----礼包增加
		if param == nil or self.gifts[param] then
			return
		end
		--self:RemoveGift()
		local cfg_data = TriggerGiftConfig:GetDataByID(param)
		if cfg_data == nil then
			return
		end
		if cfg_data.aging == "one" or cfg_data.aging == "onceday" then
			return
		end
		if GetBoolValue(cfg_data.show_alone) then
			--local item = GoActivity:Create(self.ui.m_goActivity)
			--item:SetItem(param)
			--self.gifts[param] = item
			--self:SortGoDownRootUIIndex()
			local function GoActivityBack(item)
				item:SetItem(param)
				self.gifts[param] = item
				self:SortGoDownRootUIIndex()
			end
			GoActivity:Create(self.ui.m_goActivity,GoActivityBack)
		end
		--
		--SetActive(self.ui.m_scrollviewActivity,true)
		--local item = GoActivity:Create(self.ui.m_goActivity)
		--item:SetItem(param)
		--self.gifts[param] = item
		--
		--local sortList = {}
		--for k, v in pairs(self.gifts) do
		--	table.insert(sortList,v)
		--end
		--
		--table.sort(sortList,function(a,b)
		--		return a.show_priority < b.show_priority
		--	end)
		--
		--if table.length(sortList) > 1 then
		--	for _, v in ipairs(sortList) do
		--		SetUIFirstSibling(v.go)
		--	end
		--end
		--
		--if self.ui.m_scrollviewActivity then
		--	TimeMgr:CreateTimer(self, function ()
		--			SetActive(self.ui.m_scrollviewActivity,false)
		--			SetActive(self.ui.m_scrollviewActivity,true)
		--			SetUIFirstSibling(self.ui.m_goFirstPack)
		--		end, 0.1, 1)
		--end
    elseif type == 3 then
        SetActive(self.ui.m_goBackGIft,true)
	elseif type == 29 then
		if self.limits[param] then
			self.limits[param]:ChangeValue()
		end
    elseif type == 4 then
        --tips 选中状态
        self:SetTips(param)
    elseif type == 6 then
		if param then
        	self:SetRedPoint(param[1],param[2])
		end
        self:SetListRedPoint();
	elseif type == 7 then
		if param == true then
			SetActive(self.ui.m_goArrow, true)
		else
			--if NetUpdatePlayerData.playerInfo.curMap == MAP_ID_MAIN then
				--local showRow = NetMarketData:get_IsFirst()
				--if not showRow then
					SetActive(self.ui.m_goArrow, false)
				--end
			--end
		end
    elseif type == 8 then
        self:SetGuideShow(param)
    elseif type == 10 then
        if param then
            Log.Info("+++++++++++PlayClearScreen++++++++++++++++")
            self.aAni:Play("main_clearscreenzoo")
        else
            Log.Info("+++++++++++PlayBackScreen++++++++++++++++")
			--if Game.Channel_IOS_Notch then
				--self.aAni:Play("main_backzoo_ios")
			--else
				
			--end
			self.aAni:Play("main_backzoo")
        end
    elseif type == 12 then --打开界面的时候需要的东西
        --local isShow = param.isShow
        --for _, id in ipairs(param.list) do
            --local mess = self.canvas[id]
            --if isShow then
                --if not mess.order then
                    --mess.order = mess.canvas.sortingOrder
                    --mess.canvas.sortingOrder = 199
                    --self:SetButtonGrayState(AddBtnDefine[id],1)
                --end
                --mess.count = mess.count + 1
            --else
                --if mess.order then
                    --mess.count = mess.count - 1
                    --if mess.count <= 0 then
                        --mess.count = 0
                        --mess.canvas.sortingOrder = mess.order
                        --mess.order = nil
                        --self:SetButtonGrayState(AddBtnDefine[id],-1)
                    --end
                --end
            --end
        --end
    elseif type == 13 then --刷新体力的部分
        self:OnEnergyPoint()
    elseif type == 14 then
        self:ChangeTask(param[1],param[2],param[3])
		if not param[3] then
			self:ShowActiveTaskUI()
		end
		self:UpdateTaskNum()
    elseif type == 15 then
        local count = #param
        self.taskView.content.anchoredPosition = self.defaultTaskPos
        for i = count, 1,-1 do
            local v = param[i]
            local item = self:AddTaskItem(v.id, NetTaskData:GetProgressById(v.id))
            SetUIFirstSibling(item.go)
            item:ShowActive(false)
			local function loadCallBack(obj)
				if pre == nil then
					pre = obj
				end
				local go = CreateGameObjectWithParent(obj, self.uiGameObject)
				local img = GetChild(go, "Image", UEUI.Image)
				SetImageSprite(img, NetTaskData:GetTaskItem(v.id).icon, false,function() SetActive(img,true)  end)
				EffectConfig:CreateEffect(27, 0, 0, 0, GetChildTrans(go, "Effect"), function()
					local function LastCall()
						if i == count then
							UIMgr:SetUILock(false, UIDefine.UI_DialogView,true)
							NetPushViewData:CheckOtherView(true,true)
							WorkerController:SetWorkAni()
							local isInit = NetInfoData:GetDataMess("FreeBoxInit")
							if not isInit then
								for id, item in pairs(self.taskItems) do
									if id == SpecialId.InitFreeBoxTaskId then
										SetUIFirstSibling(item.go)
										break
									end
								end
							end
						end
					end
					item:PlayAddTask(go, v.pos,self.taskPos[i], LastCall)
					self:SortOrderAllCom(true)
				end)
			end
			if pre == nil then
				ResMgr:LoadAssetAsync(prePath, AssetDefine.LoadType.Instant,loadCallBack)
			else
				loadCallBack(pre)
			end
		end
    elseif type == 17 then
        self:RemoveGift(param)
		self.triggerGiftTime = nil
    elseif type == 21 then
		if #param >= 2 then
        	self:UpdateTaskNum(param[1],param[2])
		else
			self:UpdateTaskNum()
		end
		local active = self.showActive
		if active then
			local activeId = active.info.activeId
			local item = self.limitsSpeGo[activeId]
			if item then
				item:ChangeProgress()
			end
		end

    elseif type == 22 then
        --self:UpdateLimitTaskNum(param[1], param[2])
    elseif type == 101 then
        self.ui.m_doFinish:DORestart()
    elseif type == 102 then
        self:SetHead()
	elseif name == "m_goCarousel" then
		UI_SHOW(UIDefine.UI_GiftPack)
	elseif type == 103 then
		if param.state == 1 then
			local item = self.orderList[param.id]
			if item then
				item:ChangeValue(param)
			else
				local function callBack(obj)
					item = obj
					item:SetItem(param,2)
					self.orderList[param.id] = item
				end
				GoTask:Create(self.ui.m_goTaskList,callBack)
			end
			
			MapController:RemoveSign()
			if param.require_t then
				local all_id_set =  MapController:GetAllNPC_IDSet()
				for k, v in pairs(all_id_set) do
					local listOut,count = MapController:GetItemById(v)
					if listOut ~= nil then
						for k1, v1 in pairs(listOut) do
							v1:AddSign()
						end
					end
				end
			end
		elseif param.state == 2 then
			local item = self.orderList[param.id]
			item:Close()
			self.orderList[param.id] = nil
		end
	elseif type == 104 then
		--Log.Error("xcc",104)
		--MapController:RemoveSign()
	elseif type == 105 then
		--for i, v in pairs(t) do
			--print(i, v);
		--end
		self:SetEnergyData()
		
	elseif type == 126 then
		if self.limits[param[1]] then
			self.limits[param[1]]:SetRedShow(param[2])
		end
		-- 更新保龄球红点
		self:updateActivityGroup();
	elseif type == 25 then
		self:CheckGiftPackShow()

	elseif type == 27 then
		self:ChangeActCenterRed()
	elseif type == 34 then
		self:RefreshMagnetState()
	elseif type == 35 then
		self:PlayPrivilegeHandAnim()
	elseif type == 127 then
		self:ChangePrivilegeCardRed()
    elseif type == 128 then
		--小储物箱
		local component = MapController:GetComponent(MapComponentId.SmallStorageBoxComponent,true)
		if component and component:IsWroking() then
			if self.smallStorageBox == nil then
				self.smallStorageBox = GoSmallStorageBox:Create(self.ui.m_goTaskList)
			end
			self.smallStorageBox:Refresh(param)
		end
	elseif type == 40 then--主界面ui置顶显示
		if self.canvas[param[1]] then
			local canvas = self.canvas[param[1]].canvas
			local childButton = {}
			GetChildsComponent(canvas.transform,childButton,typeof(UEUI.Button))
			for index, value in ipairs(childButton) do
				value.interactable = not param[2]
			end
			canvas.sortingLayerName = param[2] and "UI_MIDDLE" or "Default"
		end
    elseif type == 283701 then  -- 1v1 比拼条显示隐藏
        SetActive(self.ui.m_goCompetition, param)
        SetActive(self.ui.m_goNotCompetition, not param)
        if self.oneToOneTimeBg then
        	SetActive(self.oneToOneTimeBg, not param)
        end
        self:RefreshCompetition()
    elseif type == 283702 then  -- 1v1 比拼条刷新
        self:RefreshCompetition(param)
    elseif type == 283703 then  -- 1v1 比拼条剩余时间
        self:RefreshCompetitionTime(param)
	elseif type == 45 then  --触发礼包弹窗结束播飞行特效
		self:PlayTriggerGiftFlyEff(param)
    elseif type == RefreshType.RefreshAthleticTalent then
        self:UpdateAthleticTalent(param)
    elseif type == RefreshType.RefreshRank then
    	self:UpdateRankRed();
    elseif type == RefreshType.SwitchTab then
        self:MoveTargetTab(param)
	elseif type == 304 then --刷新头像
		if self.m_CustomHead then
			self.m_CustomHead:RefreshHead()
		end
	elseif type == 305 then --刷新关卡
		self:InitLevelTimer()
    end
end

function M:PlayPrivilegeHandAnim()
	SetActive(self.ui.m_imgPrivilegeHand,true)
	local Ani = GetComponent(self.ui.m_imgPrivilegeHand, UE.Animation)
	if Ani then
		local function PlayAniEnd()
			SetActive(self.ui.m_imgPrivilegeHand,false)
		end
		PlayAnimStatus(Ani,"privilege_hand",PlayAniEnd)
	end
	--self.ui.m_imgPrivilegeHand:GetComponent("CanvasGroup"):DOFade(1, 0.1)
	--local function PlayAniEnd()
	--	SetActive(self.ui.m_imgPrivilegeHand, false)
	--end
	--local tween, tId = TweenMgr:CreateSequence(UIDefine.UI_MainFace, false, PlayAniEnd)
	--local pFrom = Vector2.New(0,0)
	--local x = UIWidth*0.5
	--local y = (UIHeight+self.ui.m_goDownList.transform.localPosition.y)*0.5
	--local pTo = Vector2.New(-x,-y)
	--local itemRect = GetComponent(self.ui.m_imgPrivilegeHand,UE.RectTransform)
	--itemRect.position = pFrom
	----tween:Append(self.ui.m_imgPrivilegeHand:GetComponent("CanvasGroup"):DOFade(1, 0.5)):SetDelay(0.1)
	--tween:Append(itemRect:DOAnchorPos(pFrom, 0.6))
	--tween:Append(itemRect:DOAnchorPos(pTo, 0.6):SetDelay(0.1))
	--tween:AppendInterval(0.2)
	--
	--tween:SetLoops(2, LoopType.Restart)
end

--设置体力礼包状态
function M:SetEnergyData()
	local num=self.showActive:GetDatEnergy()
	if num >self.Maxnum then
		self.ui.m_txtEnergyNumber.text = self.Maxnum
		self.ui.m_sliderBackGround.value = 1
	else
		self.ui.m_txtEnergyNumber.text = num
		self.ui.m_sliderBackGround.value = num/self.Maxnum
	end
	self:SetEnergyData_Icon(1 , num, self.ui.m_goEnergy1)
	self:SetEnergyData_Icon(2 , num, self.ui.m_goEnergy2)
	self:SetEnergyData_Icon(3 , num, self.ui.m_goEnergy3)
end

--设置体力礼包的状态  ID  是否足够领取  礼包父物体
function M:SetEnergyData_Icon(Index, num, Obj)
	local EnergyShow = num >= self.activityTaskConfig.EnergyNum[Index]
	local ani =	GetComponent(GetChild(Obj,"EnergyBox" .. Index),UE.Animation)
	local txtBox = GetChild(Obj, "m_txtBox"..Index,UEUI.Text)
	if txtBox then
		txtBox.text = self.activityTaskConfig.EnergyNum[Index]
	end

	if EnergyShow then
		if self.showActive:GetEnergyState(Index) then
			--已领取
			self.EnergyCanGet[Index]=false
			SetActive(GetChild(Obj,"Light"),false)
			SetActive(GetChild(Obj,"Red"),false)
			--SetActive(GetChild(Obj,"Getend"),true)
			SetImageSprite( GetChild(Obj,"EnergyBox" .. Index,UEUI.Image), "Sprite/ui_mainface/fuben2_mubiao" .. Index .. ".png", false)
			if ani then
				ani:Stop()
				GameUtil.SetLocalScale(GetChild(Obj,"EnergyBox" .. Index).transform, 1.0, 1.0, 1.0)
			end
		else
			--没领取
			self.EnergyCanGet[Index]=true
			SetActive(GetChild(Obj,"Light"),true)
			SetActive(GetChild(Obj,"Red"),true)
			--SetActive(GetChild(Obj,"Getend"),false)
			SetImageSprite( GetChild(Obj,"EnergyBox" .. Index,UEUI.Image), "Sprite/ui_mainface/fuben2_mubiao" .. Index .. "_2.png", false)
			if ani then
				ani:Play()
			end
		end
	else
		--花费体力不够
		self.EnergyCanGet[Index]=false
		SetActive(GetChild(Obj,"Light"),false)
		SetActive(GetChild(Obj,"Red"),false)
		--SetActive(GetChild(Obj,"Getend"),false)
		SetImageSprite( GetChild(Obj,"EnergyBox" .. Index,UEUI.Image), "Sprite/ui_mainface/tilimubiao_mubiao" .. Index .. "_2.png", false)
		if ani then 
			ani:Stop()
		end
	end
end

function M:GetEnergyPosition()
	if self.ui.m_txtEnergyNumber~=nil then
		return self.ui.m_txtEnergyNumber.transform.position
	end
	return nil
end

function M:SetHead()
	SetActive(self.ui.m_imgNotCompetitionHeadLeft,false)
	SetActive(self.ui.m_imgHeadLeft,false)
	SetActive(self.ui.m_imgHeadRight,false)

	SetActive(self.ui.m_imgNotCompetitionHeadLeft,false)
	self.my1v1Head1 = CreateCommonHead(GetChild(self.ui.m_goNotCompetitionHead,"Left").transform,0.35)
	self.my1v1Head2 = CreateCommonHead(GetChild(self.ui.m_goHead,"Left").transform,0.35)

	SetMyHeadAndBorderByGo(self.my1v1Head1)
	SetMyHeadAndBorderByGo(self.my1v1Head2)

	local rect = GetComponent(self.my1v1Head1,UE.RectTransform)
	rect.anchoredPosition = Vector2.New(0,5)
	rect = GetComponent(self.my1v1Head2,UE.RectTransform)
	rect.anchoredPosition = Vector2.New(0,5)
	
	self.m_CustomHead:SetHeadByID(NetUpdatePlayerData:GetPlayerInfo().head)
	self.m_CustomHead:SetHeadBorderByID(NetUpdatePlayerData:GetPlayerInfo().headBorder)
end

function M:SetListRedPoint()
    local havePush = NetContactData:IsHavePushMsg()
    local headRed = NetInfoData:IsShowHeadRed() or NetHeadSeasonVip:IsShowRedPoint()
    local mailRed = RedPointMgr:IsRed(RedID.MailRoot)
    local rankRed = NetFriendData:GetNewDailyRewardRed()
	local inviteRed = NetInviteFriendData:CheckRedPoint()
    if havePush or headRed or mailRed or rankRed or inviteRed then
        if not self.setIsOpen then
            self:SetRedPoint(MainFaceDefine.SetList, true)
        end
    else
        self:SetRedPoint(MainFaceDefine.SetList, false)
    end
end

function M:RemoveGift(param)
	local item = self.gifts[param]
	if item == nil then
		return
	end
	self.gifts[param] = nil
	item:Close()

	--if table.count(self.gifts)<=0 then
	--	SetActive(self.ui.m_scrollviewActivity,false)
	--end
end

function M:onDestroy()
	self.tipGo = {}
	self.taskItems = {}
	self.gifts = {}
	self.limits = {}
	self.orderList = {}
	self.showActive = nil
	self.activityRankTabList = {}
    EventMgr:Remove(EventID.CHANGE_RESOURCE, self.ChangeResource, self)
    EventMgr:Remove(EventID.BEAST_TOUCH_RESULT, self.TouchResult, self)
	EventMgr:Remove(EventID.BAG_REFRESH, self.BombState, self)
	
	EventMgr:Remove(EventID.USING_RESOURCE,self.ResourseChange,self)
	EventMgr:Remove(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
	EventMgr:Remove(EventID.DUNGEON_UPDATE,self.OnDungeonUpdate,self)
    EventMgr:Remove(EventID.BAG_CHANGE, self.BagChange, self)
	--EventMgr:Remove(EventID.MAP_ITEM_NEW,self.ItemNewEvent,self)

    -- 清理竞技达人
    self.athleticTalent = nil
    self.isRefreshAthleticTalentRank = nil
    -- 清理活动竞赛倒计时文本
    self.oneToOneTimeBg = nil;
	self.oneToOneTimeTxt = nil;
	self.athleticTalentTimeTxt = nil;
	self.bowlingTimeTxt = nil;
	self.rankTimeTxt = nil;
	self.towerTimeTxt = nil;
	
	if self.m_CustomHead then
		self.m_CustomHead:onDestroy()
		self.m_CustomHead = nil
	end

	self.my1v1Head1 = nil
	self.my1v1Head2 = nil
	self.other1v1Head = nil
end

--function M:ItemNewEvent()
	
--end

function M:onUIEventClick(go,param)
    if self.waitClose then
        return
    end
    local name = go.name
    if name == "m_btnSetting" then
        UI_SHOW(UIDefine.UI_Setting)
    elseif name == "m_btnEmail" then
        local funBack = function(objJson)
            UIMgr:ShowWaiting(false)
			if objJson == nil then
				UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(5012))
				return
			else
				NetMailData:ReadNetData(objJson)
				UI_SHOW(UIDefine.UI_MailPanel)
			end
		end
		local params = {}
		params["cate"] = 1
		HttpClient:SendToGS("/sns/maillist", params, funBack)
        UIMgr:ShowWaiting(true)
    elseif name == "m_goCollect" then
		UI_SHOW(UIDefine.UI_Collection, nil)
    elseif name == "taskInfo" then
        UI_SHOW(UIDefine.UI_Task, param)
    elseif name == "m_btnShop" then
        UI_SHOW(UIDefine.UI_Market, 0)
    elseif name == "m_imgDiamBuy" or name == "m_goDiamond" then
        if self.tempShow[AddBtnDefine.diamond] and self.tempShow[AddBtnDefine.diamond] > 0 then
        else
			UI_SHOW(UIDefine.UI_ActCenter,13)
        end
    elseif name == "m_imgCoinBuy" or name == "m_goCoin" then
        if self.tempShow[AddBtnDefine.coin] and self.tempShow[AddBtnDefine.coin] > 0 then
        else
            UI_SHOW(UIDefine.UI_Market,"Money")
        end
    elseif name == "m_imgEnergyBuy" or name == "m_goEnergyRec" or name == "m_goEnergy" then
        --触发流程
		if self.tempShow[AddBtnDefine.energy] and self.tempShow[AddBtnDefine.energy] > 0 then
		--elseif self.grayBtn[AddBtnDefine.energy] then
		--	self:PlayGrayBtn(AddBtnDefine.energy)
		else
			--触发流程
			UIMgr:Show(UIDefine.UI_Energy)
		end
    --elseif name == "m_goWork1" or name == "m_goWork2" or name == "m_goExtraWork" or name == "workAdd" then
    --
    --	UI_SHOW(UIDefine.UI_BuyWorker)

    elseif name == "m_goRecover" then
        SetActive(self.ui.m_goRecover, false)
        SetActive(self.ui.m_goExpand, true)
        SetActive(self.ui.m_goTips, false)
        self.selectIsClose = true
    elseif name == "m_goExpand" then
        SetActive(self.ui.m_goRecover, true)
        SetActive(self.ui.m_goExpand, false)
        SetActive(self.ui.m_goTips, false)
        self.selectIsClose = false
        self:SetTips(self.selectId, true)
    elseif name == "m_goDeleted" then
        UI_SHOW(UIDefine.UI_Delete, self.selectId)
    elseif name == "m_goDetails" then
        UI_SHOW(UIDefine.UI_Collection, self.selectId)
    elseif name == "m_goHeroBtn" then
        UI_SHOW(UIDefine.UI_HeroHelp,self.selectId)
    elseif name == "m_btnFinish" then
        --UI_SHOW(UIDefine.UI_ChristmasTask)
		----UI_SHOW(UIDefine.UI_ExplorTask)
		--local isOpen,active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.LimitIns)
		--if isOpen then
			--if active.info.subtype == ActivitySubtype.ChristmasLimit then
				--UI_SHOW(UIDefine.UI_ChristmasTask)
			--elseif active.info.subtype == ActivitySubtype.NormalLimit then
				--UI_SHOW(UIDefine.UI_ExplorTask)
			--end
		--end
		
		if self.showActive then
			local activeMess = self.showActive.form.activeMess
			if activeMess.task_ui == 1 then
				--UI_SHOW(UIDefine.UI_ChristmasTask)
			elseif activeMess.task_ui == 2 then
				UI_SHOW(UIDefine.UI_ExplorTask)
			elseif activeMess.task_ui == 3 then
				UI_SHOW(UIDefine.UI_DressTask)
			end
		end
    elseif name == "back" then
        --LimitActivityController:LeaveActivity(self.limit.id)
		UI_SHOW(UIDefine.UI_Travel, nil)
        --MapController:ChangeMap(MAP_ID_MAIN)
        --self.waitClose = true
    elseif name == "m_goSetlist" then
        if self.setAni.isPlaying then
            return
        end
        local isActive = self.ui[MainFaceDefine.Mail].activeSelf
        local isSettingActive = self.ui[MainFaceDefine.Setting].activeSelf
        if self.setIsOpen then
            if isActive then
                self:SetRedPoint(MainFaceDefine.SetList,true)
            end

            if isSettingActive then
                self:SetRedPoint(MainFaceDefine.SetList,true)
            end
            self.setAni:Play("setlists_close")
        else
            if isActive then
                self:SetRedPoint(MainFaceDefine.SetList,false)
            end

            if isSettingActive then
                self:SetRedPoint(MainFaceDefine.SetList,false)
            end
            self:SetRedPoint(MainFaceDefine.SetList,false)

            self.setAni:Play("setlists_open")
        end
        self.setIsOpen = not self.setIsOpen
	elseif name == "m_goBombShop" then
		local CurmapId = NetUpdatePlayerData.playerInfo.curMap
		local buyBombCount = NetInfoData:GetItemToBag(CurmapId, NetInfoData.BagID.Boom)
		if buyBombCount <= 0 then	
			UI_SHOW(UIDefine.UI_BombShop)
		end
	elseif name == "m_goCarousel" then
		UI_SHOW(UIDefine.UI_GiftPack)
	elseif name == "RewardBG" then
		UI_SHOW(UIDefine.UI_MaxReward)

		local goArrow = GetChild(self.ui.m_goMaxReward,"RewardBG/Arrow")
		if goArrow then
			SetActive(goArrow,false)
		end
		
	elseif name == "Image" then
		UI_SHOW(UIDefine.UI_Setting)
	elseif name == "EnergyBox1" then
		self:GetEnergyItem(1)
	elseif name == "EnergyBox2" then
		self:GetEnergyItem(2)
	elseif name == "EnergyBox3" then
		self:GetEnergyItem(3)
	elseif name == "m_imgbg" then
		SetActive(self.ui.m_goShowData,false)
		SetActive(self.ui.m_imgbg,false)
	elseif name == "EnemyIcon" then
		UI_SHOW(UIDefine.UI_EnergyHelp)
	elseif name == "m_btnActCenter" then
		UI_SHOW(UIDefine.UI_ActCenter)
	elseif name == "m_btnMagnet" or name == "m_btnEnergy" then
		if not UIMgr:GetUIOpen(UIDefine.UI_PrivilegeCard) then
			UI_SHOW(UIDefine.UI_PrivilegeCard)
		end
	elseif name == "m_btnCompetition" then
		local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.OneToOne)
		Log.Info("活动 对象", activityItem)
		if activityItem:IsActivityEnd() then
			if activityItem.info.activeId then
				NetOneToOneData:CheckEndPush(activityItem.info.activeId)
			end
		else
			UI_SHOW(UIDefine.UI_ActivityRankCenter, 1)
		end
	elseif name == "m_btnBowlingBattle" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 3)
	elseif name == "m_goGiftDaily" then
		UI_SHOW(UIDefine.UI_GiftDaily)
	elseif name == "m_btnActCenter" then
		UI_SHOW(UIDefine.UI_ActCenter)
	elseif name == "m_goTriggerGift" then
		--UI_SHOW(UIDefine.UI_Shop)
		UI_SHOW(UIDefine.UI_ActCenter, 7);
	elseif name == "rankBtn" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 4);
	elseif name == "m_btnActiveFollow" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 5);
	elseif name == "m_btnTowerClimbing" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 7)
	elseif name == "m_btnSlg" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter, 6)
    end
end

--体力礼包被点击
function M:GetEnergyItem(id)
	if self.EnergyCanGet[id] then
		--获取体力礼包
		self.showActive:GetEnergy(id)
		self:SetEnergyData()
		if id==1 then
			EffectConfig:CreateEffect(144, 0, 0, 0,self.ui.m_goEnergy1.transform)
		elseif id==2 then
			EffectConfig:CreateEffect(144, 0, 0, 0,self.ui.m_goEnergy2.transform)
		elseif id==3 then
			EffectConfig:CreateEffect(144, 0, 0, 0,self.ui.m_goEnergy3.transform)
		end
	else
		--展示体力礼包内的物品
		if id==1 then
			SetParent(self.ui.m_goShowData,self.ui.m_goEnergy1)
			self:SetShowEnergyImg(id)
		elseif id==2 then
			SetParent(self.ui.m_goShowData,self.ui.m_goEnergy2)
			self:SetShowEnergyImg(id)
		elseif id==3 then
			SetParent(self.ui.m_goShowData,self.ui.m_goEnergy3)
			self:SetShowEnergyImg(id)
		end
		self.ui.m_goShowData.transform.localPosition=Vector3.New(0,0,0)
		SetActive(self.ui.m_goShowData,true)
		SetActive(self.ui.m_imgbg,true)
	end
end

function M:SetShowEnergyImg(index)
	for k,v in pairs(self.EnergyShowIconList) do
		SetActive(v,false)
	end
	local config = self.activityTaskConfig
	local data=config.EnergyGet[index]
	local i=1
	for k,v in pairs(data) do
		if self.EnergyShowIconMaxCount < i then
			local obj=UEGO.Instantiate(self.ui.m_imgEnergyData,self.ui.m_imgEnergyData.transform.parent)
			--SetParent(obj,self.ui.m_imgEnergyData.transform.parent)
			SetActive(obj,true)
			self.EnergyShowIconList[i] = obj.transform:GetComponent(typeof(UEUI.Image))
			self.EnergyShowIconMaxCount = self.EnergyShowIconMaxCount + 1
		end
		local imgurl=ItemConfig:GetIcon(tonumber(v.k))
		SetImageSprite(self.EnergyShowIconList[i],imgurl,false)
		SetActive(self.EnergyShowIconList[i],true)
		i = i + 1
	end
	--SetImageSprite(self.ui.m_imgEnergyData,ItemConfig:GetIcon(tonumber(config.energy_1)),false)
end

function M:SetTips(id, isOnlyShow)
    if not isOnlyShow then
        self.selectId = id
    end
    if not self.selectIsClose then
        if self.selectId == nil then
            SetActive(self.ui.m_goSelect, false)
            SetActive(self.ui.m_goNoSelect, true)
        else
            SetActive(self.ui.m_goSelect, true)
            SetActive(self.ui.m_goNoSelect, false)
            self.ui.m_txtTipTitle.text = ItemConfig:GetRareID(self.selectId,true)
            local data = ItemConfig:GetDataByID(self.selectId)
            self.ui.m_txtTipContent.text = LangMgr:GetLang(data.explain)
            if data.is_can_delete == 1 then
                SetActive(self.ui.m_goDeleted, true)
            else
                SetActive(self.ui.m_goDeleted, false)
            end
            if data.type_use == ItemUseType.HeroFly then
                SetActive(self.ui.m_goHeroBtn,true)
            else
                SetActive(self.ui.m_goHeroBtn,false)
            end
            self.selectId = id
            local isType = CollectionItems:GetCollectId(self.selectId)
            if isType == nil then
                SetActive(self.ui.m_goDetails, false)
            else
                SetActive(self.ui.m_goDetails, true)
            end
        end
    end
end

--设置主界面红点是否显示
function M:SetRedPoint(faceType,isShow)
    local ui = self.ui[faceType]
    if ui == nil then
        --Log.Error("red point name is wrong")
        return
    end
    if ui.activeSelf ~= isShow then
        SetActive(ui,isShow)
    end

    local uiEffect = self.mUIEffects[faceType]
    if nil ~= uiEffect then
        if uiEffect.activeSelf ~= isShow then
            SetActive(uiEffect,isShow)
        end
    end
end

function M:RefreshCompetition(param)
	-- 刷新玩家头像
	--local playerHead = NetUpdatePlayerData:GetPlayerInfo().head
	--local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerHead)
	--if headConfig then
	--	SetUIImage(self.ui.m_imgHeadLeft, headConfig["icon"], false)
	--end
	SetMyHeadAndBorderByGo(self.my1v1Head1)
	SetMyHeadAndBorderByGo(self.my1v1Head2)

	-- 刷新玩家名字
	if NetUpdatePlayerData:GetPlayerInfo().name then
		self.ui.m_txtNameLeft.text = NetUpdatePlayerData:GetPlayerInfo().name
	else
		self.ui.m_txtNameLeft.text = "player-" .. NetUpdatePlayerData:GetPlayerInfo().id
	end
	---- 设置匹配对手头像
	--local currentRobotHead = NetOneToOneData:GetDataByKey("currentRobotHead")
	--if not IsNilOrEmpty(currentRobotHead) then
	--    SetUIImage(self.ui.m_imgHeadRight, currentRobotHead, false)
	--
	--	if self.other1v1Head == nil then
	--		self.other1v1Head = CreateCommonHead(GetChild(self.ui.m_goHead,"Right").transform,0.45)
	--	end
	--	SetHeadAndBorderByGo(self.other1v1Head,currentRobotHead,121)
	--end

	-- 设置匹配对手头像
	local currentRobotHead = NetOneToOneData:GetDataByKey("currentRobotHead")
	local currentRobotBorder = NetOneToOneData:GetDataByKey("currentRobotBorder")
	if self.other1v1Head == nil then
		self.other1v1Head = CreateCommonHead(GetChild(self.ui.m_goHead,"Right").transform,0.35)
		local rect = GetComponent(self.other1v1Head,UE.RectTransform)
		rect.anchoredPosition = Vector2.New(0,5)
	end

	if not IsNilOrEmpty(currentRobotHead)  and not IsNilOrEmpty(currentRobotBorder)then
		SetHeadAndBorderByGo(self.other1v1Head,currentRobotHead,currentRobotBorder)
	end

	local currentRobotName = NetOneToOneData:GetDataByKey("currentRobotName")
	self.ui.m_txtNameRight.text = currentRobotName
	-- 显示当前比拼分数
	self:RefreshScore()
	local winNum = NetOneToOneData:GetDataByKey("winningStreak")
	local config = OneToOneManager:GetVictoryConfig(winNum)
	if config then
		self.ui.m_txtCompetition.text = config.victory_points
	end
	-- 飞积分
	-- if param and param.isFly then
	--     local view = UIMgr:GetUIItem(UIDefine.UI_OneToOneView)
	--     local isShow = view and view.isShow
	--     if isShow then return end
	--     local oldPos =  UIMgr:GetObjectScreenPos(self.ui.m_transFlyPos)
	--     local endPos
	--     -- 玩家
	--     if param.endPos == 0 then
	--         endPos = self.ui.m_imgHeadLeft
	--         if param.startPos then
	--             oldPos = param.startPos
	--             OneToOneManager.startPos = nil
	--         end
	--     -- 机器人
	--     elseif param.endPos == 1 then
	--         endPos = self.ui.m_imgHeadRight
	--         oldPos =  UIMgr:GetObjectScreenPos(self.ui.m_transFlyPosRobot)
	--     end
	--     local posScreen = UIMgr:GetUIPosByWorld(endPos.transform.position)
	--     local flyId = 55002
	--     local score = 5
	--     MapController:FlyUIAnim(oldPos.x, oldPos.y, flyId, score, posScreen.x, posScreen.y,
	--     nil, nil, nil, 0.7, nil,
	--     function ()

	--     end)
	-- end
end

function M:RefreshCompetitionTime(time)
    self.ui.m_txtCompetitionCountDown.text = TimeMgr:ConverSecondToString(time)
end

function M:RefreshScore()
    local curScore = NetOneToOneData:GetDataByKey("curScore")
    local robotScore = NetOneToOneData:GetDataByKey("robotScore")
    if NetOneToOneData.playerChangeScore > 0 then
        if self.txtScoreBlueTween then
            self.txtScoreBlueTween:Kill()
            self.txtScoreBlueTween = nil
        end
        self.txtScoreBlueTween = Tween.To(function(value)
            self.ui.m_txtScoreBlue.text = tostring(math.floor(value))
        end, curScore - NetOneToOneData.playerChangeScore, curScore, 1)

        local item = UEGO.Instantiate(self.ui.m_goScoreBlue, self.ui.m_goScore.transform)
        SetActive(item, true)
        local text = GetChild(item.transform, "m_txtScoreBlueAdd", UEUI.Text)
        text.text = "+" .. NetOneToOneData.playerChangeScore
        item.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOScale(item.transform, Vector3.New(1.0, 1.0, 1.0), 0.8, nil, Ease.OutBack)
        local rect = GetComponent(item.transform, UE.RectTransform)
        rect.anchoredPosition3D = Vector3.New(-188, -70, 0)
        DOLocalMove(item.transform, Vector3.New(-168, -64, 0), 0.8, nil, Ease.OutBack):OnComplete(function ()
            DOScale(item.transform, Vector3.New(0, 0, 0), 1, nil, Ease.OutBack):SetDelay(1)
            DOLocalMove(item.transform, Vector3.New(-109, -2, 0), 1, nil, Ease.OutBack):OnComplete(function ()
                UEGO.Destroy(item)
            end):SetDelay(1)
        end)

        NetOneToOneData.playerChangeScore = 0
    else
        self.ui.m_txtScoreBlue.text = tostring(curScore)
    end

    if NetOneToOneData.robotChangeScore > 0 then
        if self.txtScoreRedTween then
            self.txtScoreRedTween:Kill()
            self.txtScoreRedTween = nil
        end
        self.txtScoreRedTween = Tween.To(function(value)
            self.ui.m_txtScoreRed.text = tostring(math.floor(value))
        end, robotScore - NetOneToOneData.robotChangeScore, robotScore, 1)

        local item = UEGO.Instantiate(self.ui.m_goScoreRed, self.ui.m_goScore.transform)
        SetActive(item, true)
        local text = GetChild(item.transform, "m_txtScoreRedAdd", UEUI.Text)
        text.text = "+" .. NetOneToOneData.robotChangeScore
        item.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOScale(item.transform, Vector3.New(1.0, 1.0, 1.0), 0.8, nil, Ease.OutBack)
        local rect = GetComponent(item.transform, UE.RectTransform)
        rect.anchoredPosition3D = Vector3.New(82, -70, 0)
        DOLocalMove(item.transform, Vector3.New(62, -64, 0), 0.8, nil, Ease.OutBack):OnComplete(function ()
            DOScale(item.transform, Vector3.New(0, 0, 0), 1, nil, Ease.OutBack):SetDelay(1)
            DOLocalMove(item.transform, Vector3.New(3, -2, 0), 1, nil, Ease.OutBack):OnComplete(function ()
                UEGO.Destroy(item)
            end):SetDelay(1)
        end)

        NetOneToOneData.robotChangeScore = 0
    else
        self.ui.m_txtScoreRed.text = tostring(robotScore)
    end

    if curScore > 0 or robotScore > 0 then
        local percent = curScore / (curScore + robotScore)
        local diff = math.abs(percent - 0.5)
        local target = diff * 0.4
        if curScore > robotScore then
            target = 0.5 + target
        else
            target = 0.5 - target
        end
        self.ui.m_sliderVS:DOValue(target, 1)
    else
        self.ui.m_sliderVS.value = 0.5
    end
end


function M:GetActivityGroupSortList()
	local sortList = {}
	local config = ConfigMgr:GetData(ConfigDefine.ID.activity_rank)
	for i, v in ipairs(config) do
		table.insert(sortList,v)
	end
	table.sort(sortList, function(a, b)
		return a.sort < b.sort;
	end)
	return sortList
end

function M:updateActivityGroup()
	local config = self:GetActivityGroupSortList()
	local count = 0
	local nameStr;
	local redImg;
	local level = NetUpdatePlayerData:GetLevel()
	for i, v in ipairs(config) do
		nameStr = "m_goActivityTabGroup"..v.id;
		local activityOpen ,activity = NetGlobalData:GetIsOpenActivityRank(v)
		if activityOpen then
			count = count + 1
			SetActive(self.ui[nameStr],true)
		else
			if activity and activity.info.state == 3 and v.open == ActivityTotal.BowlingBattle then
				activityOpen = activity and activity.info.state == 3;
				count = count + 1
				SetActive(self.ui[nameStr],true)
			else
				SetActive(self.ui[nameStr],false)
			end
		end
		self.activityRankTabList[v.id] = activityOpen
		self.activityRankTabIndexList[v.id] = count

		if activityOpen and activity then
			local redImg = GetChild(self.ui[nameStr], "redImg");
			if openParm == ActivityTotal.BowlingBattle then
				SetActive(redImg, BowlingBattleManager:CheckRedPoint())
			elseif openParm == ActivityTotal.OneToOne then
				SetActive(redImg, NetOneToOneData:IsShowRedPoint())
				self.oneToOneTime = activity:GetRemainingTime();
			elseif openParm == ActivityTotal.AthleticTalent then
				SetActive(redImg, NetAthleticTalentData:IsShowRedPoint())
				self.athleticTalentTime = activity:GetRemainingTime();
			elseif openParm == ActivityTotal.Rank then
				SetActive(redImg, NetRankData:IsShowRedPoint(ActivityTotal.Rank) or NetUpdatePlayerData:GetPlayerRankOneRewardRed());
				self.rankTime = activity:GetRemainingTime();
			else
				SetActive(redImg, false)
			end
		end
	end
	self.maxActivityRankTabPage = count - 1
	local tabNameStr;
	for i = 1, #config do
		tabNameStr = "m_imgActivityTab"..i;
		if self.ui[tabNameStr] then
			SetActive(self.ui[tabNameStr], i <= count)
		end
	end
	for i, v in ipairs(config) do
		nameStr = "m_goActivityTabGroup"..v.id;
		SetUILastSibling(self.ui[nameStr])
	end
	SetActive(self.ui.m_goActivityGrop,count > 0)
	SetActive(self.ui.m_goActivityTabRoot,count > 0)

	if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.AthleticTalent] then
		self:UpdateAthleticTalent(true)
	end
	if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.BowlingBattle] then
		self:updateBowlingShow()
	end
	if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.Rank] then
		self:UpdateRankShow();
	end
	if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.TowerClimbing] then
		self:UpdateTowerShow()
	end
end

function M:updateBowlingShow()
	local isOpen, activity = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.BowlingBattle);
	local isPreview = activity and activity.info.state == 3;
	if isPreview then
		local helperNum = 0
		local detail = LeagueManager:GetMyLeagueDetails()
		if detail and detail.GetHelpNumber then
			helperNum = detail:GetHelpNumber()
		end

		SetActive(self.ui.m_goBowlingConditionOK, helperNum > 0)
		SetActive(self.ui.m_goBowlingConditionNO, helperNum <= 0)

		local replaceText;
		if helperNum > 0 then
			replaceText = "(" .. helperNum .. "/1)"
		else
			replaceText = string.format("(<color=#%s>0</color>/1)", "ff0000")
		end
		self.ui.m_txtBowlingCondition.text = LangMgr:GetLangFormat(9312, replaceText);

		self.bowlingDownTime = activity:GetStartRemainingTime();
	elseif isOpen then
		local rank = NetBowlingBattleData:GetRankMyLeague();
		if rank > 0 then
			self.ui.m_txtRank.text = rank;
		end
		self.bowlingDownTime = activity:GetRemainingTime();

		isOpen = rank > 0;
	end

	SetActive(self.ui.m_goOpen, isPreview);
	SetActive(self.ui.m_goRank, isOpen);
end

function M:UpdateDownTimer()
	if self.oneToOneTimeTxt then
		if self.oneToOneTime and self.oneToOneTime > 0 then
			self.oneToOneTime = self.oneToOneTime - 1;
			self.oneToOneTimeTxt.text = TimeMgr:ConverSecondToString(self.oneToOneTime);
		else
			self.oneToOneTimeTxt.text = TimeMgr:ConverSecondToString(0);
		end
	end

	if self.athleticTalentTimeTxt then
		if self.athleticTalentTime and self.athleticTalentTime > 0 then
			self.athleticTalentTime = self.athleticTalentTime - 1;
			self.athleticTalentTimeTxt.text = TimeMgr:ConverSecondToString(self.athleticTalentTime);
		else
			self.athleticTalentTimeTxt.text = TimeMgr:ConverSecondToString(0);
		end
	end

	if self.bowlingTimeTxt then
		if self.bowlingDownTime and self.bowlingDownTime > 0 then
			self.bowlingDownTime = self.bowlingDownTime - 1;
			self.bowlingTimeTxt.text = TimeMgr:ConverSecondToString(self.bowlingDownTime);
		else
			self.bowlingTimeTxt.text = TimeMgr:ConverSecondToString(0);
		end
	end
	
	if self.rankTimeTxt then
		if self.rankTime and self.rankTime > 0 then
			self.rankTime = self.rankTime - 1;
			self.rankTimeTxt.text = TimeMgr:ConverSecondToString(self.rankTime);
		else
			self.rankTimeTxt.text = TimeMgr:ConverSecondToString(0);
		end
	end

	if self.towerTimeTxt then
		if not self.towerTime then
			local nowTime = TimeMgr:GetServerTimestamp()
			--明天天零点
			local tomorrowTimeStamp = TimeZoneMgr:GetServerClockStampByNDay(1,nowTime)
			self.towerTime = tomorrowTimeStamp - nowTime
		end

		if self.towerTime and self.towerTime > 0 then
			self.towerTime = self.towerTime - 1
			self.towerTimeTxt.text = TimeMgr:ConverSecondToString(self.towerTime);
		else
			self.towerTime = nil
			self.towerTimeTxt.text = TimeMgr:ConverSecondToString(0);
		end
	end

	self:CheckLevelTimer()
end

--- 刷新竞技达人
--- @param refreshRank boolean 是否刷新排名
function M:UpdateAthleticTalent(refreshRank)
    local type = ACTIVITY_RANK_TABINDEX.AthleticTalent--ActivityRankCenterType.AthleticTalent
    local nameStr = "m_goActivityTabGroup" .. type
    local root = self.ui[nameStr]
    local activity = self.activityRankTabList[type]
    -- 初始获取组件
    if IsTableEmpty(self.athleticTalent) then
        local icon = GetChild(root, "icon", UEUI.Image)
        local title = GetChild(root, "title", UEUI.Text)
        local button = GetChild(root, "button", UEUI.Button)
        if button then
            button.onClick:AddListener(function ()
                local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.AthleticTalent)
                if activityItem:IsActivityEnd() then
                    if activityItem.info.activeId then
                        NetAthleticTalentData:CheckEndPush(activityItem.info.activeId)
                    end
                else
                    UI_SHOW(UIDefine.UI_ActivityRankCenter, type)
                end
            end)
        end
        local score = GetChild(root, "score/text", UEUI.Text)
        local rank = GetChild(root, "rank/text", UEUI.Text)
        local rankParent = GetChild(root, "rank")
        self.athleticTalent.icon = icon
        self.athleticTalent.title = title
        self.athleticTalent.button = button
        self.athleticTalent.score = score
        self.athleticTalent.rank = rank
        self.athleticTalent.rankParent = rankParent
    end
    -- 刷新图片
    if self.athleticTalent.icon then
        local iconPath = "Sprite/ui_mainface/mainface2_huodong_jingjidaren.png"
        SetUIImage(self.athleticTalent.icon, iconPath, false)
    end
    -- 刷新标题
    if self.athleticTalent.title then
        local langID = AthleticTalentManager:GetActivityNameLangID() or 2203001
        self.athleticTalent.title.text = LangMgr:GetLang(langID)
    end
    -- 刷新分数
    if self.athleticTalent.score then
        local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
        if 1 <= currentDay and currentDay <= 6 then
            local score = NetAthleticTalentData:GetDailyScore()
            self.athleticTalent.score.text = tostring(score)
        elseif currentDay >= 7 then
            local score = NetAthleticTalentData:GetTotalScore()
            self.athleticTalent.score.text = tostring(score)
        end
    end
    -- 刷新排名
    if self.athleticTalent.rank and refreshRank then
        if not self.isRefreshAthleticTalentRank then
            self:RefreshAthleticTalentRank()
            self.isRefreshAthleticTalentRank = true
            TimeMgr:CreateTimer("RefreshAthleticTalentRank", function()
                self.isRefreshAthleticTalentRank = false
            end, 1, 1)
        end
    end
    -- 刷新红点
    local redImg = GetChild(self.ui.m_goActivityTabGroup2, "redImg")
    SetActive(redImg, NetAthleticTalentData:IsShowRedPoint())
end

--- 刷新竞技达人排名
function M:RefreshAthleticTalentRank()
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    local function dayRankCallBack(data)
        -- 已上榜
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        if data.player.rank and isEnterRank then
            if self.athleticTalent then
                self.athleticTalent.rank.text = tostring(data.player.rank)
                SetActive(self.athleticTalent.rankParent, true)
            end
        -- 未上榜
        else
            if self.athleticTalent then
                self.athleticTalent.rank.text = ""
                SetActive(self.athleticTalent.rankParent, false)
            end
        end
    end
    if 1 <= currentDay and currentDay <= 6 then
        NetAthleticTalentData:RequestRankData(dayRankCallBack, currentDay, true)
    end

    local function totalRankCallBack(data)
        -- 已上榜
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        if data.player.rank and isEnterRank then
            if self.athleticTalent then
                self.athleticTalent.rank.text = tostring(data.player.rank)
                SetActive(self.athleticTalent.rankParent, true)
            end
        -- 未上榜
        else
            if self.athleticTalent then
                self.athleticTalent.rank.text = ""
                SetActive(self.athleticTalent.rankParent, false)
            end
        end
    end
    if currentDay >= 7 then
        NetAthleticTalentData:RequestRankData(totalRankCallBack, nil, true)
    end
end

function M:UpdateRankShow()
	local _, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Rank);
	if not active then return end
	
	if self.rankSubType ~= active.info.subtype then
		self.rankSubType = active.info.subtype;
		local bg = GetChild(self.ui.m_goActivityTabGroup4, "bg", UEUI.Image);
		local score = GetChild(self.ui.m_goActivityTabGroup4, "score", UEUI.Image);
		local title = GetChild(self.ui.m_goActivityTabGroup4, "title", UEUI.Text);
		local titleOutline = GetChild(self.ui.m_goActivityTabGroup4, "title", UEUI.Outline);
		local titleUIShadow = GetChild(self.ui.m_goActivityTabGroup4, "title", CS.Coffee.UIEffects.UIShadow);

		if self.rankSubType == ActivitySubtype.MergeRank then -- 合成竞赛
			SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_hechengjinsai.png", false);
			SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_hechengjinsai_2.png", false);
			title.color = Color.New(1, 253/255, 204/255, 1); -- fffdcc
			titleOutline.effectColor = Color.New(200/255, 24/255, 129/255, 1); -- c81881
			titleUIShadow.effectColor = Color.New(200/255, 24/255, 129/255, 1); -- c81881
		elseif self.rankSubType == ActivitySubtype.BuildRank then -- 建造竞赛
			SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_jianzaojinsai.png", false);
			SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_jianzaojinsai_2.png", false);
			title.color = Color.New(242/255, 1, 252/255, 1); -- f2fffc
			titleOutline.effectColor = Color.New(47/255, 90/255, 192/255, 1); -- 2f5ac0
			titleUIShadow.effectColor = Color.New(47/255, 90/255, 192/255, 1); -- 2f5ac0
		elseif self.rankSubType == ActivitySubtype.CollectRank then -- 采集竞赛
			SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_caijijinsai.png", false);
			SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_caijijinsai_2.png", false);
			title.color = Color.New(1, 1, 229/255, 1); -- ffffe5
			titleOutline.effectColor = Color.New(179/255, 76/255, 0, 1); -- b34c00
			titleUIShadow.effectColor = Color.New(179/255, 76/255, 0, 1); -- b34c00
		elseif self.rankSubType == ActivitySubtype.OrderRank then -- 订单排行榜
			SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_dingdanjinsai.png", false);
			SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_dingdanjinsai_2.png", false);
			title.color = Color.New(1, 254/255, 238/255, 1); -- fffeee
			titleOutline.effectColor = Color.New(0, 120/255, 71/255, 1); -- 007847
			titleUIShadow.effectColor = Color.New(0, 120/255, 71/255, 1); -- 007847
		end
		title.text = LangMgr:GetLang(active.form.title)
	end

	local actInfo = active.info
	local rank = actInfo.rank;
	if rank > 0 then
		local text = GetChild(self.ui.m_goActivityTabGroup4, "rank/text", UEUI.Text);
		text.text = rank;
	end
	local rankObj = GetChild(self.ui.m_goActivityTabGroup4, "rank");
	SetActive(rankObj, rank > 0);
	
	local numTxt = GetChild(self.ui.m_goActivityTabGroup4, "score/numTxt", UEUI.Text);
	numTxt.text = actInfo.integral or 0;
end

function M:UpdateRankRed()
	local redImg = GetChild(self.ui.m_goActivityTabGroup4, "redImg");
    SetActive(redImg, NetRankData:IsShowRedPoint(ActivityTotal.Rank) or NetUpdatePlayerData:GetPlayerRankOneRewardRed());
end

function M:UpdateTowerShow()
	local _,imgPath = TowerManager:GetTodayOpenTowerType()
	SetImageSprite(self.ui.m_imgSlgTower,imgPath)
end

---------------------- 轮播 ------------------------------------------------
function M:ActivityRankTabTimer(deltaTime)
	if self.tabDeltaTime then
		if self.tabDeltaTime < 10 then
			self.tabDeltaTime = self.tabDeltaTime + 1
		else
			self.tabDeltaTime = 0
			self.curActivityRankTabPage = self.curActivityRankTabPage + 1
            local isStartCompetition = NetOneToOneData:GetDataByKey("isStartCompetition")
            if isStartCompetition then
                self.curActivityRankTabPage = Mathf.Clamp(self.activityRankTabIndexList[ACTIVITY_RANK_TABINDEX.One_to_one] - 1, 0, self.maxActivityRankTabPage)
            end
			if self.curActivityRankTabPage > self.maxActivityRankTabPage then
				self.curActivityRankTabPage = 0
			end
			self:SwitchActivityTabPage(self.curActivityRankTabPage)
		end
	end
end

function M:InitActivityRankTab()
	self.uiDrag = self.ui.m_scrollviewActivityEnter:GetComponent(typeof(CS.UIDrag))
	self.curActivityRankTabPage = 0
	local startPosition = 0
	self:SwitchActivityTabPage(self.curActivityRankTabPage)
	self.uiDrag.m_BeginDrag = function ()
		if self.ActivityTabTween then
			self.ActivityTabTween:Kill()
		end
		startPosition = self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition
	end

	self.uiDrag.m_EndDrag = function ()
		local endPosition = self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition
		if endPosition > startPosition then
			self:MoveNextTab()
		else
			self:MovePreTab()
		end
	end
end

function M:SwitchActivityTabPage(pageIndex)
	local moveParam = pageIndex
	if self.maxActivityRankTabPage > 1 then
		moveParam = pageIndex / self.maxActivityRankTabPage--* 0.5
	end
	self.ActivityTabTween = Tween.To(function(value)
		self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition = value
		self.ui.m_scrollviewActivityEnter.velocity.x = 0
	end,self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition,moveParam,0.3)
	for i = 1, self.maxActivityRankTabPage + 1 do
		local tabNameStr = "m_imgActivityTab"..i;
		if self.ui[tabNameStr] then
			local tabImag = tabNormalImg
			local selectIndex = pageIndex + 1
			if i == selectIndex then
				tabImag = tabSelectImg
			end
			SetImageSprite(self.ui[tabNameStr],tabImag,true)
		end
	end

end

function M:MovePreTab()
	if self.maxActivityRankTabPage <= 0 then
		return
	end

	self.curActivityRankTabPage = self.curActivityRankTabPage - 1
	if self.curActivityRankTabPage < 0 then
		local step = 1/self.maxActivityRankTabPage
		self.curActivityRankTabPage = self.maxActivityRankTabPage
		self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition = (1+step)+self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition
	end

	if self.ActivityTabTween then
		self.ActivityTabTween:Kill()
	end
	self:SwitchActivityTabPage(self.curActivityRankTabPage)
end

function M:MoveNextTab()
	if self.maxActivityRankTabPage <= 0 then
		return
	end

	self.curActivityRankTabPage = self.curActivityRankTabPage + 1
	if self.curActivityRankTabPage > self.maxActivityRankTabPage then
		local step = 1/self.maxActivityRankTabPage
		self.curActivityRankTabPage = 0
		self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition = -(1+step)+(self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition)
	end

	if self.ActivityTabTween then
		self.ActivityTabTween:Kill()
	end
	self:SwitchActivityTabPage(self.curActivityRankTabPage)
end

function M:MoveTargetTab(type)
    local isStartCompetition = NetOneToOneData:GetDataByKey("isStartCompetition")
    if isStartCompetition then return end
    local index = self.activityRankTabIndexList[type] - 1
    local lastPage = self.curActivityRankTabPage
    self.curActivityRankTabPage = Mathf.Clamp(index, 0, self.maxActivityRankTabPage)
    if lastPage == self.curActivityRankTabPage then
        return
    end
    if self.ActivityTabTween then
        self.ActivityTabTween:Kill()
    end
    self:SwitchActivityTabPage(self.curActivityRankTabPage)
end

------------------------------------------------------------------------------
function M:RefreshTriggerGiftTime()
	if self.triggerGiftTime == nil then
		local gift = NetGiftBox:GetGiftLessTime()
		if nil == gift or gift.time == nil then
			SetActive(self.ui.m_goTriggerGift,false)
			return
		end
		SetActive(self.ui.m_goTriggerGift,true)
		local data = TriggerGiftConfig:GetDataByID(gift.id)
		if data.main_icon then
			SetUIImage(self.ui.m_imgTriggerGiftIcon,data.main_icon,false)
		end
		self.triggerGiftTime = gift.time or 0
	end
	local time = self.triggerGiftTime - TimeMgr:GetServerTime()
	if time > 0 then
		self.ui.m_txtTriggerGift.text = TimeMgr:CheckHMS(time)
	else
		self.ui.m_txtTriggerGift.text = ""
		self.triggerGiftTime = nil
	end
end

function M:PlayTriggerGiftFlyEff(id)
	if nil ~= id then
		local data = TriggerGiftConfig:GetDataByID(v2n(id))
		if data and data.main_icon then
			local toPos = UIMgr:GetObjectScreenPos(self.ui.m_goTriggerGift.transform)
			EffectConfig:CreateEffect(136, toPos.x, toPos.y, 0,UIMgr.layers[UILayerType.Top])
			MapController:FlyUIAnimByImg(0,0,data.main_icon, 1,toPos.x, toPos.y,nil,nil,nil,nil,FlyResourceType.FlyNone,function ()

			end)
		end
	end
end

function M:SortGoDownRootUIIndex()
	local sortList = {}
	local sortTirggerGiftList = {}

	for _, v in pairs(self.limits) do
		local config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_sorting,v.totalType)
		v.sortIndex = config and config.sort or 1
		table.insert(sortList,v)
	end
	local config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_sorting,18)
	--if config then
	--local growthfund = {}
	--growthfund.go = self.ui.m_goGrowthFund
	--growthfund.sortIndex = config.sort
	--table.insert(sortList,growthfund)
	--end
	--新成长基金
	--config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_sorting,999)
	--if config then
	--local new_growthfund = {}
	--new_growthfund.go = self.ui.m_goGrowthFundNew
	--new_growthfund.sortIndex = config.sort
	--table.insert(sortList,new_growthfund)
	--end
	--7日
	config = ConfigMgr:GetDataByID(ConfigDefine.ID.activity_sorting,998)

	if table.length(sortList) > 1 then
		table.sort(sortList,function(a,b)
			return a.sortIndex < b.sortIndex
		end)

		for _, v in ipairs(sortTirggerGiftList) do
			SetUIFirstSibling(v.go)
		end
		for _, v in ipairs(sortList) do
			SetUIFirstSibling(v.go)
		end
		for _, v in pairs(self.limitsSpeGo) do
			SetUILastSibling(v.go)
		end
	end
end
--------------------------------- 每日礼包免费礼包领取 ---------------------------------------------
function M:GiftDailyFreeTimer()
	local freeDayRestTime = NetGiftDaily:GetFreeDayRestTime()
	local nowTime = TimeMgr:GetServerTimestamp()
	if freeDayRestTime - nowTime < 0 then
		NetGiftDaily:ResetFreeDayState()
		self:CheckActCenterRed()
	end
end
------------------------------------------------------------------------------------------------

-------------------------------------------关卡系统----------------------------------------------
--初始化关卡系统
function M:InitLevelTimer()
	local level = DungeonManager:GetLevelId()
	self.MaxHangTime = DungeonManager:GetMaxHangTime()
	self.hangUpTime1,self.hangUpTime2 = DungeonManager:GetHangConfigTime()
	if level == 1 then
		self.canTimer = false
		self.levelTimeTxt.text = "00:00:00"
		SetActive(self.ui.m_goFullBoxDot,false)
		SetUIImage(self.ui.m_imgBoxIcon,self:GetBoxSprite(1),false)
	else
		self.canTimer = true
		self.levelHangTime = TimeMgr:GetServerTime() - DungeonManager:GetTimestamp()
		self.str1 = LangMgr:GetLang(70000086)
		self.levelTimeTxt.text = self.levelHangTime >= self.MaxHangTime and self.str1 or TimeMgr:ConverSecondToString(self.levelHangTime)
	end
end

--关卡系统倒计时逻辑
function M:CheckLevelTimer()
	if self.canTimer then
		self.levelHangTime = self.levelHangTime + 1
		local str
		if self.levelHangTime >= self.MaxHangTime then
			str = self.str1
			self.canTimer = false
		else
			str = TimeMgr:ConverSecondToString(self.levelHangTime)
		end
		self.levelTimeTxt.text = str
		self:CheckLevelEnterIcon(self.levelHangTime)
	end
end

---判断关卡系统入口状态贴图
---在0-1小时之间，显示空宝箱；
---在1-4小时之间，显示开启一半的宝箱
---在4-挂机时间上限，显示满载宝箱
function M:CheckLevelEnterIcon(time)
	local status = self.levelBoxStatus
	if time < self.hangUpTime1 then
		status = 1
	elseif time >= self.hangUpTime1 and time < self.hangUpTime2 then
		status = 2
	else
		status = 3
	end
	
	SetActive(self.ui.m_goFullBoxDot,DungeonManager:CheckLevelEnterRedDot())
	if status == self.levelBoxStatus then
		return
	end
	self.levelBoxStatus = status
	SetUIImage(self.ui.m_imgBoxIcon,self:GetBoxSprite(self.levelBoxStatus),false)
	--SetActive(self.ui.m_goFullBoxDot,time >= self.hangUpTime1)
end

--获取关卡入口宝箱贴图
function M:GetBoxSprite(status)
	return "Sprite/ui_mainface/mainface2_huodong_PVE_jl"..status..".png"
end

------------------------------------------------------------------------------------------------

function M:BagChange(data)
    self.ui.m_txtDiamond.text = NetUpdatePlayerData:GetResourceNumByID(ItemID.DIAMOND)
end

return M