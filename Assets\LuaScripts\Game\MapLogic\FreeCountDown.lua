local FreeCountDown = {}
local M = FreeCountDown
local prePath = "Prefab/Map/FreeCountDown.prefab"
local pre
local StateType = {
    None = 1,
    Diamond = 2,
    Free = 3,
}

function M:Create(pos, callback)
    local item = {}
    setmetatable(item,{__index = FreeCountDown})

    -- 异步加载预制体
    ResMgr:LoadAssetWithCache(prePath, AssetDefine.LoadType.Instant, function(prefab)
        if not prefab then
            Log.Error("Failed to load FreeCountDown prefab")
            if callback then
                callback(nil)
            end
            return
        end

        pre = prefab -- 缓存预制体引用
        local newGo, newTrans = CreateGOAndTrans(pre)
        item.go = newGo
        MapController:AddUIToWorld(newTrans)
        SetUIPos(newGo, pos.x, pos.y)
        item:Init()

        if callback then
            callback(item)
        end
    end)

    return item -- 注意：此时 item.go 可能还未初始化
end

--初始化
function M:Init()
    self.count = GetChild(self.go,"count",UEUI.Text)
    self.icon = GetChild(self.go,"icon",UEUI.Image)
    self.bg = GetChild(self.go,"bg")
    self.free = GetChild(self.go,"free",UEUI.Button)
    self.freeObj = self.free.gameObject
    self.adsObj = GetChild(self.go,"ads")
    self.speedObj = GetChild(self.go,"speedUp")
    self.diamond = GetChild(self.go,"Diamond",UEUI.Button)
    self.diamondObj = self.diamond.gameObject
    self.diaTxt = GetChild(self.go,"Diamond/m_txtDiam",UEUI.Text)
    self.diaImg = GetChild(self.diamondObj,"m_txtDiam/Image",UEUI.Image)
    GetChild(self.go,"free/m_txtFree",UEUI.Text).text = LangMgr:GetLang(66)

    self.isClose = false
    self.state = StateType.None
    self.redTime = false
    self.showState = 0
    AddUIComponentEventCallback(self.free,UEUI.Button,function()
        if not self.redTime then
            self:ClickFree()
        end
    end)
    AddUIComponentEventCallback(self.diamond,UEUI.Button,function()
        self:ClickDimaond()
    end)

    EventMgr:Add(EventID.TOUCH_INTERRUPT,self.CloseDiamond,self)
end

function M:SetItem(i,callback,btnState,typeUse,id,newId)
    self.workerId = i
    self.call = callback
    self.btnState = btnState
    if typeUse == 2 then
        self.notifyType = NotifyDefine.CutTreeSpeedUp
        self.consumeType = 13
    elseif typeUse == 14 or typeUse == 13 or typeUse == 36 or typeUse == 54 then
        self.notifyType = NotifyDefine.BuildSpeedUp
        self.consumeType = 8
    elseif typeUse == 15 then
        self.consumeType = 14
        self.notifyType = NotifyDefine.BigTreeSpeedUp
    elseif typeUse == 7 then
        self.consumeType = 24
        self.notifyType = NotifyDefine.FieldSpeedUp
    end
    self.itemId = id
    self.newID = newId
end

function M:SetOtherImg(img)
    if self.icon then
        SetImageSprite(self.icon,img,false)
    end
end

function M:Hide()
    if self.go then
        SetActive(self.go,false)
    end
end

function M:Show(x,y)
    if not self.go or self.workerId == nil  then return end
    SetActive(self.go,true)
    if x and y then
        SetUIPos(self.go,x,y)
    end
end

--点击采集事件
function M:ClickBG()
    local time = WorkerController:GetWorkJobTime(self.workerId);
    if time > FREE_TIME then
        if self.showState == 1 and time > GlobalConfig.TIME_SHOW_AD then
            UIMgr:Show(UIDefine.UI_CompleteWithAds,self.workerId,self.btnState,self.notifyType,self.itemId,self.consumeType,self.newID)
        else
            UIMgr:Show(UIDefine.UI_Complete,self.workerId,self.btnState,self.notifyType,self.itemId,self.consumeType,self.newID)
        end
    else
        self:ClickFree()
    end
end

function M:Timer(isInit)
    if self.workerId then
        local time = WorkerController:GetWorkJobTime(self.workerId)
        if time > 0 then
            -- 检查UI组件是否已初始化
            if self.count then
                self.count.text = TimeMgr:GetHMS(math.floor(time))
            end
            if self.state ~= StateType.Free and time <= FREE_TIME and self.freeObj and self.freeObj.activeSelf == false then
                self:CloseDiamond()
                self.state = StateType.Free
                SetActive(self.freeObj,true)
            end
            if self.state == StateType.Diamond then
                local isCoin,cost = GetSpeedUpDiamond(time)
                self.diaTxt.text = cost
                if self.isCoin ~= isCoin then
                    self.isCoin = isCoin
                    if isCoin then
                        SetImageSprite(self.diaImg,ItemConfig:GetIcon(ItemID.COIN),false)
                    else
                        SetImageSprite(self.diaImg,ItemConfig:GetIcon(ItemID.DIAMOND),false)
                    end
                end
            end
            local isCanShow = ADMovieModule:StateCache(ADMovieModule.EM_AD_ID.CD_REDUCE_0)
            --判断广告是否显示
            if self.showState == 1 then
                if not isCanShow or time < GlobalConfig.TIME_SHOW_AD then
                    self.showState = 0
                    SetActive(self.adsObj,false)
                end
            else
                if isCanShow and time > GlobalConfig.TIME_SHOW_AD then
                    self.showState = 1
                    SetActive(self.adsObj,true)
                end
            end
            local isSpeedShow = self.state ~= StateType.Free and NetUpdatePlayerData.playerInfo.curMap == MAP_ID_MAIN and LimitActivityController:GetActiveIsOpen(ActivityTotal.GoldBuff,ActivitySubtype.GoldTime)
            if self.showState ~= 1 then
                if self.showState == 2 and not isSpeedShow then
                    SetActive(self.speedObj,false)
                    self.showState = 0
                elseif self.showState == 0 and isSpeedShow then
                    SetActive(self.speedObj,true)
                    self.showState = 2
                end
            end
        elseif time <= 0 and not isInit then
            self:ClickFree()
        end
    end
end

function M:AddDiamondSpeedUp()
   if self.state ~= StateType.None then return end
    self.state = StateType.Diamond
    if self.diamondObj then
        SetActive(self.diamondObj,true)
    end
    self:Timer()
end

function M:CloseDiamond(type)
    if type and type ~= 1 and type ~= 0 then return end
    if self.state ~= StateType.Diamond then return end
    self.state = StateType.None
    if self.diamondObj then
        SetActive(self.diamondObj,false)
    end
end

function M:ClickDimaond()
    local time = WorkerController:GetWorkJobTime(self.workerId)
    local isCoin,diamond = GetSpeedUpDiamond(time)
    if NetUpdatePlayerData:ConsumeResource(isCoin,diamond,self.consumeType,self.itemId,1,nil,"CutDown") then
        self:ClickFree(true)
    end
end

function M:SetTimeShow(time)
	if IsNil(self.count) or self.workerId == nil then return end
	self.count.text = TimeMgr:GetHMS(math.floor(time+0.5))
end

function M:SetTime(time)
	if IsNil(self.count) then return end
	self.count.text = TimeMgr:GetHMS(math.floor(time+0.5))
end

function M:SetRedTime(isRed)
    if self.count == nil or self.workerId == nil then return end
    self.redTime = isRed
end

--点击免费按钮事件
function M:ClickFree(isPay)
    if self.workerId == nil then
        Log.Error("workerId is nil")
    end
    WorkerController:SetWorkOver(self.workerId)
    if self.call then
        self.call()
    end
    if self.notifyType then
        if isPay then
            NetNotification:NotifyNormal(self.notifyType,1)

        else
            NetNotification:NotifyNormal(self.notifyType,2)
        end
    end
    self.redTime = false
    self:Destroy()
end

function M:Destroy()
    EventMgr:Remove(EventID.TOUCH_INTERRUPT,self.CloseDiamond,self)
    self.workerId = nil
    if self.go then
        UEGO.Destroy(self.go)
    end
end


return M