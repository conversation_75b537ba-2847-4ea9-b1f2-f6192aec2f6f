local GoLimit26 = {}
local M = GoLimit26
local prePath = "Assets/ResPackage/Prefab/UI/GoLimit26.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"doLimit/bg/icon",UEUI.Image)
    self.time = GetChild(self.go,"doLimit/bg/CountDown/countTxt",UEUI.Text)
    self.red = GetChild(self.go,"doLimit/bg/redPoint")
    self.text_progress = GetChild(self.go,"doLimit/bg/Limit/text_progress",UEUI.Text)
	self.img = GetChild(self.go,"doLimit/bg/Limit/img",UEUI.Image)
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
		if not NetLuckOreData.data.isFirstOpenTreasure then
			NetPushViewData:PushView(PushDefine.UI_TreasureHunt_LuckyMine,true)
		else
			UI_SHOW(UIDefine.UI_TreasureHunt_LuckyMine)
		end
    end)
    self.slider = GetChild(self.go,"doLimit/bg/Limit/Slider",UEUI.Slider)
	if NetLuckOreData:HasCanReward() or not NetLuckOreData.data.isFirstOpenTreasure then
		self:SetRedShow(true)
	end
    self:ChangState()
    EventMgr:Add(EventID.CHANGE_ACTIVE_STATE, self.ChangState, self)
end
--init
function M:SetItem(param)
    self.id = param.id
    local activeInfo = LimitActivityController:GetActiveMessage(self.id)
	self.activeInfo = activeInfo
    self.active = activeInfo.form.activeMess
	self.totalType = param.totalType
    SetImageSprite(self.icon,self.active.icon,false)

	LuckOreManager:InitActive(activeInfo.info.activeId)
end

function M:ChangState(id)
    self:ChangeValue()
end

function M:ChangeValue()
    local config = LuckOreManager:GetPassPortConfig()
    local currentLevel = NetLuckOreData:GetTreasureLevel()
    local needExp = LuckOreManager:GetNeedScoreByLevel(config, currentLevel)
    local score = NetLuckOreData:GetTreasureScore()
    self.slider.value = score / needExp
    self.text_progress.text = score .. "/" .. needExp
end
---tick
function M:ChangeItem()
	if not self.activeInfo then
		return
	end
	local time = self.activeInfo:GetRemainingTime()
	if time > 0 then
		self.time.text = TimeMgr:CheckHMSNotEmpty(time)
	else
		self.time.text = LangMgr:GetLang(7077)
	end
end
function M:SetRedShow(isShow)
	self.red:SetActive(isShow)
end
function M:Close()
    EventMgr:Remove(EventID.CHANGE_ACTIVE_STATE, self.ChangState, self)
    UEGO.Destroy(self.go)
end

return M