local UI_tgCommon = Class(BaseView)

local tgShowRewardItemPath = "TinyGame/Empty/Prefab/UI/tgShowRewardItem.prefab"
local tgShowRewardItemPref = nil
local list_ShowItem = {}
local i_ShowItemIndex = 1

local list_Tween = {}

local is_CanSuceessClose = false

function UI_tgCommon:OnInit()
	
end

function UI_tgCommon:OnCreate(param)
	self.isCloseUI = false
	
	--local tgListCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.tinygame_list, TinyGameMgr:GetTgStageID())
	--self.ui.m_txtTopTips.text = LangMgr:GetLang(tgListCfg.tips)
	local str = "tinygame_block_".. TinyGameMgr:GetTinyGameID()
	local tgDetailInfo = ConfigMgr:GetDataByID(ConfigDefine.ID[str], TinyGameMgr:GetTgStageID()) 
	self.ui.m_txtTopTips.text = LangMgr:GetLang(tgDetailInfo.tips)
	
	SetActive(self.ui.m_goFailUI, false)
	SetActive(self.ui.m_goSuccessUI, false)
	
	
	local tgInfo = ConfigMgr:GetDataByID(ConfigDefine.ID.tinygame, TinyGameMgr:GetTinyGameID())
	if IsNilOrEmpty(tgInfo.game_tips_code)  then
		SetActive(self.ui.m_goGameTips, false)
	else
		SetActive(self.ui.m_goGameTips, true)
		--self.ui.m_txtGameTips.text = TinyGameMgr:tg_GetLang(TinyGameMgr:GetTinyGameID(), tgInfo.game_tips_code)

		local strContent = TinyGameMgr:tg_GetLang(TinyGameMgr:GetTinyGameID(), tgInfo.game_tips_code)
		local strTemp = string.gsub(strContent,"\\n","\n")
		self.ui.m_txtGameTips.text = strTemp
	end
	
	--是否需要第一下点击的提示
	if tgInfo.first_click_tips == 1 then
		self.ui.m_txtFirstClickTips_1.text = TinyGameMgr:tg_GetLang(TinyGameMgr:GetTinyGameID(), tgInfo.first_click_tips_code_1)
		
		TinyGameMgr:tg_SetFirstClickTips(true)
		SetActive(self.ui.m_btnFirstClickTips.gameObject, true)
	end
	
	local isHideResetBtn = false
	if tgInfo.is_hide_tg_common_reset then
		isHideResetBtn = true
	end

	
	if not tgInfo.ui_btn_wait_time then
		SetActive(self.ui.m_btnReset.gameObject, not isHideResetBtn)
		SetActive(self.ui.m_btnBack.gameObject, TinyGameMgr:IsShowBackBtn())
	else
		local timeShowWait = v2n(tgInfo.ui_btn_wait_time)

		SetActive(self.ui.m_btnReset.gameObject, false)
		SetActive(self.ui.m_btnBack.gameObject, false)
		local TempTween = AddDOTweenNumberDelay(0, 1, timeShowWait or 0, 0,
			function()
			end,
			function()
				SetActive(self.ui.m_btnReset.gameObject, not isHideResetBtn)
				SetActive(self.ui.m_btnBack.gameObject, TinyGameMgr:IsShowBackBtn())
			end)
		table.insert(list_Tween, TempTween)
	end
	
	local isHideTopsTips = false
	if tgInfo.is_hide_top_tips and tgInfo.is_hide_top_tips == 1 then
		isHideTopsTips = true
	end
	SetActive(self.ui.m_goTopTips, not isHideTopsTips)

	--是否需要显示关卡数
	if tgInfo.go_level_text then
		local goLv = GetChild(self.ui.m_goLevel, tgInfo.go_level_text)
		if goLv then
			local txtLv = GetChild(goLv, "txtLv", UEUI.Text)
			txtLv.text = v2s(TinyGameMgr:GetStageID())
			
			SetActive(goLv, true)
		else
			Log.Error(tgInfo.go_level_text .. "Level text not exist  ")
		end
	end

	
	local txtFail = GetChild(self.ui.m_goFailUI,"Image/Text", UEUI.Text)
	local txtFailConfirm = GetChild(self.ui.m_goFailUI,"btn_Fail/Text", UEUI.Text)
	local btn_Fail = GetChild(self.ui.m_goFailUI,"btn_Fail", UEUI.Button)
	local txtSuccess = GetChild(self.ui.m_goSuccessUI,"imgTitle/Text", UEUI.Text)
	local txtSuccessConfirm = GetChild(self.ui.m_goSuccessUI,"btn_Success/Text", UEUI.Text)
	local btn_Skip = GetChild(self.ui.m_goFailUI,"btn_Skip", UEUI.Button)
	
	txtSuccess.text = TinyGameMgr:tg_GetLang(10001, 3)
	txtFail.text = TinyGameMgr:tg_GetLang(10001, 4)
	
	txtFailConfirm.text = TinyGameMgr:tg_GetLang(4, 3002)
	--txtSuccessConfirm.text = TinyGameMgr:tg_GetLang(4, 3002)
	txtSuccessConfirm.text = LangMgr:GetLang(32)
	
	local iPassStage = TinyGameMgr:GetPassStageID()
	local iFailSkipStage = v2n(ConfigMgr:GetDataByID(ConfigDefine.ID.global_setting, 10302).value)
	if iFailSkipStage >= iPassStage then
		SetActive(btn_Fail, false)
		SetLocalPositionTrans(btn_Skip.transform, 0, -374, 0)
	end
	
	self:ShowItemInit()
end


function UI_tgCommon:OnRefresh(param, param2, param3, param4, param5, param6)
	if param == 1 then
		--挑战成功
		--SetActive(self.ui.m_goSuccessUI, true)
		
		local TempTween = AddDOTweenNumberDelay(0, 1, param2 or 0, 0,
			function()
			end,
			function()
				self:OnSuccess()
			end)
		table.insert(list_Tween, TempTween)
		
	elseif param == 2 then
		--挑战失败
		--SetActive(self.ui.m_goFailUI, true)
		--self:onCommonFuncSkip()
		
		local TempTween = AddDOTweenNumberDelay(0, 1, param2 or 0, 0,
			function()
			end,
			function()
				--SetActive(self.ui.m_goFailUI, true)
				--self:onCommonFuncSkip()
				
				self:OnFail()
			end)
		table.insert(list_Tween, TempTween)
		
	elseif param == 3 then
		--隐藏点击提示
		SetActive(self.ui.m_btnFirstClickTips.gameObject, false)
	elseif param == 4 then
		--皮绳、解环 显示奖励
		--单个奖励缩放出现
		local vtPos = Vector3.New(param3, param4, param5)
		local _, cur_pos = UE.RectTransformUtility.ScreenPointToLocalPointInRectangle(UIMgr:GetCanvasRectTrans(), vtPos, UIMgr:GetCamera())
		self:ShowItem(param2, cur_pos.x, cur_pos.y, 0)
	elseif param == 5 then
		--建桥 显示奖励
		--结算界面，弹出拖尾特效，显示配表里的奖励
		local iStageID = TinyGameMgr:GetStageID()
		local iPassStageID =TinyGameMgr:GetPassStageID()
		
		if iPassStageID >= iStageID and TinyGameMgr:GetAssignEnterWay() == 0 then
			return
		end
		
		local vtPos = Vector3.New(param3, param4, param5)
		local _, cur_pos = UE.RectTransformUtility.ScreenPointToLocalPointInRectangle(UIMgr:GetCanvasRectTrans(), vtPos, UIMgr:GetCamera())
		self:ShowAllItemWithTrailEff(cur_pos.x, cur_pos.y, 0, 0, 0)
	elseif param == 6 then
		--结算界面 显示奖励（依据传入参数）
		self:ShowItem_static(param2, param3, param4, param5, param6)
	elseif param == 7 then
		--结算界面 显示奖励（依据配表里的奖励）
		
		local iStageID = TinyGameMgr:GetStageID()
		local iPassStageID =TinyGameMgr:GetPassStageID()
		
		if iPassStageID >= iStageID and TinyGameMgr:GetAssignEnterWay() == 0 then
			return
		end
		
		local cfgStage = ConfigMgr:GetDataByID(ConfigDefine.ID.tinygame_list, TinyGameMgr:GetStageID())
		local tbRewardShow = Split2(cfgStage.block_reward_show,";","|")
		local itemCount = GetTableLength(tbRewardShow)
		local itemPosX = 0
		for i = 1, itemCount do
			if itemCount == 1 then
				itemPosX = 0
			elseif itemCount%2 == 0 then
				itemPosX = (0-180)+((i-1)*360)
			else
				itemPosX = (0-360)+((i-1)*360)
			end

			--UI_UPDATE(TinyGameUIDefine.UI_tgCommon, 6, tbRewardShow[i].id, tbRewardShow[i].count, itemPosX, -250, 0)
			self:ShowItem_static(tbRewardShow[i].id, tbRewardShow[i].count, itemPosX, -250, 0)
		end
		
		
	end
end

function UI_tgCommon:onDestroy()
	for i = 1, 3 do
		if list_ShowItem[i].go ~= nil then
			UEGO.Destroy(list_ShowItem[i].go)
			list_ShowItem[i].go = nil
		end
		
		if list_ShowItem[i].Eff_tGo ~= nil then
			UEGO.Destroy(list_ShowItem[i].Eff_tGo)
		end
		list_ShowItem[i].Eff_tGo = nil
		
		DOKill(list_ShowItem[i].imgItem.transform)
		
		if list_ShowItem[i].tween then
			list_ShowItem[i].tween:Kill()
			list_ShowItem[i].tween = nil
		end

		if list_ShowItem[i].effDelayTween then
			list_ShowItem[i].effDelayTween:Kill()
		end
		list_ShowItem[i].effDelayTween = nil
	end
	
	if list_Tween then
		for k, v in pairs(list_Tween) do
			if v then
				v:Kill()
			end
			v = nil
		end
	end
	list_Tween = {}
end

function UI_tgCommon:onUIEventClick(go,param)
    local name = go.name

	if name == "m_btnBack" or name == "btn_Fail" then
		self:onCommonFuncClose()
		
	elseif name == "m_btnReset" then
		self:onCommonFuncReset()
		
	elseif name == "btn_Success" and is_CanSuceessClose == true then
		TinyGameMgr:SetStageCanReward(TinyGameMgr:GetStageID())
		TinyGameMgr:CheckTinyGameBoxMode()
		EventMgr:Dispatch(EventID.TINYGAME_COMPLETE)
 		self:onCommonFuncClose()
		
	elseif name == "btn_Skip" then
		TinyGameMgr:SetStageCanReward(TinyGameMgr:GetStageID(), true)
		TinyGameMgr:CheckTinyGameBoxMode()
		EventMgr:Dispatch(EventID.TINYGAME_COMPLETE)
		self:onCommonFuncClose()
		
	end

end

function UI_tgCommon:onCommonFuncSkip()
	SetActive(self.ui.m_goBanSkip, false)
	self.ui.m_txtSkip.text = LangMgr:GetLangFormat(8038)
end

function UI_tgCommon:onCommonFuncClose()
	if self.isCloseUI == true then
		return
	end
	
	local iTgWay = TinyGameMgr:GetAssignEnterWay()
	if iTgWay and iTgWay > 0 then
		TinyGameMgr:ResetAssignEnterWay()
	end
	
	self.isCloseUI = true
	t_MiniGameCtrl:Close()
	--通用功能界面
	UI_CLOSE(TinyGameUIDefine.UI_tgCommon)
	
end

function UI_tgCommon:onCommonFuncReset()
	TinyGameMgr:RetryThinkingTrack()
	t_MiniGameCtrl:Reset()
	
end

function UI_tgCommon:ShowItemInit()
	ResMgr:LoadAssetAsync(
		tgShowRewardItemPath,
		AssetDefine.LoadType.Instant,
		function(prefab)
			tgShowRewardItemPref = prefab
			list_ShowItem = {}
			for i = 1, 3 do
				local go = CreateGameObjectWithParent(tgShowRewardItemPref, self.ui.m_goCommon)
				local imgItem = GetChild(go, "imgItem", UEUI.Image)
				local txtGet = GetChild(go, "txtGet", UEUI.Text)
				local txtCount = GetChild(go, "txtCount", UEUI.Text)
				local goBg = GetChild(go, "imgBg")
				local goBg_static = GetChild(go, "imgBg_static")
				
				SetActive(go, false)
				
				list_ShowItem[i] = {}
				list_ShowItem[i].go = go
				list_ShowItem[i].imgItem = imgItem
				list_ShowItem[i].goBg = goBg
				list_ShowItem[i].goBg_static = goBg_static
				list_ShowItem[i].txtGet = txtGet
				list_ShowItem[i].txtCount = txtCount
				txtGet.text = LangMgr:GetLang(210462)
				
				if list_ShowItem[i].tween then
					list_ShowItem[i].tween:Kill()
				end
				list_ShowItem[i].tween = nil
				
				
				if list_ShowItem[i].Eff_tGo ~= nil then
					UEGO.Destroy(list_ShowItem[i].Eff_tGo)
				end
				list_ShowItem[i].Eff_tGo = nil
				
				if list_ShowItem[i].effDelayTween then
					list_ShowItem[i].effDelayTween:Kill()
				end
				list_ShowItem[i].effDelayTween = nil
				
			end
		end)
end

function UI_tgCommon:ResetShowItem(index)
	list_ShowItem[index].imgItem.transform.localScale = Vector3.zero
	SetActive(list_ShowItem[index].go, true)
end

function UI_tgCommon:ScaleAnim(trans, index, isEndDisappear, fDelay)
	fDelay = fDelay or 0
	DOScale(trans, 1.5, 0.3,
		function ()
			DOScale(trans, 1, 0.2,function ()
					
					if isEndDisappear then
						list_ShowItem[index].tween = AddDOTweenNumberDelay(0,1,0.5,0,
							function ()
								end,
							function ()
								if trans.parent then
									SetActive(trans.parent, false)
								end
						end)
					end

					end,
				Ease.InCubic)
		end,
		Ease.OutCubic, fDelay)
end

---缩放出现单个奖励
function UI_tgCommon:ShowItem(itemID, x, y, z)
	self:ResetShowItem(i_ShowItemIndex)
	
	local cfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
	SetUIImage(list_ShowItem[i_ShowItemIndex].imgItem, cfg["icon_b"], false)

	--Log.Info("*** ShowItem()  x=" .. x .. "  y=" .. y .. "  z=" .. z)
	local RectTransform = GetComponent(list_ShowItem[i_ShowItemIndex].go,UE.RectTransform)
	RectTransform.anchoredPosition = Vector3.New(x, y, z) 

	self:ScaleAnim(list_ShowItem[i_ShowItemIndex].imgItem.transform, i_ShowItemIndex, true)

	
	i_ShowItemIndex = (i_ShowItemIndex%3) + 1
end

---结束界面显示奖励
function UI_tgCommon:ShowItem_static(itemID, itemCount, x, y, z)
	SetActive(list_ShowItem[i_ShowItemIndex].goBg, false)
	SetActive(list_ShowItem[i_ShowItemIndex].txtGet, false)
	SetActive(list_ShowItem[i_ShowItemIndex].goBg_static, true)
	SetActive(list_ShowItem[i_ShowItemIndex].txtCount, true)
	
	local cfgItem = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
	SetUIImage(list_ShowItem[i_ShowItemIndex].imgItem, cfgItem["icon_b"], false)
	list_ShowItem[i_ShowItemIndex].imgItem.transform.localScale = Vector3(0.6, 0.6, 0.6)
	
	list_ShowItem[i_ShowItemIndex].txtCount.text = tostring(itemCount)
	SetLocalPositionTrans(list_ShowItem[i_ShowItemIndex].go.transform, x, y, z)
	list_ShowItem[i_ShowItemIndex].go.transform.localScale = Vector3.zero
	SetActive(list_ShowItem[i_ShowItemIndex].go, true)
	
	self:ScaleAnim(list_ShowItem[i_ShowItemIndex].go.transform, i_ShowItemIndex, false, 0.2)

	i_ShowItemIndex = (i_ShowItemIndex%3) + 1
end

---拖尾效果出现的奖励
function UI_tgCommon:ShowAllItemWithTrailEff(x, y, z, target_x, target_y)
	
	local function Show(iIndex, itemID, x, y, z, target_x, target_y)
		list_ShowItem[iIndex].imgItem.transform.localScale = Vector3.zero
		SetActive(list_ShowItem[iIndex].imgItem, false)
		SetActive(list_ShowItem[iIndex].goBg, false)
		SetActive(list_ShowItem[iIndex].txtGet, false)
		
		SetActive(list_ShowItem[iIndex].go, true)

		local cfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
		SetUIImage(list_ShowItem[iIndex].imgItem, cfg["icon_b"], false)

		local RectTransform = GetComponent(list_ShowItem[iIndex].go,UE.RectTransform)
		RectTransform.anchoredPosition = Vector3.New(x, y, z)
		
		DOLocalMoveY(list_ShowItem[iIndex].go.transform,
			340, --第一个目标点Y
			0.4,
			function ()

				DOLocalMoveY(list_ShowItem[iIndex].go.transform,
					target_y,
					0.5,
					function ()
					end,Ease.InCubic)

			end,Ease.OutCubic)
		
		DOLocalMoveX(list_ShowItem[iIndex].go.transform,
			target_x,
			1,
			function ()
				self:ScaleAnim(list_ShowItem[iIndex].imgItem.transform, iIndex)
				SetActive(list_ShowItem[iIndex].imgItem, true)
				SetActive(list_ShowItem[iIndex].goBg, true)
			end,
			Ease.Linear)
		
	end
	
	
	local iStageID = TinyGameMgr:GetStageID()
	local cfgStage = ConfigMgr:GetDataByID(ConfigDefine.ID.tinygame_list, iStageID)
	local item = SplitStringToNum(cfgStage.block_reward_show_1,"|")
	local itemCount = GetTableLength(item)
	local itemPosX = 0
	
	for i = 1, itemCount do
		if list_ShowItem[i].Eff_tGo == nil then
			local function GetFunc(data, tGo, go)
				GameUtil.SetLayer(tGo, 5, 1)
				list_ShowItem[i].Eff_tGo = tGo
				--list_ShowItem[i].Eff_tGo.transform

				--Show(i, item[i], x, y, z, itemPosX, target_y)
				list_ShowItem[i].effDelayTween = AddDOTweenNumberDelay(0, 1, (i-1)* 0.2, 0,
					function ()
					end,
					function ()
						
						if itemCount == 1 then
							itemPosX = target_x
						elseif itemCount%2 == 0 then
							itemPosX = (target_x-180)+((i-1)*360)
						else
							itemPosX = (target_x-360)+((i-1)*360)
						end
						
						Show(i, item[i], x, y, z, itemPosX, target_y)
					end)
			end

			EffectConfig:CreateEffect(142, 0, 0, 0, list_ShowItem[i].go.transform, GetFunc)
		else
			--Show(i, item[i], x, y, z, itemPosX, target_y)
			list_ShowItem[i].effDelayTween = AddDOTweenNumberDelay(0, 1, (i-1)* 0.2, 0,
				function ()
				end,
				function ()
					if itemCount%2 == 0 then
						itemPosX = (target_x-180)+((i-1)*360)
					else
						itemPosX = (target_x-360)+((i-1)*360)
					end
					
					Show(i, item[i], x, y, z, itemPosX, target_y)
				end)
			
		end
	end
	
end

function UI_tgCommon:OnSuccess()
	SetActive(self.ui.m_goSuccessUI, true)

	local goSuccess = GetChild(self.ui.m_goSuccessUI,"imgTitle")
	goSuccess.transform.localScale = Vector3.zero
	is_CanSuceessClose = false
	
	
	local TempTween = AddDOTweenNumberDelay(0, 1, 0.7, 0,
		function (value)
			goSuccess.transform.localScale = Vector3.New(value,value,value)
		end
		)
	table.insert(list_Tween, TempTween)
	
	local TempTween2 = AddDOTweenNumberDelay(0, 1, 1.2, 0,
		function (value)
		end,
		function()
			is_CanSuceessClose = true
		end)
	table.insert(list_Tween, TempTween2)
end

function UI_tgCommon:OnFail()
	local goFail = GetChild(self.ui.m_goFailUI, "Image")
	local btn_Fail = GetChild(self.ui.m_goFailUI,"btn_Fail", UEUI.Button)
	local btn_Skip = GetChild(self.ui.m_goFailUI,"btn_Skip", UEUI.Button)
	goFail.transform.localScale = Vector3.zero
	btn_Fail.gameObject.transform.localScale = Vector3.zero
	btn_Skip.gameObject.transform.localScale = Vector3.zero
	
	local TempTween = AddDOTweenNumberDelay(0, 1, 0.5, 0,
		function (value)
			goFail.transform.localScale = Vector3.New(value,value,value)
		end
	)
	table.insert(list_Tween, TempTween)
	
	
	local TempTween2 = AddDOTweenNumberDelay(0, 1, 0.5, 0.3,
		function (value)
			btn_Fail.gameObject.transform.localScale = Vector3.New(value,value,value)
			btn_Skip.gameObject.transform.localScale = Vector3.New(value,value,value)
		end
	)
	table.insert(list_Tween, TempTween2)
	
	SetActive(self.ui.m_goFailUI, true)
	self:onCommonFuncSkip()
end


return UI_tgCommon