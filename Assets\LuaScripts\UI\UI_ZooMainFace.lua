local UI_ZooMainFace = Class(BaseView)
local M = UI_ZooMainFace
local GoActivity = require("UI.GoActivity")
local GoLimit = require("UI.GoLimit")
local GoLimit21 = require("UI.GoLimit21")
local GoLimit25 = require("UI.GoLimit25")
local GoLimit28 = require("UI.GoLimit28")
local tabLoopTimes = 0
local tabNormalImg = "Sprite/ui_mainface/mainface2_weixuanzhong.png"
local tabSelectImg = "Sprite/ui_mainface/mainface2_xuanzhong.png"

local cpCustomHead = require "UI.CustomHead"

-- 刷新类型
local RefreshType = {
    ResourcePosWorker = 46,  -- 工人资源位置
    RefreshAthleticTalent = 47,  -- 刷新竞技达人
    RefreshRank = 48,  -- 刷新排行榜
    SwitchTab = 49,    -- 切换活动竞赛页签
}

-- 右下角的折叠工具栏
local FolderItemType = {
	Shovel = 1,    -- 铲子
	AiWorker = 2,  -- 智能工人
	Magnet = 3,    -- 磁铁
	Lottery = 4,   -- slg抽奖
	SLGBag = 5,    -- slg背包
}

local ActivityRankCenterType = {
    AthleticTalent = 2,  -- 竞技达人
}
--
--function M:GetPathName()
--    local is_show = DailyTargetManager:IsOpenDailyTargetSwitch()
--    local name = "UI_ZooMainFace"
--    if is_show then
--        name = "UI_ZooMainFace_2"
--    end
--
--    return name
--end

function M:OnInit()
    DungeonManager:OnRequestDungeonLoad()
    PurchaseManager:Initialize()
end

function M:OnCreate(param)
    self.limits = {}
    self.limitsGo = {}
    LimitActivityController:SetActivePos(self.limitsGo)
    self.rotateTrans = self.ui.m_goRotate.transform
    self.setAni = GetChild(self.uiGameObject, "LeftTop/Setlists", UE.Animation)
    self.aAni = GetComponent(self.uiGameObject, UE.Animation)
    self.pushAni = GetChild(self.uiGameObject, "RightBottom/under/m_goLibrary/Image", UE.Animation)
    self.goActivityRect = self.ui.m_goActivity:GetComponent(TP(UE.RectTransform))
    self.goFirstPackRect = self.ui.m_goFirstPack:GetComponent(TP(UE.RectTransform))

    self.gifts = {}
    self.changeBeginState = nil
    self.lastTween = nil
    self.tempShow = {}

    -- 竞技达人
    self.athleticTalent = {}
    self.isRefreshAthleticTalentRank = false
    local posScreen = UIMgr:GetUIPosByWorld(self.ui.m_scrollviewActivityEnter.transform.position)
    LimitActivityController:SetAthleticTalentPos(posScreen)

    -- 活动竞赛倒计时文本
    self.oneToOneTimeBg = GetChild(self.ui.m_goActivityTabGroup1, "time", UEUI.Image);
    self.oneToOneTimeTxt = GetChild(self.ui.m_goActivityTabGroup1, "time/timeTxt", UEUI.Text);
    self.athleticTalentTimeTxt = GetChild(self.ui.m_goActivityTabGroup2, "time/timeTxt", UEUI.Text);
    self.bowlingTimeTxt = GetChild(self.ui.m_goActivityTabGroup3, "time/timeTxt", UEUI.Text);
    self.rankTimeTxt = GetChild(self.ui.m_goActivityTabGroup4, "time/timeTxt", UEUI.Text);
    self.levelTimeTxt = GetChild(self.ui.m_goActivityTabGroup6, "time/timeTxt", UEUI.Text);
    self.towerTimeBg = GetChild(self.ui.m_goActivityTabGroup7, "time", UEUI.Image);
    self.towerTimeTxt = GetChild(self.ui.m_goActivityTabGroup7, "time/timeTxt", UEUI.Text);
	self.jjcTimeTxt = GetChild(self.ui.m_goActivityTabGroup9, "time/timeTxt", UEUI.Text);
    self.worldBossTimeTxt = GetChild(self.ui.m_goActivityTabGroup10, "timeBg/timeTxt", UEUI.Text);
	self.topFightTimeTxt = GetChild(self.ui.m_goActivityTabGroup11, "timeBg/timeTxt", UEUI.Text);
	if Game.Channel_IOS_Notch then
		self:IosInit()
	end
    
	self.m_CustomHead = cpCustomHead.new()
	self.m_CustomHead:CreateHead(self.ui.m_imghead.transform)
	self.m_CustomHead:SetClickCall(
		function()
			if UIMgr:GetUIOpen(UIDefine.UI_MapDressPreview) then
				return
			end
			UI_SHOW(UIDefine.UI_Setting)
		end)
	
    -- 初始化右下角的折叠工具栏
    self.rightBottomArowIsPut = NetGlobalData:GetDataByKey("rightBottomArowIsPut")
    self.rightBottomList = {}
    --折叠节点数
    self.rightBottomCount = 5
    local rbTrans = GetChild(self.ui.m_goRightBottomList,"Layout")
    self.rbLayoutTrans = rbTrans.transform
    for i = 1, 5 do
        local child = GetChildTrans(rbTrans, "sortGo" .. i)
        self.rightBottomList[i] = { go = child, isOpen = false }
    end
    self.rbBgRect = GetComponent(self.ui.m_goRightBottomList,UE.RectTransform)
    self.rbBgImg = GetComponent(self.ui.m_goRightBottomList,UEUI.Image)

    --关卡系统
    self:InitLevelTimer()
    
	-- 主界面比拼活动集合入口轮播
    self.activityRankTabList = {}
    self.activityRankTabIndexList = {}
    self.curActivityRankTabPage = 0
    self.maxActivityRankTabPage = 0
	self.tabDeltaTime = 0
    self.rankSubType = nil;
    -- 主界面礼包集合倒计时
    self.triggerGiftTime = nil
    self:SetIsUpdateTick(true)
    self:SetLevel()
    self:SetZooLevel()
    self:ChangeResource()
    self:InitResourcePos()
	MarketModule:refreshTime()
    --初始化触发礼包列表
    NetGiftBox:PushOpenBag()
    --待推送礼包推送
    NetGiftBox:PushAllWaitGift()
    EventMgr:Add(EventID.CHANGE_RESOURCE, self.ChangeResource, self)
    EventMgr:Add(EventID.DRAG_BEAST_BEGIN, self.ChangeBegin, self)
    EventMgr:Add(EventID.DRAG_BEAST_END, self.ChangeEnd, self)
    EventMgr:Add(EventID.DRAG_BEAST_CANCEL, self.DragEnd, self)
    EventMgr:Add(EventID.ZOO_LVUP, self.SetZooLevel, self)
    EventMgr:Add(EventID.GUIDE_INIT, self.GuidInit, self)
	EventMgr:Add(EventID.ADD_ZOO_FOOD, self.AddZooFood, self)
	EventMgr:Add(EventID.ANIMAL_EAT_EVENT, self.FlushZooFood, self)
	EventMgr:Add(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
	EventMgr:Add(EventID.UPDATE_HERO_TOTAL_FIGHT, self.ChangeFight, self)
    EventMgr:Add(EventID.UPDATE_HERO_INFO, self.OnHeroChange, self)
    EventMgr:Add(EventID.DUNGEON_UPDATE, self.OnDungeonUpdate, self)
    EventMgr:Add(EventID.BAG_CHANGE, self.BagChange, self)
	EventMgr:Add(EventID.ROLE_GIFT_CHANGE, self.RoleGiftChange, self)
	EventMgr:Add(EventID.BATTLE_PASS_CHANGE, self.BattlePassChange, self)
	EventMgr:Add(EventID.BATTLE_MANUAL_SAVE,self.BATTLE_MANUAL_SAVE,self)
    self.canvas = {}
    self.canvas[PlayerDefine.Coin] =  {canvas = GetComponent(self.ui.m_goCoin,UE.Canvas),count = 0}
    self.canvas[PlayerDefine.Diamond] =  {canvas = GetComponent(self.ui.m_goDiamond,UE.Canvas),count = 0}
	self.canvas[-1] =  {canvas = GetComponent(self.ui.m_goAir,UE.Canvas),count = 0}
    self:RefZooLib()
    self:BindingUIToVar()
    self:InitUIDrag()
	--初始化界面红点
	self:InitRedPoint()
    self:InitMainPos()
	MarketConfig:FreshRedPoint(6)
	self:CheckGiftPackShow()
    self:CreateScheduleFun(function() self:Timer() end,1)
	self:FlushZooFood()
	self:SetHead()
    self:MagnetInit()
    LimitActivityController:PushOpenLimit()
	self:updateActivityGroup()
    self:InitActivityRankTab()
	
    self:FoldRightBottom()
	--修复等级解锁物品链
	self:ZooCheckLevelChain()
	self:ChangeFight()
    -- 英雄
	self:OnHeroChange(nil, true);
	self:RoleGiftChange()
	self:BattlePassChange()
    self:CheckLotteryEntry()
	self:BATTLE_MANUAL_SAVE()

    SetActive(self.ui.m_goTradeWagonsRedPoint, RedPointMgr:IsRed(RedID.TradeWagons))
end


function M:BATTLE_MANUAL_SAVE()
	local data = HeroManager:GetBattleTeamByType(BATTLE_TEAM_TYPE.ARENA_DEFENDER)
	local totalPower = JJcManager:GetPower() or
		HeroManager:GetBattleListTotalPower(BATTLE_TEAM_TYPE.ARENA_DEFENDER, data)
	self.ui.m_txtJJcPower.text = NumToGameString(totalPower)
	local rank = JJcManager:GetJJcPlayerRank() or 0
	if rank > 0 then
		local color = "fffb74"
		self.ui.m_txtJJcRank.text = GetStrRichColor("No."..rank,color)
		SetOutline(self.ui.m_txtJJcRank,"6a217f")
		SetUIShadow(self.ui.m_txtJJcRank,"6a217f")
	else
		local color = "ffffff"
		self.ui.m_txtJJcRank.text = GetStrRichColor(LangMgr:GetLang(9056),color)
		SetOutline(self.ui.m_txtJJcRank,"444444")
		SetUIShadow(self.ui.m_txtJJcRank,"444444")
	end
end

function M:ChangeFight()
	SetActive(self.ui.m_goPlayerPower,NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.LevelEnter))
	self.ui.m_txtFightAdd.text = HeroManager:GetHeroTotalFight()
	SetUIForceRebuildLayout(self.ui.m_goPlayerPower)
end

function M:ZooCheckLevelChain()
	local lvId = ZoolevelConfig:GetLevel()
	for i = 1, lvId do --老用户修复配置表新增低级任务解锁物品
		local zooLvConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.zoo_level, i)
		if zooLvConfig and i ~= lvId then
			local unlock_Type = zooLvConfig.unlock_type
			if unlock_Type then
				local unlockType = string.split(unlock_Type, "|")
				for i = 1, #unlockType do
					local configZooType = ConfigMgr:GetDataByID(ConfigDefine.ID.zoo_type, unlockType[i])
					if configZooType then
						local chainId = configZooType["chain_series"]
						local chainOpenState = NetMapNoteData:GetNoteCount(MAP_ID_ZOO, NetMapNoteData.ID.chain_opened, chainId)
						if chainOpenState <= 0 then
							NetMapNoteData:AddNoteCount(MAP_ID_ZOO, NetMapNoteData.ID.chain_opened, chainId, 1)
						end
					end
				end
			end
		end
	end
end

function M:MagnetInit()
	self.magnetPress = self.ui.m_btnMagnet.transform:GetComponent(typeof(CS.ButtonPressed))
	self.magnetPress.buttonPressed = function()
		if NetPrivilegeCardData:IsHaveFreeTime() then
			MapController:SetIsMagnetValue(true)
			CombinePopMgr:InitMagnet(NetPrivilegeCardData:IsHaveFreeTime())
			return
		end
		if NetPrivilegeCardData.data.magnetState == PRIVILEGE_CARD.LOCK then
			UI_SHOW(UIDefine.UI_PrivilegeCard)
			return
		end
		MapController:SetIsMagnetValue(true)
		CombinePopMgr:InitMagnet()

	end

	self.magnetPress.buttonPressedCallBack = function()
		if CombinePopMgr:IsMagneting()then
			CombinePopMgr:EndMagnetState()
		else
			UI_SHOW(UIDefine.UI_PrivilegeCard)
		end
		MapController:SetIsMagnetValue(false)

	end
	self:RefreshMagnetState()
end

function M:RefreshMagnetState()
    local img = GetChild(self.ui.m_goPrivilege, "m_btnMagnet", UEUI.Image)
    if NetPrivilegeCardData.data.magnetState == PRIVILEGE_CARD.LOCK then
        if NetPrivilegeCardData:IsHaveFreeTime() then
            --SetUIImage(img, "Sprite/ui_mainface/icon_tq_citieshenqi.png", false)
            --
            --SetUIImage(self.ui.m_imgMagnet, "Sprite/ui_mainface/iconbox6.png", false)
            --SetUIImage(self.ui.m_imgPrivilegeTime,"Sprite/ui_mainface/iconbox6_time.png",false)
            SetUIImageGray(img,false)
            SetUIImageGray(self.ui.m_imgMagnet,false)
            SetUIImageGray(self.ui.m_imgPrivilegeTime,false)
        else
            --SetUIImage(img, "Sprite/ui_mainface/icon_tq_citieshenqi_1.png", false)
            --
            --SetUIImage(self.ui.m_imgMagnet, "Sprite/ui_mainface/iconbox6_1.png", false)
            --SetUIImage(self.ui.m_imgPrivilegeTime,"Sprite/ui_mainface/iconbox6_time_1.png",false)
            SetUIImageGray(img,true)
            SetUIImageGray(self.ui.m_imgMagnet,true)
            SetUIImageGray(self.ui.m_imgPrivilegeTime,true)
        end
    else
        --SetUIImage(img, "Sprite/ui_mainface/icon_tq_citieshenqi.png", false)
        --
        --SetUIImage(self.ui.m_imgMagnet, "Sprite/ui_mainface/iconbox6.png", false)
        --SetUIImage(self.ui.m_imgPrivilegeTime,"Sprite/ui_mainface/iconbox6_time.png",false)
        SetUIImageGray(img,false)
        SetUIImageGray(self.ui.m_imgMagnet,false)
        SetUIImageGray(self.ui.m_imgPrivilegeTime,false)
    end
end
function M:GetLimits(ActivityID)
	return self.limits[ActivityID]
end
function M:TickUI(deltaTime)
    --if MapController:GetMagnetState() then
        --CombinePopMgr:IsShowMagnet()
    --end
end

function M:ChangePrivilegeCardRed()
    local isShow = NetPrivilegeCardData:CheckEnergyRed()
    SetActive(self.ui.m_goPrivilegeRed,isShow)
end

--ios适配
function M:IosInit()
	local trans = GetComponent(self.uiGameObject.transform , UE.RectTransform)
	local off = (70/(trans.rect.size.x))
	trans.anchorMin = Vector2.New( off, 0)
	trans.anchorMax = Vector2.New( 1-off, 1)
end

function M:CheckGiftPackShow()
	local level = LevelConfig:GetLevel()
	SetActive(self.ui.m_goCarousel,level >= GlobalConfig.OPEN_GIFTPACK_LEVEL)
	self.ui.m_txtGiftPack.text =  TimeMgr:CheckHMS(GiftPackModule:getLastTime())

    local isGetAllReward = NetFirstGiftData:CheckAllReward(true)
    if isGetAllReward then
        SetActive(self.ui.m_goFirstPack,false)
    else
        SetActive(self.ui.m_goFirstPack,level >= GlobalConfig.OPEN_FIRSTPAY_LEVEL)
    end
	
	local isShow = NetFirstGiftData:GetFirstGifyRed()
	SetActive(self.ui.m_imgFirstGiftRed,isShow)
	
	local firstGiftConfig = ConfigMgr:GetData(ConfigDefine.ID.first_gift)
	self.ui.m_txtFirstPack.text = LangMgr:GetLang(firstGiftConfig[1].title)
end

function M:ChangeActCenterRed()
	local actCenter,num = self:CheckActCenterRed()
	SetActive(self.ui.m_goActPoint,actCenter)
	--SetActive(self.ui.m_txtActPointNum.gameObject,num > 1)
	--self.ui.m_txtActPointNum.text = num
	local actView = UIMgr:GetUIItem(UIDefine.UI_ActCenter)
	if nil ~= actView and actView.isShow then
		actView:UpdateRedPoint()
	end
end

function M:CheckActCenterRed()
	local redNum = 0
	local monthRed = NetMonthCardData:CheckMonthRedPoint()
	if monthRed then
		redNum = redNum + 1
		--return true
	end

	--local firstGiftRed = NetFirstGiftData:GetFirstGifyRed()
	--if firstGiftRed then
	--redNum = redNum + 1
	----return true
	--end
	if not NetGiftDaily.data.getFreeDay then
		redNum = redNum + 1
	end

	if NetGrowthFund:IsOpenActivity() then
		local GrowthFundRed = NetGrowthFund:CheckShowRed()
		if GrowthFundRed then
			redNum = redNum + 1
			--return true
		end
	else
		local GrowthFundRedNew = NetGrowthFundNew:GetIsShowRed()
		if GrowthFundRedNew then
			redNum = redNum + 1
			--return true
		end
	end



	return redNum > 0,redNum
end

function M:Timer()
    for _, v in pairs(self.gifts) do
        v:ChangeItem()
    end
    for _, v in pairs(self.limits) do
        v:ChangeItem()
    end
    self:SetAirPoint()
	self.ui.m_txtGiftPack.text =  TimeMgr:CheckHMS(GiftPackModule:getLastTime())
	
	
	if NetPrivilegeCardData:ClockReached() then
		self:RefreshMagnetState()
	end
    
    --if not NetPrivilegeCardData:IsHaveFreeTime() and NetPrivilegeCardData.data.magnetState == PRIVILEGE_CARD.LOCK then
    --	local time = NetPrivilegeCardData.data.magnetFreeTime - TimeMgr:GetServerTimestamp()
    --	--self.ui.m_txtPrivilegeTime.text =  TimeMgr:CheckHMS(time)
    --	self.ui.m_txtPrivilegeTime.text =  TimeMgr:GetHMS(time)
    --else
    --	self.ui.m_txtPrivilegeTime.text = "00:00"--LangMgr:GetLang(8319)
    --end
    self.ui.m_txtPrivilegeTime.text = NetPrivilegeCardData:GetMagnetTitleStr()
    
	if self.rightBottomArowIsPut then
		self:CheckRBFoldBtnRed()
	end
    self:UpdateDownTimer()
    self:ActivityRankTabTimer()
    self:RefreshTriggerGiftTime()
    self:GiftDailyFreeTimer()
	self:UpdateRoleGift()
end

function M:SetAirPoint()
    local data = BackgroundConfig:GetData()
    local level = LevelConfig:GetLevel()
    local isRed = false
    local limitIsOpen = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.LimitIns)
    for mapId, v in pairs(data) do
        if (v.map_type ~= MapType.Limit or limitIsOpen) and mapId ~= NetUpdatePlayerData.playerInfo.curMap then
            if not v.unlock_lv or v.unlock_lv <= level then
                if MarketConfig:isHaveRedPoint(mapId) then
                    isRed = true
                    break
                end
                local reward = NetMarketData:GetPopReward(mapId)
                if reward then -- gift point
                    isRed = true
                    break
                end
                if v.map_type ~= MapType.Zoo then -- energy point
                    if EnergyModule:get_IsEnergyMax(mapId) then
                        isRed = true
                        break
                    end
                end
            end
        end
    end
    self:SetRedPoint(MainFaceDefine.AirPoint,isRed)
end

function M:AddZooFood(id,count)
	NetBeastBagData:UpdateZooFood(count)
	self:FlushZooFood()
	
end

function M:FlushZooFood()
	
	TimeMgr:CreateTimer(UIDefine.UI_ZooMainFace,function()
			self.ui.m_txtFood.text = NetBeastBagData:GetZooFood()
		end,0.5,1)
end


function M:GuidInit(mapId)
    self:RefFeed()
end

function M:InitUIDrag()
    local uiDrag = GetComponent(self.ui.m_imgIcon,CS.UIDrag)
    uiDrag.m_BeginDrag = function( eventData, go , x, y )
        EventMgr:Dispatch(EventID.BEAST_TOUCH_BEGIN,x, y, self.nowDragId)
    end

    uiDrag.m_OnDrag = function( eventData, go , x, y )
        EventMgr:Dispatch(EventID.BEAST_TOUCH_MOVE,x, y, self.nowDragId)
    end

    uiDrag.m_EndDrag = function( eventData, go , x, y )
        EventMgr:Dispatch(EventID.BEAST_TOUCH_END,x, y, self.nowDragId)
    end
end

function M:InitMainPos()
    local pos
    pos = UIMgr:GetUIPosByWorld(self.ui.m_goLibrary.transform.position)
    TouchMoveController:SetUIPos(UIPosId.Library,pos)
end

function M:ChangeBegin(status,isContact,name,itemId)
    if status == BEAST_UI_STATUS.BEAST_DRAG and self.changeBeginState ~= isContact then
        if name == nil then
            if self.changeBeginState then
                self.changeBeginState = nil
                if self.libState == ZooLibState.Full then
                    self:PlayLibraryAni(ZooLibState.FullCloseOnly)
                elseif self.libState == ZooLibState.Put or self.libState == ZooLibState.NotSave then
                    self:PlayLibraryAni(nil)
                end
            end
        elseif name == "m_goLibrary" then
            self.changeBeginState = isContact
            if isContact then
                if itemId == nil or ItemConfig:GetZooType(itemId) then
                    if NetBeastBagData:GetIsFull() then
                        self:PlayLibraryAni(ZooLibState.Full)
                    else
                        self:PlayLibraryAni(ZooLibState.Put)
                    end
                    EventMgr:Dispatch(EventID.EVENT_GUIDE, 12, itemId)
                else
                    self:PlayLibraryAni(ZooLibState.NotSave)
                end
            else
                if self.libState == ZooLibState.Full then
                    self:PlayLibraryAni(ZooLibState.FullClose)
                elseif self.libState == ZooLibState.Put or self.libState == ZooLibState.NotSave then
                    self:PlayLibraryAni(nil)
                end
            end
        end
    end
end

function M:ChangeEnd(status)
    if self.changeBeginState then
        if status == BEAST_UI_STATUS.BEAST_FULL then
            self:PlayLibraryAni(ZooLibState.FullClose)
        elseif status == BEAST_UI_STATUS.BEAST_FINISHED then
            self:PlayLibraryAni(ZooLibState.Finish)
        end
    end
end

function M:RefFeed(isAni)
    local function SetSlider(nowNum,max)
        if nowNum < max then
            self.ui.m_txtSlider.text = nowNum .. "/" .. max
			SetActive(self.ui.m_goBXeff,false)
        else
            self.ui.m_txtSlider.text = LangMgr:GetLang(17)
			SetActive(self.ui.m_goBXeff,true)
        end
        self.ui.m_imgFeedSlider.fillAmount = nowNum / max
    end
    local nowNum = NetInfoData:GetDataFeedProgress()
    local max = GlobalConfig:GetFeedProgressZooMax()
    local up = GlobalConfig.FEED_PROGRESS_ZOO_ADD
    if isAni then
        AddDOTweenNumberDelay(nowNum - up, nowNum, 0.3, 0.8, function(value)
            value = math.floor(value)
            SetSlider(value ,max)
        end)
        self.ui.m_doFeed:DORestart()
    else
        SetSlider(nowNum,max)
    end
end

function M:SetButtonGrayState(btnType,showState)
    self.tempShow[btnType] = (self.tempShow[btnType] or 0) + showState
    if self.tempShow[btnType] > 0 then
        SetActive(self.ui[btnType],false)
    else
        SetActive(self.ui[btnType],true)
    end
end

function M:InitResourcePos()
    local ResourceItemsGo = GetChild(self.uiGameObject, "RightTop/ResourceItems")
    SetUIForceRebuildLayout(ResourceItemsGo)
    local pos
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doDiamond.transform.position)
    MapController:SetUIResourcePos(ItemID.DIAMOND, pos.x, pos.y)
    MapController:SetUIResourcePos(ItemID.RED_DIAMOND, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doCoin.transform.position)
    MapController:SetUIResourcePos(ItemID.COIN, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_goMagic.transform.position)
    MapController:SetUIResourcePos(ItemID.MAGIC, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_doFeed.transform.position)
    MapController:SetUIResourcePos(ItemID.FoodZoo, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_txtZooLevel.transform.position)
    MapController:SetUIResourcePos(ItemID.ENERGY, pos.x, pos.y)
    MapController:SetUIResourcePos(ItemID.ZOO_EXP, pos.x, pos.y)
    pos = UIMgr:GetUIPosByWorld(self.ui.m_goAir.transform.position)
    MapController:SetUIResourcePos(FlyId.Air, pos.x, pos.y)
	pos = UIMgr:GetUIPosByWorld(self.ui.m_btnZooFood.transform.position)
	MapController:SetUIResourcePos(ItemID.EATFOODZOO, pos.x, pos.y)
	
	pos = UIMgr:GetUIPosByWorld(self.ui.m_goCollect.transform.position)
	MapController:SetUIResourcePos(ItemID.COLLECTION, pos.x, pos.y)
end

function M:InitRedPoint()
    CollectionItems:ReflushMainPoint()
    
    --市场红点
    self:UpdateMarketRedDot();
    --特权月卡
    self:ChangePrivilegeCardRed()
	--活动中心红点
	self:ChangeActCenterRed()
	--右下角折叠按钮红点
	self:CheckRBFoldBtnRed()
	local isShow = NetFirstGiftData:GetFirstGifyRed()
	SetActive(self.ui.m_imgFirstGiftRed,isShow)
    -- 设置红点
    self:SetListRedPoint()
    -- 英雄红点
	SetActive(self.ui.m_goHeroRed, RedPointMgr:IsRed(RedID.HeroRedEntry));
	SetActive(self.ui.m_goBagRed, RedPointMgr:IsRed(RedID.BagRedEntry));
	SetActive(self.ui.m_goLotteryRed, RedPointMgr:IsRed(RedID.LotteryEntry))
    SetActive(self.ui.m_goArenaRedPoint, RedPointMgr:IsRed(RedID.ArenaEntry))
    SetActive(self.ui.m_goWorldBossRed, RedPointMgr:IsRed(RedID.WorldBoss))
	SetActive(self.ui.m_goBattleRed, RedPointMgr:IsRed(RedID.BattlePass))
    SetActive(self.ui.m_goFullBoxDot, RedPointMgr:IsRed(RedID.DungeonEntry))
    SetActive(self.ui.m_goTopFightRed, RedPointMgr:IsRed(RedID.TopFight) or NetLimitActGift:IsShowRed(TopFightManager.GIFT_TYPE))
end

function M:UpdateMarketRedDot()
    local curMapID = NetUpdatePlayerData.playerInfo.curMap
    local isReceived = MarketConfig:isHaveRedPoint(curMapID, 6)
    self:SetRedPoint(MainFaceDefine.Market, isReceived)
end

function M:ChangeResource(type, num, changeValue,isTop)
    local info = NetUpdatePlayerData:GetPlayerInfo()
    --初始化的时候type为空
    if type == nil then
        self.ui.m_txtDiamond.text = math.floor(info["diamond"])
        self.ui.m_txtCoin.text = math.floor(info["coin"])
    end
    function FinishOrder(type)
		
    end
    if type == PlayerDefine.Exp then
        self:SetLevel(nil, changeValue)
    elseif type == PlayerDefine.ZooExp then
        self:SetZooLevel(nil, changeValue)
    elseif type == PlayerDefine.Diamond then
        local diamond = info["diamond"]
        self.ui.m_doDiamond:DORestart()
        AddDOTweenNumberDelay(diamond - changeValue, diamond, 0.3, 0.8, function(value)
          
            self.ui.m_txtDiamond.text = math.floor(value)
        end,function() FinishOrder(PlayerDefine.Diamond) end)
		if changeValue >0 then
			AudioMgr:Play(12)
		end
		
    elseif type == PlayerDefine.Coin then
        local coin = info["coin"]
        self.ui.m_doCoin:DORestart()
        AddDOTweenNumberDelay(coin - changeValue, coin, 0.3, 0.8, function(value)
            
            self.ui.m_txtCoin.text = math.floor(value)
        end,function() FinishOrder(PlayerDefine.Coin) end)
		if changeValue >0 then
			AudioMgr:Play(10)
		end
    end
end

function M:SetZooLevel(id, add)
    function SetLevelSlider(value,max)
        self.ui.m_txtZooExp.text = NumToGameString(value)  .. "/" .. NumToGameString(max)
        local fill = value / max
        self.ui.m_imgZooLevelSlider.fillAmount = fill
    end
    local level, exp, maxExp = NetUpdatePlayerData:GetZOOLevel()
    self.ui.m_txtZooLevel.text = level
    if add then
        AddDOTweenNumberDelay(exp - add, exp, 0.3, 0.8, function(value)
            value = math.floor(value)
            SetLevelSlider(value ,maxExp)
        end)
        AudioMgr:Play(2)
        --self.ui.m_doExp:DORestart()
    else
        SetLevelSlider( exp, maxExp)
    end
    self:RefZooLib()

    if id then
        self:UpdateMarketRedDot();
    end
end

--设置主界面红点是否显示
function M:SetRedPoint(faceType,isShow)
    local ui = self.ui[faceType]
    if ui == nil then
        Log.Error("red point name is wrong")
        return
    end
    if ui.activeSelf ~= isShow then
        SetActive(ui,isShow)
    end
end

--设置等级经验，只改经验level可以传nil
function M:SetLevel(_, add)
    function SetLevelSlider(value,max)
        self.ui.m_txtExp.text = NumToGameString(value)  .. "/" .. NumToGameString(max)
        local fill = value / max
        self.ui.m_imgSlider.fillAmount = fill
        self.rotateTrans.localRotation = Quaternion.Euler(0,0,(0.5-fill)*360)
        self.ui.m_imgSliderMask.fillAmount = fill+0.2
    end
    local level, exp, maxExp = NetUpdatePlayerData:GetLevel()
    --self:SetButtonGray(AddBtnDefine.worker,level<workOpenLevel)
    self.ui.m_txtLev.text = level
    if add then
        AddDOTweenNumberDelay(exp - add, exp, 0.3, 0.8, function(value)
            value = math.floor(value)
            SetLevelSlider(value ,maxExp)
        end)
        AudioMgr:Play(2)
        self.ui.m_doExp:DORestart()
    else
        SetLevelSlider( exp, maxExp)
    end
	SetActive(self.ui.m_goPrivilege.gameObject,level >= GlobalConfig.OPEN_PRIVILEGECARD_LEVEL)
    self.rightBottomList[FolderItemType.Magnet].isOpen = level >= GlobalConfig.OPEN_PRIVILEGECARD_LEVEL
    SetActive(self.rightBottomList[FolderItemType.Magnet].go,level >= GlobalConfig.OPEN_PRIVILEGECARD_LEVEL)
	SetActive(self.ui.m_goActCenter,level >= GlobalConfig.OPEN_ACTCENTER_LEVEL)
	self:UpdateRightBottomArow()
end

function M:PlayLibraryAni(state,dragId)
    function CleanState()
        self.libState = nil
    end
    function PlayStateAni(trans,isOpen,call,duration,delay)
        local endValue
        duration = duration or 0.4
        if isOpen then
            endValue =  -740 + 498
        else
            endValue =  -740
            CleanState()
        end
        self.lastTween = DOAnchorPos(trans,Vector2.New(endValue,-2.2),duration,call,Ease.OutSine,delay)
    end

    Log.Info("=====================================",state,self.libState)
    function PlayAni()
        self.libState = state
        if state == nil then
            return
        end
        if state == ZooLibState.Put then
            PlayStateAni(self.ui.m_rtransShowPut,true)
        elseif state == ZooLibState.Show then
            self.nowDragId = dragId
            PlayStateAni(self.ui.m_rtransShowAni,true)
        elseif state == ZooLibState.Finish then
            PlayStateAni(self.ui.m_rtransShowFinish,true,function()
                PlayStateAni(self.ui.m_rtransShowFinish,false,nil,0.5,1)
            end,0.5)
        elseif state == ZooLibState.Full then
            PlayStateAni(self.ui.m_rtransShowFull,true)
        elseif state == ZooLibState.FullClose then
            PlayStateAni(self.ui.m_rtransShowFull,false)
            NetBeastBagData:BuyGrid(function()
                self:PlayLibraryAni(ZooLibState.BuyGridAutoClose)
            end,210559)
        elseif state == ZooLibState.FullCloseOnly then
            PlayStateAni(self.ui.m_rtransShowFull,false,nil,0.4)
        elseif state == ZooLibState.FullAutoClose then
            PlayStateAni(self.ui.m_rtransShowFull,true,function()
                self:PlayLibraryAni(ZooLibState.FullClose)
            end)
        elseif state == ZooLibState.BuyGridAutoClose then
            PlayStateAni(self.ui.m_rtransShowBuyGrid,true,function()
                PlayStateAni(self.ui.m_rtransShowBuyGrid,false)
            end )
        elseif state == ZooLibState.NotSave then
            PlayStateAni(self.ui.m_rtransShowNotSave,true)
        end
    end
    if self.lastTween then
        if self.lastTween:IsPlaying() then
            self.lastTween:Complete()
        end
        self.lastTween = nil
    end
    if self.pushAni:IsPlaying() then
        self.pushAni:Stop()
    end
    self.nowDragId = nil
    if self.libState == ZooLibState.Show then
        PlayStateAni(self.ui.m_rtransShowAni,false,function()
            PlayAni()
        end)
    elseif self.libState == ZooLibState.Put then
        if state == ZooLibState.Finish then
            self.pushAni:Play()
            CleanState()
        else
            PlayStateAni(self.ui.m_rtransShowPut,false)
        end
    elseif self.libState == ZooLibState.NotSave then
        PlayStateAni(self.ui.m_rtransShowNotSave,false)
    else
        PlayAni()
    end

end

function M:CloseShowAni()
	self:PlayLibraryAni(nil)
	--PlayStateAni(self.ui.m_rtransShowAni,false,function()
		--self.libState = nil
		----PlayAni()
	--end)
end

function M:RefZooLib()
    local maxNum = NetBeastBagData:GetBeastMaxNum()
    local maxBag = NetBeastBagData:GetBagNum()
    local heroAnimalCount = NetHeroData:GetHeroAnimalCount();
    if heroAnimalCount > 0 then
        self.ui.m_txtLib.text = maxNum .. "/" .. maxBag .. string.format("(+%s)", heroAnimalCount);
    else
        self.ui.m_txtLib.text = maxNum .. "/" .. maxBag;
    end
    self:ClickAni()
end

function M:ClickAni(param)
    local id = param or self.nowDragId
    local num = NetBeastBagData:GetBeastNumById(id)
    if num > 0 then
        SetImageSprite(self.ui.m_imgIcon,ItemConfig:GetIcon(id),false)
        local data = ItemConfig:GetZooType(id)
        SetImageSprite(self.ui.m_imgQuality,data.tag_icon,false)
        self.ui.m_txtAniNum.text = num
        if self.libState == nil then
            self:PlayLibraryAni(ZooLibState.Show,id)
        else
            self.nowDragId = id
        end
    else
        self:PlayLibraryAni(nil)
    end
end

function M:FinishLibAni(isFull)
    if isFull then
        self:PlayLibraryAni(ZooLibState.FullAutoClose)
    else
        self:PlayLibraryAni(ZooLibState.Finish)
    end
end

function M:OnRefresh(type, param)
    type = tonumber(type)
    --刷新新任务item
    if type == 1 then
        self:RefZooLib()
    elseif type == 2 then
        if param == nil or self.gifts[param] then
            return
        end
        --self:RemoveGift()
        local cfg_data = TriggerGiftConfig:GetDataByID(param)
        if cfg_data == nil then
            return
        end
        if cfg_data.aging == "one" or cfg_data.aging == "onceday" then
            return
        end
        if GetBoolValue(cfg_data.show_alone) then
            --local item = GoActivity:Create(self.ui.m_goActivity)
            --item:SetItem(param)
            --self.gifts[param] = item
			
			local function GoActivityBack(item)
				item:SetItem(param)
				self.gifts[param] = item
			end
			GoActivity:Create(self.ui.m_goActivity,GoActivityBack)
        end
        
        --local item = GoActivity:Create(self.ui.m_goActivity)
        --item:SetItem(param)
        --local customSize = 1.4
        --item:SetScale(customSize)
        --self.gifts[param] = item
        --self.itemSize = item:GetHeight()*customSize
        --
        --
        local sortList = {}
		for k, v in pairs(self.gifts) do
			table.insert(sortList,v)
		end

		table.sort(sortList,function(a,b)
				return a.show_priority < b.show_priority
			end)

		if table.length(sortList) > 1 then
			for _, v in ipairs(sortList) do
				SetUIFirstSibling(v.go)
			end
		end

		if self.ui.m_scrollviewActivity then
			TimeMgr:CreateTimer(self, function ()
					SetActive(self.ui.m_scrollviewActivity,false)
					SetActive(self.ui.m_scrollviewActivity,true)
					SetUIFirstSibling(self.ui.m_goFirstPack)
				end, 0.1, 1)
		end
        --self:AutoFitGiftList()
    elseif type == 3 then
        self:FinishLibAni(param)
    elseif type == 4 then
        --tips 选中状态
        self:SetTips(param)
    elseif type == 5 then
        self:ClickAni(param)
    elseif type == 6 then
		if param then
        	self:SetRedPoint(param[1],param[2])
		end
        self:SetListRedPoint();
    elseif type == 7 then
        self:RefFeed(true)
    elseif type == 8 then
        self:SetGuideShow(param)
    elseif type == 10 then
        if param then
            Log.Info("+++++++++++PlayClearScreen++++++++++++++++")
            self.aAni:Play("main_clearscreenzoo")
        else
            Log.Info("+++++++++++PlayBackScreen++++++++++++++++")
			--if Game.Channel_IOS_Notch then
				--self.aAni:Play("main_backzoo_ios")
			--else
				--self.aAni:Play("main_backzoo")
			--end
			self.aAni:Play("main_backzoo")
        end
    elseif type == 11 then
        if param then
            self:PlayLibraryAni(nil)
            self.aAni:Play("ZooMainHide")
        else
            self.aAni:Play("ZooMainShow")
        end
    elseif type == 12 then
        --local isShow = param.isShow
        --for _, id in ipairs(param.list) do
            --local mess = self.canvas[id]
            --if mess == nil then return end
            --if isShow then
                --if not mess.order then
                    --mess.order = mess.canvas.sortingOrder
                    --mess.canvas.sortingOrder = 199
                    --self:SetButtonGrayState(AddBtnDefine[id],1)
                --end
                --mess.count = mess.count + 1
            --else
                --if mess.order then
                    --mess.count = mess.count - 1
                    --if mess.count <= 0 then
                        --mess.count = 0
                        --mess.canvas.sortingOrder = mess.order
                        --mess.order = nil
                        --self:SetButtonGrayState(AddBtnDefine[id],-1)
                    --end
                --end
            --end
        --end
	elseif type == 29 then
		if self.limits[param] then
			self.limits[param]:ChangeValue()
		end
	elseif type == 17 then
		self:RemoveGift(param)
        self.triggerGiftTime = nil
	elseif type == 18 then
		self:CloseShowAni()
	elseif type == 25 then
		self:CheckGiftPackShow()
	elseif type == 27 then
		self:ChangeActCenterRed()	
    elseif type == 35 then
        self:PlayPrivilegeHandAnim()
	elseif type == 102 then
		self:SetHead()
	elseif type == 34 then
		self:RefreshMagnetState()
    elseif type == 126 then
        -- 更新保龄球红点
        self:updateActivityGroup();
	elseif type == 127 then
		self:ChangePrivilegeCardRed()
    elseif type == 128 then
        if param.state == 1 then
            self:AddLimits(param)			
			if param.totalType == ActivityTotal.BowlingBattle or param.totalType == ActivityTotal.OneToOne
				or param.totalType == ActivityTotal.AthleticTalent or param.totalType == ActivityTotal.Rank then
				self:updateActivityGroup()
				UI_UPDATE(UIDefine.UI_ActivityRankCenter, 1);
			end
        elseif param.state == 2 then
			if self.limits[param.id] then
				self.limits[param.id]:ChangeValue()
			end
			
			local totalType = LimitActivityController:GetTotalTypeById(param.id)
			if totalType and totalType == ActivityTotal.BowlingBattle or totalType == ActivityTotal.OneToOne
				or totalType == ActivityTotal.AthleticTalent or totalType == ActivityTotal.Rank then
				self:updateActivityGroup()
				UI_UPDATE(UIDefine.UI_ActivityRankCenter, 2);
			end
        elseif param.state == 3 then
            self:CloseLimits(param.id)
			
			local totalType = LimitActivityController:GetTotalTypeById(param.id)
			if totalType and totalType == ActivityTotal.BowlingBattle or totalType == ActivityTotal.OneToOne
				or totalType == ActivityTotal.AthleticTalent or totalType == ActivityTotal.Rank then
                self:updateActivityGroup()
				UI_UPDATE(UIDefine.UI_ActivityRankCenter, 1);
			end
        end
	elseif type == 40 then --主界面ui置顶显示
		if self.canvas[param[1]] then
			local canvas = self.canvas[param[1]].canvas
			local childButton = {}
			GetChildsComponent(canvas.transform,childButton,typeof(UEUI.Button))
			for index, value in ipairs(childButton) do
				value.interactable = not param[2]
			end
			canvas.sortingLayerName = param[2] and "UI_MIDDLE" or "Default"
		end
    elseif type == 283701 then  -- 1v1 比拼条显示隐藏
		SetActive(self.ui.m_goCompetition, param)
		SetActive(self.ui.m_goNotCompetition, not param)
        if self.oneToOneTimeBg then
            SetActive(self.oneToOneTimeBg, not param)
        end
		self:RefreshCompetition()
    elseif type == 283702 then  -- 1v1 比拼条刷新
		self:RefreshCompetition(param)
    elseif type == 283703 then  -- 1v1 比拼条剩余时间
		self:RefreshCompetitionTime(param)    
    elseif type == 45 then  --触发礼包弹窗结束播飞行特效
        self:PlayTriggerGiftFlyEff(param)
    elseif type == RefreshType.RefreshAthleticTalent then
        self:UpdateAthleticTalent(param)
    elseif type == RefreshType.RefreshRank then
        self:UpdateRankRed();
    elseif type == RefreshType.SwitchTab then
        self:MoveTargetTab(param)
	elseif type == 304 then --刷新头像
		if self.m_CustomHead then
			self.m_CustomHead:RefreshHead()
		end
    elseif type == 305 then --刷新关卡
        self:InitLevelTimer()
    elseif type == 306 then --刷新关卡
        --self.rightBottomArowIsPut  true 折叠 false 展开
        --param  0:折叠  1:展开
        if param == 0 then
            if not self.rightBottomArowIsPut then
                self:ChangeRightBottomArow()
            end
        elseif param == 1 then
            if self.rightBottomArowIsPut then
                self:ChangeRightBottomArow()
            end
        end
	end
end
function M:AddLimits(param)
    local item
	local function loadCallBack(obj)
		item = obj
		if not item then
			return
		end
		item:SetItem(param)
		self.limits[param.id] = item
		SetUIForceRebuildLayout(self.ui.m_goUp)
		self.limitsGo[param.id] = item.go
		if param.totalType == ActivityTotal.LimitRank then -- or param.totalType == ActivityTotal.Rank
			self:CheckRankAutoOpen(item, param)
		end
	end
    if param.totalType == ActivityTotal.MonthlySeason then
		item = GoLimit21:Create(self.ui.m_goUp,loadCallBack)
	elseif param.totalType == ActivityTotal.ContinuousRecharge then
		if ContinuousRechargeManager:HasCanReward() then
			item = GoLimit25:Create(self.ui.m_transLimitParent,loadCallBack)
		else
			return
		end
    elseif param.totalType == ActivityTotal.OneToOne then
		NetOneToOneData:InitActiveData(param.id,loadCallBack)
        --item = GoLimit28:Create(self.ui.m_transLimitParent)
    elseif param.totalType == ActivityTotal.AthleticTalent then
        NetAthleticTalentData:InitActiveData(param.id,loadCallBack)
    elseif param.totalType == ActivityTotal.Rank then
        self.limitsGo[param.id] = self.ui.m_scrollviewActivityEnter.gameObject;
        self:CheckRankAutoOpen(nil, param)
    else
        item = GoLimit:Create(self.ui.m_transLimitParent)
    end
	--if not item then
		--return
	--end
    --item:SetItem(param)
    --self.limits[param.id] = item
    --self.limitsGo[param.id] = item.go
end

function M:CheckRankAutoOpen(item, param)
    local active = LimitActivityController:GetActiveMessage(param.id);
    if active.info.isNew then
        active:SetIsNewFalse()
        if item then
            item:ClickItemPush()
        end
        return
    end
end

function M:CloseLimits(id)
    local item = self.limits[id]
    self.limitsGo[id] = nil
	if item == nil then
		return nil
	end
    item:Close()
    self.limits[id] = nil
end
function M:PlayPrivilegeHandAnim()
    SetActive(self.ui.m_imgPrivilegeHand,true)
    local Ani = GetComponent(self.ui.m_imgPrivilegeHand, UE.Animation)
    if Ani then
        local function PlayAniEnd()
            SetActive(self.ui.m_imgPrivilegeHand,false)
        end
        PlayAnimStatus(Ani,"privilege_hand",PlayAniEnd)
    end
    --self.ui.m_imgPrivilegeHand:GetComponent("CanvasGroup"):DOFade(1, 0.1)
    --local function PlayAniEnd()
    --	SetActive(self.ui.m_imgPrivilegeHand, false)
    --end
    --local tween, tId = TweenMgr:CreateSequence(UIDefine.UI_MainFace, false, PlayAniEnd)
    --local pFrom = Vector2.New(0,0)
    --local x = UIWidth*0.5
    --local y = (UIHeight+self.ui.m_goDownList.transform.localPosition.y)*0.5
    --local pTo = Vector2.New(-x,-y)
    --local itemRect = GetComponent(self.ui.m_imgPrivilegeHand,UE.RectTransform)
    --itemRect.position = pFrom
    ----tween:Append(self.ui.m_imgPrivilegeHand:GetComponent("CanvasGroup"):DOFade(1, 0.5)):SetDelay(0.1)
    --tween:Append(itemRect:DOAnchorPos(pFrom, 0.6))
    --tween:Append(itemRect:DOAnchorPos(pTo, 0.6):SetDelay(0.1))
    --tween:AppendInterval(0.2)
    --
    --tween:SetLoops(2, LoopType.Restart)
end

function M:RemoveGift(param)
    local item = self.gifts[param]
    if item == nil then
        return
    end
    self.gifts[param] = nil
    item:Close()
	
	if table.count(self.gifts)<=0 then 
	 	SetActive(self.ui.m_scrollviewActivity,false)
	end
end

function M:BindingUIToVar()
    local list = {}
    list[1] = self.ui.m_goDiamond--宝石
    list[2] = self.ui.m_goCoin--金币
    list[3] = self.ui.m_goLevel--等级
    list[8] = self.ui.m_goCollect--藏品
    list[9] = self.ui.m_goMarket--市场
    list[10] = self.ui.m_goDeleted--删除
    list[12] = self.ui.m_goTip --提示
    list[14] = self.ui.m_goSetlist --信息列表
    list[19] = self.ui.m_goAir -- 地图切换
    list[20] = self.ui.m_goLibrary--动物园神兽库
    list[21] = self.ui.m_goBusiness--交易
    list[22] = self.ui.m_goFeed--藏品
    list[23] = self.ui.m_goZooLevel--市场
    self.m_ArrUI = list
end

function M:SetGuideShow(param)
    param = param or {}
    local list = GlobalConfig:GetBtnMess("0")
    for i, v in pairs(self.m_ArrUI) do
        local strIndex = tostring(i)
        local isShow = param[strIndex] ~= nil
        for _, k in ipairs(list) do
            if k[1] == strIndex then
                isShow = GuideController:IsGuideDone(k[2])
            end
        end
		if i == 12 then
			isShow = false
		end
        if i == 14 and not isShow and self.setIsOpen then
            self.setIsOpen = not self.setIsOpen
            self.setAni:Play("setlists_close")
        end
		if i == 22 then 
			SetActive(self.ui.m_btnZooFood, isShow)
		end
		
		if (i == 19) and NetUpdatePlayerData:GetLevel() >= 7 then isShow = true end
		
		--if (i == 24) and NetUpdatePlayerData:GetZOOLevel() >= 5 then isShow = true end
		--if (i == 20) and NetUpdatePlayerData:GetZOOLevel() >= 7 then isShow = true end
		--if (i == 21) and NetUpdatePlayerData:GetZOOLevel() >= 13 then isShow = true end
		
		if i == 20 or i == 21 or i == 24 then
			if isShow == false or v.activeSelf == false then
				local mapType, level = FunctionOpenConfig:GetopenConditionById(i)
				if mapType == MapType.Zoo and NetUpdatePlayerData:GetZOOLevel() >= level then
					isShow = true
				end
			end
		end
		
		if i == 10 then
            self.rightBottomList[FolderItemType.Shovel].isOpen = isShow
            SetActive(self.rightBottomList[FolderItemType.Shovel].go, isShow)
            self:FoldRightBottom()
		end
		
        SetActive(v, isShow)
    end
	self:UpdateRightBottomArow()
end

function M:DragEnd()
    if self.libState == ZooLibState.NotSave then
        self:PlayLibraryAni(nil)
    end
end

function M:onDestroy()
    EventMgr:Remove(EventID.CHANGE_RESOURCE, self.ChangeResource, self)
    EventMgr:Remove(EventID.DRAG_BEAST_BEGIN, self.ChangeBegin, self)
    EventMgr:Remove(EventID.DRAG_BEAST_END, self.ChangeEnd, self)
    EventMgr:Remove(EventID.DRAG_BEAST_CANCEL, self.DragEnd, self)
    EventMgr:Remove(EventID.ZOO_LVUP, self.SetZooLevel, self)
    EventMgr:Remove(EventID.GUIDE_INIT, self.GuidInit, self)
	EventMgr:Remove(EventID.ADD_ZOO_FOOD, self.AddZooFood, self)
	EventMgr:Remove(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
	EventMgr:Remove(EventID.UPDATE_HERO_TOTAL_FIGHT, self.ChangeFight, self)
    EventMgr:Remove(EventID.UPDATE_HERO_INFO, self.OnHeroChange, self)
    EventMgr:Remove(EventID.DUNGEON_UPDATE, self.OnDungeonUpdate, self)
    EventMgr:Remove(EventID.BAG_CHANGE, self.BagChange, self)
	EventMgr:Remove(EventID.ROLE_GIFT_CHANGE, self.RoleGiftChange, self)
	EventMgr:Remove(EventID.BATTLE_PASS_CHANGE, self.BattlePassChange, self)
	EventMgr:Remove(EventID.BATTLE_MANUAL_SAVE,self.BATTLE_MANUAL_SAVE,self)
	self.activityRankTabList = {}
    -- 清理竞技达人
    self.athleticTalent = nil
    self.isRefreshAthleticTalentRank = nil
    -- 清理活动竞赛倒计时文本
    self.oneToOneTimeBg = nil;
    self.oneToOneTimeTxt = nil;
    self.athleticTalentTimeTxt = nil;
    self.bowlingTimeTxt = nil;
    self.rankTimeTxt = nil;
    self.towerTimeTxt = nil;
	self.jjcTimeTxt = nil
    self.worldBossTimeTxt = nil;
	self.topFightTimeTxt = nil
	if self.m_CustomHead then
		self.m_CustomHead:onDestroy()
		self.m_CustomHead = nil
	end
    self.rbBgImg = nil

    self.my1v1Head1 = nil
    self.my1v1Head2 = nil
    self.other1v1Head = nil
end

function M:SetTips(id, isOnlyShow)
    if not isOnlyShow then
        self.selectId = id
    end
    if not self.selectIsClose then
        if self.selectId == nil then
            SetActive(self.ui.m_goSelect, false)
            SetActive(self.ui.m_goNoSelect, true)
        else
            SetActive(self.ui.m_goSelect, true)
            SetActive(self.ui.m_goNoSelect, false)
            self.ui.m_txtTipTitle.text = ItemConfig:GetRareID(self.selectId,true)
            local data = ItemConfig:GetDataByID(self.selectId)
            self.ui.m_txtTipContent.text = LangMgr:GetLang(data.explain)
            if data.is_can_delete == 1 then
                SetActive(self.ui.m_goDeleted, true)
            else
                SetActive(self.ui.m_goDeleted, false)
            end
            if data.type_use == ItemUseType.HeroFly then
                SetActive(self.ui.m_goHeroBtn,true)
            else
                SetActive(self.ui.m_goHeroBtn,false)
            end
			self.selectId = id
			
			local isType = CollectionItems:GetCollectId(self.selectId)
			if isType == nil then
				SetActive(self.ui.m_goDetails, false)
			else
				SetActive(self.ui.m_goDetails, true)
			end
        end
    end
end


function M:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnSetting" then
        UI_SHOW(UIDefine.UI_Setting)
	elseif  name == "m_btnDelete" then
		UI_SHOW(UIDefine.UI_DeleteMain)
    elseif name == "m_btnEmail" then
		local funBack = function(objJson)
            UIMgr:ShowWaiting(false)
			if objJson == nil then
				UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(5012))
				return
			else
				NetMailData:ReadNetData(objJson)
				UI_SHOW(UIDefine.UI_MailPanel)
			end
		end
		local params = {}
		params["cate"] = 1
		HttpClient:SendToGS("/sns/maillist", params, funBack)
        UIMgr:ShowWaiting(true)
    elseif name == "m_goCollect" then
		UI_SHOW(UIDefine.UI_Collection, nil)
    elseif name == "m_goLibrary" then
        UI_SHOW(UIDefine.UI_ZooBeastLibrary)
        self:OnRefresh(11,true)
    elseif name == "m_goBusiness" then
        UI_SHOW(UIDefine.UI_ZooExchange)
    elseif name == "m_btnShop" then
        --self:OnRefresh(7, false)
        --UI_SHOW(UIDefine.UI_LimitList)
		--UI_SHOW(UIDefine.UI_SeasonDesc)
		UI_SHOW(UIDefine.UI_Market)
    elseif name == "m_imgDiamBuy" or name == "m_goDiamond" then
		UI_SHOW(UIDefine.UI_ActCenter,13)
		--UI_SHOW(UIDefine.UI_Shop)
    elseif name == "m_imgCoinBuy" or name == "m_goCoin" then
        UI_SHOW(UIDefine.UI_Market)
    elseif name == "m_goAir" then
        UI_SHOW(UIDefine.UI_Travel, nil)
    elseif name == "m_goRecover" then
        SetActive(self.ui.m_goRecover, false)
        SetActive(self.ui.m_goExpand, true)
        SetActive(self.ui.m_goTips, false)
        self.selectIsClose = true
    elseif name == "m_goExpand" then
        SetActive(self.ui.m_goRecover, true)
        SetActive(self.ui.m_goExpand, false)
        SetActive(self.ui.m_goTips, false)
        self.selectIsClose = false
        self:SetTips(self.selectId, true)
    elseif name == "m_goDeleted" then
		UI_SHOW(UIDefine.UI_DeleteMain)
    elseif name == "m_goDetails" then
		UI_SHOW(UIDefine.UI_Collection, self.selectId)
    elseif name == "m_goHeroBtn" then
        UI_SHOW(UIDefine.UI_HeroHelp,self.selectId)
    elseif name == "FeedBtn" then
        if NetInfoData:GetFeedIsFull() then
			UI_SHOW(UIDefine.UI_ItemFeedRewardZoo)
        else
            UI_SHOW(UIDefine.UI_FeedHelp)
        end
	elseif name == "m_btnZooFood" then
			UI_SHOW(UIDefine.UI_FeedHelp2)
    elseif name == "m_goSetlist" then
        if self.setAni.isPlaying then
            return
        end

        local isActive = self.ui[MainFaceDefine.Mail].activeSelf
		local isSettingActive = self.ui[MainFaceDefine.Setting].activeSelf
        if self.setIsOpen then
            if isActive then
                self:SetRedPoint(MainFaceDefine.SetList,true)
            end

			if isSettingActive then
				self:SetRedPoint(MainFaceDefine.SetList,true)
			end
            self.setAni:Play("setlists_close")
        else
			if isActive then
				self:SetRedPoint(MainFaceDefine.SetList,false)
			end

			if isSettingActive then
				self:SetRedPoint(MainFaceDefine.SetList,false)
			end
			self:SetRedPoint(MainFaceDefine.SetList,false)

            self.setAni:Play("setlists_open")
        end
        self.setIsOpen = not self.setIsOpen

    elseif name == "m_btnEnergyTask" then
        UI_SHOW(UIDefine.UI_ZooBeastLibrary)
	elseif name == "m_goCarousel" then
		UI_SHOW(UIDefine.UI_GiftPack)
	elseif name == "expbg" then
		UI_SHOW(UIDefine.UI_Setting)
	elseif name == "m_btnActCenter" then
		UI_SHOW(UIDefine.UI_ActCenter)
	elseif name == "m_btnMagnet" or name == "m_btnEnergy" then
		if not UIMgr:GetUIOpen(UIDefine.UI_PrivilegeCard) then
			UI_SHOW(UIDefine.UI_PrivilegeCard)
		end
    elseif name == "m_btnCompetition" then
        local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.OneToOne)
		Log.Info("活动 对象", activityItem)
		if activityItem:IsActivityEnd() then
			if activityItem.info.activeId then
				NetOneToOneData:CheckEndPush(activityItem.info.activeId)
			end
		else
			UI_SHOW(UIDefine.UI_ActivityRankCenter, 1)
		end
    elseif name == "m_btnBowlingBattle" then
        UI_SHOW(UIDefine.UI_ActivityRankCenter, 3)
	elseif name == "m_btnActivityEnter" then
		UI_SHOW(UIDefine.UI_ActivityRankCenter)
	elseif name == "m_goFirstPack" then
		UI_SHOW(UIDefine.UI_FirstGift)
    elseif name == "m_btnRightBottomRow" then
        self:ChangeRightBottomArow()
    elseif name == "m_goTriggerGift" then
        --UI_SHOW(UIDefine.UI_Shop)
        UI_SHOW(UIDefine.UI_ActCenter, 7);
    elseif name == "rankBtn" then
        UI_SHOW(UIDefine.UI_ActivityRankCenter, 4);
    elseif name == "m_btnActiveFollow" then
        UI_SHOW(UIDefine.UI_ActivityRankCenter, 5);
    elseif name == "goHero" then
        UI_SHOW(UIDefine.UI_HeroWindow);
	elseif name == "m_goBag" then
		UI_SHOW(UIDefine.UI_BagView)
	elseif name == "m_btnLottery" then
		UI_SHOW(UIDefine.UI_LotteryCenter)
    elseif name == "m_btnTowerClimbing" then
        UI_SHOW(UIDefine.UI_ActivityRankCenter, 7)
    elseif name == "m_btnSlg" then
        UI_SHOW(UIDefine.UI_ActivityRankCenter, 6)
	elseif name == "m_goRoleGift" then
		UI_SHOW(UIDefine.UI_RoleGiftList,self.roleGiftId)
    elseif name == "m_btnTradeWagons" then
        UI_SHOW(UIDefine.UI_ActivityRankCenter, 8)    
    elseif name == "m_btnArena" then
        UI_SHOW(UIDefine.UI_ActivityRankCenter, 9)
    elseif name == "worldBossBtn" then
        UI_SHOW(UIDefine.UI_ActivityRankCenter, 10)
	elseif name == "m_goBattle" then
        BattlePassManager:OnReqBattlepassLoad(true)
    elseif name == "topFightBtn" then
        TopFightManager:OpenTopFightView()
    end
end

function M:RefreshCompetition(param)
    -- 刷新玩家头像
    --local playerHead = NetUpdatePlayerData:GetPlayerInfo().head
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerHead)
    --if headConfig then
    --	SetUIImage(self.ui.m_imgHeadLeft, headConfig["icon"], false)
    --end
    SetMyHeadAndBorderByGo(self.my1v1Head1)
    SetMyHeadAndBorderByGo(self.my1v1Head2)

    -- 刷新玩家名字
    if NetUpdatePlayerData:GetPlayerInfo().name then
        self.ui.m_txtNameLeft.text = NetUpdatePlayerData:GetPlayerInfo().name
    else
        self.ui.m_txtNameLeft.text = "player-" .. NetUpdatePlayerData:GetPlayerInfo().id
    end
    ---- 设置匹配对手头像
    --local currentRobotHead = NetOneToOneData:GetDataByKey("currentRobotHead")
    --if not IsNilOrEmpty(currentRobotHead) then
    --    SetUIImage(self.ui.m_imgHeadRight, currentRobotHead, false)
    --
    --	if self.other1v1Head == nil then
    --		self.other1v1Head = CreateCommonHead(GetChild(self.ui.m_goHead,"Right").transform,0.45)
    --	end
    --	SetHeadAndBorderByGo(self.other1v1Head,currentRobotHead,121)
    --end

    -- 设置匹配对手头像
    local currentRobotHead = NetOneToOneData:GetDataByKey("currentRobotHead")
    local currentRobotBorder = NetOneToOneData:GetDataByKey("currentRobotBorder")
    if self.other1v1Head == nil then
        CreateCommonHeadAsync(GetChild(self.ui.m_goHead,"Right").transform, 0.35, nil, function (go, trans)
            self.other1v1Head = go
        end)
        local rect = GetComponent(self.other1v1Head,UE.RectTransform)
        rect.anchoredPosition = Vector2.New(0,5)
    end

    if not IsNilOrEmpty(currentRobotHead)  and not IsNilOrEmpty(currentRobotBorder)then
        SetHeadAndBorderByGo(self.other1v1Head,currentRobotHead,currentRobotBorder)
    end

    local currentRobotName = NetOneToOneData:GetDataByKey("currentRobotName")
    self.ui.m_txtNameRight.text = currentRobotName
    -- 显示当前比拼分数
    self:RefreshScore()
    local winNum = NetOneToOneData:GetDataByKey("winningStreak")
    local config = OneToOneManager:GetVictoryConfig(winNum)
    if config then
        self.ui.m_txtCompetition.text = config.victory_points
    end
    -- 飞积分
    -- if param and param.isFly then
    --     local view = UIMgr:GetUIItem(UIDefine.UI_OneToOneView)
    --     local isShow = view and view.isShow
    --     if isShow then return end
    --     local oldPos =  UIMgr:GetObjectScreenPos(self.ui.m_transFlyPos)
    --     local endPos
    --     -- 玩家
    --     if param.endPos == 0 then
    --         endPos = self.ui.m_imgHeadLeft
    --         if param.startPos then
    --             oldPos = param.startPos
    --             OneToOneManager.startPos = nil
    --         end
    --     -- 机器人
    --     elseif param.endPos == 1 then
    --         endPos = self.ui.m_imgHeadRight
    --         oldPos =  UIMgr:GetObjectScreenPos(self.ui.m_transFlyPosRobot)
    --     end
    --     local posScreen = UIMgr:GetUIPosByWorld(endPos.transform.position)
    --     local flyId = 55002
    --     local score = 5
    --     MapController:FlyUIAnim(oldPos.x, oldPos.y, flyId, score, posScreen.x, posScreen.y,
    --     nil, nil, nil, 0.7, nil,
    --     function ()
            
    --     end)
    -- end
end

function M:RefreshCompetitionTime(time)
    self.ui.m_txtCompetitionCountDown.text = TimeMgr:ConverSecondToString(time)
end

function M:RefreshScore()
    local curScore = NetOneToOneData:GetDataByKey("curScore")
    local robotScore = NetOneToOneData:GetDataByKey("robotScore")
    if NetOneToOneData.playerChangeScore > 0 then
        if self.txtScoreBlueTween then
            self.txtScoreBlueTween:Kill()
            self.txtScoreBlueTween = nil
        end
        self.txtScoreBlueTween = Tween.To(function(value)
            self.ui.m_txtScoreBlue.text = tostring(math.floor(value))
        end, curScore - NetOneToOneData.playerChangeScore, curScore, 1)

        local item = UEGO.Instantiate(self.ui.m_goScoreBlue, self.ui.m_goScore.transform)
        SetActive(item, true)
        local text = GetChild(item.transform, "m_txtScoreBlueAdd", UEUI.Text)
        text.text = "+" .. NetOneToOneData.playerChangeScore
        item.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOScale(item.transform, Vector3.New(1.0, 1.0, 1.0), 0.8, nil, Ease.OutBack)
        local rect = GetComponent(item.transform, UE.RectTransform)
        rect.anchoredPosition3D = Vector3.New(-188, -70, 0)
        DOLocalMove(item.transform, Vector3.New(-168, -64, 0), 0.8, nil, Ease.OutBack):OnComplete(function ()
            DOScale(item.transform, Vector3.New(0, 0, 0), 1, nil, Ease.OutBack):SetDelay(1)
            DOLocalMove(item.transform, Vector3.New(-109, -2, 0), 1, nil, Ease.OutBack):OnComplete(function ()
                UEGO.Destroy(item)
            end):SetDelay(1)
        end)

        NetOneToOneData.playerChangeScore = 0
    else
        self.ui.m_txtScoreBlue.text = tostring(curScore)
    end

    if NetOneToOneData.robotChangeScore > 0 then
        if self.txtScoreRedTween then
            self.txtScoreRedTween:Kill()
            self.txtScoreRedTween = nil
        end
        self.txtScoreRedTween = Tween.To(function(value)
            self.ui.m_txtScoreRed.text = tostring(math.floor(value))
        end, robotScore - NetOneToOneData.robotChangeScore, robotScore, 1)

        local item = UEGO.Instantiate(self.ui.m_goScoreRed, self.ui.m_goScore.transform)
        SetActive(item, true)
        local text = GetChild(item.transform, "m_txtScoreRedAdd", UEUI.Text)
        text.text = "+" .. NetOneToOneData.robotChangeScore
        item.transform:SetLocalScale(0.0, 0.0, 0.0)
        DOScale(item.transform, Vector3.New(1.0, 1.0, 1.0), 0.8, nil, Ease.OutBack)
        local rect = GetComponent(item.transform, UE.RectTransform)
        rect.anchoredPosition3D = Vector3.New(82, -70, 0)
        DOLocalMove(item.transform, Vector3.New(62, -64, 0), 0.8, nil, Ease.OutBack):OnComplete(function ()
            DOScale(item.transform, Vector3.New(0, 0, 0), 1, nil, Ease.OutBack):SetDelay(1)
            DOLocalMove(item.transform, Vector3.New(3, -2, 0), 1, nil, Ease.OutBack):OnComplete(function ()
                UEGO.Destroy(item)
            end):SetDelay(1)
        end)

        NetOneToOneData.robotChangeScore = 0
    else
        self.ui.m_txtScoreRed.text = tostring(robotScore)
    end

    if curScore > 0 or robotScore > 0 then
        local percent = curScore / (curScore + robotScore)
        local diff = math.abs(percent - 0.5)
        local target = diff * 0.4
        if curScore > robotScore then
            target = 0.5 + target
        else
            target = 0.5 - target
        end
        self.ui.m_sliderVS:DOValue(target, 1)
    else
        self.ui.m_sliderVS.value = 0.5
    end
end
---add
function M:ChangeRightBottomArow()
    self.rightBottomArowIsPut = not self.rightBottomArowIsPut
    NetGlobalData:SetDataByKey("rightBottomArowIsPut", self.rightBottomArowIsPut)
    self:FoldRightBottom()
    self:CheckRBFoldBtnRed()
    self:ShowRBFoldPop()
end

--- 折叠右下角的工具栏
function M:FoldRightBottom()
    -- 折叠
    if self.rightBottomArowIsPut then
        self.ui.m_btnRightBottomRow.transform:SetLocalScale(1, 1, 1)
        -- 展开
    else
        self.ui.m_btnRightBottomRow.transform:SetLocalScale(-1, 1, 1)
        local childCount = self.rbLayoutTrans.childCount
        local activeCount = 0
        for i = 0,childCount - 1 do
            local child = self.rbLayoutTrans:GetChild(i)
            if child.gameObject.activeSelf then
                activeCount = activeCount + 1
            end
        end
        self.rbBgRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Horizontal, activeCount*120+46);
    end

    local openCount = 0
    for i = 1, 5 do
        local isOpen = self.rightBottomList[i].isOpen
        if isOpen then
            openCount = openCount + 1
        end
    end
    --region    ------- 旧收起逻辑 -------------
    --local hideCount = 1
    --if openCount > 2 then
    --    hideCount = 2
    --end
    --
    ---- 折叠时隐藏前两个
	--if openCount >= 2 then
	--    for i = 1, hideCount do
	--        local go = self.rightBottomList[i].go
	--        if not IsNil(go) then
	--            SetActive(go, not self.rightBottomArowIsPut and self.rightBottomList[i].isOpen)
	--        end
	--    end
	--end
    --
    ----(扩展逻辑)隐藏新增的按钮
    --local extendList = {4,5}
    --for k,v in ipairs(extendList) do
    --    local go = self.rightBottomList[v].go
    --    if not IsNil(go) then
    --        --true 折叠 false 展开
    --        if self.rightBottomArowIsPut then
    --            SetActive(go, false)
    --        else
    --            SetActive(go, self.rightBottomList[v].isOpen)
    --        end
    --    end
    --end
    --endregion
    
    local showIndex = 0
    if self.rightBottomList[FolderItemType.SLGBag].isOpen then
        showIndex = FolderItemType.SLGBag
    else
        showIndex = FolderItemType.Magnet
    end
    for i = 1, self.rightBottomCount do
        local isOpen = self.rightBottomList[i].isOpen
        if isOpen then
            local go = self.rightBottomList[i].go
            if not IsNil(go) and i ~= showIndex then
                --true 折叠 false 展开
                if self.rightBottomArowIsPut then
                    SetActive(go, false)
                else
                    SetActive(go, self.rightBottomList[i].isOpen)
                end
            end
        end
    end
    
    --当工具栏只有1个入口时就不用显示黑色地图和箭头图标(新增需求)
    local onlyOne = (openCount == 1)
    self.rbBgImg.enabled = not onlyOne
    SetActive(self.ui.m_btnRightBottomRow, not onlyOne)

    local goBagPos = UIMgr:GetUIPosByWorld(self.ui.m_goBag.transform.position)
    EquipmentManager:SetFlyEndPos(goBagPos)
end

--右下角折叠按钮红点检测
function M:CheckRBFoldBtnRed()
    local isShow
    local openCount = 0
    for i = 1, 5 do
        local isOpen = self.rightBottomList[i].isOpen
        if isOpen then
            openCount = openCount + 1
        end
    end
    if self.rightBottomArowIsPut then
        if openCount > 2 then
            isShow = IntelligentWorkerManager:HasRedPoint()
        else
            isShow = false
        end
    else
        --在展开时折叠按钮不显示红点
        isShow = false
    end
    SetActive(self.ui.m_goRightBottomRed,isShow)
end

--处理右下角工具栏箭头
function M:UpdateRightBottomArow()
	local count = 0
	for i = 1, 5 do
		local isOpen = self.rightBottomList[i].isOpen
		if isOpen then
			count = count + 1
		end
	end
	SetActive(self.ui.m_goRightBottomList,count > 0)
	SetActive(self.ui.m_btnRightBottomRow,count > 1)
end

function M:GetActivityGroupSortList()
    local sortList = {}
    local config = ConfigMgr:GetData(ConfigDefine.ID.activity_rank)
    for i, v in ipairs(config) do
        table.insert(sortList,v)
    end
    table.sort(sortList, function(a, b)
        return a.sort < b.sort;
    end)
    return sortList
end

function M:updateActivityGroup()
    local config = self:GetActivityGroupSortList()
    local count = 0
    local nameStr;
    local redImg;
    local level = NetUpdatePlayerData:GetLevel()
    for i, v in ipairs(config) do
        nameStr = "m_goActivityTabGroup"..v.id;
        local openArr = string.split(v.open,"|")
        local openType = v2n(openArr[1])
        local openParm = v2n(openArr[2])
        local activityOpen ,activity = NetGlobalData:GetIsOpenActivityRank(v)
        if activityOpen then
            count = count + 1
            SetActive(self.ui[nameStr],true)
        else
            if activity and activity.info.state == 3 and openParm == ActivityTotal.BowlingBattle then
                activityOpen = activity and activity.info.state == 3;
                count = count + 1
                SetActive(self.ui[nameStr],true)
            else
                SetActive(self.ui[nameStr],false)
            end
        end
        self.activityRankTabList[v.id] = activityOpen
        self.activityRankTabIndexList[v.id] = count

        if activityOpen and activity then
            local redImg = GetChild(self.ui[nameStr], "redImg");
            if openParm == ActivityTotal.BowlingBattle then
                SetActive(redImg, BowlingBattleManager:CheckRedPoint())
            elseif openParm == ActivityTotal.OneToOne then
                SetActive(redImg, NetOneToOneData:IsShowRedPoint())
                self.oneToOneTime = activity:GetRemainingTime();
            elseif openParm == ActivityTotal.AthleticTalent then
                SetActive(redImg, NetAthleticTalentData:IsShowRedPoint())
                self.athleticTalentTime = activity:GetRemainingTime();
            elseif openParm == ActivityTotal.Rank then
                SetActive(redImg, NetRankData:IsShowRedPoint(ActivityTotal.Rank) or NetUpdatePlayerData:GetPlayerRankOneRewardRed());
                self.rankTime = activity:GetRemainingTime();
            else
                SetActive(redImg, false)
            end
        end
    end
    self.maxActivityRankTabPage = count - 1
    local tabNameStr;
    for i = 1, #config do
        tabNameStr = "m_imgActivityTab"..i;
        if self.ui[tabNameStr] then
            SetActive(self.ui[tabNameStr], i <= count)
        end
    end
    for i, v in ipairs(config) do
        nameStr = "m_goActivityTabGroup"..v.id;
        SetUILastSibling(self.ui[nameStr])
    end
    SetActive(self.ui.m_goActivityGrop,count > 0)
    SetActive(self.ui.m_goActivityTabRoot,count > 0)

    if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.AthleticTalent] then
        self:UpdateAthleticTalent(true)
    end
    if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.BowlingBattle] then
        self:updateBowlingShow()
    end
    if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.Rank] then
        self:UpdateRankShow();
    end
    if self.activityRankTabList[ACTIVITY_RANK_TABINDEX.TowerClimbing] then
        self:UpdateTowerShow()
    end

    SetActive(self.ui.m_goPlayerPower, NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.LevelEnter))
end

function M:updateBowlingShow()
    local isOpen, activity = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.BowlingBattle);
    local isPreview = activity and activity.info.state == 3;
    if isPreview then
        local helperNum = 0
        local detail = LeagueManager:GetMyLeagueDetails()
        if detail and detail.GetHelpNumber then
            helperNum = detail:GetHelpNumber()
        end

        SetActive(self.ui.m_goBowlingConditionOK, helperNum > 0)
        SetActive(self.ui.m_goBowlingConditionNO, helperNum <= 0)

        local replaceText;
        if helperNum > 0 then
            replaceText = "(" .. helperNum .. "/1)"
        else
            replaceText = string.format("(<color=#%s>0</color>/1)", "ff0000")
        end
        self.ui.m_txtBowlingCondition.text = LangMgr:GetLangFormat(9312, replaceText);

        self.bowlingDownTime = activity:GetStartRemainingTime();
    elseif isOpen then
        local rank = NetBowlingBattleData:GetRankMyLeague();
        if rank > 0 then
            self.ui.m_txtRank.text = rank;
        end
        self.bowlingDownTime = activity:GetRemainingTime();

        isOpen = rank > 0;
    end

    SetActive(self.ui.m_goOpen, isPreview);
    SetActive(self.ui.m_goRank, isOpen);
end

function M:UpdateDownTimer()
    if self.oneToOneTimeTxt then
        if self.oneToOneTime and self.oneToOneTime > 0 then
            self.oneToOneTime = self.oneToOneTime - 1;
            self.oneToOneTimeTxt.text = TimeMgr:ConverSecondToString(self.oneToOneTime);
        else
            self.oneToOneTimeTxt.text = TimeMgr:ConverSecondToString(0);
        end
    end

    if self.athleticTalentTimeTxt then
        if self.athleticTalentTime and self.athleticTalentTime > 0 then
            self.athleticTalentTime = self.athleticTalentTime - 1;
            self.athleticTalentTimeTxt.text = TimeMgr:ConverSecondToString(self.athleticTalentTime);
        else
            self.athleticTalentTimeTxt.text = TimeMgr:ConverSecondToString(0);
        end
    end

    if self.bowlingTimeTxt then
        if self.bowlingDownTime and self.bowlingDownTime > 0 then
            self.bowlingDownTime = self.bowlingDownTime - 1;
            self.bowlingTimeTxt.text = TimeMgr:ConverSecondToString(self.bowlingDownTime);
        else
            self.bowlingTimeTxt.text = TimeMgr:ConverSecondToString(0);
        end
    end
    
    if self.rankTimeTxt then
        if self.rankTime and self.rankTime > 0 then
            self.rankTime = self.rankTime - 1;
            self.rankTimeTxt.text = TimeMgr:ConverSecondToString(self.rankTime);
        else
            self.rankTimeTxt.text = TimeMgr:ConverSecondToString(0);
        end
    end

    if self.towerTimeTxt then
        if not self.towerTime then
            local nowTime = TimeMgr:GetServerTimestamp()
            --明天天零点
            local tomorrowTimeStamp = TimeZoneMgr:GetServerClockStampByNDay(1,nowTime)
            self.towerTime = tomorrowTimeStamp - nowTime
        end

        if self.towerTime and self.towerTime > 0 then
            self.towerTime = self.towerTime - 1
            self.towerTimeTxt.text = TimeMgr:ConverSecondToString(self.towerTime);
        else
            self.towerTime = nil
            self.towerTimeTxt.text = TimeMgr:ConverSecondToString(0);
        end
    end
	if self.jjcTimeTxt then
		local time = JJcManager:GetJJcRemainingTime()
		if time and time > 0 then
			self.jjcTimeTxt.text = TimeMgr:ConverSecondToString(time)
		else
			self.jjcTimeTxt.text = TimeMgr:ConverSecondToString(0)
		end
    end
    if self.worldBossTimeTxt then
        local time = WorldBossManager:GetRemainTime();
        if time > 0 then
            self.worldBossTimeTxt.text = TimeMgr:ConverSecondToString(time);
        else
            self.worldBossTimeTxt.text = LangMgr:GetLang(70000697);
        end
    end
	if self.topFightTimeTxt then
		local time = TopFightManager:GetTopFightAt(FightStage.End)
		if time and time > 0 then
			self.topFightTimeTxt.text = TimeMgr:ConverSecondToString(time)
		else
			self.topFightTimeTxt.text = LangMgr:GetLang(70000697)
		end
	end
    self:CheckLevelTimer()
end

--- 刷新竞技达人
--- @param refreshRank boolean 是否刷新排名
function M:UpdateAthleticTalent(refreshRank)
    local type = ACTIVITY_RANK_TABINDEX.AthleticTalent--ActivityRankCenterType.AthleticTalent
    local nameStr = "m_goActivityTabGroup" .. type
    local root = self.ui[nameStr]
    local activity = self.activityRankTabList[type]
    -- 初始获取组件
    if IsTableEmpty(self.athleticTalent) then
        local icon = GetChild(root, "icon", UEUI.Image)
        local title = GetChild(root, "title", UEUI.Text)
        local button = GetChild(root, "button", UEUI.Button)
        if button then
            button.onClick:AddListener(function ()
                local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.AthleticTalent)
                if activityItem:IsActivityEnd() then
                    if activityItem.info.activeId then
                        NetAthleticTalentData:CheckEndPush(activityItem.info.activeId)
                    end
                else
                    UI_SHOW(UIDefine.UI_ActivityRankCenter, type)
                end
            end)
        end
        local score = GetChild(root, "score/text", UEUI.Text)
        local rank = GetChild(root, "rank/text", UEUI.Text)
        local rankParent = GetChild(root, "rank")
        self.athleticTalent.icon = icon
        self.athleticTalent.title = title
        self.athleticTalent.button = button
        self.athleticTalent.score = score
        self.athleticTalent.rank = rank
        self.athleticTalent.rankParent = rankParent
    end
    -- 刷新图片
    if self.athleticTalent.icon then
        local iconPath = "Sprite/ui_mainface/mainface2_huodong_jingjidaren.png"
        SetUIImage(self.athleticTalent.icon, iconPath, false)
    end
    -- 刷新标题
    if self.athleticTalent.title then
        local langID = AthleticTalentManager:GetActivityNameLangID() or 2203001
        self.athleticTalent.title.text = LangMgr:GetLang(langID)
    end
    -- 刷新分数
    if self.athleticTalent.score then
        local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
        if 1 <= currentDay and currentDay <= 6 then
            local score = NetAthleticTalentData:GetDailyScore()
            self.athleticTalent.score.text = tostring(score)
        elseif currentDay >= 7 then
            local score = NetAthleticTalentData:GetTotalScore()
            self.athleticTalent.score.text = tostring(score)
        end
    end
    -- 刷新排名
    if self.athleticTalent.rank and refreshRank then
        if not self.isRefreshAthleticTalentRank then
            self:RefreshAthleticTalentRank()
            self.isRefreshAthleticTalentRank = true
            TimeMgr:CreateTimer("RefreshAthleticTalentRank", function()
                self.isRefreshAthleticTalentRank = false
            end, 1, 1)
        end
    end
    -- 刷新红点
    local redImg = GetChild(self.ui.m_goActivityTabGroup2, "redImg")
    SetActive(redImg, NetAthleticTalentData:IsShowRedPoint())
end

--- 刷新竞技达人排名
function M:RefreshAthleticTalentRank()
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    local function dayRankCallBack(data)
        -- 已上榜
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        if data.player.rank and isEnterRank then
            if self.athleticTalent then
                self.athleticTalent.rank.text = tostring(data.player.rank)
                SetActive(self.athleticTalent.rankParent, true)
            end
        -- 未上榜
        else
            if self.athleticTalent then
                self.athleticTalent.rank.text = ""
                SetActive(self.athleticTalent.rankParent, false)
            end
        end
    end
    if 1 <= currentDay and currentDay <= 6 then
        NetAthleticTalentData:RequestRankData(dayRankCallBack, currentDay, true)
    end

    local function totalRankCallBack(data)
        -- 已上榜
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        if data.player.rank and isEnterRank then
            if self.athleticTalent then
                self.athleticTalent.rank.text = tostring(data.player.rank)
                SetActive(self.athleticTalent.rankParent, true)
            end
        -- 未上榜
        else
            if self.athleticTalent then
                self.athleticTalent.rank.text = ""
                SetActive(self.athleticTalent.rankParent, false)
            end
        end
    end
    if currentDay >= 7 then
        NetAthleticTalentData:RequestRankData(totalRankCallBack, nil, true)
    end
end

function M:UpdateRankShow()
    local _, active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Rank);
    if not active then return end
    
    if self.rankSubType ~= active.info.subtype then
        self.rankSubType = active.info.subtype;
        local bg = GetChild(self.ui.m_goActivityTabGroup4, "bg", UEUI.Image);
        local score = GetChild(self.ui.m_goActivityTabGroup4, "score", UEUI.Image);
        local title = GetChild(self.ui.m_goActivityTabGroup4, "title", UEUI.Text);
        local titleOutline = GetChild(self.ui.m_goActivityTabGroup4, "title", UEUI.Outline);
        local titleUIShadow = GetChild(self.ui.m_goActivityTabGroup4, "title", CS.Coffee.UIEffects.UIShadow);

        if self.rankSubType == ActivitySubtype.MergeRank then -- 合成竞赛
            SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_hechengjinsai.png", false);
            SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_hechengjinsai_2.png", false);
            title.color = Color.New(1, 253/255, 204/255, 1); -- fffdcc
            titleOutline.effectColor = Color.New(200/255, 24/255, 129/255, 1); -- c81881
            titleUIShadow.effectColor = Color.New(200/255, 24/255, 129/255, 1); -- c81881
        elseif self.rankSubType == ActivitySubtype.BuildRank then -- 建造竞赛
            SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_jianzaojinsai.png", false);
            SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_jianzaojinsai_2.png", false);
            title.color = Color.New(242/255, 1, 252/255, 1); -- f2fffc
            titleOutline.effectColor = Color.New(47/255, 90/255, 192/255, 1); -- 2f5ac0
            titleUIShadow.effectColor = Color.New(47/255, 90/255, 192/255, 1); -- 2f5ac0
        elseif self.rankSubType == ActivitySubtype.CollectRank then -- 采集竞赛
            SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_caijijinsai.png", false);
            SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_caijijinsai_2.png", false);
            title.color = Color.New(1, 1, 229/255, 1); -- ffffe5
            titleOutline.effectColor = Color.New(179/255, 76/255, 0, 1); -- b34c00
            titleUIShadow.effectColor = Color.New(179/255, 76/255, 0, 1); -- b34c00
        elseif self.rankSubType == ActivitySubtype.OrderRank then -- 订单排行榜
            SetUIImage(bg, "Sprite/ui_mainface/mainface2_huodong_dingdanjinsai.png", false);
            SetUIImage(score, "Sprite/ui_mainface/mainface2_huodong_dingdanjinsai_2.png", false);
            title.color = Color.New(1, 254/255, 238/255, 1); -- fffeee
            titleOutline.effectColor = Color.New(0, 120/255, 71/255, 1); -- 007847
            titleUIShadow.effectColor = Color.New(0, 120/255, 71/255, 1); -- 007847
        end
        title.text = LangMgr:GetLang(active.form.title)
    end

    local actInfo = active.info
    local rank = actInfo.rank;
    if rank > 0 then
        local text = GetChild(self.ui.m_goActivityTabGroup4, "rank/text", UEUI.Text);
        text.text = rank;
    end
    local rankObj = GetChild(self.ui.m_goActivityTabGroup4, "rank");
    SetActive(rankObj, rank > 0);
    
    local numTxt = GetChild(self.ui.m_goActivityTabGroup4, "score/numTxt", UEUI.Text);
    numTxt.text = actInfo.integral or 0;
end

function M:UpdateRankRed()
    local redImg = GetChild(self.ui.m_goActivityTabGroup4, "redImg");
    SetActive(redImg, NetRankData:IsShowRedPoint(ActivityTotal.Rank) or NetUpdatePlayerData:GetPlayerRankOneRewardRed());
end

function M:UpdateTowerShow()
    local _,imgPath = TowerManager:GetTodayOpenTowerType()
    SetImageSprite(self.ui.m_imgSlgTower,imgPath)
end

---------------------- 轮播 ------------------------------------------------
function M:ActivityRankTabTimer(deltaTime)
    if self.tabDeltaTime then
        if self.tabDeltaTime < 10 then
            self.tabDeltaTime = self.tabDeltaTime + 1
        else
            self.tabDeltaTime = 0
            self.curActivityRankTabPage = self.curActivityRankTabPage + 1
			if UIMgr.uiLockActivityRankTabPage and UIMgr.uiLockActivityRankTabPage > 0 then
				self.curActivityRankTabPage = Mathf.Clamp(self.activityRankTabIndexList[UIMgr.uiLockActivityRankTabPage] - 1, 0, self.maxActivityRankTabPage)
			else
				local isStartCompetition = NetOneToOneData:GetDataByKey("isStartCompetition")
				if isStartCompetition then
					self.curActivityRankTabPage = Mathf.Clamp(self.activityRankTabIndexList[ACTIVITY_RANK_TABINDEX.One_to_one] - 1, 0, self.maxActivityRankTabPage)
				end
			end
            if self.curActivityRankTabPage > self.maxActivityRankTabPage then
                self.curActivityRankTabPage = 0
            end
            self:SwitchActivityTabPage(self.curActivityRankTabPage)
        end
    end
end

function M:InitActivityRankTab()
    self.uiDrag = self.ui.m_scrollviewActivityEnter:GetComponent(typeof(CS.UIDrag))
    self.curActivityRankTabPage = 0
    local startPosition = 0
    self:SwitchActivityTabPage(self.curActivityRankTabPage)
    self.uiDrag.m_BeginDrag = function ()
        if self.ActivityTabTween then
            self.ActivityTabTween:Kill()
        end
        startPosition = self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition
    end

    self.uiDrag.m_EndDrag = function ()
        local endPosition = self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition
        if endPosition > startPosition then
            self:MoveNextTab()
        else
            self:MovePreTab()
        end
    end
end

function M:SwitchActivityTabPage(pageIndex)
    local moveParam = pageIndex
    if self.maxActivityRankTabPage > 1 then
        moveParam = pageIndex / self.maxActivityRankTabPage--* 0.5
    end
    self.ActivityTabTween = Tween.To(function(value)
        self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition = value
        self.ui.m_scrollviewActivityEnter.velocity.x = 0
    end,self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition,moveParam,0.3)
    for i = 1, self.maxActivityRankTabPage + 1 do
        local tabNameStr = "m_imgActivityTab"..i;
        if self.ui[tabNameStr] then
            local tabImag = tabNormalImg
            local selectIndex = pageIndex + 1
            if i == selectIndex then
                tabImag = tabSelectImg
            end
            SetImageSprite(self.ui[tabNameStr],tabImag,true)
        end
    end

end

function M:MovePreTab()
    if self.maxActivityRankTabPage <= 0 then
		return
	end

	self.curActivityRankTabPage = self.curActivityRankTabPage - 1
	if self.curActivityRankTabPage < 0 then
		local step = 1/self.maxActivityRankTabPage
		self.curActivityRankTabPage = self.maxActivityRankTabPage
		self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition = (1+step)+self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition
	end

	if self.ActivityTabTween then
		self.ActivityTabTween:Kill()
	end
	self:SwitchActivityTabPage(self.curActivityRankTabPage)
end

function M:MoveNextTab()
    if self.maxActivityRankTabPage <= 0 then
		return
	end

	self.curActivityRankTabPage = self.curActivityRankTabPage + 1
	if self.curActivityRankTabPage > self.maxActivityRankTabPage then
		local step = 1/self.maxActivityRankTabPage
		self.curActivityRankTabPage = 0
		self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition = -(1+step)+(self.ui.m_scrollviewActivityEnter.horizontalNormalizedPosition)
	end

	if self.ActivityTabTween then
		self.ActivityTabTween:Kill()
	end
	self:SwitchActivityTabPage(self.curActivityRankTabPage)
end

function M:MoveTargetTab(type)
    local isStartCompetition = NetOneToOneData:GetDataByKey("isStartCompetition")
    if isStartCompetition then return end
    local index = self.activityRankTabIndexList[type] - 1
    local lastPage = self.curActivityRankTabPage
    self.curActivityRankTabPage = Mathf.Clamp(index, 0, self.maxActivityRankTabPage)
    if lastPage == self.curActivityRankTabPage then
        return
    end
    if self.ActivityTabTween then
        self.ActivityTabTween:Kill()
    end
    self:SwitchActivityTabPage(self.curActivityRankTabPage)
end
------------------------------------------------------------------------------
function M:SetHead()
    --SetImageSprite(self.ui.m_imgHead,NetUpdatePlayerData:GetPlayerHead(),false)
    --SetImageSprite(self.ui.m_imgNotCompetitionHeadLeft,NetUpdatePlayerData:GetPlayerHead(),false)

    SetActive(self.ui.m_imgNotCompetitionHeadLeft,false)
    SetActive(self.ui.m_imgHeadLeft,false)
    SetActive(self.ui.m_imgHeadRight,false)

    SetActive(self.ui.m_imgNotCompetitionHeadLeft,false)
    self.my1v1Head1 = CreateCommonHead(GetChild(self.ui.m_goNotCompetitionHead,"Left").transform,0.35)
    self.my1v1Head2 = CreateCommonHead(GetChild(self.ui.m_goHead,"Left").transform,0.35)

    SetMyHeadAndBorderByGo(self.my1v1Head1)
    SetMyHeadAndBorderByGo(self.my1v1Head2)

    local rect = GetComponent(self.my1v1Head1,UE.RectTransform)
    rect.anchoredPosition = Vector2.New(0,5)
    rect = GetComponent(self.my1v1Head2,UE.RectTransform)
    rect.anchoredPosition = Vector2.New(0,5)

    --self.other1v1Head = CreateCommonHead( GetChild(self.ui.m_goNotCompetitionHead,"Left"),0.45)
    --

    self.m_CustomHead:SetHeadByID(NetUpdatePlayerData:GetPlayerInfo().head)
    self.m_CustomHead:SetHeadBorderByID(NetUpdatePlayerData:GetPlayerInfo().headBorder)
end

function M:SetListRedPoint()
    local havePush = NetContactData:IsHavePushMsg()
    local haveChatRed = NetGlobalData:GetIsAIChatRed()
    local headRed = NetHeadSeasonVip:IsShowRedPoint()   -- NetInfoData:IsShowHeadRed() or
    local mailRed = RedPointMgr:IsRed(RedID.MailRoot)
    local rankRed = NetFriendData:GetNewDailyRewardRed()
	local inviteRed = NetInviteFriendData:CheckRedPoint()
    if haveChatRed or headRed or mailRed or rankRed or inviteRed then
        if not self.setIsOpen then
            self:SetRedPoint(MainFaceDefine.SetList, true)
        end
    else
        self:SetRedPoint(MainFaceDefine.SetList, false)
    end
end

--动物园右侧礼包栏适配（特殊处理）
function M:AutoFitGiftList()
    local giftCount = 0
    local trans = self.ui.m_goActivity.transform
    local childCount = trans.childCount

    local height = 0
    local sumCount = 0
    for i = 0,childCount-1 do
        local child = trans:GetChild(i)
        if child.gameObject.activeInHierarchy then
            if i == 0 then
                if child.gameObject == self.m_goFirstPack then
                    height = height + self.goFirstPackRect.rect.height
                    sumCount = sumCount + 1
                else
                    giftCount = giftCount + 1
                end
            else
                giftCount = giftCount + 1
            end
        end
    end
    sumCount = sumCount + giftCount
    --height = height + giftCount*self.itemSize+(sumCount-1)*45
    height = height + giftCount*self.itemSize
    self.goActivityRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical, height);
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.goActivityRect);
end

-------------------------- 触发礼包集合入口 -------------------------------------

function M:RefreshTriggerGiftTime()
    if self.triggerGiftTime == nil then
        local gift = NetGiftBox:GetGiftLessTime()
        if nil == gift or gift.time == nil then
            SetActive(self.ui.m_goTriggerGift,false)
            return
        end
        SetActive(self.ui.m_goTriggerGift,true)
        local data = TriggerGiftConfig:GetDataByID(gift.id)
        if data.main_icon then
            SetUIImage(self.ui.m_imgTriggerGiftIcon,data.main_icon,false)
        end
        self.triggerGiftTime = gift.time or 0
    end
    local time = self.triggerGiftTime - TimeMgr:GetServerTime()
    if time > 0 then
        self.ui.m_txtTriggerGift.text = TimeMgr:CheckHMS(time)
    else
        self.ui.m_txtTriggerGift.text = ""
        self.triggerGiftTime = nil
    end
end

function M:PlayTriggerGiftFlyEff(id)
    if nil ~= id then
        local data = TriggerGiftConfig:GetDataByID(v2n(id))
        if data and data.main_icon then
            local toPos = UIMgr:GetObjectScreenPos(self.ui.m_goTriggerGift.transform)
            EffectConfig:CreateEffect(136, toPos.x, toPos.y, 0,UIMgr.layers[UILayerType.Top])
            MapController:FlyUIAnimByImg(0,0,data.main_icon, 1,toPos.x, toPos.y,nil,nil,nil,nil,FlyResourceType.FlyNone,function ()

            end)
        end
    end
end

-------------------------------------------------------------------------------
--------------------------------- 每日礼包免费礼包领取 ---------------------------------------------
function M:GiftDailyFreeTimer()
    local freeDayRestTime = NetGiftDaily:GetFreeDayRestTime()
    local nowTime = TimeMgr:GetServerTimestamp()
	if freeDayRestTime - nowTime < 0 then
        NetGiftDaily:ResetFreeDayState()
        self:CheckActCenterRed()
    end
end
------------------------------------------------------------------------------------------------
--右下角折叠区域弹出动效
function M:ShowRBFoldPop()
    local width = 0
    if self.rightBottomArowIsPut then
        width = 167
    else
        local childCount = self.rbLayoutTrans.childCount
        local activeCount = 0
        for i = 0,childCount - 1 do
            local child = self.rbLayoutTrans:GetChild(i)
            if child.gameObject.activeInHierarchy then
                activeCount = activeCount + 1
            end
        end
        width = activeCount*120+46
    end
    local y = self.rbBgRect.sizeDelta.y
    self.rbBgRect:DOSizeDelta(Vector2.New(width,y),0.3,true):SetEase(Ease.OutBack)
end

function M:OnRedPointDirty(dirtyIdSet)
	if dirtyIdSet[RedID.HeroRedEntry] then
		SetActive(self.ui.m_goHeroRed, RedPointMgr:IsRed(RedID.HeroRedEntry));
	end
	if dirtyIdSet[RedID.BagRedEntry] then
		SetActive(self.ui.m_goBagRed, RedPointMgr:IsRed(RedID.BagRedEntry));
	end
	if dirtyIdSet[RedID.LotteryEntry] then
		SetActive(self.ui.m_goLotteryRed, RedPointMgr:IsRed(RedID.LotteryEntry))
	end
	if dirtyIdSet[RedID.MailRoot] then
		self:SetListRedPoint()
	end
    if dirtyIdSet[RedID.TradeWagons] then
        SetActive(self.ui.m_goTradeWagonsRedPoint, RedPointMgr:IsRed(RedID.TradeWagons))
    end
    if dirtyIdSet[RedID.ArenaEntry] then
        SetActive(self.ui.m_goArenaRedPoint, RedPointMgr:IsRed(RedID.ArenaEntry))
    end
    if dirtyIdSet[RedID.WorldBoss] then
		SetActive(self.ui.m_goWorldBossRed, RedPointMgr:IsRed(RedID.WorldBoss))
	end
	if dirtyIdSet[RedID.BattlePass] then
		SetActive(self.ui.m_goBattleRed, RedPointMgr:IsRed(RedID.BattlePass))
	end
    if dirtyIdSet[RedID.DungeonEntry] then
        SetActive(self.ui.m_goFullBoxDot, RedPointMgr:IsRed(RedID.DungeonEntry))
    end
    if dirtyIdSet[RedID.TopFight] then
        SetActive(self.ui.m_goTopFightRed, RedPointMgr:IsRed(RedID.TopFight) or NetLimitActGift:IsShowRed(TopFightManager.GIFT_TYPE))
    end
end

function M:OnDungeonUpdate()
    self:updateActivityGroup()
end


function M:OnHeroChange(changeIdList, isAddHero)
	if isAddHero then
		local openBagState = HeroManager:IsHeroActive(GlobalConfig.OPEN_SLG_BAG);
		--self.rightBottomList[FolderItemType.SLGBag].isOpen = openBagState
		--SetActive(self.rightBottomList[FolderItemType.SLGBag].go, openBagState)

		local openHeroState = HeroManager:IsHeroActive(GlobalConfig.OPEN_SLG_HERO);
		self.openHeroState = openHeroState
		local lockObj = GET_UI(self.ui.m_goHero, "imgLock")
		SetActive(lockObj,not openHeroState)
		SetUIObjGray(self.ui.m_goHero, not openHeroState)
		--SetActive(self.ui.m_goHero, openHeroState)

		SetActive(self.ui.m_goPlayerPower, NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.LevelEnter))
		--self:FoldRightBottom()
	end

end

-------------------------------------------关卡系统----------------------------------------------
--初始化关卡系统
function M:InitLevelTimer()
    local level = DungeonManager:GetLevelId()
    self.MaxHangTime = DungeonManager:GetMaxHangTime()
    self.hangUpTime1,self.hangUpTime2 = DungeonManager:GetHangConfigTime()
    --if level == 1 then
    --    self.canTimer = false
    --    self.levelTimeTxt.text = "00:00:00"
    --    SetActive(self.ui.m_goFullBoxDot,false)
    --    SetUIImage(self.ui.m_imgBoxIcon,self:GetBoxSprite(1),false)
    --else
    --    self.canTimer = true
    --    self.levelHangTime = TimeMgr:GetServerTime() - DungeonManager:GetTimestamp()
    --    self.str1 = LangMgr:GetLang(70000086)
    --    self.levelTimeTxt.text = self.levelHangTime >= self.MaxHangTime and self.str1 or TimeMgr:ConverSecondToString(self.levelHangTime)
    --end
    self:CheckIsCanLevelTimer()
    self:CheckLotteryEntry()
end

function M:CheckIsCanLevelTimer()
    local isCanGet = DungeonManager:GetIsCanGetFreeTicket()
    if not isCanGet then
        SetActive(self.levelTimeTxt,true)
        self.canTimer = true
        local nowTime = TimeMgr:GetServerTimestamp()
        --下一个领取时间戳
        local nextTimeStamp = DungeonManager:GetNextGetTimeStamp()
        self.levelHangTime = nextTimeStamp - nowTime
        self.levelTimeTxt.text = TimeMgr:ConverSecondToString(self.levelHangTime)
    else
        self.canTimer = false
        SetActive(self.levelTimeTxt,false)
    end
end

--关卡系统倒计时逻辑
function M:CheckLevelTimer()
    if self.canTimer then
        self.levelHangTime = self.levelHangTime - 1
        self.levelTimeTxt.text = TimeMgr:ConverSecondToString(self.levelHangTime)
        --self:CheckLevelEnterIcon(self.levelHangTime)
    end
end

---判断关卡系统入口状态贴图
---在0-1小时之间，显示空宝箱；
---在1-4小时之间，显示开启一半的宝箱
---在4-挂机时间上限，显示满载宝箱
function M:CheckLevelEnterIcon(time)
    local status = self.levelBoxStatus
    if time < self.hangUpTime1 then
        status = 1
    elseif time >= self.hangUpTime1 and time < self.hangUpTime2 then
        status = 2
    else
        status = 3
    end

    SetActive(self.ui.m_goFullBoxDot,DungeonManager:CheckLevelEnterRedDot())
    if status == self.levelBoxStatus then
        return
    end
    self.levelBoxStatus = status
    SetUIImage(self.ui.m_imgBoxIcon,self:GetBoxSprite(self.levelBoxStatus),false)
    --SetActive(self.ui.m_goFullBoxDot,time >= self.hangUpTime1)
end

--获取关卡入口宝箱贴图
function M:GetBoxSprite(status)
    return "Sprite/ui_mainface/mainface2_huodong_PVE_jl"..status..".png"
end

--判断抽卡入口是否解锁
function M:CheckLotteryEntry()
    if self.rightBottomList and self.rightBottomList[FolderItemType.Lottery] then
        local openLotteryState = LotteryManager:CheckLotteryEntryUnlock()
        self.rightBottomList[FolderItemType.Lottery].isOpen = openLotteryState
        SetActive(self.rightBottomList[FolderItemType.Lottery].go, openLotteryState)
        self:FoldRightBottom()
    end
end
------------------------------------------------------------------------------------------------

function M:BagChange(data)
    self.ui.m_txtDiamond.text = NetUpdatePlayerData:GetResourceNumByID(ItemID.DIAMOND)
end


function M:BattlePassChange()
	local isShow = BattlePassManager:IsShowEnter()
	SetActive(self.ui.m_goBattle,isShow)
end

function M:RoleGiftChange(_roleGiftList)
	local roleGiftList = _roleGiftList or BagManager:GetRoleGiftList()
	if roleGiftList then
		SetActive(self.ui.m_goRoleGift,table.count(roleGiftList)>0)
		self.roleGiftId = BagManager:GetShowRoleGiftId()
		local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_role_gift,self.roleGiftId)
		if config then
			SetUIImage(self.ui.m_imgRoleGift,config.gift_role,false)
			local giftData = BagManager:GetRoleGiftByGiftId(self.roleGiftId)
			SetActive(self.ui.m_goGiftClock,giftData.open_timestamp ~= 0)
			local pos = self.ui.m_txtRoleGift.transform.localPosition
			if giftData.open_timestamp == 0 then
				self.ui.m_txtRoleGift.transform.localPosition = Vector3(0,pos.y,pos.z)
			else
				self.ui.m_txtRoleGift.transform.localPosition = Vector3(12,pos.y,pos.z)
			end
			--self.ui.txtRoleGift.gameObject.transform.rect.
		end
	else
		SetActive(self.ui.m_goRoleGift,false)
	end
end

function M:UpdateRoleGift()
	if self.roleGiftId then
		local giftData = BagManager:GetRoleGiftByGiftId(self.roleGiftId)
		if giftData then
			local time = giftData.open_timestamp == 0 and LangMgr:GetLang(70000274) or TimeMgr:CheckHMSNotEmpty(giftData.open_timestamp - TimeMgr:GetServerTimestamp())
			self.ui.m_txtRoleGift.text = time
		end
	end
end

return M