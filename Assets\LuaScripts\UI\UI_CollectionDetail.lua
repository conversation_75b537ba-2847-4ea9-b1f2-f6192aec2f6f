local UI_CollectionDetail = Class(BaseView)

function UI_CollectionDetail:OnInit()
    self.foldRedDot = self.ui.m_transFoldDot

    self.scrollRect = GetChild(self.uiGameObject,"Bg/Scroll View",UEUI.ScrollRect)
    self.contentRect = GetComponent(self.ui.m_transContent,UE.RectTransform)
    
    SetActive(self.ui.m_goListItem,false)
    SetActive(self.ui.m_goGridItem,false)
end

function UI_CollectionDetail:OnCreate(param)
    self.configList = param.collection_ids
    self.ui.m_txtPanelTitle.text = LangMgr:GetLang(param.name)
    SetActive(self.ui.m_goRoleCollection,false)
    for _,v in ipairs(self.configList) do
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.collection_items,v)
        if config then
            if config.hero_id then
                self:DisplayRoleCollection(config)
            else
                self:DisplayNormalCollection(config)
            end
        end
    end
    self:FocusRewardItem()
end

function UI_CollectionDetail:OnRefresh(param)
    
end

function UI_CollectionDetail:onDestroy()
    self.contentRect:DOKill();
    self.contentRect = nil
end

function UI_CollectionDetail:onUIEventClick(go,param)
    local name = go.name
    if name == "btnClose" then
        UI_UPDATE(UIDefine.UI_CollectionNew,NetCollection.UpdateType.RefreshCollectScore)
        self:Close()
    end
end

--********************************业务逻辑********************************
--自适应列表栏
function UI_CollectionDetail:AutoFitListItem(obj,count)
    local rowCount = math.ceil(count/4)

    local padding = 30*(rowCount-1)
    padding = (padding > 0) and padding or 0
    local contentHeight = 208*rowCount+padding+40
    local height = 100 + contentHeight
    local bgHeight = 58 + contentHeight
    
    local itemRect = GetComponent(obj,UE.RectTransform)
    local bgRect = GetChild(obj,"Bg",UE.RectTransform)
    
    itemRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical, height);
    bgRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical, bgHeight);
end

--展示普通图鉴
function UI_CollectionDetail:DisplayNormalCollection(config)
    local obj = UEGO.Instantiate(self.ui.m_goListItem,self.ui.m_transContent)
    local name = GetChild(obj,"Title/txt",UEUI.Text)
    local gridList = GetChild(obj,"GridList",UE.Transform)
    name.text = LangMgr:GetLang(config.name_id)
    self:InitGridInfo(config.materialList,gridList,(config.is_res and config.is_res == 1))
    self:AutoFitListItem(obj,#config.materialList)
    SetActive(obj,true)
end

--展示有角色相关图鉴（特殊处理）
function UI_CollectionDetail:DisplayRoleCollection(config)
    local obj = self.ui.m_goRoleCollection
    local name = GetChild(obj,"Bg1/Title/txt",UEUI.Text)
    local desc = GetChild(obj,"Bg1/Desc",UEUI.Text)
    local btnShowAll = GetChild(obj,"Bg1/ShowAll",UEUI.Button)
    local btnFoldAll = GetChild(obj,"Bg1/FoldAll",UEUI.Button)
    local roleIcon = GetChild(obj,"Bg1/Role/Icon",UEUI.Image)
    local bg2 = GetChild(obj,"Bg2")
    local bg2Rect = GetComponent(bg2,UE.RectTransform)
    local gridList = GetChild(obj,"Bg2/GridList",UE.Transform)
    local bgRect = GetComponent(obj,UE.RectTransform)
    local bg1Sprite = GetChild(obj,"Bg1",UEUI.Image)
    
    SetActive(bg2,false)
    SetActive(btnShowAll,true)
    SetActive(btnFoldAll,false)
    
    desc.text = LangMgr:GetLang(config.descript_id)
    name.text = LangMgr:GetLang(config.name_id)
    --SetUIImage(bg1Sprite,"Sprite/ui_tujian/tujian_erdikuang3.png",false)
    
    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, config.hero_id)
    if itemConfig then
        SetUIImage(roleIcon, itemConfig["icon_b"], false)
    end
    
    local CheckRoleDetail = function(isShowAll)
        local originalHeight = 400
        if isShowAll then
            for i = 0, gridList.childCount - 1 do
                UEGO.Destroy(gridList:GetChild(i).gameObject)
            end
            self:InitGridInfo(config.materialList,gridList)
            local rowCount = math.ceil(#config.materialList/4)
            Log.Error(rowCount)
            local padding = 30*(rowCount-1)
            padding = (padding > 0) and padding or 0
            local height = 30+200*rowCount+padding+35
            bgRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical, originalHeight+height);
            bg2Rect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical, height);
        else
            bgRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical, originalHeight);
        end
        local sprite = isShowAll and "tujian_erdikuang5" or "tujian_erdikuang3"

        --SetUIImage(bg1Sprite,"Sprite/ui_tujian/"..sprite..".png",false)
        SetActive(btnShowAll,not isShowAll)
        SetActive(btnFoldAll,isShowAll)
        SetActive(bg2,isShowAll)
        SetUIForceRebuildLayout(self.ui.m_transContent.gameObject)
    end
    
    RemoveUIComponentEventCallback(btnShowAll, UEUI.Button)
    AddUIComponentEventCallback(btnShowAll, UEUI.Button, function()
        CheckRoleDetail(true)
    end)
    
    RemoveUIComponentEventCallback(btnFoldAll, UEUI.Button)
    AddUIComponentEventCallback(btnFoldAll, UEUI.Button, function()
        CheckRoleDetail(false)
    end)
    SetActive(obj,true)
    
    self.roleConfigId = config.id
    SetActive(self.foldRedDot,self:CheckFoldRedDot(self.roleConfigId))
end

--初始化格子item信息
function UI_CollectionDetail:InitGridInfo(materialList,gridParent,isResType)
    local childCount = #materialList
    for k,v in ipairs(materialList)do
        local gridItem = UEGO.Instantiate(self.ui.m_goGridItem,gridParent)
        local icon = GetChild(gridItem,"Icon",UEUI.Image)
        local bg = GetChild(gridItem,"Bg",UEUI.Image)
        local name = GetChild(gridItem,"Name",UEUI.Text)

        name.text = ItemConfig:GetLangByID(v)
        SetUIImage(icon,ItemConfig:GetIcon(v),false)
       
        self:CheckCollectStatus(v,gridItem,isResType)
        
        local isBest = (childCount == k)
        if isBest then
            local arrow = GetChild(gridItem,"Arrow")
            SetActive(arrow,false)
        end

        local sprite = isBest and "tujian_dikuang_wujian1" or "tujian_dikuang_wujian"
        SetUIImage(bg,"Sprite/ui_tujian/"..sprite..".png",false)
        
        SetActive(gridItem,true)
    end
end

--判断图鉴的收集状态
function UI_CollectionDetail:CheckCollectStatus(id,obj,isResType)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, id)
    if config == nil then
       return
    end
    
    local flag = GetChild(obj,"Flag")
    local lock = GetChild(obj,"Lock")
    local reward = GetChild(obj,"Reward")
    local icon = GetChild(obj,"Icon",UEUI.Image)
    local btnGetReward = GetChild(obj,"Reward/GetReward",UEUI.Button)
    local rewardAni = GetChild(reward,"GetReward",UE.Animation)
    local tipObj = GetChild(obj,"Tip")
    
    local typeUse = config.type_use
    local showFlag = false
    local sprite = ""
    if typeUse == 12 then
        showFlag = true
        sprite = "StatusIcon-harvest"
    elseif typeUse == 2 then
        showFlag = true
        sprite = "StatusIcon-collect"
    end
    if showFlag then
        SetUIImage(flag,"Sprite/ui_public/"..sprite..".png",false)
    end
    
    --是否已经有了这个元素
    local hasOwn = CollectionItems:CheckResTypeUnlock(isResType,id)
    local canReceive = false
    if hasOwn  then
        local matState = NetCollection:get_ItemState(id)
        if not matState then
            rewardAni:Play()
            canReceive = true
        end
    end

    icon.color = hasOwn and Color.New(1, 1, 1, 1) or Color.New(1, 1, 1, 0.5)
    SetUIImageGray(icon, not hasOwn)
    SetActive(reward, canReceive)
    SetActive(lock, not hasOwn)
    SetActive(icon, not canReceive)
    SetActive(flag, not canReceive and showFlag)
    
    RemoveUIComponentEventCallback(btnGetReward, UEUI.Button)
    AddUIComponentEventCallback(btnGetReward, UEUI.Button, function()
        self:GetGoodsReward(btnGetReward, config.unlock_coins or 0, id, config)
        
        local list = {
            [1] = {id = ItemID.COIN;num = config.unlock_coins};
        }
        local collectionScore = config.collection_new_score
        if collectionScore and collectionScore > 0 then
            table.insert(list,{id = 70003;num = collectionScore})
        end
        MapController:FlyTip(0,120,list,tipObj.transform)
        SetActive(reward, false)
        SetActive(self.foldRedDot,self:CheckFoldRedDot(self.roleConfigId))
        SetActive(icon, true)
        SetActive(flag, showFlag)
        UI_UPDATE(UIDefine.UI_CollectionNew,NetCollection.UpdateType.ReceiveUnlockReward)
        UI_UPDATE(UIDefine.UI_CollectionCenter,"RefreshRedDot",1)
    end)
end

function UI_CollectionDetail:GetGoodsReward(obj, num, itemId, config)
    local matState = NetCollection:get_ItemState(itemId)
    if not matState then
        NetNotification:NotifyCoin(1,num)
        NetCollection:set_ItemState(itemId , true)
        local flyPos = UIMgr:GetObjectScreenPos(obj.transform)
        MapController:AddResourceBoomAnim(flyPos.x, flyPos.y, ItemID.COIN, num , false)
        NetUpdatePlayerData:AddResource(PlayerDefine.Coin, num,true,nil,"UI_Collection")
    end
    --self:RefreshData(itemId)
end

--判断角色图鉴处红点
function UI_CollectionDetail:CheckFoldRedDot(id)
    local curMapID = NetUpdatePlayerData.playerInfo.curMap
    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.collection_items,id)
    if itemConfig then
        for m,n in ipairs(itemConfig.materialList) do
            local itemCount = NetMapNoteData:GetNoteCount(curMapID,NetMapNoteData.ID.item_has,n)
            if itemCount > 0 then
                local matState = NetCollection:get_ItemState(n)
                if not matState then
                    return true
                end
            end
        end
    end
    return false
end

--聚焦定位到可领奖的item的位置
function UI_CollectionDetail:FocusRewardItem()
    local targetRowIndex = self:GetFocusRow()
    self:ScrollToTarget(targetRowIndex)
end

--获取需要聚焦的行数
function UI_CollectionDetail:GetFocusRow()
    for k,v in ipairs(self.configList) do
        local canReceive = NetCollection:CheckSingleLinkReward(v)
        if canReceive then
            return k
        end
    end
    return 0
end

--（动画表现）滑动到可领奖位置
function UI_CollectionDetail:ScrollToTarget(rowIndex)
    SetUIForceRebuildLayout(self.contentRect)
    local maxRow = #self.configList
    if rowIndex >= maxRow then
        self.scrollRect.normalizedPosition = Vector2.New(0,0);
    else
        local height = 0
        local padding = 20
        for i = 0 ,rowIndex-2 do
            local child = self.ui.m_transContent:GetChild(i)
            local childRect = GetComponent(child,UE.RectTransform)
            SetUIForceRebuildLayout(childRect)
            height = height + childRect.rect.height+padding;
        end
        self.contentRect:DOAnchorPos(Vector2.New(0, height), rowIndex*0.1);
    end
end

return UI_CollectionDetail