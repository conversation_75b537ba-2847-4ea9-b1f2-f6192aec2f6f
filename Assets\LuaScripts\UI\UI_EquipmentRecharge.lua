local UI_EquipmentRecharge = Class(BaseView)
local SlideRect = require("UI.Common.SlideRect")
local ItemBase = require("UI.Common.BaseSlideItem")
local RewardItem = Class(ItemBase)
local BagBase = require("UI.BagItem")
local BagItem = Class(BagBase)

local DelayTime = 0.1
local CurDelayTime = 0.1
local DefaultEndPos = { x = 853.5, y = -261.6 }

function UI_EquipmentRecharge:OnInit()
    
end

function UI_EquipmentRecharge:OnCreate(param)
    self.rewards = param
    self:InitRewardList()
    self:RefreshRewardList()

    local row = #self.rewardSlider.datas
    if row == 1 then
        local verticalLayoutGroup = self.ui.m_transContent:GetComponent(typeof(UEUI.VerticalLayoutGroup))
        verticalLayoutGroup.enabled = true
    end

    -- 动画过程中不能拖动滚动视图
    self.ui.m_scrollviewReward.vertical = false
end

function UI_EquipmentRecharge:OnRefresh(type, param)
    if type == 1 then
        if not self.ui then return end
        -- 前两行不用滑动
        if param >= 3 then
            self.rewardSlider:MoveToIndex(param, 0.8)
        end
        -- 动画结束后可以拖动滚动视图
        if self.rowCount == param then
            self.ui.m_scrollviewReward.vertical = true
        end
    end
end

function UI_EquipmentRecharge:onDestroy()
    Tween.Kill("AutoMoveFunc")
    self:FlyAnim()
end

function UI_EquipmentRecharge:onUIEventClick(go,param)
    local name = go.name

end

--- 根据列数分割数据
--- @param data table 原始数据
--- @param column number|nil 列数
--- @return table result 分割数据
function UI_EquipmentRecharge:SplitDataByColumns(data, column)
    local result = {}
    local group = {}
    local count = 0
    local curColumn = column or 5
    for index, value in ipairs(data) do
        table.insert(group, value)
        count = count + 1
        -- 当前分组已满 或 循环结束
        if count >= curColumn or index == #data then
            table.insert(result, group)
            -- 新分组
            group = {}
            count = 0
        end
    end
    return result
end

--- 初始化奖励列表
function UI_EquipmentRecharge:InitRewardList()
    -- 初始化组件
    SetActive(self.ui.m_goRewardItem, false)
    self.rewardSlider = SlideRect.new()
    self.rewardSlider:Init(self.ui.m_scrollviewReward, 2)
    self.rewardList = {}
    for i = 1, 4 do
        self.rewardList[i] = RewardItem.new()
        self.rewardList[i]:Init(UEGO.Instantiate(self.ui.m_goRewardItem.transform))
        self.rewardList[i]:SetCallback(function (type, param)
            self:OnRefresh(type, param)
        end)
    end
    self.rewardSlider:SetItems(self.rewardList, 20, Vector2.New(0, 0))
end

--- 刷新奖励列表
function UI_EquipmentRecharge:RefreshRewardList()
    local data = {}
    CurDelayTime = DelayTime
    for _, value in ipairs(self.rewards) do
        local typeUse = ItemConfig:GetTypeUse(value.code)
        local bagItemMoudle = BagItemModule.new({
            id = value.code,
            num = value.amount
        })
        if typeUse == ItemUseType.BagHeroClothes then
            for i = 1, value.amount, 1 do
                bagItemMoudle.equipmentData = EquipmentModule.new({
                    equip_id = "",
                    code = value.code,
                    amount = 1,
                    level = 0,
                    star = 0,
                    target_id = 0
                })
                bagItemMoudle.num = 1
                table.insert(data, bagItemMoudle)
            end
        else
            table.insert(data, bagItemMoudle)
        end
    end
    table.sort(data, function (a, b)
        if a.id ~= b.id then
            return a.id > b.id
        elseif a.quality ~= b.quality then
            return a.quality > b.quality
        end
        return false
    end)
    local result = self:SplitDataByColumns(data, 5)
    self.rewardSlider:SetData(result)

    self.rowCount = #self.rewardSlider.datas

    -- 底部添加内边距
    local rectContent = GetComponent(self.ui.m_transContent, UE.RectTransform)
    local width = rectContent.rect.width
    local height = rectContent.rect.height
    local bottom = 40
    rectContent.sizeDelta = Vector2.New(width, height + bottom)
end

--- 计算奖励
--- @param rewardList table 奖励列表
--- @param rewardStr string 奖励字符串
function UI_EquipmentRecharge:ComputeRewardReward(rewardList, rewardStr)
    local breakRewardList = string.split(rewardStr, ";")
    for _, reward in ipairs(breakRewardList) do
        local itemTable = string.split(reward, "|")
        local itemID = v2n(itemTable[1])
        local itemNum = v2n(itemTable[2])

        local typeUse = ItemConfig:GetTypeUse(itemID)
        if typeUse == ItemUseType.BagHeroClothes then
            for i = 1, itemNum, 1 do
                local bagItemMoudle = BagItemModule.new({
                    id = itemID,
                    num = itemNum
                })
                bagItemMoudle.equipmentData = EquipmentModule.new({
                    equip_id = "",
                    code = itemID,
                    amount = 1,
                    level = 0,
                    star = 0,
                    target_id = 0
                })
                table.insert(rewardList, bagItemMoudle)
            end
        else
            self:AddRewardList(rewardList, itemID, itemNum)
        end
    end
end

function UI_EquipmentRecharge:FlyAnim()
    if self.rewardList then
        for i = 1, #self.rewardList, 1 do
            self.rewardList[i]:FlyAnim()
        end
    end
end

function RewardItem:OnInit(transform)
    self.group = GetChild(transform, "group")
    self.itemList = {}
    for i = 1, 6, 1 do
        local bagItem = BagItem.new()
        bagItem:Create(self.group)
        bagItem:SetScale(0.95, 0.95)
        SetActive(bagItem.go, true)
        table.insert(self.itemList, bagItem)
    end
    self.animList = {}
end

function RewardItem:UpdateData(data, index)
    if not data then return end
    self.data = data
    self.index = index
    for itemIndex, item in ipairs(self.itemList) do
        if self.data[itemIndex] then
            item:UpdateInfo(self.data[itemIndex])
            item.itemName.text = ItemConfig:GetLangByID(self.data[itemIndex].id)
            SetActive(item.itemName, true)
            SetActive(item.go, true)
            local animIndex = self.index * 7 + itemIndex
            -- 未播放过动画的物品
            if not self.animList[animIndex] then
                self.animList[animIndex] = true
                item.go.transform:DOKill()
                item.go.transform.localScale = Vector3.zero
                -- 控制动画节奏，从第 5 行开始，延迟时间不能太长
                if self.index >= 5 and itemIndex == 1 then
                    CurDelayTime = 0.8
                end
                DOScale(item.go.transform, 1.1, 0.2, function ()
                    DOScale(item.go.transform, 0.95, 0.2, function ()
                        DOScale(item.go.transform, 0.98, 0.08, function ()
                            DOScale(item.go.transform, 0.95, 0.08, function ()
                                -- 滚动到下一行
                                -- UI_UPDATE(UIDefine.UI_EquipmentRecharge, 1, self.index)

                                -- 因为 Model 的 type 改成了 UIType.Dialog，导致 UI_UPDATE 不能执行，所以采取传入回调的方式执行
                                -- 改成 UIType.Dialog 的原因，是可能网络卡顿，协议回调多次打开此界面，导致物品重叠
                                self:InvokeCallback(1, self.index)
                            end, Ease.OutCubic)
                        end, Ease.OutCubic)
                    end, Ease.OutCubic)
                    CurDelayTime = CurDelayTime - DelayTime
                end, Ease.OutCubic, CurDelayTime)
                CurDelayTime = CurDelayTime + DelayTime
            end
        else
            SetActive(item.go, false)
        end
    end
end

function RewardItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function RewardItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function RewardItem:Refresh()
    self:UpdateData(self.data, self.index)
end

function RewardItem:FlyAnim()
    if not self.data then return end
    for itemIndex, item in ipairs(self.itemList) do
        if self.data[itemIndex] then
            local itemID = v2n(self.data[itemIndex].id)
            if itemID <= ItemID._RESOURCE_MAX then
                local flyPos = UIRectPosFit(item.go)
                MapController:AddResourceBoomAnim(flyPos[1], flyPos[2],
                itemID, self.data[itemIndex].num, false)
            else
                local flyPos = UIRectPosFit(item.go)
                local endPos = EquipmentManager:GetFlyEndPos(itemID) or DefaultEndPos
                local icon = ItemConfig:GetIcon(itemID)
                MapController:FlyUIAnimByImg(flyPos[1], flyPos[2],
                icon, self.data[itemIndex].num, endPos.x, endPos.y)
            end
        end
    end
end

function RewardItem:SetCallback(callback)
    self.callBack = callback
end

function RewardItem:InvokeCallback(type, param)
    if self.callBack then self.callBack(type, param) end
end

function BagItem:ClickItem()

end

return UI_EquipmentRecharge