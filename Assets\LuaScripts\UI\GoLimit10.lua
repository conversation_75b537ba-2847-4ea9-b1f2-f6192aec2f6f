local GoLimit10 = {}
local M = GoLimit10

local prePath = "Assets/ResPackage/Prefab/UI/GoLimit10.prefab"
local pre

function M:Create(parent,loadCallBack)
    local item = {}
    setmetatable(item,{__index = M})
    item.loadCallBack = loadCallBack
 
    local function callBack(obj)
        local newGo = CreateGameObjectWithParent(obj,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
        if item.loadCallBack then
            item.loadCallBack(item)
        end
    end
    ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,callBack)

    --return item
end

function M:Init()
    self.icon = GetChild(self.go,"bg/icon",UEUI.Image)
    self.icon2 = GetChild(self.go,"bg/icon2",UEUI.Image)
    self.red =  GetChild(self.go,"bg/goPoint")
    self.rank = GetChild(self.go,"bg/rank",UEUI.Text)
    self.scheduleText = GetChild(self.go,"bg/num",UEUI.Text)
    self.count = GetChild(self.go,"bg/CountDown/countTxt",UEUI.Text)
    self.showRed = false
    AddUIComponentEventCallback(GetComponent(self.go,UEUI.Button),UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
end

function M:SetItem(param)
	NetEndlessData:SetDataByKey("activityId",param.id)
    self.id = param.id
    self.totalType = param.totalType
    self.activeInfo = LimitActivityController:GetActiveMessage(self.id)
	local active = self.activeInfo.form.activeMess
    self.info = self.activeInfo.info
	self:ChangeValue()
end

function M:ChangeItem()
	local time = self.activeInfo:GetRemainingTime()
	if time <= 0 then 
		--self:ClickItemPush()
	end
    self.count.text = TimeMgr:CheckHMSNotEmpty(self.activeInfo:GetRemainingTime())
	if NetEndlessData:IsAllReward() then
		SetActive(self.go,false)
	else
		SetActive(self.go,true)
	end
	
end

function M:ChangeValue()
	local hasRed = NetEndlessData:CheckHaveRed()
	SetActive(self.red,hasRed)
end

function M:GetNowPointSch()
    --local pointsList = self.rankConfig.rankPoints
    --local nowSch = 0
    --local integral = self.info.integral
    --local temp
    --for i, v in ipairs(pointsList) do
        --temp = v2n(v)
        --if integral >= temp then
            --nowSch = i
        --else
            --break
        --end
    --end
    --if self.info.schedule < nowSch then
        --if not self.showRed then
            --SetActive(self.red,true)
            --self.showRed = true
        --end
    --else
        --if self.showRed then
            --SetActive(self.red,false)
            --self.showRed = false
        --end
    --end
end

function M:End()

end

function M:ClickItem(arg1)
	UI_SHOW(UIDefine.UI_Endless,self.id)
    --NetRankData:GetRankMess(self.id,self.activeInfo.info.integral,nil,nil,nil,true)
end

function M:ClickItemPush()
	self.isPush = true
	--NetRankData:GetRankMess(self.id,self.activeInfo.info.integral,true)
end

function M:Close()
    UEGO.Destroy(self.go)
end

return M