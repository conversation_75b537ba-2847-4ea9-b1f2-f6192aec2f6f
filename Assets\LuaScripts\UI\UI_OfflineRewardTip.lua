--挂机关卡奖励预览
local UI_OfflineRewardTip = Class(BaseView)
local SlideRect = require("UI.Common.SlideRect")
local ItemBase = require("UI.Common.BaseSlideItem")
local TipItem = Class(ItemBase)
local ItemShowNums = 8

local curLevelId = 0
local maxConfigLevel = 30

--关卡状态
local NodeStatus = {
    Current = 0;--当前关卡
    Lock = -1;--未解锁
    Finish = 1;--已通关
}

function UI_OfflineRewardTip:OnInit()


    ----------初始化界面相关逻辑----------
    SetActive(self.ui.m_goPrefab,false)
end

function UI_OfflineRewardTip:OnCreate(param)
    --当前挑战关卡
    curLevelId = DungeonManager:GetLevelId()
    self.configData = ConfigMgr:GetData(ConfigDefine.ID.slg_dungeon)
    if not self.configData then
        Log.Error("slg_dungeon 配表数据读取错误！")
        return
    end
    maxConfigLevel = #self.configData
    self.ui.m_scrollviewMsg.onValueChanged:AddListener(function(vec)
        self.ui.m_scrollviewPro.content.anchoredPosition = self.ui.m_scrollviewMsg.content.anchoredPosition
    end);
    ----------初始化业务逻辑----------
    self:InitScrollView()
    self:ShowDescList()
end

function UI_OfflineRewardTip:OnRefresh(param)
    
end

function UI_OfflineRewardTip:onDestroy()
    
end

function UI_OfflineRewardTip:onUIEventClick(go,param)
    local name = go.name
    if name == "btnReturn" then
        self:Close()
    end
end

--********************************业务逻辑********************************
--初始化收益描述列表
function UI_OfflineRewardTip:InitScrollView()
    self.tipSlider = SlideRect.new()
    self.tipSlider:Init(self.ui.m_scrollviewMsg,2)
    self.playerList = {}
    for i = 1, ItemShowNums do
        self.playerList[i] = TipItem.new()
        self.playerList[i]:Init(UEGO.Instantiate(self.ui.m_goOfflineRewardItem.transform))
    end
    self.tipSlider:SetItems(self.playerList, 2, Vector2.New(0, 0))
end

--展示收益列表
function UI_OfflineRewardTip:ShowDescList()
    local leftCount = 5
    local rightCount = 14
    local sumCount = 20
    
    local min,max
    if curLevelId <= leftCount then
        min,max = 1,sumCount
    elseif curLevelId >= maxConfigLevel - rightCount then
        min,max = maxConfigLevel-sumCount,maxConfigLevel
    else
        min = curLevelId - leftCount
        max = curLevelId + rightCount
    end
    
    local list = {}
    local index = 0
    local targetIndex = 1
    for i = min,max do
        table.insert(list,self.configData[i])
        index = index + 1
        if i == curLevelId then
            targetIndex = index
        end
    end
    
    if curLevelId >= maxConfigLevel then
        targetIndex = sumCount
    end
    self.tipSlider:SetData(list)
    
    self.tipSlider:MoveToIndex(targetIndex,0.5)
    self:CalculateProgressContent(#list);
    
    if curLevelId > maxConfigLevel then
        self.ui.m_sliderValue.value = 1
    else
        local startRatio = 0.027
        local value = (1-startRatio)/(#list - 1)
        local ratioList = {}
        table.insert(ratioList,startRatio);
        for i = 1,#list - 1 do
            table.insert(ratioList,value);
        end
        local sumRatio = 0
        for i = 1,targetIndex do
            sumRatio = sumRatio + ratioList[i]
        end
        self.ui.m_sliderValue.value = sumRatio
    end
end

function UI_OfflineRewardTip:CalculateProgressContent(sumCount) 
    local itemHeight = GetComponent(self.ui.m_goOfflineRewardItem,UE.RectTransform).rect.height
    local sumHeight = sumCount* itemHeight
    local sliderRect = GetComponent(self.ui.m_sliderValue,UE.RectTransform)
    sliderRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Horizontal,sumHeight - 100);
    local msgHeight = self.ui.m_scrollviewMsg.content.rect.height
    self.ui.m_scrollviewPro.content:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical,msgHeight);
    --self.ui.m_scrollviewMsg.onValueChanged:RemoveAllListeners();
end    

--------------------------TipItem--------------------------
function TipItem:OnInit(transform) 
    self.transform = transform
    self.earningItem = GetChild(self.transform,"earningItem")
    self.rewardItem = GetChild(self.transform,"rewardItem")
    self.txt1 = GetChild(self.transform,"bg/txt1",UEUI.Text)
    self.txt2 = GetChild(self.transform,"finish/txt2",UEUI.Text)
    self.earningNode = GetChild(self.transform,"earningNode")
    self.earningNodeTrans = self.earningNode.transform
    self.rewardContent = GetChild(self.transform,"finish/Scroll View/Viewport/rewardContent")
    self.rewardContentTrans = self.rewardContent.transform
    self.giftHLayout = GetComponent(self.rewardContent,UEUI.GridLayoutGroup)
    self.giftRect = GetComponent(self.rewardContent,UE.RectTransform)
    self.rewardScrollRect = GetChild(self.transform,"finish/Scroll View",UEUI.ScrollRect)
    self.leftBg = GetChild(self.transform,"bottom/leftBg",UEUI.Image)
    self.rightBg = GetChild(self.transform,"bottom/rightBg",UEUI.Image)
    self.bottomArrow = GetChild(self.transform,"bottom/arrow",UEUI.Image)
    self.bottomCurBg = GetChild(self.transform,"bottom/curBg")
    self.bottomLockBg = GetChild(self.transform,"bottom/lockBg")
    self.bottomFinishBg = GetChild(self.transform,"bottom/finishBg")
    self.flagBg = GetChild(self.transform,"flag",UEUI.Image)
    self.bottomNum = GetChild(self.transform,"flag/num",UEUI.Text)
    self.statusBg1 = GetChild(self.transform,"bg",UEUI.Image)
    self.statusBg2 = GetChild(self.transform,"finish",UEUI.Image)
    self.xian = GetChild(self.transform,"bg/xian",UEUI.Image)
    
    --概率掉落
    self.txt3 = GetChild(self.transform,"bg/txt3",UEUI.Text)
    self.dropScroll = GetChild(self.transform,"bg/dropScroll",UEUI.ScrollRect)
    self.sweepScroll = GetChild(self.transform,"bg/dropScroll2",UEUI.ScrollRect)
    self.dropContent = GetChild(self.transform,"bg/dropScroll/Viewport/rewardContent")
    self.sweepRewardContent = GetChild(self.transform,"bg/dropScroll2/Viewport/sweepRewardContent")
    self.dropContentTrans = self.dropContent.transform
    self.sweepContentTrans = self.sweepRewardContent.transform
    self.dropHLayout = GetComponent(self.dropContent,UEUI.GridLayoutGroup)
    self.dropRect = GetComponent(self.dropContent,UE.RectTransform)    
    self.sweepHLayout = GetComponent(self.sweepRewardContent,UEUI.GridLayoutGroup)
    self.sweepRect = GetComponent(self.sweepRewardContent,UE.RectTransform)
    
    SetActive(self.earningItem,false)
    SetActive(self.rewardItem,false)
end

function TipItem:UpdateData(data, index)
    --状态逻辑显示
    local status = self:CheckTipStatus(data.id)
    local hex
    local bgPath
    local flagPath
    local xianPath
    if status == NodeStatus.Current then
        hex = "#BF5D37"
        bgPath = "Sprite/ui_public/windows_list3_slg.png"
        flagPath = "Sprite/ui_huodongjingsai_damaoxian/dmx_pve_guaka2_2.png"
        xianPath = "Sprite/ui_huodongjingsai/dmx_guanqia_fbt2.png"
    elseif status == NodeStatus.Lock then
        hex = "#3A78CD"
        bgPath = "Sprite/ui_public/windows_list4.png"
        flagPath = "Sprite/ui_huodongjingsai_damaoxian/dmx_pve_guaka2_2_1.png"
        xianPath = "Sprite/ui_huodongjingsai/dmx_guanqia_fbt3.png"
    elseif status == NodeStatus.Finish then
        hex = "#1B8100"
        bgPath = "Sprite/ui_public/windows_list4_slg.png"
        flagPath = "Sprite/ui_huodongjingsai_damaoxian/dmx_pve_guaka2_3.png"
        xianPath = "Sprite/ui_huodongjingsai/dmx_guanqia_fbt4.png"
    end

    SetUIImage(self.statusBg1, bgPath, false)
    SetUIImage(self.statusBg2, bgPath, false)
    SetUIImage(self.flagBg, flagPath, false)
    SetUIImage(self.xian, xianPath, false)
    
    self.txt1.text = LangMgr:GetLang(70001004)
    self.txt2.text = LangMgr:GetLang(70000088)
    self.txt3.text = LangMgr:GetLang(246)

    if hex then
        local color = GetColorByHex(hex);
        self.txt1.color = color
        self.txt2.color = color
        self.txt3.color = color
    end
    
    --通关奖励生成+状态判断
    local rewardList = Split1(data.reward_first,";")
    self:HideOtherChild(self.rewardContentTrans,#rewardList)
    for k,v in ipairs(rewardList) do
        local item = self:ActiveChild(self.rewardContentTrans,k,v,self.rewardItem)
        local bg = GetChild(item,"bg",UEUI.Image)
        local icon = GetChild(item,"icon",UEUI.Image)
        local count = GetChild(item,"count",UEUI.Text)
        local btn = GetChild(item,"btn",UEUI.Button)
        local lock = GetChild(item,"lock")
        
        local temp = Split1(v,"|")
        local itemId = v2n(temp[1])
        count.text = NumToGameString(v2n(temp[2]))
        self:SetItemIcon(itemId,icon)
        self:SetItemQuality(itemId,bg)
        RemoveUIComponentEventCallback(btn,UEUI.Button)
        AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
            UI_SHOW(UIDefine.UI_ItemTips,itemId)
        end)
        --SetActive(lock,status == NodeStatus.Finish)
        SetActive(lock,false)
    end

    self:AutoFitGiftList(#rewardList,self.giftHLayout,self.giftRect)
    self.rewardScrollRect.enabled = #rewardList > 4
    --self.rewardScrollRect.horizontalNormalizedPosition = 0
    
    --掉落奖励(最多显示前四个掉落奖励)
    local dropId = v2n(data.sweep_drop)
    local dropConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_drop,dropId)
    if dropConfig then
        local temp = Split1(dropConfig.drop_list,";")
        local dropList = {}
        for i = 1,4 do
            local dataStr = temp[i]
            if dataStr then
                local key = Split1(dataStr,"|")[1]
                table.insert(dropList,key)
            end
        end
        self:HideOtherChild(self.dropContentTrans,#dropList)
        for k,v in ipairs(dropList) do
            local item = self:ActiveChild(self.dropContentTrans,k,v,self.rewardItem)
            local bg = GetChild(item,"bg",UEUI.Image)
            local icon = GetChild(item,"icon",UEUI.Image)
            local count = GetChild(item,"count",UEUI.Text)
            local btn = GetChild(item,"btn",UEUI.Button)
            local lock = GetChild(item,"lock")
            
            local itemId = v
            count.text = ""
            self:SetItemIcon(itemId,icon)
            self:SetItemQuality(itemId,bg)
            RemoveUIComponentEventCallback(btn,UEUI.Button)
            AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
                UI_SHOW(UIDefine.UI_ItemTips,itemId)
            end)
            SetActive(lock,false)
        end
        self:AutoFitGiftList(#dropList,self.dropHLayout,self.dropRect)
        self.dropScroll.enabled = #dropList > 4
        --self.dropScroll.horizontalNormalizedPosition = 0
        
        local temp2 = Split1(data.reward_sweep,";")
        local sweepDropList = {}
        for i, v in ipairs(temp2) do
            local dataStr = v
            if dataStr then
                local dataArr = Split1(dataStr,"|")
                local key = dataArr[1]
                local count = dataArr[2]
                table.insert(sweepDropList, { itemId = key,count =  count})
            end
        end
        self:HideOtherChild(self.sweepContentTrans,#sweepDropList)
        for k,v in ipairs(sweepDropList) do
            local item = self:ActiveChild(self.sweepContentTrans,k,v,self.rewardItem)
            local bg = GetChild(item,"bg",UEUI.Image)
            local icon = GetChild(item,"icon",UEUI.Image)
            local count = GetChild(item,"count",UEUI.Text)
            local btn = GetChild(item,"btn",UEUI.Button)
            local lock = GetChild(item,"lock")

            local itemId = v.itemId
            count.text = v.count
            self:SetItemIcon(itemId,icon)
            self:SetItemQuality(itemId,bg)
            RemoveUIComponentEventCallback(btn,UEUI.Button)
            AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
                UI_SHOW(UIDefine.UI_ItemTips,itemId)
            end)
            SetActive(lock,false)
        end
        self:AutoFitGiftList(#sweepDropList,self.sweepHLayout,self.sweepRect)
        self.sweepScroll.enabled = #sweepDropList > 4
        --self.sweepScroll.horizontalNormalizedPosition = 0
    end
    
    self.bottomNum.text = data.id

    SetActive(self.bottomCurBg,status == NodeStatus.Current)
    --SetActive(self.bottomLockBg,status == NodeStatus.Lock)
    SetActive(self.bottomLockBg,false)
    SetActive(self.bottomFinishBg,status == NodeStatus.Finish)

    local gray = self:GetSpritePath("dmx_guanqia_jindu2")
    local green = self:GetSpritePath("dmx_guanqia_jindu1")

    SetUIImage(self.leftBg,status == NodeStatus.Lock and gray or green,false)
    SetUIImage(self.rightBg,status ~= NodeStatus.Finish and gray or green,false)

    SetUIImage(self.bottomArrow,status == NodeStatus.Lock 
            and self:GetSpritePath("dmx_pve_guakajt2") 
            or self:GetSpritePath("dmx_pve_guakajt1"),false)
    
    SetActive(self.bottomArrow,index ~= 1)
end

function TipItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function TipItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function TipItem:onDestroy()
    UEGO.Destroy(self.transform.gameObject)
end

--激活若干子节点
function TipItem:ActiveChild(trans,index,info,item)
    local childCount = trans.childCount
    if index > childCount then
        local obj = UEGO.Instantiate(item,trans)
        SetActive(obj,true)
        return obj
    end
    local child = trans:GetChild(index-1)
    SetActive(child,true)
    return child
end

--隐藏所有子节点
function TipItem:HideOtherChild(trans,needCount)
    local childCount = trans.childCount
    if needCount >= childCount then
        return
    end
    for i = needCount-1,childCount-1 do
        local child = trans:GetChild(i)
        if child and child.gameObject.activeInHierarchy then
            SetActive(child,false)
        end
    end
end

--自动适配直购礼包奖励列表
function TipItem:AutoFitGiftList(count,layout,rect)
    local isOverflow = count > 4
    local value1,value2 = math.modf(count/2)
    if(value2 > 0)then
        value1 = value1 + 1
    end
    local height = isOverflow and (layout.padding.top+ layout.cellSize.y*count+layout.spacing.y*(count - 1)) or 205
    layout.childAlignment = isOverflow and UE.TextAnchor.UpperLeft or UE.TextAnchor.MiddleCenter
    rect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical, height)
end

--设置道具图标
function TipItem:SetItemIcon(itemId,icon)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,itemId)
    if config then
        SetUIImage(icon, config.icon_b, false)
    end
end

--获取关卡系统图片资源路径
function TipItem:GetSpritePath(str)
    return "Sprite/ui_huodongjingsai/"..str..".png"
end

--设置道具品质底图
function TipItem:SetItemQuality(itemId,icon)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,itemId)
    if config then
        local id = config.slg_quality or 1
        SetUIImage(icon, "Sprite/ui_huodongjingsai/dmx_guaji_daoju"..id..".png", false)
    end
end

--检测判断关卡状态
function TipItem:CheckTipStatus(id)
    if curLevelId > maxConfigLevel then
        --通关
       return NodeStatus.Finish
    end
    
    local status
    if id == curLevelId then
        status = NodeStatus.Current
    elseif id > curLevelId then
        status = NodeStatus.Lock
    elseif id < curLevelId then
        status = NodeStatus.Finish
    end
    return status
end

return UI_OfflineRewardTip