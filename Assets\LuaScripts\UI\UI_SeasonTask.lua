local UI_SeasonTask = Class(BaseView)

local ItemBase = require("UI.Common.BaseSlideItem")
local seasonTaskItem = Class(ItemBase)

function UI_SeasonTask:OnInit()
	self.aniIndex = 0
end

function UI_SeasonTask:OnCreate(areaLevel)
	local transform = self.uiGameObject.transform
	self.slider = require("UI.Common.SlideRect").new()
	self.slider:Init(transform:Find("state/viewPort"):GetComponent(typeof(UEUI.ScrollRect)),2)
	
	local season_items = {}
	local itemTrans = transform:Find("state/item")
	
	for i=1,5 do
		season_items[i] = seasonTaskItem.new()
		season_items[i]:Init(UEGO.Instantiate(itemTrans),true)
	end
	self.items = season_items
	self.slider:SetItems(season_items,12,Vector2.New(5,10))	 
	self.datas = self:GetCircleList()
	if next(self.datas)~= nil then
		table.sort(self.datas, function(a, b)
				return a.id < b.id
			end)
		self.slider:SetData(self.datas)
	end
	--self.aniIndex = table.count(self.datas)-1
	self.boxOpen = false
	self.ui.m_txtScore.text = NetUpdatePlayerData.playerInfo.seasonEnergy or 0
	self.areaLevel = areaLevel or NetUpdatePlayerData.playerInfo.seasonAreaLevel or 1
	local areaConfig = ConfigMgr:GetData(ConfigDefine.ID.island_area)
	local curAreaConif = nil 
	for k, v in pairs(areaConfig) do
		if self.areaLevel == v.id then
			curAreaConif = v 
			break
		end
	end
	
	if curAreaConif then
		SetUIImage(self.ui.m_imgBg,curAreaConif.area_pic_1,false) 
		--self.ui.m_txtTitle.text = LangMgr:GetLang(curAreaConif.name)
		
		self.rewardArr = curAreaConif.reward
		local btn = self.ui.m_goReward:GetComponent(typeof(UEUI.Button))
		--local btn = obj.transform:Find("go_reward"):GetComponent(typeof(UEUI.Button))
		if btn ~= nil then
			RemoveUIComponentEventCallback(btn,UEUI.Button)
			AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
					self:RefreshGoBox()
				end)
		end
		self.ui.m_txtTitle.text = LangMgr:GetLang(curAreaConif.name)
	end
	
	--进度
	local islandConfig = ConfigMgr:GetData(ConfigDefine.ID.island_task)
	local maxTimes = 0
	for k, v in pairs(islandConfig) do
		if v.task_area then
			if v.task_area == self.areaLevel then
				maxTimes = maxTimes + 1
			end
		end
	end
	local curTimes = NetSeasonIslandData.data.areaUpTimes or 0
	self.maxTimes = maxTimes
	self.ui.m_sliderWeek.value = curTimes/maxTimes
	self.ui.m_txtSeasonNum.text = math.floor((curTimes/maxTimes)*100).."%"
	
	if areaLevel ~= nil then
		self.ui.m_sliderWeek.value = 1
		self.ui.m_txtSeasonNum.text = "100%"
		local view = self.uiGameObject.transform:Find("state/viewPort")
		SetActive(view,false)
		self:ClickSeasonReward()
	end
end

function UI_SeasonTask:RefreshGoBox()
		local go = self.ui.m_goBox
		local rewardItem = self.ui.m_goRewardItem
		SetActive(go,false)
		if self.boxOpen then
			SetActive(go,false)
			self.boxOpen = false
		else
			self.boxOpen = true
			local arrList = string.split(self.rewardArr,";")
			local count = table.count(arrList)
			SetActive(go,true)
			if count > go.transform.childCount then
				for i = 1, count do
					local obj = UEGO.Instantiate(self.ui.m_goRewardItem)
					SetParent(obj, self.ui.m_goBox)		
					--obj.name = "rookie_btn" .. index
					obj.transform.localScale = Vector3.New(1, 1, 1)
					SetActive(obj,true)
					local itemIcon = GetChild(obj, "itemIcon",UEUI.Image)
					local itemText = GetChild(obj, "itemText",UEUI.Text)
					local arr = string.split(arrList[i],"|")
					local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item,tonumber(arr[1]))
					SetUIImage(itemIcon,itemConfig.icon_b,false)
					itemText.text = "x"..arr[2]
				end
			end
		end

end

function UI_SeasonTask:ClickSeasonReward()
	local areaConfig = ConfigMgr:GetData(ConfigDefine.ID.island_area)
	local curAreaConif = nil
	for k, v in pairs(areaConfig) do
		if self.areaLevel == v.id then
			curAreaConif = v
			break
		end
	end
	
	local Ani = GetComponent(self.ui.m_goBox_anim, UE.Animation)
	if Ani then	
		local function RewardEnd()						
			if curAreaConif.reward ~= nil then					
				local airList = {}
				local rewardArr = string.split(curAreaConif.reward,";")
				for k, v in pairs(rewardArr) do
					local item = string.split(v,"|")
					item.id = v2n(item[1])
					item.count = v2n(item[2])
					if item.id < ItemID._RESOURCE_MAX then
						--MapController:AddResourceBoomAnim(0,0, tonumber(item.id), 10 , false)
						NetUpdatePlayerData:AddResource(PlayerDefine[item.id], item.count,nil,nil,"seasonTask")
					else
						for i = 1, item.count do
							table.insert(airList,item.id)
						end
					end
				end
				if nil ~= next(airList) then
					MapController:SendRewardToMap(airList,nil,nil,nil,"SeasonTask")
				end
				
				local param = {}
				param.type = 13
				param.rewards = curAreaConif.reward
				
				local function LogEnd()
					--UIMgr:SetUILock(false, UIDefine.UI_SeasonTask)
					local function AreaEnd()
						UI_SHOW(UIDefine.UI_AreaComplete,curAreaConif)
					end
					if curAreaConif.dlg ~= nil then
						local log = NetPushViewData:SplitProcess(curAreaConif.dlg)
						
						UI_SHOW(UIDefine.UI_DialogView, nil, log,AreaEnd)
					else
						UI_SHOW(UIDefine.UI_AreaComplete,curAreaConif)
						--Log.Error(k1.."/"..v1.."   没有配置结束后对话")
					end
					if not IsNil(self.uiGameObject) then
						self:Close()
					end
				end
				
				UI_SHOW(UIDefine.UI_Recharge, param,LogEnd)
			end
		end
		
		AddDOTweenNumberDelay(0,1,2.1,0,function (value)
			
		end,RewardEnd)

		PlayAnimStatus(Ani,"season_reward")
	end
	--Log.Error("btn",curAreaConif.reward)
end
--找可以升级的装扮
function UI_SeasonTask:GetCircleList()
	local data = {}
	local list = MapController:GetItemByType(ItemUseType.SeasonIsland)
	for k, v in pairs(list) do
		if next(v.rootType) ~= nil then
			for k1, v1 in pairs(v.rootType) do
				for k2, v2 in pairs(v1) do
					if v:CheckCanLevel(tonumber(k2),tonumber(v2)) then
						local islandConfig = IslandTaskConfig:GetConfigByTypeAndLevel(k2,v2)
						if islandConfig ~= nil then
							table.insert(data,islandConfig)
						else
							Log.Error("找不到",v1.root_type,v1.root_level,"配置表")
						end
					end
				end
			end
		end
	end
	return data
end

function UI_SeasonTask:UpdateItem(data)
	
end

function UI_SeasonTask:OnRefresh(param,param2,param3)
    if param == 1 then
		
		--UIMgr:SetUILock(false, UIDefine.UI_SeasonTask)
		UIMgr:SetUILock(true, UIDefine.UI_SeasonTask)
		self:Close()
	elseif param == 2 then
		if param3 == nil then
			return 
		end
		local item = param3
		self.completeItem = param3
		--function p2_End3 ()
			--function lastEnd()
				--item:ClickCallBack()
			--end

			--local curTimes = NetSeasonIslandData.data.areaUpTimes
			--local starValue = (curTimes-1)/self.maxTimes
			--local endValue = curTimes/self.maxTimes
			--self.ui.m_txtSeasonNum.text = math.floor((curTimes/self.maxTimes)*100).."%"
			--AddDOTweenNumberDelay(starValue, endValue, 1, 0, function(value)
					--self.ui.m_sliderWeek.value = value

				--end,lastEnd)
		--end
		
		local function p2_End2 ()
			local datas = self:GetCircleList()
			--self.datas
			local insertT = {}
			local change = false
			for k, v in pairs(datas) do
				local id = tonumber(v.id)
				local insert = true
				for k1, v1 in pairs(self.datas) do
					if tonumber(v1.id) == id then
						insert = false
					end
				end
				if insert then
					table.insert(insertT,v)
					change = true
				end
			end
			if not change then
				UI_UPDATE(UIDefine.UI_SeasonTask,4)
				return
			else
				local parent = self.uiGameObject.transform:Find("state/viewPort/content")
				local itemTrans = self.uiGameObject.transform:Find("state/item")
				for k, v in pairs(insertT) do
					local index = 5 + k
					local seasonitem = seasonTaskItem.new()
					seasonitem:Init(UEGO.Instantiate(itemTrans),true)
					SetParent(seasonitem.transform.gameObject,parent)
					seasonitem.transform:SetLocalScale(1.0,1.0,1.0)
					seasonitem:UpdateData(v,k)
					if k == #insertT then
						seasonitem:NewAni(true)
					else
						seasonitem:NewAni()
					end
					
					self.items[index] = seasonitem
				end
			end					
		end
		
		
		local function p2_End1()
			item:CompleteAni(p2_End2)
		end
		

		--local starPos = UIMgr:GetUIPosByWorld(self.ui.m_txtScore.gameObject.transform.position)
		--local endPos = UIMgr:GetUIPosByWorld(item.go_star.transform.position)
		local starScore = tonumber(param2)
		local endScore =tonumber(NetUpdatePlayerData.playerInfo.seasonEnergy)
		--MapController:SetUIResourcePos(13,endPos.x,endPos.y)
		--MapController:AddResourceBoomAnim(starPos.x, starPos.y,13,1,nil,nil,13,8)
		
		local Pos = UIMgr:GetObjectScreenPos(self.ui.m_txtScore.gameObject.transform)
		local Pos2 = UIMgr:GetObjectScreenPos(item.go_star.transform)
		MapController:FlyUIAnim(Pos.x,
			Pos.y,
			13, 1,
			Pos2.x, Pos2.y,nil, 0,8,3,FlyResourceType.FlyOrder)
		
		AddDOTweenNumberDelay(starScore, endScore, 1.3, 0, function(value)
				self.ui.m_txtScore.text = math.floor(value)
		end,p2_End1)
		--,function() item:ClickCallBack() end
	elseif param == 3 then
		local id = tonumber(param2)
		for k, v in pairs(self.datas) do
			if tonumber(v.id) == id then
				self.aniIndex = k
				self.completeId = id
				--table.remove(self.datas,k)
			end
		end
	elseif param == 4 then
		for k, v in pairs(self.items) do
			if k >= self.aniIndex then
				if k == #self.items then
					v:MoveYAni(true)
				else
					v:MoveYAni()
				end
			end
		end
	elseif param == 5 then
		local function lastEnd()
			self.completeItem:ClickCallBack()
		end

		local curTimes = NetSeasonIslandData.data.areaUpTimes
		local starValue = (curTimes-1)/self.maxTimes
		local endValue = curTimes/self.maxTimes
		self.ui.m_txtSeasonNum.text = math.floor((curTimes/self.maxTimes)*100).."%"
		AddDOTweenNumberDelay(starValue, endValue, 1, 0, function(value)
				self.ui.m_sliderWeek.value = value

			end,lastEnd)
	elseif param == 6 then
		--self.areaLevel = param2
	end
end

function UI_SeasonTask:onDestroy()
    self.rewardArr = nil
	self.items = nil
	self.areaLevel = nil
end

function UI_SeasonTask:onUIEventClick(go,param)
    local name = go.name
	if name == "m_btnClose" then
		if not SeasonIslandMgr:IsCanShowUI() then
			return
		end
		self:Close()
	elseif name == "m_btnBox" then
		self:RefreshGoBox()
	elseif name == "btnHelp" then
		UI_SHOW(UIDefine.UI_SeasonTaskHelp)
	end
end

function UI_SeasonTask:onEscape()
	if not SeasonIslandMgr:IsCanShowUI() then
		return
	end
	self:Close()
end

---------item template--------------------------------------
function seasonTaskItem:OnInit(transform)
	self.transform = transform
	self.stateNormalBg = ResMgr:LoadAssetSync("Sprite/ui_task/taskbox1.png",AssetDefine.LoadType.Sprite)
	self.stateFinishBg = ResMgr:LoadAssetSync("Sprite/ui_task/taskbox2.png",AssetDefine.LoadType.Sprite)
	self.itemNormalBg = ResMgr:LoadAssetSync("Sprite/ui_public/windows-listbox1.png",AssetDefine.LoadType.Sprite)
	self.itemNormalBtnBg = ResMgr:LoadAssetSync("Sprite/ui_public/button1-blue.png",AssetDefine.LoadType.Sprite)
	self.itemFinishBtnBg = ResMgr:LoadAssetSync("Sprite/ui_public/button1-green.png",AssetDefine.LoadType.Sprite)

	self.twoNumSprint = ResMgr:LoadAssetSync("Sprite/ui_public/weektask_icon_points.png",AssetDefine.LoadType.Sprite)

	--背景图
	self.imgBg = transform:GetComponent(typeof(UEUI.Image))
	--
	self.canvasGroup = transform:GetComponent(typeof(UE.CanvasGroup))
	--任务Icon
	self.imgTarget = transform:Find("obj/taskBg/imgTarget"):GetComponent(typeof(UEUI.Image))
	--任务进度
	self.complete = transform:Find("obj/complete")
	self.go_obj = transform:Find("obj")
	--self.m_imgFinish = transform:Find("obj/m_imgFinish")
	--任务标题
	self.txtContent = transform:Find("obj/txtTargetContent"):GetComponent(typeof(UEUI.Text))
	--奖励文字
	self.txtReward = transform:Find("obj/txtReward"):GetComponent(typeof(UEUI.Text))
	--奖励Icon
	self.imgRewardIcon = transform:Find("obj/txtReward/imgIcon"):GetComponent(typeof(UEUI.Image))
	--奖励数量文字
	self.txtRewardNum = transform:Find("obj/txtReward/imgIcon/txtNum"):GetComponent(typeof(UEUI.Text))
	--第二个奖励
	self.go_twoRewardIcon = transform:Find("obj/txtReward/imgIcon_2")
	self.twoRewardIcon = transform:Find("obj/txtReward/imgIcon_2"):GetComponent(typeof(UEUI.Image))
	self.go_twoRewardNum = transform:Find("obj/txtReward/imgIcon_2/txtNum")
	self.twoRewardNum = transform:Find("obj/txtReward/imgIcon_2/txtNum"):GetComponent(typeof(UEUI.Text))
	self.txtPercent = transform:Find("obj/numPercent"):GetComponent(typeof(UEUI.Text))
	--按钮背景图
	self.btnImg = transform:Find("obj/btnGet"):GetComponent(typeof(UEUI.Image))
	--按钮
	self.go_btn = transform:Find("obj/btnGet")
	self.go_star = transform:Find("obj/numPercent/star")
	self.btnGetReward = transform:Find("obj/btnGet"):GetComponent(typeof(UEUI.Button))
	--按钮的文字
	self.btnContent = transform:Find("obj/btnGet/txtBtnContent"):GetComponent(typeof(UEUI.Text))
	--设置奖励静态文字
	self.txtReward.text = LangMgr:GetLang(16)
	self.btnContent.text = LangMgr:GetLang(17)
	self.maskGraphics = transform:GetComponentsInChildren(typeof(UEUI.MaskableGraphic),true)
	self.grayed = false
	local function onClick()
		if not SeasonIslandMgr:IsCanShowUI() then
			return
		end
		
		
		local curScore = NetUpdatePlayerData.playerInfo.seasonEnergy
		if curScore >= self.data.open_condition then
			UIMgr:SetUILock(true, UIDefine.UI_SeasonTask)
			local gItem = MapController:GetItemById(self.data.item_id)
			gItem[1]:UpLevel(self.data.root_type)
			
			SeasonIslandMgr:SetPlayState(true)
			NetSeasonIslandData:SetDataByKey("areaUpTimes",NetSeasonIslandData.data.areaUpTimes+1)
			NetUpdatePlayerData:AddResourceNumByID(ItemID.SeasonEnergy, -self.data.open_condition,nil,"UI_SeasonTask")
			
			local curTimes = NetSeasonIslandData.data.areaUpTimes
			local islandConfig = ConfigMgr:GetData(ConfigDefine.ID.island_task)
			local maxTimes = 0
			local areaLevel = NetUpdatePlayerData.playerInfo.seasonAreaLevel or 1
			for k, v in pairs(islandConfig) do
				if v.task_area then
					if v.task_area == areaLevel then
						maxTimes = maxTimes + 1
					end
				end
			end
			local trueAreaLevel = SeasonIslandMgr:GetCircleMinLevel() or areaLevel
			local area_process = string.format("%.2f", curTimes/maxTimes)
			local thinkTable = {["area_level"] = trueAreaLevel,["area_process"] = area_process}
			SdkHelper:ThinkingTrackEvent(ThinkingKey.season_area,thinkTable)
			
			
			UIMgr:Refresh(UIDefine.UI_MainFace, 31, SpecialId.SeasonsTaskId)
			self:GetReward()
			UI_UPDATE(UIDefine.UI_SeasonTask,2,curScore,self)	
		else
			--季节能量不足，请打开季节宝箱合成季节物品，获得季节能量。
			UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(71008))
		end
		--Log.Error("bbtn",self.data)
	end
	self.btnGetReward.onClick:AddListener(onClick)
end

function seasonTaskItem:MoveYAni(islast)
	local function MoveYEnd()
		--SetActive(self.go_obj,true)
		--DOLocalMoveY(self.go_obj.transform,0,0.5,function()
		if islast then
			UI_UPDATE(UIDefine.UI_SeasonTask,5)
		end
		--end,Ease.OutSine)
	end
	DOLocalMoveY(self.go_obj.transform,180,0.5,MoveYEnd):SetEase(Ease.OutSine)
end

function seasonTaskItem:NewAni(islast)
	self.go_obj.transform.localPosition = Vector3(1200,0,0)
	SetActive(self.transform.gameObject,true)
	local function NewAniEnd()
		--SetActive(self.go_obj,true)
		--DOLocalMoveY(self.go_obj.transform,0,0.5,function()
		if islast then
			UI_UPDATE(UIDefine.UI_SeasonTask,4,self)
		end
			--end,Ease.OutSine)
	end
	
	DOLocalMoveX(self.go_obj.transform,0,1,NewAniEnd):SetEase(Ease.OutSine)
end

function seasonTaskItem:CompleteAni(callback)
	SetActive(self.go_btn,false)
	SetActive(self.complete,true)
	
	local function AniEnd()
		local rewardArr = string.split(self.data.task_reward,";")
		for k, v in pairs(rewardArr) do
			local item = string.split(v,"|")
			item.id = v2n(item[1])
			item.count = v2n(item[2])
			if item.id < ItemID._RESOURCE_MAX then
				NetUpdatePlayerData:AddResource(PlayerDefine[item.id], item.count,nil,nil,"seasonTask")
				MapController:AddResourceBoomAnim(0,0, tonumber(item.id), item.count , false)
			else	
			end
		end
		DOLocalMoveX(self.go_obj.transform,-1200,0.5,function()
				SetActive(self.go_obj.transform.gameObject,false)
				UI_UPDATE(UIDefine.UI_SeasonTask,3,self.data.id)
				if callback ~= nil then
					callback()
				end
			end,Ease.OutSine)
	end
	
	DOScale(self.complete.transform,1,0.5,AniEnd):SetEase(Ease.OutSine)
end

function seasonTaskItem:ClickCallBack()
	local function onTalkState()
		local gItem = MapController:GetItemById(self.data.item_id)
		local curCircle = gItem[1]:GetCurCircleByType(self.data.root_type)
		local function MoveEnd()
			local time = TimeMgr:CreateTimer(self, function()
				gItem[1]:UpLevelOnRefrsh(self.data.root_type)
					UIMgr:SetUILock(true, UIDefine.UI_SeasonTask)
			end, 0.5, 1)

		end
		MapController:SetMoveCameraToPos(curCircle.item.go.transform.position,MoveEnd)
		--MapController:MoveCameraToGrid(gItem[1].m_GridX,gItem[1].m_GridY,MoveEnd)
	end

	--function ClickEnd()
		if self.data.dlg_js ~= nil then
			local log = NetPushViewData:SplitProcess(self.data.dlg_js)
			UI_SHOW(UIDefine.UI_DialogView, nil, log, onTalkState)
		else
			local gItem = MapController:GetItemById(self.data.item_id)
			local curCircle = gItem[1]:GetCurCircleByType(self.data.root_type)
			local function MoveEnd()
				local time = TimeMgr:CreateTimer(self, function()
					gItem[1]:UpLevelOnRefrsh(self.data.root_type)
					UIMgr:SetUILock(true, UIDefine.UI_SeasonTask)
				end, 0.5, 1)
			end
			MapController:SetMoveCameraToPos(curCircle.item.go.transform.position,MoveEnd)
			--Log.Error("xxx",curCircle.m_GridX,curCircle.m_GridY)
			--MapController:MoveCameraToGrid(gItem[1].m_GridX,gItem[1].m_GridY,MoveEnd)
		end
	--end
	UI_UPDATE(UIDefine.UI_SeasonTask,1)
	--local param = {}
	--param.type = 13
	--param.rewards = self.data.task_reward
	--UI_SHOW(UIDefine.UI_Recharge, param,ClickEnd)
end

function seasonTaskItem:GetReward()
	local airList = {}
	local rewardArr = string.split(self.data.task_reward,";")
	for k, v in pairs(rewardArr) do
		local item = string.split(v,"|")
		item.id = v2n(item[1])
		item.count = v2n(item[2])
		if item.id < ItemID._RESOURCE_MAX then
			--MapController:AddResourceBoomAnim(0,0, tonumber(item.id), item.count , false)
			--NetUpdatePlayerData:AddResource(PlayerDefine[item.id], item.count,nil,nil,"seasonTask")
		else
			for i = 1, item.count do
				table.insert(airList,item.id)
			end
		end
	end
	if nil ~= next(airList) then
		MapController:SendRewardToMap(airList,nil,nil,nil,"SeasonTask")
	end
end

function seasonTaskItem:UpdateScrollItem(data)
	local a
end

function seasonTaskItem:UpdateData(data,index)
	if data then
		local taskConfig = IslandTaskConfig:GetConfigByTypeAndLevel(data.root_type,data.root_level)
		--self.imgTarget
		self.txtContent.text = LangMgr:GetLang(taskConfig.task_name)
		SetUIImage(self.imgTarget,taskConfig.task_icon,false)
		local curScore = NetUpdatePlayerData.playerInfo.seasonEnergy
		
		local colorStr = "<color=#%s>%d</color>/%d"
		local color
		if curScore >= taskConfig.open_condition then
			color = "29f800"
		else
			color = "ff6868"
		end
		self.txtPercent.text = string.format(colorStr,color,curScore,taskConfig.open_condition)
		
		
		
		if taskConfig.task_reward ~= nil then
			--local rewardArr = taskConfig.reward
			local rewardArr = string.split(taskConfig.task_reward,";")
			if #rewardArr == 1 then
				local itemMid= string.split(rewardArr[1],"|")
				SetImageSprite(self.imgRewardIcon,ItemConfig:GetIcon(v2n(itemMid[1])),false)
				self.txtRewardNum.text = "x"..itemMid[2]
				
				SetActive(self.go_twoRewardNum,false)
				SetActive(self.go_twoRewardIcon,false)
			else
				local itemMid= string.split(rewardArr[1],"|")
				local itemLast = string.split(rewardArr[2],"|")

				SetImageSprite(self.imgRewardIcon,ItemConfig:GetIcon(v2n(itemMid[1])),false)
				self.txtRewardNum.text = "x"..itemMid[2]

				SetImageSprite(self.twoRewardIcon,ItemConfig:GetIcon(v2n(itemLast[1])),false)
				self.twoRewardNum.text = "x"..itemLast[2]
				
				SetActive(self.go_twoRewardNum,true) 
				SetActive(self.go_twoRewardIcon,true)	
			end
			
		end
	end
	self.data = data
end

function seasonTaskItem:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec
end

function seasonTaskItem:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition
end

return UI_SeasonTask