local UI_LeagueChatHelpList = Class(BaseView)

local BubbleItem = require("UI.BubbleItem");
local LeagueChatHelpPeople = require "UI.LeagueChatHelpPeople"

local tb_HelpPeopleView = {}
local tb_SelfHelpInfo

local str_key_Id_Help = "playerId"

local timer_AutoMainChatRefresh
local timer_HelpResendCDCountDown
local timer_HelpRequireCDCountDown

local str_timerKey_HelpResendCD = string.format("%s_%s", UIDefine.UI_LeagueChat, "HelpResendCD")
local str_timerKey_HelpPeopleView = string.format("%s_%s", UIDefine.UI_LeagueChat, "HelpPeople")
local str_timerKey_GiftPeopleView = string.format("%s_%s", UIDefine.UI_LeagueChat, "GiftPeople")
local str_timerKey_AutoRefreshMainChat = string.format("%s_%s", UIDefine.UI_LeagueChat, "RefreshMainChat")

function UI_LeagueChatHelpList:OnInit()
    self.curIndex = -1
    self.isTurnDown = false;
    self.isCheckClose = false

    SetActive(self.ui.m_goItem, false)
    SetActive(self.ui.m_goBuildHelpMsg, false)

    self.buildHelpList = nil  --建筑帮助列表数据缓存
    self.myPlayerId = v2n(NetUpdatePlayerData:GetPlayerInfo().id)
    NetLeagueData:UpdateBuildHelpList()
end

function UI_LeagueChatHelpList:OnCreate(param)
    self.tableViewV = GetChild(self.ui.m_goHelp, "goScroll", CS.Mosframe.TableView)
    self.buildList = GetChild(self.ui.m_goBuildHelp, "goScroll", CS.Mosframe.TableView)

    self.bubbleItem = BubbleItem.new("UI_LeagueChatHelpList");
    self.bubbleItem:Init(self.uiGameObject, { x = -350, y = 800 }, function()
        self:Close();
    end);

    self:onRefreshList()
    self:ShowBuildHelpList()

    self:onHelpRequireRefresh()

    self:SetProgress()

    self:CheckAllHelpBtnStatus()

    self:ShowPageTog()

    EventMgr:Add(EventID.REFRESH_CHAT_ONLYRELOADVIEW, self.ReloadView, self)
    EventMgr:Add(EventID.REFRESH_CHAT_REQUITEREFRESH, self.onHelpRequireRefresh, self)
    EventMgr:Add(EventID.GLOBAL_TOUCH_BEGIN, self.OnGlobalTouchBegin, self)
    EventMgr:Add(EventID.UNION_ID_CHANGE, self.CheckClose, self);
end

function UI_LeagueChatHelpList:OnRefresh(param)
    if param == 1 then
        -- 物资帮助列表滑倒底
        self.isTurnDown = true;
    elseif param == 2 then
        --联盟贡献值更新
        self:SetProgress()
    elseif param == 3 then
        --建筑帮助列表更新
        self:RefreshBuildHelpList()
    end
end

function UI_LeagueChatHelpList:onDestroy()
    if not self.isCheckClose and self.bubbleItem and self.bubbleItem:IsHaveItem() then
        self.isCheckClose = true;
        self.bubbleItem:SetCloseCall(nil);
        self.bubbleItem:CloseImmediately();
    end
    
    EventMgr:Remove(EventID.REFRESH_CHAT_ONLYRELOADVIEW, self.ReloadView, self)
    EventMgr:Remove(EventID.REFRESH_CHAT_REQUITEREFRESH, self.onHelpRequireRefresh, self)
    EventMgr:Remove(EventID.GLOBAL_TOUCH_BEGIN, self.OnGlobalTouchBegin, self)
    EventMgr:Remove(EventID.UNION_ID_CHANGE, self.CheckClose, self);

    TimeMgr:DestroyTimer(str_timerKey_HelpResendCD)
    TimeMgr:DestroyTimer(str_timerKey_AutoRefreshMainChat)
    TimeMgr:DestroyTimer(UIDefine.UI_LeagueChat)

    timer_AutoMainChatRefresh = nil
    timer_HelpResendCDCountDown = nil
    timer_HelpRequireCDCountDown = nil

    self:onClearHelpPeopleView()
end

function UI_LeagueChatHelpList:onUIEventClick(go, param)
    local name = go.name

    if name == "closeBtn" then
        self:CheckClose()
    elseif name == "m_btnOneClick" then
        --全部帮助
        LeagueChatManager:ClickHelpOther(-1, function(count)
            --local config = ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 16)
            --if not config then
            --	Log.Error("union_setting config error" .. 16)
            --	return
            --end
            --local strValue = config.value
            --local tbValue = SplitStringToNum(strValue, "|")
            --local itemId = tbValue[1]
            --local itemNum = tbValue[2]
            --local btnObj = self.ui.m_btnOneClick.gameObject;
            --for i = 1, count do
            --	if itemId < ItemID._RESOURCE_MAX then
            --		MapController:AddResourceBoomAnim(btnObj.transform.localPosition.x, btnObj.transform.localPosition.y, itemId, itemNum)
            --		NetUpdatePlayerData:AddResource(PlayerDefine[itemId], itemNum, nil, nil, "union_help")
            --	else
            --      self.bubbleItem:FlyItem(itemId, itemNum, btnObj.transform.position);
            --	end
            --end
        end)
    elseif name == "m_btnHelpRequire" then
        UI_SHOW(UIDefine.UI_LeagueChatSelectItem)

    elseif name == "btnHelpTip" then
        --帮助贡献提示
        if not self.ui.m_goInfoTip.activeInHierarchy then
            SetActive(self.ui.m_goInfoTip, true)
            SetUIForceRebuildLayout(GetChild(self.ui.m_goInfoTip, "Image"))
        end
    elseif name == "m_btnHelpResendOrReward" then
        self:onBtnHelpResendOrReward()
    elseif name == "m_btnBuildHelpAll" then
        NetLeagueData:HelpAllBuildApply()
    end
end

function UI_LeagueChatHelpList:AutoClose()
    self:CheckClose();
end

function UI_LeagueChatHelpList:CheckClose()
    if not self.isCheckClose and self.bubbleItem and self.bubbleItem:IsHaveItem() then
        self.isCheckClose = true;
        self.bubbleItem:Close();
    else
        self.isCheckClose = true;
        self:Close();
    end
end

function UI_LeagueChatHelpList:onRefreshList()
    self:onClearHelpPeopleView()

    self:initEmptyState()

    self:LoadTableView_Help()
end

function UI_LeagueChatHelpList:initEmptyState()
    local helpListCount = LeagueChatManager:GetLenHelpList()
    SetActive(self.ui.m_goNoHelpList, helpListCount <= 0)
end

function UI_LeagueChatHelpList:onClearHelpPeopleView()
    for k, v in pairs(tb_HelpPeopleView) do
        if v.timer then
            TimeMgr:DestroyTimer(str_timerKey_HelpPeopleView, v.timer)
        end
        if v.cp then
            v.cp:onDestroy()
            v.cp = nil
        end
    end
    tb_HelpPeopleView = {}
end

---------------------------------------事件监听---------------------------------------
function UI_LeagueChatHelpList:ReloadView()
    self:initEmptyState()

    if self.initHelpCount == 0 then
        self.initHelpCount = LeagueChatManager:GetLenHelpList();
        self.tableViewV:refreshByIndex(0, self.initHelpCount)
    else
        self.tableViewV:ReloadData()
    end

    if self.isTurnDown then
        self.isTurnDown = false;
        self:OnMoveToIndex(LeagueChatManager:GetLenHelpList());
    end

    self:CheckAllHelpBtnStatus()
end

function UI_LeagueChatHelpList:OnGlobalTouchBegin(vtArray, touchCount, isForce3D)
    SetActive(self.ui.m_goInfoTip, false)
end

function UI_LeagueChatHelpList:onHelpRequireRefresh()
    SetActive(self.ui.m_btnHelpRequire, false)
    SetActive(self.ui.m_btnHelpRequireCD, false)
    SetActive(self.ui.m_btnHelpResendOrReward, false)
    SetActive(self.ui.m_btnHelpResendCD, false)
    tb_SelfHelpInfo = {}

    if timer_HelpResendCDCountDown then
        TimeMgr:DestroyTimer(str_timerKey_HelpResendCD, timer_HelpResendCDCountDown)
        timer_HelpResendCDCountDown = nil
    end
    if timer_HelpRequireCDCountDown then
        TimeMgr:DestroyTimer(UIDefine.UI_LeagueChat, timer_HelpRequireCDCountDown)
        timer_HelpRequireCDCountDown = nil
    end

    local tbHelp = LeagueChatManager:GetHelpList()
    local selfPlayerID = NetUpdatePlayerData:GetPlayerInfo().id
    local isInHelp = false
    for k, v in pairs(tbHelp) do
        if v2n(v[str_key_Id_Help]) == selfPlayerID then
            isInHelp = true
            tb_SelfHelpInfo = v
            break
        end
    end

    local iCurTime = TimeMgr:GetServerTimestamp()
    local isInCD = false
    if isInHelp then
        --重发cd

        local iHelpResendCD = v2n(LeagueManager:GetConfigById(8))
        local iHelpSendTime = v2n(tb_SelfHelpInfo["chatTime"])
        local status = v2n(tb_SelfHelpInfo["status"])

        if iCurTime < (iHelpSendTime + iHelpResendCD) then
            isInCD = true
        end

        SetActive(self.ui.m_btnHelpResendOrReward, ((not isInCD) or (status == 2)) and (isInHelp))
        SetActive(self.ui.m_btnHelpResendCD, (status == 1) and (isInCD) and (isInHelp))

        if isInCD and status == 1 then
            self:onHelpResendCDInit()
        else
            self:onHelpResendOrRewardInit()
        end
    else
        --申请cd
        local iHelpRequireCD = v2n(LeagueManager:GetConfigById(7))
        local iHelpNextTime = LeagueChatManager:GetHelpNextTime()

        if iCurTime < iHelpNextTime then
            isInCD = true
            self:onHelpRequireCDInit()
        end

        SetActive(self.ui.m_btnHelpRequire, (not isInCD) and (not isInHelp))
        SetActive(self.ui.m_btnHelpRequireCD, (isInCD) and (not isInHelp))
    end

    -- 物资帮助相关红点
    local redState = LeagueChatManager:OnCheckHelpRed();
    SetActive(self.ui.m_imgResRed, redState);
    SetActive(self.ui.m_imgResBtnRed, not isInCD or (isInHelp and status == 2));
end

---------------------------------------界面逻辑---------------------------------------
--切页标签相关逻辑
function UI_LeagueChatHelpList:ShowPageTog()
    local togGroup = GetChild(self.uiGameObject, "Tag", UEUI.ToggleGroup)

    local buildHelpTog = GetChild(togGroup, "BuildHelp", UEUI.Toggle)
    local ResHelpTog = GetChild(togGroup, "ResHelp", UEUI.Toggle)
    local togList = { buildHelpTog, ResHelpTog }

    togGroup.enabled = false;
    for k, v in pairs(togList) do
        v.group = togGroup
        local onTxt = GetChild(v, "onTxt", UEUI.Text)
        local offTxt = GetChild(v, "offTxt", UEUI.Text)
        local str = k == 1 and LangMgr:GetLang(9348) or LangMgr:GetLang(9334)
        onTxt.text = str
        offTxt.text = str

        v.onValueChanged:RemoveAllListeners()
        v.onValueChanged:AddListener(function(isOn)
            SetActive(onTxt, isOn)
            SetActive(offTxt, not isOn)
            if isOn then
                if self.curIndex == k then
                    return
                end
                self.curIndex = k
                SetActive(self.ui.m_goHelp, k == 2)
                SetActive(self.ui.m_goBuildHelp, k == 1)
            end
        end);

        if k == 2 then
            v.isOn = true;
        end
    end
    togGroup.enabled = true;
end

function UI_LeagueChatHelpList:onBtnHelpResendOrReward()
    if not tb_SelfHelpInfo then
        Log.Error("*** onBtnHelpResendOrReward()   tb_SelfHelpInfo=nil")
        return
    end

    local iStatus = v2n(tb_SelfHelpInfo["status"])    --1进行中 2完成 3已领取
    if iStatus == 1 then
        --重发
        LeagueChatManager:ResendHelp()
    elseif iStatus == 2 then
        --领取
        local selfId = NetUpdatePlayerData:GetPlayerID()
        local chatSort = LeagueChatManager:GetHelpListSortByPlayerId(selfId)
        if not chatSort then
            Log.Error("***** onBtnHelpResendOrReward()  chatSort  Err")
        end

        local cfgID = v2n(chatSort["configId"])
        local itemHelpInfo = ConfigMgr:GetDataByID(ConfigDefine.ID.union_help, cfgID)
        if not itemHelpInfo then
            Log.Error("union_help Config Error!  id is" .. cfgID)
            return
        end

        local tbReward = SplitStringToNum(itemHelpInfo.reward, "|")
        local itemId = tbReward[1]
        local itemNum = tbReward[2]
        local function callback(itemId, itemNum)
            local go = self.ui.m_btnHelpResendOrReward.gameObject
            if itemId < ItemID._RESOURCE_MAX then
                MapController:AddResourceBoomAnim(go.transform.localPosition.x, go.transform.localPosition.y, itemId, itemNum)
                NetUpdatePlayerData:AddResource(PlayerDefine[itemId], itemNum, nil, nil, "union_help_complete")
            else
                self.bubbleItem:FlyItem(itemId, itemNum, go.transform.position);
                Game.Save(2);
            end
        end
        LeagueChatManager:RewardHelpItem(itemId, itemNum, cfgID, callback)
    end
end

---------------------------------------物资帮助列表---------------------------------------
function UI_LeagueChatHelpList:LoadTableView_Help()
    --创建头像
    local headNode = GetChild(self.ui.m_goItem, "headNode")
    CreateCommonHead(headNode.transform, 0.8)

    self.startIndex = 0;
    self.isFirstIn = true;
    self.initHelpCount = LeagueChatManager:GetLenHelpList();

    self.tableViewV.GetItemCount = function(num)
        return LeagueChatManager:GetLenHelpList();
    end
    self.tableViewV.GetItemGo = function(obj)
        return self.ui.m_goItem
    end
    self.tableViewV.UpdateItemCell = function(idx, obj)
        self:LoadCellData_MainChat(idx, obj)
    end
    self.tableViewV:InitTableViewByIndex();
end

function UI_LeagueChatHelpList:LoadCellData_MainChat(index, obj)
    local tId = index + 1
    local realId = v2n(obj.name)

    local childObj
    for i = 1, obj.transform.childCount do
        childObj = obj.transform:GetChild(i - 1).gameObject
        SetActive(childObj, false)
    end

    local selfPlayerID = NetUpdatePlayerData:GetPlayerInfo().id
    local chatSort = LeagueChatManager:GetHelpListSortByIndex(tId)
    local useObj

    if v2n(chatSort[str_key_Id_Help]) ~= selfPlayerID then
        useObj = GetChild(obj, "m_gotHelpCell_Other")
    else
        useObj = GetChild(obj, "m_gotHelpCell_Self")
    end
    SetActive(useObj, true)

    local goChatBg = GetChild(useObj, "imgChatBg")
    local imgRolePositon = GetChild(useObj, "imgChatBg/imgRolePositon", UEUI.Image)
    local headBtn = GetChild(useObj, "imgHeadBg/imgHead", UEUI.Button)
    local txtName = GetChild(useObj, "imgChatBg/goTop/txtName", UEUI.Text)

    local txtRolePositon = GetChild(useObj, "imgChatBg/imgRolePositon/txtRolePositon", UEUI.Text)

    local playerData = chatSort
    local headNode = GetChild(obj, "headNode")
    local customHeadObj = GetChild(obj, "headNode/CustomHead")
    SetHeadAndBorderByGo(customHeadObj, playerData.icon, playerData.border, function()
        FriendManager:ShowPlayerById(v2n(chatSort[str_key_Id_Help]))
    end)
    SetActive(headNode, true)

    local isMyself = v2n(chatSort[str_key_Id_Help]) == selfPlayerID
    local nodeRect = GetComponent(headNode, UE.RectTransform)
    nodeRect.anchoredPosition = isMyself and Vector2.New(370, 0) or Vector2.New(-374, 0)

    txtName.text = chatSort["name"] or ""

    local iLeagueDuty = chatSort["leagueDuty"] or 3
    SetActive(imgRolePositon.gameObject, (iLeagueDuty < 3))
    if iLeagueDuty < 3 then
        local str = { LangMgr:GetLang(9263), LangMgr:GetLang(9264) }
        txtRolePositon.text = str[iLeagueDuty]
    end

    local imgItem = GetChild(useObj, "imgChatBg/imgItemBg/imgItem", UEUI.Image)
    local itemBtn = GetChild(useObj, "imgChatBg/imgItemBg/imgItem", UEUI.Button)
    local txtState = GetChild(useObj, "imgChatBg/goTop/txtState", UEUI.Text)
    local txtObj = GetChild(useObj, "imgChatBg/goTop/txtObj")
    local txtCount = GetChild(useObj, "imgChatBg/goTop/txtObj/txtCount", UEUI.Text)
    local txtTips = GetChild(useObj, "imgChatBg/goTop/txtObj/txtTips", UEUI.Text)
    --local TableViewH = GetChild(useObj, "imgChatBg/goSlideRect", CS.Mosframe.TableView)
    local goContent = GetChild(useObj, "imgChatBg/goSlideRect/Viewport/Content")
    local texReward = GetChild(useObj, "imgChatBg/imgItemBg/imgItem/txtReward", UEUI.Text)
    local cfgID = v2n(chatSort["configId"])

    local itemHelpInfo = ConfigMgr:GetDataByID(ConfigDefine.ID.union_help, cfgID)
    if not itemHelpInfo then
        Log.Error("union_help Config Error!  id is" .. cfgID)
        return
    end

    local iNeedHelp = itemHelpInfo.need_help
    local tbReward = SplitStringToNum(itemHelpInfo.reward, "|")
    local itemID = tbReward[1]

    local itemCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
    if not itemCfg then
        Log.Error("help item error  cfgID=" .. tostring(cfgID))
        return
    end

    SetImageSync(imgItem, itemCfg.icon_b, false)
    RemoveUIComponentEventCallback(itemBtn, UEUI.Button)
    AddUIComponentEventCallback(itemBtn, UEUI.Button, function()
        UI_SHOW(UIDefine.UI_ItemTips, itemCfg.id)
    end)

    if not tb_HelpPeopleView[realId] then
        tb_HelpPeopleView[realId] = {}
    end

    local iIndex = 1
    --local tbHelpPIds = chatSort["helpPIds"]
    --
    --Log.Error("-------------",table.dump(tbHelpPIds))
    --
    --tb_HelpPeopleView[realId].tbHelpPIds = {}
    --for k, v in pairs(tbHelpPIds) do
    --	tb_HelpPeopleView[realId].tbHelpPIds[iIndex] = {}
    --	tb_HelpPeopleView[realId].tbHelpPIds[iIndex].id = v2n(k)
    --	tb_HelpPeopleView[realId].tbHelpPIds[iIndex].icon = v2n(v)
    --
    --	iIndex = iIndex + 1
    --end

    local tbHelpPIds = chatSort["helperPlayers"]
    tb_HelpPeopleView[realId].tbHelpPIds = {}
    if tbHelpPIds then
        for k, v in pairs(tbHelpPIds) do
            tb_HelpPeopleView[realId].tbHelpPIds[iIndex] = {}
            tb_HelpPeopleView[realId].tbHelpPIds[iIndex].id = v2n(v.id)
            tb_HelpPeopleView[realId].tbHelpPIds[iIndex].icon = v2n(v.icon)
            tb_HelpPeopleView[realId].tbHelpPIds[iIndex].border = v2n(v.border)
            iIndex = iIndex + 1
        end
    end

    local applyTime = v2n(chatSort["applyTime"]) or 0
    local OverDeltaTime = v2n(ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 14).value) or 0
    local curTime = TimeMgr:GetServerTimestamp()
    local t = (applyTime + OverDeltaTime) - curTime
    if t >= 0 then
        txtState.text = TimeMgr:CheckHMSNotEmpty(t)
    end

    local iHelpsCount = GetTableLength(tb_HelpPeopleView[realId].tbHelpPIds)
    txtCount.text = string.format("%d/%d", iHelpsCount, iNeedHelp)
    txtTips.text = LangMgr:GetLang(9112)
    UIRefreshLayout(txtObj);

    if not tb_HelpPeopleView[realId].cp then
        tb_HelpPeopleView[realId].cp = LeagueChatHelpPeople.new()
        tb_HelpPeopleView[realId].cp:OnInit()

        local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_LeagueChatHeadCell")
        local HeadObj = ResMgr:LoadAssetSync(assetPath, AssetDefine.LoadType.Instant)
        tb_HelpPeopleView[realId].cp:SetCellGo(HeadObj)
    end
    tb_HelpPeopleView[realId].cp:SetPlayerID(v2n(chatSort[str_key_Id_Help]))
    --tb_HelpPeopleView[realId].cp:SetParentIndex(tId)
    --tb_HelpPeopleView[realId].cp:SetTableViewH(TableViewH)
    tb_HelpPeopleView[realId].cp:SetCount(iNeedHelp)
    tb_HelpPeopleView[realId].cp:SetType(1)
    tb_HelpPeopleView[realId].cp:SetPeopleData(tb_HelpPeopleView[realId].tbHelpPIds)
    --tb_HelpPeopleView[realId].cp:SetCellGo(self.ui.m_goHeadShow)
    tb_HelpPeopleView[realId].cp:SetPeopleParent(goContent)

    if v2n(chatSort[str_key_Id_Help]) ~= selfPlayerID then
        local btnHelp = GetChild(useObj, "imgChatBg/btnHelp", UEUI.Button)
        local txtHelp = GetChild(btnHelp, "txtHelp", UEUI.Text)

        local goBtnHelped = GetChild(useObj, "imgChatBg/btnHelped")
        local txtHelped = GetChild(goBtnHelped, "txtHelped", UEUI.Text)

        local goBtnCompleted = GetChild(useObj, "imgChatBg/btnCompleted")
        local txtCompleted = GetChild(goBtnCompleted, "txtCompleted", UEUI.Text)

        txtHelp.text = LangMgr:GetLang(9103)
        txtHelped.text = LangMgr:GetLang(9106)
        txtCompleted.text = LangMgr:GetLang(5003)

        local isSelfHelped = false --自己是否已经帮助过此玩家
        for k, v in pairs(tb_HelpPeopleView[realId].tbHelpPIds) do
            if v.id == selfPlayerID then
                isSelfHelped = true
                break
            end
        end

        SetActive(btnHelp.gameObject, not isSelfHelped)
        SetActive(goBtnHelped, isSelfHelped)
        SetActive(goBtnCompleted, iHelpsCount >= iNeedHelp)

        if self.startIndex == 0 and not isSelfHelped and iHelpsCount < iNeedHelp then
            self.startIndex = tId;
        end

        if isSelfHelped == false then
            RemoveUIComponentEventCallback(btnHelp, UEUI.Button)
            AddUIComponentEventCallback(btnHelp, UEUI.Button, function(go, param)
                local id = param[1]
                local configId = param[2]

                local function callback()
                    --local config = ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 16)
                    --if not config then
                    --    Log.Error("union_setting config error" .. 16)
                    --    return
                    --end
                    --local strValue = config.value
                    --local tbValue = SplitStringToNum(strValue, "|")
                    --local itemId = tbValue[1]
                    --local itemNum = tbValue[2]
                    --if itemId < ItemID._RESOURCE_MAX then
                    --    MapController:AddResourceBoomAnim(go.transform.localPosition.x, go.transform.localPosition.y, itemId, itemNum)
                    --    NetUpdatePlayerData:AddResource(PlayerDefine[itemId], itemNum, nil, nil, "union_help")
                    --else
                    --    self.bubbleItem:FlyItem(itemId, itemNum, go.transform.position);
                    --end
                end

                LeagueChatManager:ClickHelpOther(id, callback)

                local thinkTable = {
                    ["team_helpid"] = configId,
                    ["team_roleid"] = id
                }
                SdkHelper:ThinkingTrackEvent(ThinkingKey.team, thinkTable)
            end, { v2n(chatSort[str_key_Id_Help]), v2n(chatSort["configId"] or "0") })
        end
        RemoveUIComponentEventCallback(headBtn, UEUI.Button)
    else
        local goBtnClaim = GetChild(useObj, "imgChatBg/btnClaim")
        local txtClaim = GetChild(goBtnClaim, "txtClaim", UEUI.Text)

        local goBtnNotClaim = GetChild(useObj, "imgChatBg/btnNotClaim")
        local txtNotClaim = GetChild(goBtnNotClaim, "txtNotClaim", UEUI.Text)

        local goBtnClaimed = GetChild(useObj, "imgChatBg/btnClaimed")
        local txtClaimed = GetChild(goBtnClaimed, "txtClaimed", UEUI.Text)

        txtClaim.text = LangMgr:GetLang(17)
        txtNotClaim.text = LangMgr:GetLang(17)
        txtClaimed.text = LangMgr:GetLang(71)

        local iStatus = v2n(chatSort["status"])
        SetActive(goBtnNotClaim, iStatus == 1) --领取 灰色
        SetActive(goBtnClaim, iStatus == 2) --领取 绿色
        SetActive(goBtnClaimed, iStatus == 3) --已领取 灰色

        if self.startIndex == 0 and iStatus == 2 then
            self.startIndex = tId;
        end

        RemoveUIComponentEventCallback(goBtnClaim, UEUI.Button)
        if iStatus == 2 then
            AddUIComponentEventCallback(goBtnClaim, UEUI.Button, function(go, param)
                local itemId = param[1]
                local itemNum = param[2]
                local configId = param[3]

                local function callback(itemId, itemNum)
                    if itemId < ItemID._RESOURCE_MAX then
                        MapController:AddResourceBoomAnim(go.transform.localPosition.x, go.transform.localPosition.y, itemId, itemNum)
                        NetUpdatePlayerData:AddResource(PlayerDefine[itemId], itemNum, nil, nil, "union_help_complete")
                    else
                        self.bubbleItem:FlyItem(itemId, itemNum, go.transform.position);
                    end
                end

                LeagueChatManager:RewardHelpItem(itemId, itemNum, configId, callback)
            end, { tbReward[1], tbReward[2], cfgID })
        end
    end
    texReward.text = "x" .. tbReward[2] or ""
    --AddUIComponentEventCallback(headBtn, UEUI.Button, function(arg1,arg2)
    --	FriendManager:ShowPlayerById(v2n(chatSort[str_key_Id_Help]))
    --end)

    tb_HelpPeopleView[realId].cp:ReloadPeopleView()
    --if tb_HelpPeopleView[realId].timer then
    --TimeMgr:DestroyTimer(str_timerKey_HelpPeopleView , tb_HelpPeopleView[realId].timer)
    --end
    --tb_HelpPeopleView[realId].timer = TimeMgr:CreateTimer(str_timerKey_HelpPeopleView,
    --function(param)
    --local id = param[1]
    --Log.Info("*** UI_LeagueChatHelpList   id=".. id)
    --tb_HelpPeopleView[id].cp:LoadTableView_People()
    --end,
    --0.1, 1, tId)

    if self.startIndex > 0 and self.isFirstIn then
        self.isFirstIn = false;
        self:OnMoveToIndex(self.startIndex);
    end
end

--判断全部帮助按钮
function UI_LeagueChatHelpList:CheckAllHelpBtnStatus()
    --资源帮助按钮显隐判断
    local count = LeagueChatManager:GetLenHelpList()
    local canShowBtn = false
    for i = 1, count do
        local canHelp = self:CanHelp(i)
        if canHelp then
            canShowBtn = true
            break ;
        end
    end
    SetActive(self.ui.m_btnOneClick, canShowBtn)
    SetActive(self.ui.m_goOneClickGrey, not canShowBtn)
end

--判断是否可以帮助该玩家请求
function UI_LeagueChatHelpList:CanHelp(index)
    local selfPlayerID = v2n(NetUpdatePlayerData:GetPlayerInfo().id)
    local helpMsg = LeagueChatManager:GetHelpListSortByIndex(index)
    if v2n(helpMsg["playerId"]) == selfPlayerID or v2n(helpMsg.status) ~= 1 then
        return false
    end

    --进行中
    for k, v in pairs(helpMsg.helpPIds) do
        if v2n(k) == selfPlayerID then
            --是否已经帮助过该玩家
            return false
        end
    end

    return true
end

function UI_LeagueChatHelpList:onHelpResendOrRewardInit()
    self:onBtnHelpCommonInit(self.ui.m_imgItem_HelpResendOrReward,
            self.ui.m_txtItemCount_HelpResendOrReward,
            self.ui.m_txtProgress_HelpResendOrReward,
            self.ui.m_slider_HelpResendOrReward)

    local iStatus = v2n(tb_SelfHelpInfo["status"])    --1进行中 2完成 3已领取
    local str = LangMgr:GetLang(9117)
    if iStatus == 2 then
        str = LangMgr:GetLang(17)
    end

    self.ui.m_txtState_ResendOrReward.text = str
end

function UI_LeagueChatHelpList:onBtnHelpCommonInit(imgItemIcon, txtItemCount, txtHelpProgress, sliderHelpProgress)
    if not tb_SelfHelpInfo then
        Log.Error("*** onBtnHelpCommonInit()   tb_SelfHelpInfo=nil")
        return
    end

    local cfgID = v2n(tb_SelfHelpInfo["configId"])
    local itemHelpInfo = ConfigMgr:GetDataByID(ConfigDefine.ID.union_help, cfgID)
    if not itemHelpInfo then
        Log.Error("union_help Config Error!  id is" .. cfgID)
        return
    end

    local iNeedHelp = itemHelpInfo.need_help
    local tbReward = SplitStringToNum(itemHelpInfo.reward, "|")
    local itemID = tbReward[1]
    local itemCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
    if not itemCfg then
        Log.Error("help item error  cfgID=" .. tostring(cfgID))
        return
    end
    local iHelpsCount = GetTableLength(tb_SelfHelpInfo["helpPIds"])

    if imgItemIcon then
        SetImageSync(imgItemIcon, itemCfg.icon_b, false)
    end

    if txtItemCount then
        txtItemCount.text = tbReward[2]
    end

    if txtHelpProgress then
        txtHelpProgress.text = string.format("%d/%d", iHelpsCount, iNeedHelp)
    end

    if sliderHelpProgress then
        sliderHelpProgress.value = (iHelpsCount) / (iNeedHelp)
    end
end

function UI_LeagueChatHelpList:SetResendTime()
    local iCurTime = TimeMgr:GetServerTimestamp()
    local iHelpResendCD = v2n(ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 8).value)
    local iHelpSendTime = v2n(tb_SelfHelpInfo["chatTime"])

    if iCurTime < (iHelpResendCD + iHelpSendTime) then
        local t = ((iHelpResendCD + iHelpSendTime)) - iCurTime
        self.ui.m_txtState_ResendCD.text = TimeMgr:GetHMS(t)
    else
        if timer_HelpResendCDCountDown then
            TimeMgr:DestroyTimer(str_timerKey_HelpResendCD, timer_HelpResendCDCountDown)
            timer_HelpResendCDCountDown = nil
        end
        self:onHelpRequireRefresh()
    end
end

function UI_LeagueChatHelpList:onHelpResendCDInit()
    self:onBtnHelpCommonInit(self.ui.m_imgItem_HelpResendCD,
            self.ui.m_txtItemCount_HelpResendCD,
            self.ui.m_txtProgress_HelpResendCD,
            self.ui.m_slider_HelpResendCD)

    self:SetResendTime()

    if timer_HelpResendCDCountDown then
        TimeMgr:DestroyTimer(str_timerKey_HelpResendCD, timer_HelpResendCDCountDown)
        timer_HelpResendCDCountDown = nil
    end
    timer_HelpResendCDCountDown = TimeMgr:CreateTimer(str_timerKey_HelpResendCD,
            function(param)
                self:SetResendTime()
            end,
            1, -1)
end

function UI_LeagueChatHelpList:onHelpRequireCDInit()
    self:SetRequireCDInfo()

    if timer_HelpRequireCDCountDown then
        TimeMgr:DestroyTimer(UIDefine.UI_LeagueChatHelpList, timer_HelpRequireCDCountDown)
        timer_HelpRequireCDCountDown = nil
    end
    timer_HelpRequireCDCountDown = TimeMgr:CreateTimer(UIDefine.UI_LeagueChatHelpList,
            function(param)
                self:SetRequireCDInfo()
            end,
            1, -1)
end

function UI_LeagueChatHelpList:SetRequireCDInfo()
    local iCurTime = TimeMgr:GetServerTimestamp()
    local iHelpRequireCD = v2n(ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 7).value)
    --local iHelpRequireTime = NetLeagueData:GetChatHelpItemRequireTime()
    local iHelpNextTime = LeagueChatManager:GetHelpNextTime()

    --if iCurTime < (iHelpRequireCD + iHelpRequireTime) then
    if iCurTime < iHelpNextTime then
        --local t = ((iHelpRequireCD + iHelpRequireTime)) - iCurTime
        local t = iHelpNextTime - iCurTime
        self.ui.m_txtCDTime.text = TimeMgr:GetHMS(t)
    else
        if timer_HelpRequireCDCountDown then
            TimeMgr:DestroyTimer(UIDefine.UI_LeagueChat, timer_HelpRequireCDCountDown)
            timer_HelpRequireCDCountDown = nil
        end
        self:onHelpRequireRefresh()
    end
end

--联盟贡献金币数量进度
function UI_LeagueChatHelpList:SetProgress()
    local ownCount = NetLeagueData:GetHelpCoin()
    local configCount = NetLeagueData:GetHelpCoinLimit()
    local ratio, count1, count2 = self:GetRatio(ownCount, configCount)
    self.ui.m_sliderProgress.value = ratio
    self.ui.m_txtLeagueCoin.text = count1 .. "/" .. count2
end

function UI_LeagueChatHelpList:OnMoveToIndex(index)
    local totalHeight = 570;
    local cellHeight = 229;
    local offset = cellHeight * index - totalHeight;
    if offset > 0 then
        local Content = GetChild(self.ui.m_goHelp, "goScroll/Viewport/Content");
        SetUIPos(Content, 0, offset);
    end
end

---------------------------------------建筑帮助列表---------------------------------------
--建筑帮助列表
function UI_LeagueChatHelpList:ShowBuildHelpList()
    self.buildHelpList = NetLeagueData:GetApplyList()

    if not self.buildHelpList then
        return
    end

    --创建头像
    local headNode = GetChild(self.ui.m_goBuildHelpMsg, "headNode")
    if not GetChild(headNode, "CustomHead") then
        CreateCommonHead(headNode.transform, 0.5)
    end

    local sumCount = #self.buildHelpList
    self.buildList.GetItemCount = function(num)
        return sumCount
    end

    self.buildList.GetItemGo = function(obj)
        return self.ui.m_goBuildHelpMsg
    end

    self.buildList.UpdateItemCell = function(idx, obj)
        self:ShowBuildHelpMsg(idx, obj)
    end

    self.buildList:InitTableViewByIndex()
    self:CheckBuildHelpBtnActive(self.buildHelpList)
end

--展示建造帮助信息
function UI_LeagueChatHelpList:ShowBuildHelpMsg(index, obj)
    local data = self.buildHelpList[index + 1]
    local bg = GetChild(obj, "bg", UEUI.Image)
    local name = GetChild(obj, "name", UEUI.Text)--名字

    local descTxt = GetChild(obj, "descTxt", UEUI.Text)
    local timeTxt = GetChild(obj, "timeTxt", UEUI.Text)

    --进度条
    local sliderBg = GetChild(obj, "slider/Background", UEUI.Image)
    local slider = GetChild(obj, "slider", UEUI.Slider)
    local sliderValue = GetChild(obj, "slider/Text", UEUI.Text)

    --是否是自己的帮助请求
    local isMyHelp = self.myPlayerId == v2n(data.player_id)
    local sprite = isMyHelp and "jianzaobangzhu-dikuang2.png" or "jianzaobangzhu-dikuang1.png"
    sprite = "Sprite/ui_lianmeng/" .. sprite
    SetImageSync(bg, sprite, false)
    UnifyOutline(name, isMyHelp and "C44C0A" or "07619B")

    sprite = isMyHelp and "jianzaobangzhu-jindutiaodi2.png" or "jianzaobangzhu-jindutiaodi1.png"
    sprite = "Sprite/ui_lianmeng/" .. sprite
    SetImageSync(sliderBg, sprite, false)
    UnifyOutline(sliderValue, isMyHelp and "922D13" or "174890")

    local color1 = isMyHelp and "#C44C0A" or "#0463C4"
    local color2 = isMyHelp and "#922D13" or "#0748A9"

    local list = Split1(data.building_id, "|")
    local workerId = v2n(list[1])
    local itemId = v2n(list[2])

    --建筑名称
    local buildName = ""
    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemId)
    if itemConfig then
        local str = LangMgr:GetLang(itemConfig.id_lang) or ""
        buildName = "[" .. str .. "]"
    end

    --建筑进度
    local ownCount = v2n(data.num)
    local configCount = v2n(data.max_num)
    local ratio, count1, count2 = self:GetRatio(ownCount, configCount)
    slider.value = ratio
    sliderValue.text = count1 .. "/" .. count2
    name.text = data.player_name

    descTxt.text = string.format("<color=%s>%s </color><color=%s>%s</color>",
            color1,
            LangMgr:GetLang(9350), --"帮我建造"
            color2,
            buildName
    )

    local reduceTime = self:GetReduceTime(itemId, ownCount)
    timeTxt.text = string.format("<size=%d><color=%s>%s: </color></size><size=%d><color=%s>%s</color></size>",
            26,
            color1,
            LangMgr:GetLang(9351), --"已减少时间"
            30,
            color2,
            reduceTime
    )

    local customHeadObj = GetChild(obj, "headNode/CustomHead")
    SetHeadAndBorderByGo(customHeadObj, data.player_icon, data.player_border, function()
        if not isMyHelp then
            FriendManager:ShowPlayerById(v2n(data.player_id))
        end
    end)

end

--获取进度值
function UI_LeagueChatHelpList:GetRatio(ownCount, configCount)
    ownCount = (ownCount and ownCount > 0) and ownCount or 0
    ownCount = (ownCount >= configCount) and configCount or ownCount
    configCount = (configCount and configCount > 0) and configCount or 0
    local ratio = ownCount / configCount
    ratio = ratio > 1 and 1 or ratio
    return ratio, ownCount, configCount
end

--更新建造帮助列表
function UI_LeagueChatHelpList:RefreshBuildHelpList()
    self:ShowBuildHelpList()
end

--获取帮助减少时间
function UI_LeagueChatHelpList:GetReduceTime(itemId, count)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemId)
    if config then
        local setting = ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 37)
        if setting then
            local ratio = 1 - 1 / (1 + v2n(setting.value) / 100)
            local time = math.ceil(v2n(config.id_use or 0) * ratio * count)
            return TimeMgr:GetHMS(time)
        end
    end
    return "00:00:00"
end

--建筑帮助按钮显隐判断
function UI_LeagueChatHelpList:CheckBuildHelpBtnActive(data)
    local isActive = false
    for _, v in pairs(data) do
        if self.myPlayerId ~= v2n(v.player_id) then
            isActive = true
            break
        end
    end

    SetActive(self.ui.m_btnBuildHelpAll, isActive)
    SetActive(self.ui.m_goNoBuildHelp, #self.buildHelpList <= 0)
end

return UI_LeagueChatHelpList